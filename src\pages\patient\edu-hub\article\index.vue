<script setup lang="ts">
import {
    ref,
    definePageMeta,
    usePagePatientEduHubArticleFirstAid,
} from '#imports'
import { appRoutes } from '~/constants'

definePageMeta({
    layout: 'nested-page',
})

const { t } = useI18n()
const router = useRouter()

const layoutStore = useLayoutStore()
const { isMembership } = useMembership()

const from = ref<number>(0)
const size = ref<number>(15)
const visibleMembershipPopup = ref<boolean>(false)

const { articles, total } = usePagePatientEduHubArticleFirstAid(from, size)

const onLoadMore = () => {
    const totalArticles = total.value
    const currentSize = size.value

    if (totalArticles && isMembership.value && currentSize < totalArticles) {
        size.value = Math.min(currentSize + 15, totalArticles)
    }
}

const goToArticle = (slug: string) => {
    if (!isMembership.value) {
        visibleMembershipPopup.value = true
    } else {
        router.push(appRoutes.patient.eduhub.article.detail(slug))
    }
}

onMounted(() => layoutStore.setTitle(t('title')))
</script>

<template>
    <div>
        <SharedSmoothScroll
            style="height: calc(100vh - 64px)"
            @arrived-bottom="onLoadMore"
        >
            <template #main>
                <div class="app-margin-x mt-4 flex flex-col gap-3">
                    <div
                        v-for="article in articles"
                        :key="article._id"
                        class="flex h-24 w-full items-stretch gap-3 overflow-hidden"
                        @click="goToArticle(article.slug)"
                    >
                        <NuxtImg
                            :src="article.cover"
                            :alt="article.name"
                            width="96"
                            height="96"
                            class="rounded-lg"
                        />
                        <div class="flex flex-1 flex-col gap-1">
                            <p class="font-semibold">
                                {{ article.name }}
                            </p>
                            <p
                                v-dompurify-html="article.content"
                                :class="[
                                    'mt-1 line-clamp-3 text-sm leading-[1.6]',
                                    {
                                        '!mt-[2px] !line-clamp-2':
                                            article.name.toString().length >=
                                            30,
                                    },
                                ]"
                            />
                        </div>
                    </div>
                </div>

                <div v-if="!isMembership" class="my-4 flex justify-center">
                    <Button
                        v-if="total && articles?.length < total"
                        :label="t('btn:show-more')"
                        @click="visibleMembershipPopup = true"
                    />
                </div>
            </template>
        </SharedSmoothScroll>
        <SharedMembershipPopup
            v-model:visible="visibleMembershipPopup"
            @skip="visibleMembershipPopup = false"
            @update:visible="(val: boolean) => (visibleMembershipPopup = val)"
        />
    </div>
</template>

<i18n lang="yaml">
en:
    title: 'First aids'
    'btn:show-more': 'See more'
vi:
    title: 'Hướng dẫn sơ cứu'
    'btn:show-more': 'Xem thêm'
</i18n>
