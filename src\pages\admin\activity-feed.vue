<script setup lang="ts">
import { ref, useActivityFeed } from '#imports'
// import { Capacitor } from '@capacitor/core'
// https://github.com/knocklabs/nuxt-feed-example/blob/main/README.md

const { init, client, feed, fetchFeed } = useActivityFeed()

const menus = ref([{ name: 'all' }, { name: 'inbox' }, { name: 'archived' }])
const active = ref(0)

init({ id: 'public' })

fetchFeed()

// const feedItems = computed(() => {
//   return feed.value.items.filter((item) => !item.archived_at);
// });fv
// const archivedItems = computed(() => {
//   return feed.value.items.filter((item) => item.archived_at);
// });
</script>

<template>
    <div
        class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
    >
        <!-- BackToHome -->
        <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
            <button @click="navigateTo('/debug')">
                <span class="underline">Back to home</span>
            </button>
        </div>
        <TabMenu v-model:active-index="active" :model="menus">
            <template #item="{ item, props }">
                <a
                    v-ripple
                    v-bind="props.action"
                    class="align-items-center flex gap-2"
                >
                    <!-- <img :alt="item" :src="`/images/avatar/${item.image}`" style="width: 32px" /> -->
                    <span class="font-bold">{{ item.name }}</span>
                    <Badge value="3" severity="danger" />
                </a>
            </template>
        </TabMenu>

        <div
            v-for="feedItem in feed?.items"
            :key="feedItem.id"
            class="border-b border-[#333333] px-4 py-4"
            :class="feedItem.read_at ? 'opacity-70' : ''"
        >
            <div class="relative mb-2 flex items-center">
                <Badge v-if="!feedItem.read_at" />
                <Avatar
                    icon="pi pi-user"
                    class="mr-2"
                    size="large"
                    style="background-color: #ece9fc; color: #2a1261"
                    shape="circle"
                />
                <div class="ml-2">
                    <p class="font-semibold">
                        {{
                            `${feedItem.actors
                                .map((actor) => actor.name)
                                .join(' &')} took an action`
                        }}
                        <span class="text-sm text-[#BBBBBB]">
                            {{
                                new Date(
                                    feedItem.inserted_at,
                                ).toLocaleDateString('en-US', {
                                    month: 'short',
                                    day: 'numeric',
                                })
                            }}
                        </span>
                    </p>
                </div>
                <div class="ml-16 place-self-end">
                    <Button
                        v-if="feedItem.read_at === null"
                        variant="outline"
                        icon="pi pi-bookmark-fill"
                        size="small"
                        class="mx-1"
                        @click="client?.markAsRead(feedItem)"
                    />

                    <Button
                        v-if="feedItem.read_at !== null"
                        variant="outline"
                        icon="pi pi-bookmark"
                        size="small"
                        class="mx-1"
                        @click="client?.markAsUnread(feedItem)"
                    />

                    <Button
                        v-if="feedItem.archived_at === null"
                        variant="outline"
                        icon="pi pi-trash"
                        size="small"
                        class="mx-1"
                        @click="client?.markAsArchived(feedItem)"
                    />

                    <Button
                        v-if="feedItem.archived_at !== null"
                        variant="outline"
                        icon="pi pi-undo"
                        size="small"
                        class="mx-1"
                        @click="client?.markAsUnarchived(feedItem)"
                    />
                </div>
            </div>
            <p
                v--dompurify-html="
                    feedItem.blocks.filter((block) => block.name === 'body')[0]
                        .rendered
                "
                class="mb-1 ml-[48px] text-sm"
            />
        </div>

        <div v-if="feed?.items.length === 0" class="px-4 py-4 opacity-70">
            <p>No feeds</p>
        </div>
    </div>
</template>
