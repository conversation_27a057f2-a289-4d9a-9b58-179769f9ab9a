<script setup lang="ts">
import { parsePhoneNumberFromString } from 'libphonenumber-js'
import { wellcareUrl } from '~/constants'

export interface IResetPasswordParams {
    otp: string
    phone: string
    countryCode: string
    password: string
}

const { $fetchWellcare } = useNuxtApp()
const { t } = useI18n()
const authStore = useAuthStore()
const isLoading = ref(false)

const phoneNumber = computed(() => authStore.resetPassword?.phoneNumber || null)
const countryCode = computed(() => {
    const parsedNumber = phoneNumber.value
        ? parsePhoneNumberFromString('+' + phoneNumber.value)
        : null
    return parsedNumber?.countryCallingCode
})
const password = computed(() => authStore.resetPassword?.password || null)

const sendOtpResetPassword = (data: IResetPasswordParams) => {
    try {
        return $fetchWellcare(wellcareUrl.sendOtpForgot(), {
            method: 'POST',
            body: {
                phone: data.phone,
                countryCode: data.countryCode,
            },
        })
    } catch (error) {
        console.error('Reset password error:', error)
        throw error
    }
}

const resetPassword = async (data: IResetPasswordParams) => {
    try {
        return await $fetchWellcare('/identity/public/password/forget', {
            method: 'POST',
            body: data,
        })
    } catch (error) {
        console.error('Reset password error:', error)
        throw error
    }
}

const onSubmit = async (otp: string) => {
    console.log(phoneNumber.value, countryCode.value, password.value)

    if (!phoneNumber.value || !countryCode.value || !password.value) {
        throw new Error('Missing required fields for password reset')
    }

    const notification = push.promise(t('auth.reset-password.loading'))

    try {
        isLoading.value = true

        const resetData: IResetPasswordParams = {
            otp,
            phone: phoneNumber.value,
            countryCode: countryCode.value,
            password: password.value,
        }

        await resetPassword(resetData)

        notification.resolve(t('auth.reset-password.success'))
        const redirect = authStore.resetPassword.redirectUri
        navigateTo(redirect, { external: true })
    } catch (error) {
        notification.reject(t('auth.reset-password.error'))
        throw error
    } finally {
        isLoading.value = false
    }
}

onBeforeMount(async () => {
    await sendOtpResetPassword({
        phone: phoneNumber.value,
        countryCode: countryCode.value,
    })
})
</script>

<template>
    <PageAuthTemplate>
        <PageAuthResetPasswordVerifyOtp
            :is-loading="isLoading"
            @on:submit="onSubmit"
        />
    </PageAuthTemplate>
</template>

<i18n lang="json">
{
    "en": {
        "auth": {
            "reset-password": {
                "loading": "Processing password reset...",
                "success": "Password has been reset successfully",
                "error": "Failed to reset password. Please try again"
            }
        }
    },
    "vi": {
        "auth": {
            "reset-password": {
                "loading": "Đang xử lý đặt lại mật khẩu...",
                "success": "Đặt lại mật khẩu thành công",
                "error": "Đặt lại mật khẩu thất bại. Vui lòng thử lại"
            }
        }
    }
}
</i18n>
