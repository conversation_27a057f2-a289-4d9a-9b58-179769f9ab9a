<script setup lang="ts">
import * as yup from 'yup'

const { t } = useI18n()
const onboardingStore = useOnboardingStore()

const dob = ref('')
const inputRef = ref<any>(null)

// Calculate max date (16 years ago from today)
const maxDate = new Date()
maxDate.setFullYear(maxDate.getFullYear() - 16)

const { errors, defineField, validate, setFieldValue } = useForm({
    initialValues: {
        dob: onboardingStore.profile.dob || null, // Changed from empty string to null
    },
    validationSchema: yup.object({
        dob: yup
            .date()
            .nullable() // Allow null values
            .transform((curr, orig) => (orig === '' ? null : curr)) // Transform empty string to null
            .required(t('required'))
            .max(maxDate, t('onboarding.dob.must-be-at-least-16'))
            .min(new Date(1900, 0, 1), t('onboarding.dob.invalid-date'))
            .test('age', t('onboarding.dob.must-be-at-least-16'), (value) => {
                if (!value) return false
                const age = new Date().getFullYear() - value.getFullYear()
                // Add check for birth month and day for more accurate age calculation
                if (age === 16) {
                    const today = new Date()
                    const birthDate = new Date(value)
                    if (
                        today.getMonth() < birthDate.getMonth() ||
                        (today.getMonth() === birthDate.getMonth() &&
                            today.getDate() < birthDate.getDate())
                    ) {
                        return false
                    }
                }
                return age >= 16
            }),
    }),
})

const [dobField, dobAttrs] = defineField('dob')

// Default to editing mode if no DOB in store
const isEditingDob = ref(!onboardingStore.profile.dob)

// Watch dobField changes
watch(
    dobField,
    async (newValue) => {
        if (newValue) {
            try {
                const result = await validate()
                if (result.valid) {
                    dob.value = newValue
                    onboardingStore.setProfile({
                        ...onboardingStore.profile,
                        dob: newValue,
                    })
                }
            } catch (error) {
                console.error('Validation error:', error)
            }
        }
    },
    { deep: true },
)

// Watch store changes
watch(
    () => onboardingStore.profile.dob,
    (newDob) => {
        if (newDob && newDob !== dobField.value) {
            dob.value = newDob
            setFieldValue('dob', newDob)
        }
    },
    { immediate: true },
)

async function handleSave() {
    try {
        const result = await validate()
        if (result.valid) {
            onboardingStore.setProfile({
                ...onboardingStore.profile,
                dob: dobField.value,
            })
            isEditingDob.value = false
        }
    } catch (error) {
        console.error('Save error:', error)
    }
}

const submit = async () => {
    try {
        const result = await validate()
        if (result.valid) {
            isEditingDob.value = false
            return true
        }
        return result.valid
    } catch (error) {
        console.error('Submit error:', error)
        return false
    }
}

defineExpose({
    submit,
})
</script>

<template>
    <div class="flex min-h-[80vh] pt-8 md:items-center md:justify-center">
        <div class="w-full max-w-3xl rounded-lg">
            <div class="mb-6 w-full text-center">
                <div class="space-y-6 text-center">
                    <h1>
                        {{ t('onboarding.dob.title') }}
                    </h1>

                    <div>
                        <Datepicker
                            ref="inputRef"
                            v-model="dobField"
                            class="w-full md:max-w-xl"
                            v-bind="dobAttrs"
                            :max-date="maxDate"
                            :enable-time-picker="false"
                            text-input
                            show-icon
                            :invalid="!!errors.dob"
                            placeholder="DD/MM/YYYY"
                            date-format="dd/mm/yy"
                            @closed="handleSave"
                        />
                        <small
                            v-if="errors.dob"
                            class="inline-block text-red-500"
                            >{{ errors.dob }}</small
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
