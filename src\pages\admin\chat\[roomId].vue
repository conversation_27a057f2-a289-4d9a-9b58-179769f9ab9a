<script lang="ts" setup>
import { useRoute, useRouter, useUserInfo } from '#imports'

const route = useRoute()
const router = useRouter()
const { params, query }: any = useRoute()

const { getUserManager } = useOidc()
const { user } = useUserInfo({ scope: ['_id', 'name', 'avatar'] })

const { searchAgent } = useChat()
const { createToken, createAgentToken } = useAuthWellchat()

const agents = ref<any[]>()
const selectedAgent = ref<any>()
const firebaseToken = ref<string>('')

const visibleInformation = ref<boolean>(false)
const isShowInformation = ref<boolean>(true)

const onBack = () => {
    router.push({
        path: `/admin/chat`,
        query: {
            agent: selectedAgent.value._id,
        },
    })
}

const onChooseContact = (contact: any) => {
    let _view

    switch (contact.type) {
        case 'chat-admin':
            _view = 'admin-conversation'
            break

        case 'chat-gpt':
            _view = 'gpt-conversation'
            break

        case 'general-chat':
            _view = 'timelines'
            break

        default:
            _view = 'default-conversation'
            break
    }

    router.push({
        path: `/admin/chat/${contact._id}`,
        query: {
            _view,
        },
    })
}

const onConnected = () => {
    console.log('User connected to the chat room!')
}

const onMemberRole = (payload: any) => {
    console.log('Member role updated:', payload)
}

const onDisconnected = () => {
    console.log('User disconnected from the chat room!')
}

const onUpdatePartners = (partners: any) => {
    console.log('User partners updated:', partners)
}

const onPermissionUpdated = (permissions: any) => {
    console.log('User permissions updated:', permissions)
}

const onPressTextMessage = (message: any) => {
    console.log('User pressed a text message:', message)
}
const onPressCallMessage = (message: any) => {
    console.log('User pressed a call message:', message)
}

const signOut = () => getUserManager.signoutRedirect()

watch(selectedAgent, async () => {
    router.replace({
        ...route,
        query: {
            ...route.query,
            agent: selectedAgent.value._id,
        },
    })

    let customToken
    switch (selectedAgent.value?.type) {
        case 'user':
            customToken = await createToken(user.value?._id)
            break

        case 'agent':
            customToken = await createAgentToken({
                userId: user.value?._id,
                agent: selectedAgent.value?._id,
            })

            break

        default:
            break
    }

    if (customToken) {
        firebaseToken.value = customToken?.token
    }
})

onMounted(async () => {
    const _agents = (await searchAgent(user.value?._id)) as any[]
    agents.value = _agents.map((agent: any) => {
        return {
            _id: agent?._id,
            name: agent?.organization?.name,
            avatar: agent?.organization?.logo?.url,
            type: 'agent',
        }
    })

    agents.value.push({
        _id: user.value?._id,
        name: user.value.name,
        avatar: user.value.avatar.url,
        type: 'user',
    })

    if (route.query?.agent) {
        selectedAgent.value = agents.value.find(
            (agent: any) => agent?._id === route.query.agent,
        )
    } else {
        selectedAgent.value = agents.value.find(
            (agent: any) => agent?._id === user.value?._id,
        )
    }
})
</script>

<template>
    <div class="h-screen lg:flex lg:flex-row">
        <div class="hidden md:block lg:w-1/4">
            <w-chat-contact
                class="h-full"
                :user-id="selectedAgent?._id"
                :firebase-token="firebaseToken"
                @choose-contact="onChooseContact"
            />
        </div>

        <div class="h-full flex-1 border-l border-r">
            <w-chat-window
                v-if="firebaseToken"
                :user-id="selectedAgent?._id"
                :room-id="params.roomId"
                :firebase-token="firebaseToken"
                :theme="query?._theme"
                :blocks="
                    query?._blocks?.split(',') || ['header', 'body', 'footer']
                "
                :view="query?._view"
                hide-admin
                class="h-full"
                @connected="onConnected"
                @member-role="onMemberRole"
                @disconnected="onDisconnected"
                @update:partners="onUpdatePartners"
                @permission-updated="onPermissionUpdated"
                @press-text-message="onPressTextMessage"
                @press-call-message="onPressCallMessage"
            >
                <template #header-top>
                    <div />
                </template>
                <template #header-left>
                    <Button
                        class="lg:hidden"
                        text
                        severity="secondary"
                        @click="onBack"
                    >
                        <template #icon>
                            <iconify-icon
                                icon="mingcute:left-line"
                                class="text-2xl"
                            />
                        </template>
                    </Button>
                    <div class="hidden pr-2 lg:block" />
                </template>
                <template #header-right>
                    <Button
                        class="lg:hidden"
                        text
                        severity="secondary"
                        @click="visibleInformation = true"
                    >
                        <template #icon>
                            <iconify-icon
                                icon="bx:dock-right"
                                class="text-2xl"
                            />
                        </template>
                    </Button>
                    <Button
                        class="hidden lg:block"
                        text
                        severity="secondary"
                        @click="isShowInformation = !isShowInformation"
                    >
                        <template #icon>
                            <iconify-icon
                                icon="bx:dock-right"
                                class="text-2xl"
                            />
                        </template>
                    </Button>
                </template>
            </w-chat-window>
        </div>

        <div
            v-if="isShowInformation && firebaseToken"
            class="hidden h-full flex-col lg:flex lg:w-1/4"
        >
            <w-conversation-information
                :user-id="selectedAgent?._id"
                :room-id="params.roomId"
                :firebase-token="firebaseToken"
                class="flex-1"
            />

            <div class="m-4 flex flex-col gap-4">
                <Select
                    v-model="selectedAgent"
                    :options="agents"
                    option-label="name"
                    placeholder="Select a agent"
                >
                    <template #value="slotProps">
                        <div
                            v-if="slotProps.value"
                            class="flex items-center gap-2"
                        >
                            <img
                                :alt="slotProps.value.name"
                                :src="slotProps.value.avatar"
                                class="size-5 rounded-full"
                            />
                            <div>{{ slotProps.value.name }}</div>
                        </div>
                        <span v-else>
                            {{ slotProps.placeholder }}
                        </span>
                    </template>
                    <template #option="slotProps">
                        <div class="flex items-center gap-2">
                            <img
                                :alt="slotProps.option.name"
                                :src="slotProps.option.avatar"
                                class="size-5 rounded-full"
                            />
                            <div>{{ slotProps.option.name }}</div>
                        </div>
                    </template>
                </Select>

                <Button label="Sign out" severity="danger" @click="signOut" />
            </div>
        </div>

        <Drawer
            v-if="firebaseToken"
            v-model:visible="visibleInformation"
            position="full"
            pt:header:class="hidden"
            pt:content:class="p-0"
        >
            <div class="flex h-full flex-col">
                <w-conversation-information
                    :user-id="selectedAgent?._id"
                    :room-id="params.roomId"
                    :firebase-token="firebaseToken"
                    class="flex-1 lg:hidden"
                >
                    <template #title-left>
                        <Button
                            class="absolute top-1/2 z-10 ml-2 -translate-y-1/2 lg:hidden"
                            text
                            severity="secondary"
                            @click="visibleInformation = false"
                        >
                            <template #icon>
                                <iconify-icon
                                    icon="mingcute:left-line"
                                    class="text-2xl"
                                />
                            </template>
                        </Button>
                    </template>
                </w-conversation-information>

                <div class="m-4 flex items-center justify-between gap-4">
                    <Select
                        v-model="selectedAgent"
                        :options="agents"
                        option-label="name"
                        placeholder="Select a agent"
                    >
                        <template #value="slotProps">
                            <div
                                v-if="slotProps.value"
                                class="flex items-center gap-2"
                            >
                                <img
                                    :alt="slotProps.value.name"
                                    :src="slotProps.value.avatar"
                                    class="size-5 rounded-full"
                                />
                                <div>{{ slotProps.value.name }}</div>
                            </div>
                            <span v-else>
                                {{ slotProps.placeholder }}
                            </span>
                        </template>
                        <template #option="slotProps">
                            <div class="flex items-center gap-2">
                                <img
                                    :alt="slotProps.option.name"
                                    :src="slotProps.option.avatar"
                                    class="size-5 rounded-full"
                                />
                                <div>{{ slotProps.option.name }}</div>
                            </div>
                        </template>
                    </Select>

                    <Button
                        label="Sign out"
                        severity="danger"
                        @click="signOut"
                    />
                </div>
            </div>
        </Drawer>
    </div>
</template>
