// import { IProvider } from "./provider";
// import { IUser } from "./user";
// import { IConversation } from "./conversation";
// import { IDiagnosis } from "./diagnosis";

import type { IConversation } from './conversation'
import type { IDiagnosis } from './diagnosis'
import type { IProvider } from '../../response/user-management/provider'
import type { IUser } from '../../response/user-management/user'

export enum IConsultationState {
    WAITING = 'WAITING',
    INCONSULTATION = 'INCONSULTATION',
    COMPLETED = 'COMPLETED',
    FREE = 'FREE',
    CANCELLED = 'CANCELLED',
    REJECTED = 'REJECTED',
}
export declare enum ConsultationType {
    indepth = 'indepth',
    hometreatment = 'home-treatment',
    simple = 'simple',
    virtual = 'virtual',
    ondemand = 'ondemand',
    question = 'question',
}
export interface ICancelledBy {
    user?: string
    role?: string
    reason?: string
    comment?: string
}

export interface IExpectation {
    key: string
    order: number
    answer: {
        text: string
        audio: string
    }
    _id?: string
}
export interface IConsultationDetail {
    _id?: string
    conversation?: string | IConversation
    provider?: IProvider
    providerUser?: string | IUser
    user?: IUser
    patient?: IUser
    diagnosis?: IDiagnosis[]
    state?: keyof typeof IConsultationState
    medium?: string
    noneDrug?: boolean
    isWelcome?: boolean
    test?: boolean
    time?: string
    chiefComplaint?: string
    reason?: string
    symptom?: string
    questions?: any[]
    note?: string
    type?: keyof typeof ConsultationType
    shortLink?: {
        provider?: string
        patient?: string
    }
    altDiagnoses?: any[]
    tags?: string[]
    cancelledBy?: ICancelledBy
    createdAt?: string
    closedAt?: string
    noteAudio?: any
    forms: any[]
    followUpQuestion?: any
    lang?: 'vi' | 'en'
    expectations?: Array<IExpectation>
}
