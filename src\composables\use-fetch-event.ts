import { ref, useOidc } from '#imports'
import {
    EventStreamContentType,
    fetchEventSource,
    type FetchEventSourceInit,
} from '@microsoft/fetch-event-source'
export function useFetchEvent() {
    const logger = useLogger('useFetchEvent')
    const { getUserManager } = useOidc()
    const status = ref('idle')
    const error = ref('')
    let ctrl = new AbortController()
    const onopen = async (res: any) => {
        if (
            res.ok &&
            res.headers.get('content-type') === EventStreamContentType
        ) {
            logger.log({ msg: 'onopen', res })
        } else if (
            res.status >= 400 &&
            res.status < 500 &&
            res.status !== 429
        ) {
            logger.error({
                msg: 'onopen',
                status: res.status,
                code: res.code,
            })
        } else {
            logger.error({
                msg: 'onopen',
                status: res.status,
                code: res.code,
            })
        }
    }
    const onmessage = ({ event, data, id }: Record<string, any>) => {
        status.value = 'streaming'
        logger.trace({ msg: 'onmessage', event, data, id })
    }
    const onclose = () => {
        status.value = 'close'
    }
    const onerror = (err: any) => {
        status.value = 'close'
        error.value = err.message
    }
    const request = async (url: string, opts: FetchEventSourceInit) => {
        const account = await getUserManager.getUser()
        ctrl = new AbortController()
        logger.log('invite', url)
        fetchEventSource(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'text/event-stream',
                Accept: 'text/event-stream',
                token: account?.access_token || '',
            },
            signal: ctrl.signal,
            openWhenHidden: true,
            onopen,
            onmessage,
            onclose,
            onerror,
            ...opts,
        })
        return ctrl
    }
    const stop = () => {
        logger.log('stop', ctrl)
        ctrl.abort('stop')
        status.value = 'close'
    }

    return {
        status,
        request,
        stop,
    }
}
