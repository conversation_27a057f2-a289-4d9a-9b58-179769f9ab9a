import { it, expect, describe } from 'vitest'
import { menuLinks } from '../../utils'
import { $fetch } from '@nuxt/test-utils/e2e'
export const home = async () => {
  describe('structured data', async () => {
    it('should contain structured data', async () => {
      const html = await $fetch('/')
      expect(html).toContain(`"@type":"Organization"`)
      expect(html).toContain(`"@type":"WebSite"`)
      expect(html).toContain(`"@type":"SearchAction"`)
      expect(html).toContain(`"@type":"SoftwareApplication"`)
      expect(html).toContain(`"@type":"PostalAddress"`)
    })
  })

  it('should contain menu links', async () => {
    const html = await $fetch('/')
    menuLinks().forEach((link) => {
      expect(html).toContain(link)
    })
  })

  // it('should show address and phone', async () => {
  //   const html = await $fetch('/')
  //   expect(html).toContain(`Vietnam`)
  //   expect(html).toContain(`+84`)
  // })

  // describe('social results', async () => {
  //   it('should has links to social channels', async () => {
  //     const html = await $fetch('/')
  //     expect(html).toContain('https://www.facebook.com/')
  //     expect(html).toContain('https://www.linkedin.com/company/')
  //     expect(html).toContain('https://www.youtube.com/channel/')
  //     expect(html).toContain('https://zalo.me/')
  //   })
  // })

  // TODO
  // describe('analytics', async () => {
  //   it('should install Facebook Pixel')
  //   it('should install Google Analytics 4')
  // })
}
export default home
