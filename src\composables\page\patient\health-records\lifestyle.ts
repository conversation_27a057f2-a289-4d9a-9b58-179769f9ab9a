import type { Reactive } from 'vue'
import type { IHabitsData } from '~/models/page/health-records'

export function useLifestyle() {
  const lifeStyle: Reactive<IHabitsData> = reactive({
    lastUpdated: 'Monday 8th April 2022',
    exercise: 'Jogging, Weightlifting',
    exerciseFrequency: '5 times per week',
    tobacco: '0 cigarettes per day',
    alcohol: '2 glasses per week',
    streetDrugs: 'None',
    caffeine: '2 cups of coffee per day',
    troubleSleeping: true,
    eatingHabits: 'Well-balanced',
    eatOutMoreThanTwiceAWeek: false,
  })

  const mapLifeStyle = () => {
    if (lifeStyle)
      return [
        { label: 'Exercise', value: lifeStyle.exercise },
        {
          label: 'Exercise Frequency',
          value: lifeStyle.exerciseFrequency,
        },
        { label: 'Tobacco', value: lifeStyle.tobacco },
        { label: 'Alcohol', value: lifeStyle.alcohol },
        { label: 'Street Drugs', value: lifeStyle.streetDrugs },
        { label: 'Caffeine', value: lifeStyle.caffeine },
        {
          label: 'Trouble Sleeping',
          value: lifeStyle.troubleSleeping ? 'Yes' : 'No',
        },
        { label: 'Eating Habits', value: lifeStyle.eatingHabits },
        {
          label: 'Eat Out More Than Twice A Week',
          value: lifeStyle.eatOutMoreThanTwiceAWeek ? 'Yes' : 'No',
        },
      ]
  }

  return { lifeStyle, mapLifeStyle }
}
