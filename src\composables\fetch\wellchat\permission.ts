import { computed, useAsyncData, useNuxtApp } from '#imports'

/**
 * Composable to manage and check Wellchat permissions for GPT chat trials.
 * Automatically fetches trial status when `memberId` changes.
 *
 * @param {string} user - The authenticated user's identifier, required for the authorization header.
 * @param {Ref<string> | ComputedRef<string>} memberId - The reactive reference for the member's ID.
 * @returns {object} - Contains computed properties:
 *   - `statusCode`: The status code returned from the API response.
 *   - `activateGptTrial`: Function to activate the trial package
 *F
 * @example
 * const { statusCode, activateGptTrial } = useWellchatPermission()
 * activateGptTrial('64e5812f6e317437a4bdc246')
 *
 * // Watch for changes in the GPT trial data
 * watch(gptTrial, (newTrial) => {
 *   if (newTrial?.expiredAt) {
 *     console.log("GPT trial activated:", newTrial)
 *   }
 * })
 */

interface IGptTrialPayload {
    member: string
}

export function useWellchatPermission() {
    const { $fetchWellcare } = useNuxtApp() // Access $fetchWellcare for API requests

    const member = ref<string>('')

    // Lazy load data based on memberId updates
    const { data, execute: executeActivateGptTrial } = useAsyncData(
        member.value, // Key for caching based on the memberId value
        async () => {
            if (member.value) {
                return await $fetchWellcare(
                    '/wellchat/gpt-chat/gpt-trial/activate',
                    {
                        method: 'POST',
                        body: { memberId: member.value }, // Request body with memberId for GPT trial check
                    },
                )
            }

            return new Promise((resolve) => resolve(undefined))
        },
        {
            immediate: false,
        },
    )

    // Computed properties to access status code and GPT trial details from the response data
    const statusCode = computed(() => data.value?.code)

    const activateGptTrial = async ({ member: _member }: IGptTrialPayload) => {
        member.value = _member
        await executeActivateGptTrial()
    }

    return { statusCode, activateGptTrial }
}
