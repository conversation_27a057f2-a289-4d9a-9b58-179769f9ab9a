import type { IDate } from '../../transform'
import type { INotionPage } from '../notion'
import type { IHitNotion } from './notion'
import type { NotionConditions } from './notion-conditions'
import type { NotionSpecialties } from './notion-specialties'
import type { NotionWebsite } from './notion-website'

export interface NotionProviders {
    _id?: string
    Avatar?: {
        url?: string
    }
    Conditions: INotionPage<NotionConditions>[]
    Contents?: INotionPage<NotionWebsite>[]
    Created?: IDate
    Gender?: string
    Joined?: IDate
    Name?: string
    Rating: number
    Services?: string[]
    Slug?: string
    Specialties?: INotionPage<NotionSpecialties>[]
    StartWork?: IDate
    Status?: string
    Title?: string
    Clinics?: {
        created_time: string
        last_edited_time: string
        cover?: string
        public_url?: string
        archived: boolean
        id: string
        url: string
        properties: {
            Email: string
            Address: string
            Phone: string
            Name: string
        }
    }[]
}

export interface ProviderOutput {
    meta?: {
        minuteAvg?: number
        ratingGood?: number
    }
}

export interface HitNotionProviders extends IHitNotion {
    page: INotionPage<NotionProviders>
    output: ProviderOutput
    _highlight?: {
        page?: INotionPage<NotionProviders>
    }
}
