<template>
    <div>
        <div
            class="-ml-4 mt-0.5 flex inline-flex h-full w-full flex-row flex-nowrap items-center justify-center overflow-x-auto overflow-y-clip border border-solid border-primary bg-surface-100 bg-surface-200 bg-surface-300 bg-surface-400 bg-surface-50 bg-surface-500 bg-surface-600 bg-surface-700 bg-surface-800 bg-surface-900 bg-surface-950 px-3 text-sm text-primary text-red-500 hover:bg-primary/20"
        />

        <!-- nuxt 3 module chat -->
        <div
            class="pi pi-angle-right fixed absolute relative inset-0 left-2 right-2 top-1/2 z-10 z-50 m-0 mx-2 mx-4 mx-6 mx-auto my-2 mb-1.5 mb-2 ml-2 mt-10 mt-4 line-clamp-2 flex inline-flex grid h-12 h-16 h-8 h-[154px] h-[76px] h-auto h-full h-screen max-h-32 max-h-[500px] max-h-[96vh] min-h-16 min-h-screen w-11/12 w-12 w-3/5 w-4/5 w-8 w-[132px] w-[76px] w-fit w-full min-w-fit max-w-[80%] max-w-[96vw] flex-1 flex-grow -translate-y-1 -translate-y-1/2 cursor-pointer flex-col flex-wrap place-items-center items-end items-center items-stretch justify-end justify-center justify-between gap-2 overflow-hidden overflow-y-auto overflow-x-hidden overflow-y-hidden text-ellipsis whitespace-nowrap rounded-3xl rounded-full rounded-lg !rounded-tl-none !rounded-tr-none border border-zinc-500/10 !bg-transparent bg-[#f7f7f7] bg-black bg-primary bg-slate-200/80 bg-stone-200/50 bg-surface-300 bg-white bg-opacity-75 bg-gradient-to-r from-cyan-300 via-blue-200 to-indigo-200 object-contain object-cover p-2 p-3 p-4 px-2 py-3 text-2xl text-xl text-xs font-semibold uppercase leading-normal text-black text-surface-900 text-white shadow-sm transition-all duration-300 ease-linear hover:bg-[#efefef] hover:bg-slate-300/50 hover:bg-teal-700/90 hover:bg-transparent hover:bg-white sm:static sm:absolute sm:right-10 sm:top-2 sm:mt-0 sm:h-10 sm:w-10 sm:w-2/3 sm:translate-y-0 sm:flex-row sm:p-6 md:p-10 dark:bg-[#181818] dark:bg-[#242424] dark:bg-black dark:bg-slate-700/40 dark:bg-surface-700 dark:bg-transparent dark:text-white dark:hover:bg-[#353535] dark:hover:bg-slate-700/60 dark:hover:bg-teal-500"
        />
    </div>
</template>
