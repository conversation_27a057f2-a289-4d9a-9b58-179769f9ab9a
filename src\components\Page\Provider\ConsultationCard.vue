<template>
    <div class="card flex flex-col gap-2">
        <div class="flex flex-row items-center gap-2">
            <Tag :value="consultation.type" severity="info" />
            <Tag :value="consultation.status" />
        </div>

        <p class="truncate text-lg font-medium">
            {{ consultation.chiefComplaint }}
        </p>

        <div class="flex flex-row items-center gap-2">
            <Avatar :image="consultation.patient.avatar" />
            <p>
                {{ consultation.patient.name }}
            </p>
        </div>
        <p class="m-0">
            {{ consultation.date }}
        </p>
        <div class="flex flex-row justify-end gap-2">
            <Button
                v-for="action in consultationTypeActions[consultation.type][
                    consultation.status
                ]"
                :key="action.name"
                :label="action.name"
                :severity="action.severity"
                @click="navigateTo('/provider/patients/[id]/consultation/[id]')"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    type Consultation,
    consultationTypeActions,
} from '~/models/component/consultation'

interface Props {
    consultation?: Consultation
}
withDefaults(defineProps<Props>(), {
    consultation: () => ({
        type: 'INDEPTH',
        status: 'SCHEDULED',
        patient: {
            name: '<PERSON>',
            avatar: 'https://randomuser.me/api/portraits/men/59.jpg',
        },
        provider: {
            name: 'Elton John',
            avatar: 'https://randomuser.me/api/portraits/men/59.jpg',
        },
        date: 'Mon Jan 8',
        chiefComplaint:
            'I have an anomaly and I would like to loose some fat and burn calories',
    }),
})
</script>
