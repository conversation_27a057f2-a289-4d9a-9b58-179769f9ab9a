import { computed, useRuntimeConfig } from '#imports'
import { App } from '@capacitor/app'
import { Capacitor } from '@capacitor/core'
import { Device } from '@capacitor/device'
import { GenericEndpoint } from '~/models'

interface IPayload {
    properties?: {
        tag?: object
        language?: string
        lat: number
        long: number
        country: string
    }
    identity: {
        external_id: string
    }
    subscriptions?: Array<{
        type?: string
        token: string
        app_version?: string
        device_model?: string
        device_os?: string
        enabled: boolean
    }>
}

export interface IOneSignalUser {
    app_id?: string
    token: string
    user: string
    type: string
}

export interface IOneSignalPlayer {
    app_id?: string
    identifier: string
    external_user_id?: string
}

export function useOnesignal() {
    const { set, get } = usePreferences()
    const config = useRuntimeConfig()
    const { $fetchWellcare } = useNuxtApp()
    
    const app_id = config.public.onesignal.app_id
    const oneSignalBaseUrl = computed(() => config.public.onesignal.endpoint)
    const test_type = computed(() =>
        config.public.onesignal.isTest ? 1 : undefined,
    )

    const getPlayerId = async () => {
        return await get({ key: 'player_id' })
    }

    /**
     * Register a new device to one of OneSignal apps. Then, the user is able to receive notifications from that app.
     * @deprecated
     * @param data
     * @returns Promise response
     */
    const registerDevice = async (data: IOneSignalPlayer) => {
        const appInfo = await App.getInfo()
        const device = await Device.getInfo()
        const platform = Capacitor.getPlatform()
        let device_type = -1
        if (platform === 'ios') device_type = 0
        else device_type = 1
        const { data: res, execute } = await useFetch<any>('/api/v1/players', {
            method: 'POST',
            baseURL: oneSignalBaseUrl.value,
            body: {
                app_id: data.app_id || app_id,
                identifier: data.identifier, // device voip token
                device_type,
                external_user_id: data.external_user_id,
                game_version: appInfo.version,
                device_model: device.model,
                device_os: device.osVersion,
                test_type: test_type.value,
            },
            immediate: true,
        })

        await execute()

        await set({
            key: 'identifier',
            value: data.identifier,
        })

        await set({
            key: 'player_id',
            value: res.value.id,
        })

        return res
    }

    // const subscribe = async (data: IOneSignalPlayer) => {
    //     const appInfo = await App.getInfo()
    //     const device = await Device.getInfo()
    //     const platform = Capacitor.getPlatform()
    //     let device_type = -1
    //     if (platform === 'ios') device_type = 0
    //     else device_type = 1
    //     const res = await $fetch(
    //         '/apps/app_id/users/by/external_id/alias_id/subscriptions',
    //         {
    //             method: 'POST',
    //             body: {
    //                 subscription: {
    //                     type: 'iOSPush',
    //                     token: '20bdb8fb3bdadc1bef037eefcaeb56ad6e57f3241c99e734062b6ee829271b71',
    //                     enabled: true,
    //                     notification_types: 1,
    //                     session_time: 98,
    //                     session_count: 6,
    //                     sdk: '',
    //                     device_model: 'iPhone 14',
    //                     device_os: '18.0',
    //                     rooted: false,
    //                     test_type: 1,
    //                     app_version: '5.1.7',
    //                     web_auth: '',
    //                     web_p256: '',
    //                 },
    //             },
    //             baseUrl: oneSignalBaseUrl.value,
    //         },
    //     )
    //     await set({
    //         key: 'identifier',
    //         value: data.identifier,
    //     })
    //     await set({
    //         key: 'player_id',
    //         value: res.id,
    //     })
    // }

    /**
     * Remove user record from app in OneSignal. Afterward, the user will not receive notifications from this app.
     * @param player_id
     * @param app_id
     * @returns Promise AxiosResponse
     */
    const deletePlayerRecord = async (data: { app_id?: string }) => {
        const playerId = await get({ key: 'player_id' })
        if (!playerId) return

        return await useAsyncData('notification-logout', () =>
            $fetchWellcare(
                `${GenericEndpoint.NOTIFICATION}/push/onesignal/app/${data.app_id || app_id}/logout`,
                {
                    method: 'POST',
                    body: {
                        id: playerId
                    },
                    immediate: true,
                },
            ),
        )
    }

    /**
     * @function createUser
     * @description Create a new user using the OneSignal API.
     * @see {@link https://documentation.onesignal.com/reference/create-user}
     * @param {string} app_id - OneSignal App ID.
     * @param {string} user
     * @param {string} token
     * @param {string} type
     * @returns {Promise<void>} A promise that resolves when the user is created.
     */
    const createUser = async (data: IOneSignalUser) => {
        try {
            const appInfor = await App.getInfo()
            const device = await Device.getInfo()
            const payload: IPayload = {
                identity: {
                    external_id: data.user,
                },
                subscriptions: [
                    {
                        token: data.token,
                        app_version: appInfor.version,
                        device_model: device.model,
                        device_os: device.osVersion,
                        type: data.type,
                        enabled: true,
                    },
                ],
            }
            const { data: res, execute } = await useFetch<any>(
                `/api/v1/apps/${data.app_id || app_id}/users`,
                {
                    baseURL: oneSignalBaseUrl.value,
                    method: 'POST',
                    body: payload,
                },
            )
            await execute()
            return res
        } catch (error) {
            console.error('Error creating user:', error)
        }
    }

    /**
     * @function deleteUser
     * @description Delete a user using the OneSignal API.
     * @see {@link https://documentation.onesignal.com/reference/delete-user}
     * @param {string} external_id - Unique identifier of the user to be deleted.
     * @param {string} app_id - OneSignal App ID.
     * @returns {Promise<void>} A promise that resolves when the user is deleted.
     */
    const deleteUser = async (data: {
        external_id: string
        app_id?: string
    }) => {
        try {
            const { data: res, execute } = await useFetch<any>(
                `/api/v1/apps/${data.app_id || app_id}/users/by/external_id/${data.external_id}`,
                {
                    baseURL: oneSignalBaseUrl.value,
                    method: 'DELETE',
                },
            )
            await execute()
            return res
        } catch (error) {
            console.error('Error deleting user:', error)
        }
    }

    return {
        registerDevice,
        deletePlayerRecord,
        getPlayerId,
        createUser,
        deleteUser,
    }
}
