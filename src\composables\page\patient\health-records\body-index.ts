export function useBodyIndex() {
  const bodyIndex = ref({
    height: '180',
    weight: '75',
    bmi: '23.1',
    bodyFat: '18%',
    waistCircumference: '85',
  })
  const bodyIndexMapped = computed(() => [
    { label: 'Height (cm)', value: bodyIndex.value.height },
    {
      label: 'Weight (kg)',
      value: bodyIndex.value.weight,
    },
    {
      label: 'BMI',
      value: bodyIndex.value.bmi,
    },
  ])

  return { bodyIndexMapped }
}
