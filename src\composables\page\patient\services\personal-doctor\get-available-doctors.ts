import { computed } from '#imports'
import { ElasticIndex } from '~/models'
import type { HitNotionWebsite, IProvider } from '~/models'

export function useGetAvailableDoctors() {
    const { hits, status, execute } =
        useFetchElasticWithDefault<HitNotionWebsite>(
            ElasticIndex.PROVIDERS,
            {
                size: 150,
                filters: computed(() => [
                    {
                        terms: {
                            'page.properties.Services.keyword': [
                                'personal-doctor',
                            ],
                        },
                    },
                    // {
                    //     terms: {
                    //         'output.active': [true],
                    //     },
                    // },
                    // {
                    //     terms: {
                    //         'output.published': [true],
                    //     },
                    // },
                ]),
                sort: [
                    {
                        _score: 'asc',
                    },
                    {
                        'page.properties.Rating': 'desc',
                    },
                ],
                _source: {
                    excludes: [],
                    includes: [
                        'page.properties.Slug',
                        'page.properties.Name',
                        'page.properties.Title',
                        'page.properties.Status',
                        'page.properties.Services',
                        'page.properties.Avatar.url',
                        'page.properties.Specialties.properties.Name',
                    ],
                },
            },
            {
                immediate: false,
            },
        )

    const transformDoctorsData = (hits: HitNotionWebsite[]): IProvider[] => {
        return hits.map((hit) => {
            const { page }: any = hit
            return {
                title: page?.properties.Title,
                name: page?.properties.Name,
                avatar: {
                    url: page?.properties.Avatar?.url || '',
                },
                specialties: page.properties.Specialties.map(
                    (spec: any) => spec.properties.Name,
                ),
                isEminent: page?.properties?.Services.includes('eminent'),
                slug: page?.properties.Slug,
            }
        })
    }

    const transformedHits: ComputedRef<IProvider[]> = computed(() =>
        transformDoctorsData(hits.value || []),
    )

    return {
        transformedHits,
        status,
        execute,
    }
}
