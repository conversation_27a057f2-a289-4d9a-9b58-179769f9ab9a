// utils/transform/hit.ts
import type {
    IHitNotion,
    IResponseElasticSearch,
    ITransformElasticAggregations,
    ITransformElasticHits,
} from '../../models'
import { blockstring } from './blockstring'
import { unflatten } from './unflatten'

const inner_hits = (item: IHitNotion): IHitNotion => {
    try {
        if (item.inner_hits && Object.keys(item.inner_hits).length > 0) {
            Object.keys(item.inner_hits).forEach((inner_hit_key) => {
                if (
                    item.inner_hits &&
                    item.inner_hits[inner_hit_key] &&
                    item.inner_hits[inner_hit_key].hits &&
                    item.inner_hits[inner_hit_key].hits.hits
                ) {
                    item.inner_hits[inner_hit_key].hits.hits = item.inner_hits[
                        inner_hit_key
                    ].hits.hits.map((i: any) => {
                        return unflatten(i._source)
                    })
                }
            })
        }
    } catch (e) {
        console.error(e)
    }
    return item
}

export const hit = (value: IResponseElasticSearch<IHitNotion>): IHitNotion => {
    const initial = {
        page: { properties: {}, cover: {} },
    }
    let item = value?.body?.hits?.hits[0]
    item = blockstring(item)

    return {
        ...initial,
        ...item,
    }
}

export const hits = (
    value: IResponseElasticSearch<IHitNotion>,
): ITransformElasticHits<IHitNotion> => {
    return {
        hits:
            value?.body?.hits?.hits
                .map((i: any) => blockstring(i))
                .map((i: any) => inner_hits(i)) || [],
        total: value?.body?.hits?.total?.value || 0,
    }
}

export const aggregations = (
    value: IResponseElasticSearch<IHitNotion>,
): ITransformElasticAggregations<IHitNotion> => {
    return {
        hits:
            value?.body?.hits?.hits
                .map((i: any) => blockstring(i))
                .map((i: any) => inner_hits(i)) || [],
        total: value?.body?.hits?.total?.value || 0,
        aggregations: value?.body?.aggregations || [],
    }
}
