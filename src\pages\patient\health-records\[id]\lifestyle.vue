<script setup lang="ts">
import { useLifestyle } from '~/composables/page/patient/health-records/lifestyle'
definePageMeta({
    layout: 'nested-page',
})
const { lifeStyle } = useLifestyle()
</script>

<template>
    <div class="flex flex-col px-6">
        <h2>Lifestyle</h2>

        <PagePatientHealthRecordsLifestyleCard :life-style="lifeStyle" />
        <InlineMessage severity="info" class="self-center"
            >Last updated {{ lifeStyle.lastUpdated }}</InlineMessage
        >
        <Button label="Update" class="mt-3 self-end" />
    </div>
</template>
