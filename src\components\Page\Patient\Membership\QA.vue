<script setup lang="ts">
const userData = {
    remainingQuestions: 2,
    totalQuestions: 10,
    packagePrice: 500000,
    history: [
        {
            date: 'Today',
            records: [
                {
                    time: '10:30',
                    doctor: 'Dr. <PERSON><PERSON><PERSON><PERSON>',
                    question: 'T<PERSON><PERSON> muốn hỏi xyz ...',
                    cost: -1,
                },
                {
                    time: '8:00',
                    doctor: 'Dr. <PERSON><PERSON><PERSON><PERSON>',
                    question: 'Lorem ipsum dolor sit amet, ...',
                    cost: -1,
                },
            ],
        },
        {
            date: '20 January',
            records: [
                {
                    time: '19:47',
                    doctor: 'Dr. <PERSON><PERSON><PERSON><PERSON>',
                    question: 'Lorem ipsum dolor sit amet, ...',
                    cost: -1,
                },
                {
                    time: '17:05',
                    doctor: 'Dr. <PERSON><PERSON><PERSON><PERSON>',
                    question: 'Lorem ipsum dolor sit amet, ...',
                    cost: -1,
                },
                {
                    time: '15:21',
                    doctor: 'Dr. <PERSON><PERSON><PERSON><PERSON>',
                    question: 'Lorem ipsum dolor sit amet, ...',
                    cost: -1,
                },
            ],
        },
    ],
}
</script>

<template>
    <div class="">
        <!-- Package Information -->
        <div class="mb-4 rounded-md bg-orange-100 p-4 dark:bg-orange-950">
            <div class="flex items-center justify-between">
                <div>
                    <p class="text-surface-500 dark:text-surface-300">
                        Bạn còn lại
                    </p>
                    <p class="font-bold text-orange-600">
                        {{ userData.remainingQuestions }}/{{
                            userData.totalQuestions
                        }}
                        lượt hỏi
                    </p>
                </div>
                <button class="rounded-md bg-orange-500 px-4 py-2 text-white">
                    Mua thêm
                </button>
            </div>
            <p class="mt-2 text-surface-500 dark:text-surface-300">
                Bổ sung thêm 10 lượt gửi câu hỏi, thắc mắc sau khám đến bác sĩ.
            </p>
            <p class="mt-1 font-bold">
                {{ userData.packagePrice.toLocaleString() }} VND/năm
            </p>
        </div>

        <!-- Interaction History -->
        <div>
            <h2 class="mb-2 text-lg font-semibold">Lịch sử</h2>
            <div
                v-for="history in userData.history"
                :key="history.date"
                class="mb-6"
            >
                <p class="font-semibold text-surface-700 dark:text-surface-400">
                    {{ history.date }}
                </p>
                <ul>
                    <li
                        v-for="record in history.records"
                        :key="record.time"
                        class="mt-3 flex flex-row items-center"
                    >
                        <img
                            class="mr-2 h-10 w-10 rounded-full"
                            src="https://via.placeholder.com/150"
                            alt="doctor profile"
                        />
                        <div class="flex-1">
                            <p>{{ record.question }}</p>
                            <p class="text-sm text-surface-500">
                                {{ record.doctor }} - {{ record.time }}
                            </p>
                        </div>
                        <p class="text-end text-red-500 dark:text-red-400">
                            {{ record.cost }} lượt
                        </p>
                    </li>
                </ul>
            </div>
        </div>
    </div>
</template>
