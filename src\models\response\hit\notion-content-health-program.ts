import type { INotionPage, NotionHealthProgram } from '../notion'
import type { IHitNotion } from './notion'

export interface NotionContentHealthProgram {
  'Parent item'?: INotionPage<NotionContentHealthProgram>[]
  'Sub-item'?: INotionPage<NotionContentHealthProgram>[]
  Labels?: string
  Locale?: string
  Name?: string
  Program?: INotionPage<NotionHealthProgram>
  Sapo?: string
  Slug?: string
  Status?: string
  Tags?: string[]
  Translation?: INotionPage<NotionContentHealthProgram>[]
}

export interface HitNotionContentHealthProgram extends IHitNotion {
  page?: INotionPage<NotionContentHealthProgram>
}
