<!-- PhoneVerificationStep.vue -->
<script setup lang="ts">
import { ref } from 'vue'

const emit = defineEmits(['prev', 'finish'])

const phoneNumber = ref('')
const otp = ref('')

const sendOTP = () => {
  // Implement OTP sending logic
}

const verifyOTP = () => {
  // Implement OTP verification logic
  emit('finish')
}
</script>

<template>
  <div
    class="mx-auto flex flex-col gap-2"
    style="min-height: 16rem; max-width: 24rem"
  >
    <div class="mb-4 mt-4 text-center text-xl font-semibold">
      Phone Verification
    </div>
    <div class="flex space-x-2">
      <div class="flex-grow">
        <InputText
          id="phone"
          v-model="phoneNumber"
          type="tel"
          placeholder="Phone Number"
          class="w-full"
        />
      </div>
      <Button label="Send OTP" severity="secondary" @click="sendOTP" />
    </div>
    <div class="field">
      <InputText
        id="otp"
        v-model="otp"
        placeholder="Enter OTP"
        class="w-full"
      />
    </div>
    <div class="flex justify-between pt-6">
      <Button
        label="Back"
        severity="secondary"
        icon="pi pi-arrow-left"
        @click="emit('prev')"
      />
      <Button
        label="Verify & Finish"
        icon="pi pi-check"
        icon-pos="right"
        @click="verifyOTP"
      />
    </div>
  </div>
</template>
