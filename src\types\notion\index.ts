export type ParentType = 'page_id' | 'database_id' | 'block_id' | 'workspace'

export type BlockType =
  | 'heading_1'
  | 'heading_2'
  | 'heading_3'
  | 'bookmark'
  | 'breadcrumb'
  | 'bulleted_list_item'
  | 'callout'
  | 'child_database'
  | 'child_page'
  | 'code'
  | 'column_list'
  | 'column'
  | 'divider'
  | 'embed'
  | 'equation'
  | 'file'
  | 'image'
  | 'link_preview'
  | 'mention'
  | 'numbered_list_item'
  | 'paragraph'
  | 'pdf'
  | 'quote'
  | 'synced_block'
  | 'table'
  | 'table_row'
  | 'table_of_contents'
  | 'template'
  | 'to_do'
  | 'toggle'
  | 'video'

export type ColorType =
  | 'blue'
  | 'blue_background'
  | 'brown'
  | 'brown_background'
  | 'default'
  | 'gray'
  | 'gray_background'
  | 'green'
  | 'green_background'
  | 'orange'
  | 'orange_background'
  | 'yellow'
  | 'pink'
  | 'pink_background'
  | 'purple'
  | 'purple_background'
  | 'red'
  | 'red_background'
  | 'yellow_background'
  | 'default'

export type MentionType = 'database' | 'date' | 'link_preview' | 'page' | 'user'

export * from './banners'
