import { useLogs } from './use-logs'
// Bug ClarityPlugin 500 @duccanhole

declare let ClarityPlugin: any

const PROJECT_ID = 'p5e9awi1z9'
export function useClarity() {
    const log = useLogs()
    const onSuccess = (msg: string) => {
        console.log('[debug] init clarity success: ' + msg)
        log.addNativeLogs({
            context: 'clarity',
            message: 'init clarity success',
            data: {
                PROJECT_ID,
                msg,
            },
        })
    }
    const onFailure = (msg: string) => {
        console.log('[debug] init clarity fail: ' + msg)
        log.addNativeLogs({
            context: 'clarity',
            message: 'init clarity fail',
            data: {
                PROJECT_ID,
                msg,
            },
        })
    }

    const init = () => {
        console.log('[debug] init clarity')
        ClarityPlugin.initialize(PROJECT_ID, onSuccess, onFailure)
    }
    return {
        init,
    }
}
