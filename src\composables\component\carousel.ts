import { ref, computed, watch } from '#imports'
import type { Ref } from '#imports'

export interface IResponsiveOption {
  breakpoint?: string
  numVisible: number
  numScroll: number
}

export function useComponentResponsiveCarousel(
  responsiveOptions: Ref<IResponsiveOption[]>,
  defaultResponsiveOption: IResponsiveOption,
  items: Ref<any[]>,
  width: Ref<number>,
) {
  const currentPage = ref(0)
  const numVisible = ref(defaultResponsiveOption.numVisible)
  const numScroll = ref(defaultResponsiveOption.numScroll)

  const _length = computed(() => items.value?.length || 0)

  const sortedResponsiveOptions = computed(() =>
    responsiveOptions.value
      .slice()
      .sort((a, b) => parseInt(b.breakpoint) - parseInt(a.breakpoint)),
  )

  const getCurrentResponsiveOption = (currentWidth: number) => {
    let option = defaultResponsiveOption

    for (const respOption of sortedResponsiveOptions.value) {
      if (currentWidth <= parseInt(respOption.breakpoint)) {
        option = respOption
      } else {
        break
      }
    }

    numVisible.value = option.numVisible
    numScroll.value = option.numScroll
  }

  const next = () => {
    if (currentPage.value + numScroll.value <= _length.value - numVisible.value)
      currentPage.value += numScroll.value
    else if (currentPage.value + 1 <= _length.value - numVisible.value)
      currentPage.value += 1
    else currentPage.value = 0
  }

  const prev = () => {
    if (currentPage.value - numScroll.value >= 0) {
      currentPage.value -= numScroll.value
    } else if (currentPage.value - 1 >= 0) {
      currentPage.value -= 1
    } else currentPage.value = _length.value - numVisible.value
  }

  watch(width, (newWidth) => {
    getCurrentResponsiveOption(newWidth)
  })

  return {
    currentPage,
    numVisible,
    numScroll,
    _length,
    prev,
    next,
    getCurrentResponsiveOption,
  }
}
