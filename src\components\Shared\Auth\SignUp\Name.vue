<script setup lang="ts">
import * as yup from 'yup'

// const { account } = useAppAuth()
const { t } = useI18n()
const onboardingStore = useOnboardingStore()

const inputRef = ref<any>(null)

const { errors, defineField, validate } = useForm({
    validationSchema: yup.object({
        name: yup
            .string()
            .required(t('required'))
            .test(
                'fullName',
                t('onboarding.please-enter-fullname'),
                (value) => {
                    return Boolean(value?.trim()?.split(/\s+/)?.length >= 2)
                },
            ),
    }),
})

const [nameField, nameAttrs] = defineField('name')

const isEditingName = ref(false)

// Watch for nameField changes and update store only when valid
watch(nameField, async (newValue: string) => {
    const result = await validate()
    if (result.valid) {
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            name: newValue,
        })
    }
})

// Cập nhật hàm toggleEditing
async function toggleEditing() {
    isEditingName.value = !isEditingName.value

    if (isEditingName.value) {
        // Focus input khi bật chế độ edit
        await nextTick()
        inputRef.value?.focus()
    } else {
        // Khi tắt chế độ edit, validate và update store
        const result = await validate()
        if (result.valid) {
            onboardingStore.setProfile({
                ...onboardingStore.profile,
                name: nameField.value,
            })
        }
    }
}

// Thêm hàm handleSave
async function handleSave() {
    const result = await validate()
    if (result.valid) {
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            name: nameField.value,
        })
        isEditingName.value = false
    }
}

const submit = async () => {
    // onboardingStore.setEmail(account.value?.profile?.email || '')
    // onboardingStore.setUsername(
    //     (account.value?.profile?.username as string) ||
    //         account.value?.profile?.email ||
    //         '',
    // )
    // onboardingStore.setValidated({
    //     email: true,
    //     phone: false,
    // })
    const result = await validate()
    if (result.valid) {
        isEditingName.value = false
        return true
    }
    toggleEditing()
    return false
}

defineExpose({
    submit,
})
</script>

<template>
    <div class="flex min-h-[80vh] pt-8 md:items-center md:justify-center">
        <div class="w-full max-w-2xl rounded-lg">
            <div class="mb-6 w-full text-center">
                <div class="space-y-4 text-center">
                    <h1>{{ t('label-name') }}</h1>

                    <div>
                        <InputText
                            ref="inputRef"
                            v-model="nameField"
                            class="w-full"
                            :class="{
                                'border-red-400': errors.name,
                            }"
                            placeholder="Nguyen Van A"
                            v-bind="nameAttrs"
                            @keyup.enter="handleSave"
                            @blur="handleSave"
                        />
                        <small v-if="errors.name" class="text-red-500">{{
                            errors.name
                        }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<i18n lang="json">
{
    "vi": {
        "label-name": "Họ và tên"
    },
    "en": {
        "label-name": "Full name"
    }
}
</i18n>
