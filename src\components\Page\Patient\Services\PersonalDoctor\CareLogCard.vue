<script setup lang="ts">
import { appRoutes } from '~/constants'
import { useGetAssignedDoctor } from '~/composables'
import {
    ref,
    watch,
    computed,
    useI18n,
    useMembership,
    useRouter,
    useUserInfo,
} from '#imports'

const { userId } = defineProps({
    userId: {
        type: String,
        default: '',
    },
})

const { t } = useI18n()
const router = useRouter()
const isLoading = ref(false)
const error = ref<any>(null)

const doctorListRef = ref<HTMLElement | null>(null)
const doctorList: any = ref()
const userIdPS = computed(() => userId)

const {
    transformedProviders: assignedDoctors,
    additionalInfo: assignedInfo,
    execute: executeGetAssigned,
} = useGetAssignedDoctor(userIdPS)

const fetchDoctors = async () => {
    isLoading.value = true
    try {
        await executeGetAssigned()

        if (assignedDoctors.value && assignedDoctors.value.length > 0) {
            doctorList.value = assignedDoctors.value.map((doctor, index) => ({
                ...doctor,
                conversation: assignedInfo.value[index]?.conversation,
                patient: assignedInfo.value[index]?.related?._id,
            }))
        }
    } catch (err) {
        error.value = err
        console.error('Error fetching doctors:', err)
    } finally {
        isLoading.value = false
    }
}

watch(
    () => userId,
    (newValue) => {
        if (newValue) {
            fetchDoctors()
        }
    },
    { immediate: true },
)

const { user } = useUserInfo({ scope: ['_id', 'avatar', 'meta', 'name'] })

const { isMembership } = useMembership()

const handleClick = (provider: any) => {
    if (isMembership.value && provider.conversation !== undefined) {
        router.push({
            path: appRoutes.patient.diary(provider.conversation),
            query: {
                provider: provider?._id,
                providerUser: provider?.user,
                patient: provider?.patient,
            },
        })
    }
}
</script>

<template>
    <div ref="doctorListRef" class="flex flex-col gap-5 p-2">
        <template v-if="isLoading">
            <div v-for="i in 6" :key="i" class="mx-2 flex">
                <div class="shrink-0">
                    <span class="block size-28 rounded-md bg-zinc-200"></span>
                </div>

                <div class="ms-4 mt-2 w-full">
                    <p
                        class="h-4 rounded-full bg-zinc-200"
                        style="width: 40%"
                    ></p>

                    <ul class="mt-5 space-y-3">
                        <li class="h-3 w-full rounded-full bg-zinc-200"></li>
                        <li class="h-3 w-full rounded-full bg-zinc-200"></li>
                        <li class="h-3 w-full rounded-full bg-zinc-200"></li>
                    </ul>
                </div>
            </div>
        </template>
        <template v-else>
            <div
                v-for="(doctor, index) in doctorList"
                :key="doctor._id"
                class="mx-auto w-full max-w-md rounded-xl border p-4"
                style="
                    border-color: #ebecf2;
                    background: linear-gradient(
                        180deg,
                        #f5f7fa 0%,
                        rgb(255, 255, 255) 100%
                    );
                "
            >
                <div class="mb-4 flex flex-col gap-4">
                    <div class="flex flex-1 items-start gap-3">
                        <div class="h-20 w-20 flex-shrink-0">
                            <img
                                :src="
                                    doctor?.avatar?.url ||
                                    `https://ui-avatars.com/api/?name=${doctor?.name}` ||
                                    ''
                                "
                                :alt="doctor?.name"
                                class="h-full w-full rounded-lg bg-white object-cover"
                                loading="lazy"
                                decoding="async"
                                fetchpriority="high"
                                :srcset="
                                    doctor?.avatar?.url
                                        ? `${doctor?.avatar?.url} 1x, ${doctor?.avatar?.url} 2x`
                                        : ''
                                "
                                sizes="(max-width: 80px) 100vw, 80px"
                                style="
                                    border-radius: 18px;
                                    box-shadow: 1px 3px 6px #0000000f;
                                "
                            />
                        </div>
                        <div class="w-full">
                            <h2 class="text-base font-semibold text-zinc-800">
                                {{ doctor.title }} {{ doctor.name }}
                            </h2>
                            <div class="mt-2 flex justify-between gap-6">
                                <div class="flex">
                                    <img
                                        :src="
                                            user?.avatar?.url ||
                                            `https://ui-avatars.com/api/?name=${user?.name}`
                                        "
                                        :alt="user?.name"
                                        loading="lazy"
                                        decoding="async"
                                        width="32"
                                        height="32"
                                        class="h-8 w-8 rounded-full border bg-white object-cover"
                                    />
                                    <img
                                        :src="
                                            assignedInfo[index]?.related?.avatar
                                                ?.url ||
                                            `https://ui-avatars.com/api/?name=${assignedInfo[index]?.related?.name}` ||
                                            ''
                                        "
                                        :alt="
                                            assignedInfo[index]?.related?.name
                                        "
                                        loading="lazy"
                                        decoding="async"
                                        width="32"
                                        height="32"
                                        class="-ml-2 h-8 w-8 rounded-full border bg-white object-cover"
                                    />
                                </div>
                                <div class="text-right">
                                    <Button @click="handleClick(doctor)">
                                        {{ t('btn:care-log') }}
                                    </Button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </div>
</template>
