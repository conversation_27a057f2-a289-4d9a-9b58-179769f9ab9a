<template>
    <div class="flex flex-col p-4">
        <h1>All features in one app</h1>
        <ul class="m-0 mb-4 list-none p-0">
            <li
                v-for="(feature, index) in $props.features"
                :key="index"
                class="align-items-center mb-3 flex"
            >
                <i class="pi pi-check-circle mr-2 text-green-500" />
                <span class="text-surface-700">{{ feature }}</span>
            </li>
        </ul>
        <Button label="Get membership" class="p-button-lg w-full" />
    </div>
</template>

<script setup lang="ts">
defineProps<{ features: any[] }>()
</script>
