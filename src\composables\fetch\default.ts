import { ElasticIndex } from '~/models'
import { useRuntimeConfig } from '#imports'
import type { RequestElasticSearch } from '~/models'

function createConfig(): Record<ElasticIndex, RequestElasticSearch> {
    // const { locale } = useI18n()
    const runtimeConfig: any = useRuntimeConfig()

    const { page, app } = runtimeConfig.public
    const isAppProduction = app?.env === 'production'

    // const sites = page.site.split(',')
    const status = page.status.split(',')

    return {
        [ElasticIndex.NOTION_WEBSITE]: {
            query: {
                filter: [
                    // {
                    //     terms: {
                    //         'page.properties.Site.keyword': sites,
                    //     },
                    // },
                    {
                        terms: {
                            'page.properties.Status.keyword': status,
                        },
                    },
                    // {
                    //     term: {
                    //         'page.properties.Locale.keyword': locale.value,
                    //     },
                    // },
                ],
            },
            sort: [{ 'page.properties.Order': 'asc' }, '_score'],
            _source: {
                excludes: [
                    'page.properties.Sub-item',
                    'page.properties.Labels',
                    'page.properties.Status',
                    'page.properties.Site',
                    'page.archived',
                    'page.created_time',
                ],
                includes: ['page'],
            },
        },
        [ElasticIndex.CATALOG_PRODUCT]: {
            query: {
                filter: [],
                should: [],
                must: [],
            },
            sort: [{ 'output.provider.order': 'desc' }],
            _source: {
                includes: ['output.provider'],
            },
        },
        [ElasticIndex.NOTION_RATINGS]: {
            query: {
                filter: [
                    // {
                    //     term: {
                    //         'page.properties.Status.keyword': 'published',
                    //     }
                    // }
                ],
                should: [],
                must: [],
            },
            sort: ['_score'],
            _source: { includes: ['page'] },
        },
        [ElasticIndex.NOTION_BANNERS]: {
            query: {
                filter: [
                    {
                        term: {
                            'page.properties.Platforms.keyword': 'web',
                        },
                    },
                    {
                        term: {
                            'page.properties.Status.keyword': 'Done',
                        },
                    },
                    {
                        range: {
                            'page.properties.Valid.start': {
                                lt: 'now',
                            },
                        },
                    },
                    {
                        range: {
                            'page.properties.Valid.end': {
                                gt: 'now',
                            },
                        },
                    },
                ],
                should: [],
                must: [],
            },
            sort: ['_score'],
            _source: { includes: ['page.properties'] },
        },
        [ElasticIndex.PROVIDERS]: {
            query: {
                filter: [
                    {
                        terms: {
                            'page.properties.Status.keyword': isAppProduction
                                ? ['Active']
                                : ['Active', 'Inactive'],
                        },
                    },
                ],
                should: [],
                must: [],
                must_not: [],
            },
            sort: [{ 'page.properties.Rating': 'desc' }, '_score'],
            _source: {
                excludes: [],
                includes: ['page.properties', 'page.cover', 'output.meta'],
            },
        },
        [ElasticIndex.NOTION_SPECIALTIES]: {
            query: {
                filter: [
                    { term: { 'page.properties.Status.keyword': 'Done' } },
                ],
                should: [],
                must: [],
            },
            sort: ['_score'],
            _source: {
                excludes: [],
                includes: [
                    'page.properties.Slug',
                    'page.properties.Contents',
                    'page.properties.Providers',
                ],
            },
        },
        [ElasticIndex.RATINGS_HOMEPAGE]: {
            query: {
                filter: [],
                should: [],
                must: [],
            },
            sort: ['_score'],
            _source: { includes: ['page'] },
        },
        [ElasticIndex.BROADCAST_HEALTH_PROGRAM]: {},
        [ElasticIndex.NOTION_ASK_DOCTOR]: {
            query: {
                filter: [
                    {
                        terms: {
                            'page.properties.Status.keyword': status,
                        },
                    },
                ],
                should: [],
                must: [],
            },
            sort: ['_score'],
            _source: { includes: ['page'] },
        },
        [ElasticIndex.NOTION_CARETEAM_KNOWLEDGE]: {},
        [ElasticIndex.NOTION_CONDITIONS]: {},
        [ElasticIndex.NOTION_HEALTH_PROGRAM]: {},
        [ElasticIndex.NOTION_CONTENT_HEALTH_PROGRAM]: {
            _source: {
                excludes: [],
                includes: ['page'],
            },
        },
        [ElasticIndex.NOTION_EDU_HUB]: {
            query: {
                filter: [
                    {
                        terms: {
                            'page.properties.Status.keyword': status,
                        },
                    },
                ],
            },
            sort: [{ 'page.properties.Name.keyword': 'asc' }, '_score'],
            _source: {
                excludes: [],
                includes: ['page'],
            },
        },
    }
}

export function useFetchDefault(index: ElasticIndex) {
    const config = createConfig()

    const filter = config[index]?.query?.filter
    const sort = config[index]?.sort
    const should = config[index]?.query?.should
    const _source = config[index]?._source

    return { filter, sort, should, _source }
}
