import type {
  <PERSON>A<PERSON>,
  BannerPosition,
  BannerStatus,
  Platform,
} from '../../../types'
import type { IDate } from '../../transform'
import type { INotionPage, NotionFiles } from '../notion'
import type { IHitNotion } from './notion'

export interface NotionBanners {
  App?: BannerApp
  Condition?: string
  Files?: NotionFiles[]
  Platforms?: Platform[]
  Position?: BannerPosition
  Status?: BannerStatus
  Target?: string
  Title?: string
  Valid?: IDate
  Weight?: number
}

export interface HitNotionBanners extends IHitNotion {
  page?: INotionPage<NotionBanners>
}
