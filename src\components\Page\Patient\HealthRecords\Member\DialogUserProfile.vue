<script setup lang="ts">
import type { Relationship, User } from '@wellcare/nuxt3-module-account'
import { WUserProfileFormContainer } from '#components'
import { push } from 'notivue'

interface Form extends User {
    relationship: Relationship['relationship']
}

interface Props {
    visible: boolean
    isMainUser: boolean
    isCreate: boolean
    defaultDataForm: Partial<Form>
    idRelationship?: string
}

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void
    (e: 'on:refresh'): void
}>()

const props = defineProps<Props>()

const dayjs = useDayjs()
const { display } = useDisplay()
const { t } = useI18n()
const { key, rerenderSrc } = useRerender()

const wUserProfileFormContainer = ref<InstanceType<
    typeof WUserProfileFormContainer
> | null>(null)

const maxDate = computed(() =>
    props.isMainUser
        ? dayjs().subtract(16, 'years').toDate()
        : dayjs().toDate(),
)
const minDate = computed(() => dayjs().subtract(120, 'years').toDate())

const isMobile = computed(() => display?.breakpoint?.isMobile)

let loadingNotification: any = null

const handleSubmitForm = async () => {
    if (!wUserProfileFormContainer.value) return

    loadingNotification = push.promise(t('notification.loading'))

    try {
        await wUserProfileFormContainer.value.submitFormDataFn(
            props.isCreate ? 'create' : 'update',
        )
    } catch (error) {
        console.error('Form submission failed:', error)
    }
}

const handleSubmitFromSuccess = () => {
    if (loadingNotification) {
        loadingNotification.resolve({
            message: props.isCreate
                ? t('notification.create.success')
                : t('notification.update.success'),
        })
    }

    emit('update:visible', false)
    emit('on:refresh')
}

const handleSubmitFromError = () => {
    if (loadingNotification) {
        loadingNotification.reject({
            message: props.isCreate
                ? t('notification.create.error')
                : t('notification.update.error'),
        })
    }

    console.error('Form submission error occurred')
}

rerenderSrc({ source: () => props.visible })
</script>

<template>
    <Dialog
        :visible="visible"
        :draggable="false"
        :class="{
            'p-dialog-maximized': isMobile,
            'max-w-md': !isMobile,
        }"
        :pt="{ content: { class: 'px-0' } }"
        modal
        :header="
            isCreate
                ? t('dialog.add-family-member')
                : t('dialog.update-family-member')
        "
        @update:visible="(val: boolean) => emit('update:visible', val)"
    >
        <WUserProfileFormContainer
            :key="key"
            ref="wUserProfileFormContainer"
            is-require-avatar
            :max-date="maxDate"
            :min-date="minDate"
            :id-relationship="idRelationship"
            :is-show-relationship="!isMainUser"
            :text-btn-submit="isCreate ? t('button.add') : t('button.update')"
            :default-data="defaultDataForm"
            @on:submit-form="handleSubmitForm"
            @on:submit-success="handleSubmitFromSuccess"
            @on:submit-error="handleSubmitFromError"
        />
    </Dialog>
</template>

<i18n lang="json">
{
    "en": {
        "dialog": {
            "add-family-member": "Add a person",
            "update-family-member": "Update profile"
        },
        "button": {
            "add": "Add",
            "update": "Update"
        },
        "notification": {
            "loading": "Loading",
            "create": {
                "success": "Created successfully",
                "error": "Created failed"
            },
            "update": {
                "success": "Updated successfully",
                "error": "Updated failed"
            }
        }
    },
    "vi": {
        "dialog": {
            "add-family-member": "Thêm người thân",
            "update-family-member": "Cập nhật hồ sơ"
        },
        "button": {
            "add": "Thêm",
            "update": "Cập Nhật"
        },
        "notification": {
            "loading": "Đang xử lý",
            "create": {
                "success": "Tạo thành công",
                "error": "Tạo thất bại"
            },
            "update": {
                "success": "Cập nhật thành công",
                "error": "Cập nhật thất bại"
            }
        }
    }
}
</i18n>
