{"objectId": "2EqXkGNnho", "pages": [{"id": "main", "type": "input", "elements": [{"id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "input", "properties": {"input": {"label": "Reason for Request", "description": "Please enter the reason for your request", "key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "component": "short_text", "validations": [{"rule": "required"}, {"rule": "max<PERSON><PERSON><PERSON>", "params": ["50"]}], "fetch": {}}}}, {"id": "expectedTime", "type": "input", "properties": {"input": {"label": "Expected Time", "description": "Please select the expected time", "key": "expectedTime", "component": "short_text", "validations": [{"rule": "max<PERSON><PERSON><PERSON>", "params": ["50"]}], "fetch": {}}}}, {"id": "uploadedFiles", "type": "input", "properties": {"input": {"key": "uploadedFiles", "component": "file_upload", "props": {"accept": "image/*,.doc,.docx,.pdf,video/*"}, "validations": [{"rule": "max<PERSON><PERSON><PERSON>", "params": ["5"]}]}}}]}, {"id": "Ending Page", "elements": [{"id": "1", "type": "display", "properties": {"display": {"component": "html_block_content", "tag": "h1", "content": "Thank you for taking part"}}}], "type": "end"}], "title": "Callback Request", "submission": "remote", "space": {"__type": "Pointer", "className": "Space", "objectId": "ZhKJvCjuhu"}, "createdAt": "2024-09-18T05:13:30.711Z", "updatedAt": "2024-10-03T01:07:24.705Z", "updatedBy": {"__type": "Pointer", "className": "_User", "objectId": "71fBmVzaL8"}, "machine": {"id": "2EqXkGNnho", "initial": "", "states": {"main": {"on": {"SUBMIT": []}}}, "actions": {}, "guards": {}}, "computes": [{"key": "user", "value": "659bc0335028762cf46a2555", "component": "hidden_field"}, {"key": "conversation", "value": "66b20045d28f5574b24e4181", "component": "hidden_field"}, {"key": "sessionId", "value": "66b20045d28f5574b24e41811727326097111", "component": "hidden_field"}]}