import { computed, ref, type ComputedRef, type Ref } from '#build/imports'

export enum UpgradeType {
    None = '',
    Patch = 'patch',
    Minor = 'minor',
    Major = 'major',
}

/**
 * Utility function to process a version input.
 * If it's a string, return a ref of the processed version.
 * If it's a ref, return a computed ref of the processed version.
 */
function processVersion(version: string | Ref<string>): Ref<string> {
    if (typeof version === 'string') {
        return ref(version.split('+')[0])
    }
    return computed(() => version.value.split('+')[0])
}

/**
 * A composable function for comparing two semver versions and determining the type of upgrade available.
 * @param currentVersion - The current semver version as a string, a ref of a string, or a computed ref of a string.
 * @param availableVersion - The available semver version as a string, a ref of a string, or a computed ref of a string.
 * @returns An object with the type of upgrade available (patch, minor, major, or none).
 * <AUTHOR>
 */
export function useSemverCompare(
    currentVersion: string | Ref<string> | ComputedRef<string>,
    availableVersion: string | Ref<string> | ComputedRef<string>,
) {
    // Process the current version
    const currentVersionRef = processVersion(currentVersion)

    // Process the available version
    const availableVersionRef = processVersion(availableVersion)

    const upgradeType = computed(() => {
        if (!currentVersionRef.value || !availableVersionRef.value) {
            return UpgradeType.None
        }

        const [currentMajor, currentMinor, currentPatch] =
            currentVersionRef.value
                .split('.')
                .map((version) => parseInt(version))
        const [availableMajor, availableMinor, availablePatch] =
            availableVersionRef.value
                .split('.')
                .map((version) => parseInt(version))

        if (availableMajor > currentMajor) {
            return UpgradeType.Major
        }

        if (availableMajor === currentMajor && availableMinor > currentMinor) {
            return UpgradeType.Minor
        }

        if (
            availableMajor === currentMajor &&
            availableMinor === currentMinor &&
            availablePatch > currentPatch
        ) {
            return UpgradeType.Patch
        }

        return UpgradeType.None
    })

    return {
        upgradeType,
    }
}
