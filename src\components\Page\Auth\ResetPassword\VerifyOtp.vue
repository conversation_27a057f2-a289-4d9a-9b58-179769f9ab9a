<script setup lang="ts">
import { yupResolver } from '@primevue/forms/resolvers/yup'
import * as yup from 'yup'

interface Props {
    isLoading?: boolean
}

withDefaults(defineProps<Props>(), {
    isLoading: false,
})

const emit = defineEmits<{
    'on:submit': [value: number]
}>()

const { t } = useI18n()
const router = useRouter()

const PHONE_NUMBER = '(+84)2836226822'

const otp = ref()

const initialValues = ref({
    passcode: '',
})

const resolver = ref(yupResolver(yup.object({})))

const onClickCallOtp = () => {
    window.location.href = 'tel:+842836226822'
}

const onFormSubmit = ({
    valid,
    // values,
}: {
    valid: boolean
    // values: number
}) => {
    if (!valid) return
    emit('on:submit', otp.value)
    // Handle form submission
}

const handleBack = () => {
    router.back()
}
</script>

<template>
    <Form
        v-slot="$form"
        :resolver="resolver"
        :initial-values="initialValues"
        class="flex flex-col justify-center gap-6 text-center"
        @submit="onFormSubmit"
    >
        <h6 class="text-lg font-semibold">
            {{ t('verify-otp-title') }}
        </h6>

        <Button
            severity="secondary"
            class="w-52 self-center"
            @click="onClickCallOtp"
            >{{ t('call-otp') }}</Button
        >

        <p class="text-gray-700">
            {{ t('description-prefix') }}
            <a
                :href="`tel:${PHONE_NUMBER}`"
                class="text-primary hover:underline"
            >
                {{ PHONE_NUMBER }}
            </a>
            {{ t('description-suffix') }}
        </p>

        <InputOtp
            v-model="otp"
            name="passcode"
            size="large"
            integer-only
            :pt="{
                root: {
                    class: 'justify-between',
                },
                pcInputText: {
                    root: 'flex-auto',
                },
            }"
        />

        <Message
            v-if="$form.passcode?.invalid"
            severity="error"
            size="small"
            variant="simple"
        >
            {{ $form.passcode.error?.message }}
        </Message>

        <div class="space-y-4">
            <Button
                type="submit"
                class="w-full"
                :disabled="isLoading"
                :loading="isLoading"
                :label="t('submit')"
            />

            <Button
                :label="t('back')"
                severity="secondary"
                :disabled="isLoading"
                :loading="isLoading"
                class="w-full"
                @click="handleBack"
            />
        </div>
    </Form>
</template>

<i18n lang="json">
{
    "en": {
        "verify-otp-title": "The OTP",
        "call-otp": "Call to get OTP",
        "description-prefix": "Please call",
        "description-suffix": ", press 2 to hear the OTP code and enter it below:",
        "passcode-required": "Passcode is required",
        "passcode-length": "Passcode must be 6 digits",
        "passcode-number-only": "Passcode must contain only numbers",
        "submit": "Submit",
        "back": "Back"
    },
    "vi": {
        "verify-otp-title": "Nhập OTP",
        "call-otp": "Gọi để lấy OTP",
        "description-prefix": "Vui lòng gọi",
        "description-suffix": " bấm nhánh số 2 để nghe mã OTP và nhập vào ô bên dưới:",
        "passcode-required": "Vui lòng nhập mã xác thực",
        "passcode-length": "Mã xác thực phải có 6 chữ số",
        "passcode-number-only": "Mã xác thực chỉ được chứa số",
        "submit": "Xác nhận",
        "back": "Quay lại"
    }
}
</i18n>
