import type { IDate } from '../../transform'
import type {
    INotionPage,
    NotionFiles,
    NotionKeywords,
    NotionUsers,
} from '../notion'
import type { IHitNotion } from './notion'
import type { NotionProviders } from './notion-providers'

export interface NotionEduHub {
    'Date slide'?: IDate
    'Parent item'?: INotionPage<NotionEduHub>[]
    'Sub-item'?: INotionPage<NotionEduHub>[]
    Delivery?: string
    Description?: string
    Files?: INotionPage<NotionFiles>[]
    Format?: string[]
    id_provider?: string
    Keywords?: INotionPage<NotionKeywords>[]
    Labels?: string
    ModifiedAt?: IDate
    Name?: string
    Patient?: INotionPage<NotionUsers>[]
    Providers?: INotionPage<NotionProviders>[]
    PublishedAt?: IDate
    Score?: number
    Slides?: INotionPage<NotionFiles>[]
    Slug?: string
    Status?: string
    Tags?: string[]
    Type?: string
}

export interface HitNotionEduHub extends IHitNotion {
    html?: string
    page?: INotionPage<NotionEduHub>
}
