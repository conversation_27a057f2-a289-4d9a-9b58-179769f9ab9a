<script setup lang="ts">
import type { PropType } from '#imports'
import type { ComponentBanner } from '~/models'

const { banners } = defineProps({
    banners: {
        type: Array as PropType<ComponentBanner[]>,
        default: () => [],
    },
})
defineEmits(['click'])
</script>
<template>
    <Carousel
        :key="banners.length"
        :value="banners"
        :num-visible="1"
        :num-scroll="1"
        class="mt-8"
        :pt="{
            root: 'mt-8 flex flex-col px-4',
            nextbutton: 'hidden',
            previousbutton: 'hidden',
            indicatorbutton: ({ context }) =>
                `w-2 h-2 rounded-full transition duration-200 focus:outline-none focus:outline-offset-0 focus:ring-1 focus:ring-primary-500 dark:focus:ring-primary-400 ${
                    context.highlighted ? 'bg-primary' : 'bg-surface-400'
                }`,
        }"
    >
        <template #item="{ data }">
            <div :class="['w-full', data]" />
            <NuxtImg
                :src="data.source"
                class="rounded-lg transition duration-500"
                @click="$emit('click')"
            />
        </template>
    </Carousel>
</template>
