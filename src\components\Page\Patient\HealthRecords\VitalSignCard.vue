<script setup lang="ts">
import dayjs from '#build/dayjs.imports.mjs'
import type { IVitalSign } from '~/models/page/health-records'

interface Props {
    vitalSign: IVitalSign
}

defineProps<Props>()
</script>

<template>
    <div :class="['card', vitalSign.bgColor]">
        <div class="flex flex-row items-center gap-1">
            <iconify-icon
                :icon="vitalSign.icon"
                :class="['text-xl', vitalSign.iconColor]"
            />
            <p class="m-0 text-sm font-semibold">{{ vitalSign.label }}</p>
        </div>
        <p class="m-0 mt-3 text-4xl font-bold">
            {{ vitalSign.value }}
        </p>
        <p class="m-0 text-lg font-medium text-slate-500 dark:text-slate-400">
            {{ vitalSign.unit }}
        </p>

        <small>Updated {{ dayjs(vitalSign.lastUpdated).toNow() }}</small>
    </div>
</template>
