import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'K2D0mldOo9',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2jrzr4in6d',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON><PERSON> mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 24 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. <PERSON><PERSON><PERSON> sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2js00ivyt',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_media',
                            tag: 'img',
                            src: 'https://www.healthychildren.org/SiteCollectionImagesArticleImages/social-development-2-year-olds.jpg?RenditionID=6',
                            alt: '',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2js0wdixko',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chú ý lúc thấy người khác bị đau hoặc khó chịu, chẳng hạn như tạm dừng chơi hoặc trông cũng buồn khi thấy ai đó đang khóc',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2js0wdixko',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2js0xm2hjk',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhìn vào sắc mặt cha mẹ để tự biết cách phản ứng khi gặp tình huống mới',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2js0xm2hjk',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2js3td4xtj',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chỉ vào những thứ trong sách khi nghe cha mẹ hỏi, chẳng hạn như "Con gấu ở đâu nhỉ?"',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2js3td4xtj',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2js3w86lac',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói được ít nhất hai từ cùng lúc, chẳng hạn như “Thêm sữa.”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: '',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2js3w86lac',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2js3xlx85m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chỉ vào được ít nhất hai bộ phận trên cơ thể khi được cha mẹ hỏi nó ở đâu.',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: '',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2js3xlx85m',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2js3yy9bz9',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Sử dụng nhiều cử chỉ hơn là chỉ vẫy tay và chỉ tay, như là hôn gió hay gật đầu vâng.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2js3yy9bz9',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jsakly74m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Một tay nắm giữ đồ vật, trong khi tay kia mở ra để làm gì đó; ví dụ như giữ một cái hộp và tay kia mở nắp ra.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsakly74m',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jsanfehzd',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cố gắng sử dụng các công tắc, nút vặn hoặc nút bấm trên đồ chơi.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsanfehzd',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jsaoxobsm',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chơi nhiều đồ chơi cùng lúc, như là như đặt thức ăn đồ chơi lên đĩa đồ chơi.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsaoxobsm',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jsdyksw6',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Đá một trái bóng',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsdyksw6',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jse0foc4c',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chạy',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jse0foc4c',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jse1yj1ll',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Bước đi (không phải leo, bò) lên một vài bậc thang mà không cần trợ giúp.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jse1yj1ll',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jse3o45k',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Ăn được bằng thìa, muỗng',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jse3o45k',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    createdBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    updatedBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    title: '24 tháng',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
})
