import { ElasticIndex } from '~/models'
import type {
    HitNotionContentHealthProgram,
    IHealthProgramArticle,
} from '~/models'

export function usePagePatientServicesHealthProgramsSlug() {
    const { params }: any = useRoute()

    const { hit, loading } =
        useFetchElasticWithDefault<HitNotionContentHealthProgram>(
            ElasticIndex.NOTION_CONTENT_HEALTH_PROGRAM,
            {
                size: 1,
                filters: [
                    {
                        term: {
                            'page.properties.Slug.keyword': params.slug,
                        },
                    },
                ],
                _source: {
                    includes: ['page', 'blockstring'],
                },
            },
        )

    const article: ComputedRef<IHealthProgramArticle> = computed(() => {
        return {
            name: hit.value?.page?.properties.Name || '',
            cover: hit.value?.page?.cover?.url || '',
            blocks: hit.value?.page?.blocks as any,
        }
    })

    return { article, loading }
}
