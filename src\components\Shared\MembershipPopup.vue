<script lang="ts" setup>
import { Capacitor } from '@capacitor/core'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config';

const emit = defineEmits<{
    'update:visible': [value: boolean]
    skip: []
}>()
defineProps<{
    visible: boolean
}>()
const { t } = useI18n()
const { $fetchWellcare } = useNuxtApp()
const { user, refresh } = useUserInfo({ scope: 'name' })

const price = ref('250.000đ')
const period = ref(t('year'))

const isIos = computed(() => Capacitor.getPlatform() === 'ios')

const { config } = useJsonConfigApp()
const configMembershipPopup = computed(() => config.value.global?.popupMembership)
const hideTermAndPrivacy = computed(() => configMembershipPopup.value?.hideTermAndPrivacy)
const hideSignUpBtn = computed(() => configMembershipPopup.value?.hideSignUpBtn)
const hideRestoreBtn = computed(() => configMembershipPopup.value?.hideRestoreBtn)

const {
    execute: executeGetOrderProduct,
    status: statusGetOrderProduct,
    error: errorGetOrderProduct,
    data: dataGetOrderProduct,
} = useAsyncData(
    'order-single-product',
    () => {
        return $fetchWellcare('/ecommerce/order/single/product', {
            method: 'POST',
            body: {
                product: {
                    slug: 'membership',
                },
                source: window.location.origin,
                description: `${user.value?.name} membership ${new Date().toISOString().slice(0, 16).replace('T', ' ')}`,
            },
        })
    },
    {
        immediate: false,
    },
)

// Khởi tạo RevenueCat
const {
    state: rcState,
    purchasePackage,
    restorePurchases,
    $purchases,
} = useRevenueCat({
    onError: (error) => {
        push.error(error.message)
    },
})

const isLoading = computed(
    () => rcState.value.isLoading || statusGetOrderProduct.value === 'pending',
)

const closeDialog = () => {
    try {
        emit('update:visible', false)
    } catch (error) {
        console.error('Error closing dialog:', error)
    }
}

const onSignUp = async () => {
    if (rcState.value.isLoading) return

    if (isIos.value) {
        try {
            await executeGetOrderProduct()

            if (errorGetOrderProduct.value) {
                throw new Error(
                    (errorGetOrderProduct as any).value?.data?.message ||
                        errorGetOrderProduct.value.message ||
                        'Get order product failed',
                )
            }

            const orderId = dataGetOrderProduct.value?.results?.order?._id

            await $purchases.setAttributes({
                orderId,
                total: '250000',
            })

            const result = await purchasePackage()

            if (result) {
                push.success(t('subscription:success'))
                await refresh()
                window.location.reload()
                closeDialog() // Sử dụng hàm closeDialog
            } else {
                throw new Error('Purchase failed')
            }
        } catch (error) {
            console.error('Purchase failed:', error)
            push.error(t('subscription:failed'))
        }
    } else {
        navigateTo('/patient/checkout/membership')
    }
}

const onRestore = async () => {
    if (rcState.value.isLoading) return

    try {
        await restorePurchases()
        push.success(t('subscription:restored'))
        closeDialog()
    } catch (error) {
        console.error('Restore failed:', error)
        push.error(t('subscription:restore-failed'))
    }
}

const onSkip = () => emit('skip')
</script>

<template>
    <Dialog
        :visible="visible"
        :modal="true"
        :closable="false"
        class="subscription-dialog p-dialog-maximized"
        pt:root:class="bg-primary"
        pt:header:class="flex items-center justify-end shrink-0 p-4"
        pt:content:class="px-4 pb-4 pt-0 text-white flex flex-col justify-between h-full"
        :close-on-escape="false"
        @update:visible="(val: boolean) => emit('update:visible', val)"
    >
        <div class="flex h-full flex-col">
            <!-- Header Section -->
            <div class="mb-6 text-center">
                <h1 class="mb-3 text-2xl font-bold text-white">
                    {{ t('title') }}
                </h1>
                <div class="price-container">
                    <span class="text-3xl font-bold">{{ price }}</span>
                    <span class="text-lg">/{{ period }}</span>
                </div>
            </div>

            <!-- Benefits Section -->
            <div class="benefits-container flex-grow">
                <div
                    v-for="i in 4"
                    :key="`benefit-${i}`"
                    class="benefit-item mb-3 flex items-start gap-3 rounded-lg bg-white/5 p-3 hover:bg-white/10"
                >
                    <i
                        class="pi pi-check-circle mt-0.5 text-base text-green-400"
                    />
                    <span class="text-base leading-6">
                        {{ t(`default-benefit:${i}`) }}
                    </span>
                </div>
            </div>

            <!-- Terms and Privacy -->
            <div v-if="!hideTermAndPrivacy" class="mt-4 text-center text-xs text-white/60">
                <p>{{ t('terms-privacy') }}</p>
            </div>

            <!-- Action Buttons -->
            <div class="mt-6 flex flex-col gap-2">
                <Button
                    v-if="!hideSignUpBtn"
                    :label="t('btn:sign-up')"
                    :loading="isLoading"
                    class="w-full bg-white text-sm font-medium text-primary"
                    @click="onSignUp"
                />
                <Button
                    v-if="isIos && !hideRestoreBtn"
                    :label="t('btn:restore-purchase')"
                    :loading="isLoading"
                    class="w-full py-2"
                    pt:label:class="text-white/90 text-sm"
                    @click="onRestore"
                />
                <Button
                    :label="t('btn:do-later')"
                    :disabled="isLoading"
                    class="w-full py-2"
                    pt:label:class="text-white/70 text-sm"
                    @click="onSkip"
                />
            </div>
        </div>
    </Dialog>
</template>

<i18n lang="yaml">
en:
    title: 'Premium Account'
    year: year
    'default-benefit:1': 'Waiver of the service fee for every consultation'
    'default-benefit:2': 'Special rate and a prioritised schedule'
    'default-benefit:3': 'Boundless answers from our HealthGPT'
    'default-benefit:4': 'Optional upgrade: Personal doctor, Asynchronous care'
    'btn:sign-up': 'Subscribe Now'
    'btn:do-later': "I'll do it later"
    'btn:restore-purchase': 'Restore Purchase'
    'terms-privacy': 'By subscribing, you agree to our Terms of Service and Privacy Policy. Subscription auto-renews unless canceled.'
    'subscription:success': 'Successfully subscribed!'
    'subscription:failed': 'Subscription failed. Please try again.'
    'subscription:restored': 'Purchase restored successfully!'
    'subscription:restore-failed': 'Failed to restore purchase.'
vi:
    title: 'Tài khoản ưu tiên'
    year: năm
    'default-benefit:1': 'Miễn phí dịch vụ trên từng ca hẹn lẻ'
    'default-benefit:2': 'Ưu đãi phí khám, ưu tiên xếp lịch'
    'default-benefit:3': 'HealthGPT hỏi-đáp không giới hạn'
    'default-benefit:4': 'Gói nâng cấp: Bác sĩ riêng, Giải đáp sức khoẻ'
    'btn:sign-up': 'Đăng ký ngay'
    'btn:do-later': 'Để sau'
    'btn:restore-purchase': 'Khôi phục mua hàng'
    'terms-privacy': 'Bằng việc đăng ký, bạn đồng ý với Điều khoản dịch vụ và Chính sách bảo mật. Gói sẽ tự động gia hạn trừ khi bị hủy.'
    'subscription:success': 'Đăng ký thành công!'
    'subscription:failed': 'Đăng ký thất bại. Vui lòng thử lại.'
    'subscription:restored': 'Khôi phục mua hàng thành công!'
    'subscription:restore-failed': 'Khôi phục thất bại.'
</i18n>

<style scoped>
.subscription-dialog {
    max-width: 400px;
    margin: 0 auto;
}

.benefit-item {
    transition: all 0.2s ease;
}

.benefit-item:hover {
    transform: translateX(2px);
}

.price-container {
    display: inline-flex;
    align-items: baseline;
    gap: 1px;
    margin-top: 0.75rem;
    padding: 0.375rem 1rem;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 9999px;
}
</style>
