<script setup lang="ts">
type TPanel = 'radio-button' | 'checkbox'

defineProps({
    collapsed: {
        type: Boolean,
        required: true,
    },
    header: {
        type: String,
        required: true,
    },
    items: {
        type: Array as PropType<any[]>,
        required: true,
    },
    type: {
        type: String as PropType<TPanel>,
        required: true,
    },
})

const emit = defineEmits(['toggle'])

const modelValue = defineModel<any>({
    required: true,
})

const { t } = useI18n()
</script>

<template>
    <Panel :collapsed="collapsed" class="mb-3">
        <template #header>{{ header }}</template>
        <template #icons>
            <Button
                v-if="modelValue && modelValue.length"
                :label="t('btn:reset')"
                plain
                text
                size="small"
                @click="modelValue = undefined"
            />
            <Button
                :icon="collapsed ? 'pi pi-chevron-down' : 'pi pi-chevron-up'"
                text
                plain
                size="small"
                @click="emit('toggle')"
            />
        </template>
        <div
            v-for="item in items"
            :key="item.key"
            class="mb-3 flex items-center"
        >
            <RadioButton
                v-if="type === 'radio-button'"
                v-model="modelValue"
                :input-id="item.key"
                :value="item.key"
                name="dynamic"
            />
            <Checkbox
                v-if="type === 'checkbox'"
                v-model="modelValue"
                :input-id="item.key"
                :value="item.key"
                name="category"
                class="mr-2"
            />
            <label :for="item.key" class="ml-2 text-left text-sm">
                {{ item.name }}
            </label>
        </div>
    </Panel>
</template>
