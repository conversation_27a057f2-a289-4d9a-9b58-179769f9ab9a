export type IndepthStatus =
    | 'SCHEDULED'
    | 'WAITING'
    | 'INCONSULTATION'
    | 'COMPLETED'
    | 'CANCELLED'
    | 'REJECTED'

interface IAction {
    name: string
    icon: string
    severity: null | 'secondary'
}

type ConsultationType = 'INDEPTH' | 'QUESTION'

const questionActions: Record<IndepthStatus, IAction[]> = {
    SCHEDULED: [],
    INCONSULTATION: [],
    WAITING: [{ name: 'Answer', icon: 'pi pi-reply', severity: null }],
    COMPLETED: [],
    CANCELLED: [],
    REJECTED: [],
}
const indepthActions: Record<IndepthStatus, IAction[]> = {
    SCHEDULED: [
        { name: 'Reschedule', icon: 'pi pi-calendar', severity: 'secondary' },
        { name: 'Request Checkin', icon: 'pi pi-send', severity: null },
    ],
    WAITING: [
        { name: '<PERSON><PERSON>', icon: 'pi pi-times', severity: 'secondary' },
        { name: 'Start Consultation', icon: 'pi pi-play', severity: null },
    ],
    INCONSULTATION: [
        // { name: '<PERSON><PERSON>', icon: 'pi pi-times', severity: 'secondary' },
        { name: 'Complete Consultation', icon: 'pi pi-check', severity: null },
    ],
    COMPLETED: [{ name: 'Followup', icon: 'pi pi-reply', severity: null }],
    CANCELLED: [{ name: 'Reschedule', icon: 'pi pi-calendar', severity: null }],
    REJECTED: [{ name: 'Reschedule', icon: 'pi pi-calendar', severity: null }],
}

export const consultationTypeActions: Record<
    ConsultationType,
    Record<IndepthStatus, IAction[]>
> = {
    INDEPTH: indepthActions,
    QUESTION: questionActions,
}

export interface Person {
    name: string
    avatar: {
        url: string
    }
    address: string
    age: number
    gender: 'Male' | 'Female' | 'Non-binary'
}

export interface Consultation {
    type: ConsultationType
    state: IndepthStatus
    date: string
    provider: {
        name: string
        title: string
        avatar: {
            url: string
        }
    }
    patient: Person
    chiefComplaint: string
    [key: string]: any
}

export interface Question {
    patient: Person
    question: string
    date: string
    answer?: string
}
