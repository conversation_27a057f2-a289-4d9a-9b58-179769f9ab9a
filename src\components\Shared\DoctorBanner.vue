<script setup lang="ts">
import Avatar from 'primevue/avatar'
import AvatarGroup from 'primevue/avatargroup'
defineProps({
    pt: {
        type: Object,
        default: () => ({}),
    },
})
const { t } = useI18n()
const { sprites } = useImageSprite(
    'https://storage.googleapis.com/cms-gallery/664b064f1a6414290d1e15b5/danh-sach-bac-si-3.png',
    {
        rows: 4,
        cols: 7,
    },
    { width: 50 * 7, height: 50 * 4 },
)
</script>

<template>
    <div :class="['bg-zinc-100 p-4', pt.root]">
        <div :class="['mx-auto max-w-4xl', pt.avatarsBody]">
            <div class="text-xl font-bold text-primary">
                {{ t('telemedicine') }}
            </div>
            <p class="mt-3 block">
                {{ t('doctor-banner-sapo') }}
            </p>
            <AvatarGroup class="relative ml-2 mt-6 inline-flex items-center">
                <Avatar
                    v-for="(sprite, index) in sprites.slice(0, 7)"
                    :key="index"
                    :style="sprite.style"
                    shape="circle"
                    class="-ml-3 rounded-full"
                />
                <Avatar
                    shape="circle"
                    size="large"
                    :style="sprites[8].style"
                    class="overlay-avatar relative -ml-3 rounded-full"
                />
            </AvatarGroup>
            <div
                :class="[
                    'mt-3 flex gap-4 font-semibold sm:flex-row',
                    pt.bodyActions,
                ]"
            >
                <NuxtLinkLocale
                    to="/patient/services/provider"
                    :class="[pt.firstBtn]"
                >
                    <Button :label="t('btn:teleconsultation')" />
                </NuxtLinkLocale>

                <NuxtLinkLocale
                    to="/patient/checkout/dat-cau-hoi"
                    :class="[pt.secondBtn]"
                >
                    <Button outlined :label="t('btn:knowledge-question')" />
                </NuxtLinkLocale>
            </div>
        </div>
    </div>
</template>

<style scoped>
.overlay-avatar::before {
    content: '+100';
    position: absolute;
    color: white;
    background-color: #0000004a;
    border-radius: 1000px;
    height: 100%;
    width: 100%;
    display: grid;
    place-items: center;
    font-size: 14px;
}
</style>

<i18n lang="json">
{
    "en": {
        "doctor-banner-sapo": "Our MDs and therapists are available online. Connect with them for your medication management, a diagnosis, a second-opinion, or mental health consultations."
    },
    "vi": {
        "doctor-banner-sapo": "Các bác sĩ và chuyên gia tâm lý của chúng tôi hỗ trợ tư vấn trực tuyến. Hãy kết nối để hỏi về thuốc, nhận chẩn đoán, lấy ý kiến độc lập thứ hai, hoặc chăm sóc sức khỏe tinh thần."
    }
}
</i18n>
