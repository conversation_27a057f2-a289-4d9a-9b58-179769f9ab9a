<script setup lang="ts">
import { ref } from 'vue'
import { push } from 'notivue'

const value1 = ref(20)
const value2 = ref(25)
const value3 = ref(10.5)
</script>
<template>
    <div class="card flex flex-wrap gap-4">
        <div class="flex-auto">
            <label for="stacked-buttons" class="mb-2 block font-bold">
                Stacked
            </label>
            <InputNumber
                v-model="value1"
                input-id="stacked-buttons"
                show-buttons
                mode="currency"
                currency="USD"
                fluid
            />
        </div>

        <div class="flex-auto">
            <label for="minmax-buttons" class="mb-2 block font-bold">
                Min-Max Boundaries
            </label>
            <InputNumber
                v-model="value2"
                input-id="minmax-buttons"
                mode="decimal"
                show-buttons
                :min="0"
                :max="100"
                fluid
            />
        </div>
        <div class="flex-auto">
            <label for="horizontal-buttons" class="mb-2 block font-bold">
                Horizontal with Step
            </label>
            <InputNumber
                v-model="value3"
                input-id="horizontal-buttons"
                show-buttons
                button-layout="horizontal"
                :step="0.25"
                mode="currency"
                currency="EUR"
                fluid
            >
                <template #incrementbuttonicon>
                    <span class="pi pi-plus" />
                </template>
                <template #decrementbuttonicon>
                    <span class="pi pi-minus" />
                </template>
            </InputNumber>
        </div>
        <Button @click="push.success('Hello from your first notification!')">
            Push
        </Button>
        <Button @click="push.error('Lỗi rồi, ngưng trình bày!')"> Error </Button>
        <Notivue v-slot="item">
            <Notification :item="item" />
        </Notivue>
    </div>
</template>
