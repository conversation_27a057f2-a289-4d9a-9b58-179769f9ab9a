import { useNuxtApp } from '#imports'

interface ICreateOption {
    reason: string
    conversation: string
    patient: string
    provider: string
    providerUser: string
    questions: any
    user: string
    wallet: string
    state?: 'SCHEDULED' | 'WAITING' | 'INCONSULTATION'
    time: string
}

export function useConsultationSession() {
    const { $fetchWellcare } = useNuxtApp()

    const getLatestConsultationSession = async (
        user: string,
        conversation: string,
    ) => {
        const res = await $fetchWellcare(
            `/wellchat/session/${user}/${conversation}/latest`,
            {
                method: 'GET',
            },
        )
        return res
    }

    const getConsultationSessionById = async (option: {
        session: string
        user?: string
        providerUser?: string
    }) => {
        const filter: Record<string, any> = {
            session: option.session,
            state: { $in: ['SCHEDULED', 'WAITING', 'INCONSULTATION'] },
        }
        if (option.user) filter.user = option.user
        if (option.providerUser) filter.providerUser = option.providerUser
        const res = await $fetchWellcare(`/consultation-server/consultation`, {
            method: 'GET',
            query: {
                filter,
            },
        })
        return res?.results
    }

    const createConsultationSession = async (option: ICreateOption) => {
        const res = await $fetchWellcare(
            `/consultation-server/consultation/session`,
            {
                method: 'POST',
                body: option,
            },
        )
        return res?.results
    }

    const updateConsultationSession = async (
        consultation: string,
        payload: Record<string, any>,
    ) => {
        const res = await $fetchWellcare(
            `/consultation-server/consultation/session/${consultation}`,
            {
                method: 'PUT',
                body: payload,
            },
        )
        return res.results
    }

    const updateSessionWellchat = async (
        session: string,
        payload: Record<string, any>,
    ) => {
        const res = await $fetchWellcare(
            `/wellchat/session/${session}/update`,
            {
                method: 'PUT',
                body: payload,
            },
        )
        return res
    }

    const completeConsultationSession = async (
        consultation: string,
        payload: Record<string, any>,
    ) => {
        const res = await $fetchWellcare(
            `/consultation-server/consultation/session/${consultation}/complete`,
            {
                method: 'PUT',
                body: payload,
            },
        )
        return res.results
    }

    const remindCallback = async (consultation: string, time: string) => {
        const res = await $fetchWellcare(
            `/consultation-server/consultation/session/${consultation}/remind`,
            {
                method: 'PUT',
                body: {
                    time,
                },
            },
        )
        return res.results
    }

    return {
        getLatestConsultationSession,
        getConsultationSessionById,
        createConsultationSession,
        updateConsultationSession,
        updateSessionWellchat,
        completeConsultationSession,
        remindCallback,
    }
}
