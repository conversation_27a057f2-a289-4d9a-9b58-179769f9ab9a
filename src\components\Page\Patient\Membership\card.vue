<script setup lang="ts">
import { appRoutes } from '~/constants'

const router = useRouter()

const props = defineProps<{
    user: Partial<{ name: string; avatar: any }>
    title?: string
    description?: string
    showViewDetails?: boolean
}>()

const { user, title, description } = {
    user: props.user,
    title: props.title ?? 'wellcare-membership-title',
    description: props.description ?? 'wellcare-membership-description',
}

const { isMembership } = useMembership()

const handleJoinNow = async () => {
    router.push({
        path: appRoutes.patient.checkout('membership'),
    })
}

// const openMembershipDetails = () => {
//     navigateTo('/patient/services/membership-details')
// }

// const isAvatar = computed<boolean>(() => !!user?.avatar?.url || !!user?.avatar)
const avatarUrl = computed<string>(
    () =>
        user?.avatar?.url ||
        user?.avatar ||
        `https://ui-avatars.com/api/?name=${user.name}`,
)
const { t } = useI18n()
</script>

<template>
    <div class="relative dark:rounded-2xl dark:bg-white">
        <NuxtImg
            class="h-full max-h-[300px] min-h-[280px] w-full rounded object-cover xxs:min-h-[260px]"
            src="/images/membership-card.png"
        />
        <div
            class="absolute left-1/2 top-6 flex w-full -translate-x-1/2 flex-col items-center px-4"
        >
            <div
                v-show="isMembership !== undefined"
                class="relative flex flex-col items-center"
            >
                <NuxtImg
                    class="relative z-10 size-8 w-auto rounded object-cover"
                    src="/images/crown.png"
                />
                <Avatar
                    class="size-[70px] -translate-y-1 rounded-full border-2 border-yellow-300 bg-white object-cover p-[1px]"
                    :image="avatarUrl"
                    :alt="user?.name"
                    shape="circle"
                />
                <Tag class="bg-yellow-50 text-xs font-medium text-yellow-500">{{
                    t('active-member')
                }}</Tag>
            </div>

            <NuxtImg
                v-show="isMembership === undefined || !isMembership"
                class="ml-3 h-20 w-auto rounded object-cover"
                src="/images/wellcare-membership.png"
            />

            <p
                class="mt-3 text-center font-semibold uppercase text-primary-500"
            >
                {{ t(title) }}
            </p>
            <p class="mt-1 text-center text-sm text-primary-900">
                {{ t(description) }}
            </p>

            <div class="mt-3 flex items-center gap-3">
                <!-- <Button
                    v-if="isMembership"
                    :label="t('view-detail')"
                    severity="secondary"
                    @click="openMembershipDetails"
                /> -->
                <!-- <Button
                    v-if="isMembership"
                    :label="t('renew')"
                    class="border-none bg-primary-400 outline-none"
                /> -->
                <Button
                    v-if="!isMembership"
                    :label="t('join-now')"
                    class="border-none bg-primary-400 outline-none"
                    @click="handleJoinNow"
                />
            </div>
        </div>
    </div>
</template>
<i18n lang="json">
{
    "vi": {
        "active-member": "Đã kích hoạt",
        "wellcare-membership-title": "Đặc quyền thành viên",
        "wellcare-membership-description": "Truy cập kho kiến thức không giới hạn, quyền lợi chăm sóc đặc biệt với mức phí ưu đãi."
    },
    "en": {
        "active-member": "Active Member",
        "wellcare-membership-title": "All-Inclusive Health Membership",
        "wellcare-membership-description": "Unlimited Library Access, Exclusive Care Benefits, Special Rates."
    }
}
</i18n>
