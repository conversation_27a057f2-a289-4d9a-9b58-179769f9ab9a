<script setup lang="ts">
defineProps({
    cover: {
        type: String,
        default:
            'https://www.science.org/do/10.1126/science.z15gour/full/_20231214_on_mental-health-1702506339177.jpg',
    },
    title: {
        type: String,
        default: '',
    },
    subtitle: {
        type: String,
        default: '',
    },
    actionLabel: {
        type: String,
        default: '',
    },
    actionLink: {
        type: String,
        default: '',
    },
    to: {
        type: String,
        default: '',
    },
    target: {
        type: String,
        default: '',
    },
})
</script>

<template>
    <div
        v-if="actionLabel"
        class="relative h-44 overflow-hidden rounded-xl bg-teal-50"
    >
        <NuxtImg :src="cover" class="h-full w-full object-cover" />
        <div class="absolute bottom-0 left-0 right-0 p-2 text-white">
            <p>{{ subtitle }}</p>
            <p class="mb-2 text-2xl font-extrabold">{{ title }}</p>
            <Button :label="actionLabel" />
        </div>
    </div>
    <NuxtLinkLocale
        v-else
        :to="to"
        :target="target"
        class="inline-block w-[152px] overflow-hidden rounded-md border dark:border dark:border-gray-600"
    >
        <NuxtImg :src="cover" />
        <b
            class="inline-block w-full whitespace-break-spaces py-3 text-center text-sm"
        >
            {{ title }}
        </b>
    </NuxtLinkLocale>
</template>
