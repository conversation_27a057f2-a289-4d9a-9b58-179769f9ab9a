<script setup lang="ts">
interface Props {
    image?: string
    icon?: string
    contentType?: 'Icon' | 'Image'
    title: string
    description: string
}

withDefaults(defineProps<Props>(), {
    icon: 'material-symbols:health-and-safety-rounded',
    contentType: 'Icon',
    image: 'https://static.vecteezy.com/system/resources/previews/027/049/357/original/flower-peace-of-mind-mental-health-2d-color-illustrations-png.png',
})
</script>

<template>
    <div class="flex flex-row gap-3 rounded-xl bg-surface-50 p-2">
        <div class="flex-auto">
            <p class="mb-2 text-xl font-bold">{{ title }}</p>
            <p class="mb-3">{{ description }}</p>
            <Button text label="Add" size="large" />
        </div>
        <div v-if="contentType == 'Icon'" class="flex items-center self-center">
            <iconify-icon
                :icon="icon"
                class="text-7xl text-primary-400 dark:text-primary-700"
            />
        </div>
        <NuxtImg
            v-else-if="contentType == 'Image'"
            :src="image"
            class="size-16"
        />
    </div>
</template>
