export const dobDayjs = (date: string) => {
    const dayjs = useDayjs()
    const { t } = useI18n({
        messages: {
            en: {
                'years-old': 'years old',
                'months-old': 'months old',
                'weeks-old': 'weeks old',
                'days-old': 'days old',
            },
            vi: {
                'years-old': 'tuổi',
                'months-old': 'tháng tuổi',
                'weeks-old': 'tuần tuổi',
                'days-old': 'ngày tuổi',
            },
        },
    })
    const dob = dayjs(date)
    const today = dayjs()
    let returnText = ''
    switch (true) {
        // format year
        case today.diff(dob, 'y', true) > 3:
            returnText += today.diff(dob, 'y') + ' ' + t('years-old')
            break
        // format month
        case today.diff(dob, 'y', true) >= 1 && today.diff(dob, 'y', true) <= 3:
            returnText += today.diff(dob, 'M') + ' ' + t('months-old')
            break
        // format weeks
        case today.diff(dob, 'y', true) < 1 && today.diff(dob, 'm', true) > 1:
            returnText += today.diff(dob, 'w') + ' ' + t('weeks-old')
            break
        // format days
        default:
            returnText += today.diff(dob, 'd') + ' ' + t('days-old')
            break
    }
    returnText += `, ${dob.format('DD-MM-YYYY')}`
    // append dob to return text
    return returnText
}
