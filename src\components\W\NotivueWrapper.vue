<script setup>
import { navigateTo } from '#app'
import { pastelTheme } from 'notivue'
function onNotivueClick(item) {
    console.log(item)
    if (item?.props?.path) {
        navigateTo(item.props.path)
    }
}
</script>

<!-- You can access src/pages/debug/notivue-space.vue to test -->
<template>
    <Notivue v-slot="item">
        <NotivueSwipe :item="item">
            <!-- Default -->
            <Notification class="mt-safe" :item="item" :theme="pastelTheme">
                <!-- https://docs.notivue.smastrom.io/built-in-notifications/progress-bar.html#progress-bar -->
                <!-- <NotificationProgress :item="item" /> -->
                <Button
                    v-if="item.props.path"
                    icon="pi pi-check"
                    variant="text"
                    size="small"
                    class="mr-2"
                    rounded
                    @click="onNotivueClick(item)"
                />
            </Notification>
        </NotivueSwipe>
    </Notivue>
</template>
