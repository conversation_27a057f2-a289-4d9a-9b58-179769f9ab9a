<script setup lang="ts">
import * as yup from 'yup'

const { account } = useAppAuth()
const { t } = useI18n()
const onboardingStore = useOnboardingStore()

const name = ref('')
const inputRef = ref<any>(null)

// Initialize name based on priority: store -> account -> default
watch(
    () => onboardingStore.profile.name,
    (newName) => {
        if (newName) {
            name.value = newName
        }
    },
)

// Initialize name on component mount
onMounted(() => {
    if (onboardingStore.profile.name) {
        name.value = onboardingStore.profile.name
    } else if (account.value?.profile?.name) {
        name.value = account.value.profile.name as string
        // Update store with account name
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            name: account.value.profile.name,
        })
    } else {
        name.value = 'Guest'
    }
})

const { errors, defineField, validate } = useForm({
    validationSchema: yup.object({
        name: yup
            .string()
            .required(t('required'))
            .test(
                'fullName',
                t('onboarding.please-enter-fullname'),
                (value) => {
                    return Boolean(value?.trim()?.split(/\s+/)?.length >= 2)
                },
            ),
    }),
})

const [nameField, nameAttrs] = defineField('name')

const isEditingName = ref(false)
const email = computed(() => account.value?.profile.email)
const label = computed(() =>
    onboardingStore.profile?.phone ? `+${onboardingStore.profile?.phone}` : '',
)

// Watch for account changes
watch(account, (val) => {
    if (val?.profile.name && !onboardingStore.profile.name) {
        name.value = val.profile.name as string
    }
})

// Set initial value
watch(
    name,
    (newValue) => {
        nameField.value = newValue
    },
    { immediate: true },
)

// Watch for nameField changes and update store only when valid
watch(nameField, async (newValue) => {
    const result = await validate()
    if (result.valid) {
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            name: newValue,
        })
    }
})

// Cập nhật hàm toggleEditing
async function toggleEditing() {
    isEditingName.value = !isEditingName.value

    if (isEditingName.value) {
        // Focus input khi bật chế độ edit
        await nextTick()
        inputRef.value?.focus()
    } else {
        // Khi tắt chế độ edit, validate và update store
        const result = await validate()
        if (result.valid) {
            onboardingStore.setProfile({
                ...onboardingStore.profile,
                name: nameField.value,
            })
        }
    }
}

// Thêm hàm handleSave
async function handleSave() {
    const result = await validate()
    if (result.valid) {
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            name: nameField.value,
        })
        isEditingName.value = false
    }
}

const submit = async () => {
    onboardingStore.setEmail(account.value?.profile?.email || '')
    onboardingStore.setUsername(
        (account.value?.profile?.username as string) ||
            account.value?.profile?.email ||
            '',
    )
    onboardingStore.setValidated({
        email: true,
        phone: false,
    })
    const result = await validate()
    if (result.valid) {
        isEditingName.value = false
        return true
    }
    toggleEditing()
    return false
}

defineExpose({
    submit,
})
</script>

<template>
    <div class="flex min-h-[80vh] pt-8 md:items-center md:justify-center">
        <div class="w-full max-w-2xl rounded-lg">
            <div class="mb-6 w-full text-center">
                <div class="space-y-4 text-center">
                    <h1>{{ t('onboarding.welcome') }}!</h1>

                    <div>
                        <Chip v-if="label" :label="label" />
                    </div>

                    <div>
                        <InputText
                            ref="inputRef"
                            v-model="nameField"
                            class="w-full"
                            :class="{
                                'border-red-400': errors.name,
                            }"
                            v-bind="nameAttrs"
                            @keyup.enter="handleSave"
                            @blur="handleSave"
                        />
                        <small v-if="errors.name" class="text-red-500">{{
                            errors.name
                        }}</small>
                    </div>

                    <p class="text-gray-600">
                        <i class="pi pi-envelope" />
                        {{ email }}
                    </p>
                </div>
            </div>
        </div>
    </div>
</template>
