<script setup lang="ts">
interface Props {
    items: any[]
    threshold: number
}

const props = defineProps<Props>()

const { t } = useI18n()

const showMore = ref(false)
const toggle = () => {
    showMore.value = !showMore.value
}
const isShowMore = computed(() => props.items.length >= props.threshold)
const less = computed(() => props.items.filter((_, i) => i < props.threshold))
const more = computed(() => props.items.filter((_, i) => i >= props.threshold))
</script>

<template>
    <div>
        <template v-for="(item, index) in less">
            <slot name="less" :data="item" :index="index" />
        </template>
        <template v-if="showMore && isShowMore">
            <template v-for="(item, index) in more">
                <slot name="more" :data="item" :index="index" />
            </template>
        </template>
        <Button
            v-if="isShowMore"
            text
            plain
            size="small"
            class="underline text-primary"
            :label="showMore ? t('btn:show-less') : t('btn:show-more')"
            @click="toggle"
        />
    </div>
</template>
