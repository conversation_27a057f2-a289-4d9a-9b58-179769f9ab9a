import { computed, type Ref, type ComputedRef } from 'vue'
import { useWalletStore } from '~/stores/wallet'

export function useUserWallets(userId: Ref<string> | ComputedRef<string>) {
    const { $fetchWellcare } = useNuxtApp()
    const walletStore = useWalletStore()

    const {
        data,
        status,
        refresh: getUserWallets,
    } = useAsyncData(
        `user-wallets-${userId.value}`,
        () =>
            $fetchWellcare(`/wallet-journal/account/user/${userId.value}/all`),
        {
            transform: (response) => {
                if (response.code === 200 || response.code === 201) {
                    walletStore.updateWallets(response.results)
                }
                return response
            },
        },
    )

    const wallets = computed(() => walletStore.wallets)

    return {
        data,
        status,
        wallets,
        getUserWallets,
    }
}
