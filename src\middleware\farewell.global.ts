import { defineNuxtRouteMiddleware } from '#imports'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'
const includePaths = ['/patient/', '/provider/']

export default defineNuxtRouteMiddleware(async (to) => {
    if (!includePaths.find((p) => to.path.includes(p))) return
    const { getConfig } = useJsonConfigApp()
    const config = await getConfig()
    if (config.global?.mode === 'farewell') return navigateTo('/farewell')
    return
})
