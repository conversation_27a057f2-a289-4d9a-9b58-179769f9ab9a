import type { DeviceInfo } from '@capacitor/device'
// https://capacitorjs.com/docs/apis/device#interfaces

export interface ISafeArea {
  top: number
  right: number
  bottom: number
  left: number
  statusBarHeight: number
}

export interface IPadding {
  top: number
  right: number
  bottom?: number
  left: number
}

export interface IAppState {
  device: DeviceInfo | null
  safeArea: ISafeArea
  signature: string
}
