import UIKit
import AVFoundation
import Capacitor
import WellcareCapacitorPluginAgora

@available(iOS 15.0, *)
@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
    
    var window: UIWindow?
    var agoraPlugin = CapacitorPluginAgoraPlugin()
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.
        NotificationCenter.default.addObserver(self, selector: #selector(onRecieveNoti(_:)), name: NSNotification.Name("accept_callkit"), object: nil
        )
        NotificationCenter.default.addObserver(self, selector: #selector(onRejectNoti(_:)), name: NSNotification.Name("reject_callkit"), object: nil)
        return true
    }
    
    public func addLogNative(data: [String: Any]){
        let currDate = Date()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "dd/MM/yyyy HH:mm:ss"    
        let timeNowFormat = dateFormatter.string(from: currDate)
        var log = data
        log["time"] =  timeNowFormat
        
        var logs: [[String: Any]] = []
        if let savedString = UserDefaults.standard.string(forKey: "CapacitorStorage.debug-log"),
           let savedData = savedString.data(using: .utf8),
           let savedArray = try? JSONSerialization.jsonObject(with: savedData, options: []) as? [[String: Any]] {
            logs = savedArray
        }
        logs.append(log)
        
        if let jsonData = try? JSONSerialization.data(withJSONObject: logs, options: .prettyPrinted),
           let jsonString = String(data: jsonData, encoding: .utf8) {
            UserDefaults.standard.setValue(jsonString, forKey: "CapacitorStorage.debug-log")
        }
    }
    
    func decodeJsonString(data: String) -> [String: Any] {
        if let jsonData = data.data(using: .utf8) {
            do{
                if let jsonDictionary = try JSONSerialization.jsonObject(with: jsonData, options: []) as? [String: Any] {
                    return jsonDictionary
                }
            }
            catch {
                debugPrint("data is invalid")
            }
        }
        return [:]
    }
    
    @objc func onRecieveNoti(_ noti: Notification){
        //        UserDefaults.standard.set("recieve noti", forKey: "CapacitorStorage.call_kit_status")
        do {
            var data = noti.userInfo;
            var payload: [String: Any] = data!["payload"] as! [String : Any]
            
            var callback = [
                "url": data!["callbackUrl"],
                "data": payload
            ]
            
//            let userStorage = UserDefaults.standard.string(forKey: "CapacitorStorage.user")
//            
//            let user = decodeJsonString(data: userStorage ?? "{}")
//
//            let authId = user["_id"]
//            
//            if user.isEmpty {
//                return
//            }
            if let env = payload["env"] as? String {
                agoraPlugin.configSocketService(env: env)
            }
            var appId = payload["appid"] as! String
            var token = payload["token"] as! String
            var channel = payload["conversation"] as! String
            let mode = payload["mode"] as! String
//            var userId: String = ""
            var authId: String = ""
            if let from = payload["to"] as? [String: Any],
               let userId = from["user"] as? String {
                authId = userId
            }
            addLogNative(data: [
                "data": [
                    "appId": appId,
                    "token": token,
                    "channelName": channel,
                    "authId": authId,
                    "callback": callback
                ],
                "context": "CONFERENCE",
                "message": "join agora channel - foreground",
            ])
            agoraPlugin.joinByCallManager(appId: appId, token: token, channelName: channel, authId: authId, mode: mode, callback: callback)
//            agoraPlugin.joinByCallManager(appId: data?["appid"] as! String, token: data?["token"] as! String, channelName: data?["conversation"] as! String, authId: authId as! String)
        }
        catch {
            debugPrint("\(error.localizedDescription)")
        }
    }
    
    @objc func onRejectNoti(_ noti: Notification) {
        agoraPlugin.leaveByCallManager()
    }
    
    func applicationWillResignActive(_ application: UIApplication) {
        // Sent when the application is about to move from active to inactive state. This can occur for certain types of temporary interruptions (such as an incoming phone call or SMS message) or when the user quits the application and it begins the transition to the background state.
        // Use this method to pause ongoing tasks, disable timers, and invalidate graphics rendering callbacks. Games should use this method to pause the game.
    }
    
    func applicationDidEnterBackground(_ application: UIApplication) {
        // Use this method to release shared resources, save user data, invalidate timers, and store enough application state information to restore your application to its current state in case it is terminated later.
        // If your application supports background execution, this method is called instead of applicationWillTerminate: when the user quits.
    }
    
    func applicationWillEnterForeground(_ application: UIApplication) {
        // Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
    }
    
    func applicationDidBecomeActive(_ application: UIApplication) {
        // Restart any tasks that were paused (or not yet started) while the application was inactive. If the application was previously in the background, optionally refresh the user interface.
    }
    
    func applicationWillTerminate(_ application: UIApplication) {
        // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    }
    
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey: Any] = [:]) -> Bool {
        // Called when the app was launched with a url. Feel free to add additional processing here,
        // but if you want the App API to support tracking app url opens, make sure to keep this call
        return ApplicationDelegateProxy.shared.application(app, open: url, options: options)
    }
    
    func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
        // Called when the app was launched with an activity, including Universal Links.
        // Feel free to add additional processing here, but if you want the App API to support
        // tracking app url opens, make sure to keep this call
        return ApplicationDelegateProxy.shared.application(application, continue: userActivity, restorationHandler: restorationHandler)
    }
}
