import type { HitNotionEduHub, IItemCard } from '~/models'
import { ElasticIndex } from '~/models'
import type { ComputedRef, Ref } from '#imports'
import { useFetchElasticWithDefault } from '#imports'

export function usePagePatientEduHubMediaTopicSlug(
    parent: string,
    from: Ref<number> | ComputedRef<number>,
    size: Ref<number> | ComputedRef<number>,
) {
    const { params }: any = useRoute()
    const dayjs = useDayjs()

    const { hit: topic } = useFetchElastic<HitNotionEduHub>(
        ElasticIndex.NOTION_EDU_HUB,
        {
            size: 1,
            filters: [
                {
                    term: {
                        'page.properties.Slug.keyword': params.topic,
                    },
                },
            ],
        },
    )

    const { hits, total, status } = useFetchElasticWithDefault<HitNotionEduHub>(
        ElasticIndex.NOTION_EDU_HUB,
        {
            from,
            size,
            filters: [
                {
                    term: {
                        'page.properties.Parent item.properties.Slug.keyword':
                            params.topic,
                    },
                },
                {
                    term: {
                        'page.properties.Parent item.id.keyword': parent,
                    },
                },
                { term: { 'page.properties.Type.keyword': 'Item' } },
            ],
            _source: {
                includes: [
                    'page.cover.url',
                    'page.properties.Name',
                    'page.properties.Labels',
                    'page.properties.Delivery',
                    'page.properties.PublishedAt',
                    'page.properties.Files.*',
                    'page.properties.Patient.*',
                    'page.properties.Providers.*',
                ],
            },
            sort: [
                { 'page.properties.Delivery.keyword': 'desc' },
                { 'page.properties.Name.keyword': 'asc' },
            ],
        },
    )

    const convertSecondsToTime = (totalSeconds: number): string => {
        const hours = Math.floor(totalSeconds / 3600)
        const minutes = Math.floor((totalSeconds % 3600) / 60)
        const seconds = totalSeconds % 60

        const formatNumber = (num: number) => (num < 10 ? `0${num}` : `${num}`)

        if (totalSeconds < 60) {
            return `00:${formatNumber(totalSeconds)}`
        } else if (totalSeconds >= 60 && totalSeconds < 3600) {
            return `${formatNumber(minutes)}:${formatNumber(seconds)}`
        } else {
            return `${formatNumber(hours)}:${formatNumber(minutes)}:${formatNumber(seconds)}`
        }
    }

    const getDuration = (hit: HitNotionEduHub) => {
        const duration =
            hit.page?.properties?.Files?.[0]?.properties?.Duration ?? 0
        return convertSecondsToTime(duration)
    }

    const getPublishedAt = (hit: HitNotionEduHub) => {
        const dateString: string =
            hit.page?.properties?.PublishedAt?.start ||
            hit.page?.properties?.Files?.[0]?.properties?.UpdatedAt ||
            hit.page?.properties?.Files?.[0]?.last_edited_time ||
            ''

        const date = dayjs(dateString)
        return date.isValid() ? date.format('MM-YYYY') : 'Invalid Date'
    }

    const items: ComputedRef<IItemCard[]> = computed(() => {
        return hits.value.map((hit: HitNotionEduHub) => {
            return {
                _id: hit._id || '',
                name: hit.page?.properties?.Name || '',
                cover:
                    hit.page?.properties.Files?.[0]?.properties?.Thumbnail ||
                    '',
                delivery: hit.page?.properties.Delivery || '',
                duration: getDuration(hit),
                publishedAt: getPublishedAt(hit),
                provider: {
                    name:
                        hit.page?.properties.Providers?.[0]?.properties?.Name ||
                        '',
                    title:
                        hit.page?.properties.Providers?.[0]?.properties
                            ?.Title || '',
                    avatar:
                        hit.page?.properties.Providers?.[0]?.properties?.Avatar
                            ?.url || '',
                },
                patient: {
                    name:
                        hit.page?.properties.Patient?.[0]?.properties?.Name ||
                        '',
                    avatar: hit.page?.properties.Patient?.[0]?.cover?.url || '',
                },
            }
        })
    })

    return { topic, items, total, status }
}
