<script setup lang="ts">
interface Props {
    title?: string
    to?: string
    userId: string
    gender?: string
    dob?: string
    isReadOnly?: boolean
}

const props = defineProps<Props>()

const wBodyIndex = ref<any | null>(null)

const computedDob = computed(() =>
    props.dob ? new Date(props.dob) : new Date(),
)

const handleAdd = (key: string) => {
    console.log('click add', key)
}

const handleClick = ({
    key,
    typeChart,
}: {
    key: string
    typeChart: string
}) => {
    navigateTo({
        path: `/patient/health-records/${props.userId}/vital-signs/${typeChart}`,
        query: {
            key,
            gender: props.gender,
            dob: props.dob,
        },
    })
}

const refreshSearch = () => {
    if (wBodyIndex.value) {
        wBodyIndex.value?.refreshSearch()
    }
}

const obsSocket = useSocketIo({
    channel: '/Observation',
    debugLevel: 'debug',
})

watch(
    [() => props.userId, () => obsSocket.state.value.isConnected],
    async ([newUserId, isConnected]) => {
        if (newUserId && isConnected) {
            await obsSocket.joinRoom({
                roomId: newUserId,
                userId: newUserId,
            })
        }
    },
    { immediate: true },
)

const debouncedRefresh = useDebounceFn(() => {
    refreshSearch()
    console.log('[socket] refreshSearch')
}, 500)

obsSocket.on('created', debouncedRefresh)
obsSocket.on('updated', debouncedRefresh)
obsSocket.on('removed', debouncedRefresh)
</script>

<template>
    <SharedPageSection
        :title="title"
        :pt="{ content: 'flex flex-col gap-3 px-6' }"
    >
        <WBodyIndex
            v-if="userId"
            :key="userId"
            ref="wBodyIndex"
            :user-id="userId"
            :is-read-only="isReadOnly"
            :dob="computedDob"
            @click="handleClick"
            @add="handleAdd"
        />
    </SharedPageSection>
</template>
