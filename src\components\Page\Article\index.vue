<script setup lang="ts">
import { type ComputedRef, type PropType, computed, useRoute } from '#imports'
import type { INotionPage, MenuItem, NotionWebsite } from '~/models'

const props = defineProps({
  breadcrumbs: {
    type: Array as PropType<MenuItem[]>,
    default: () => [],
  },
  faqs: {
    type: Object,
    default: () => ({}),
  },
  loading: {
    type: Boolean,
    default: false,
  },
  page: {
    type: Object as PropType<INotionPage<NotionWebsite>>,
    required: true,
  },
})

const { query } = useRoute()

const page: ComputedRef<INotionPage<NotionWebsite>> = computed(() => props.page)
const computedBreadcrumbs = computed(() => {
  return props.breadcrumbs.concat([
    {
      label: page.value?.properties?.Name,
    },
  ])
})
const isEditorEnabled = computed(() => query?.editor == 'true')
</script>

<template>
  <PageArticleEditor v-if="isEditorEnabled && page" :page="page" />
  <div :key="page?.id" class="relative break-words pb-12">
    <WArticleHeader class="mt-4 lg:mt-0" :page="page" :loading="loading" />

    <div class="grid grid-cols-1 gap-6 md:grid-cols-12 xl:grid-cols-12">
      <div
        class="px-5 sm:px-5 md:col-span-7 md:col-start-2 md:px-5 xl:col-span-6 xl:col-start-3 xl:px-0 2xl:px-0"
      >
        <WBreadcrumb
          :model="computedBreadcrumbs"
          :loading="loading"
          class="border-none !bg-transparent !px-0"
        />

        <WArticle :blocks="page?.blocks" :page="page" :loading="loading" body />

        <PageArticleFAQ :faqs="faqs?.hits" :loading="loading" />
      </div>
      <div
        class="relative mt-4 py-3 md:col-span-3 md:col-start-9 xl:col-span-3 xl:col-start-9 xl:px-2"
      >
        <WBanner position="right" />
      </div>
    </div>
  </div>
</template>
