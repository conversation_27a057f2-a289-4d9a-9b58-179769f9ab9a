---
kind: pipeline
type: docker
name: integration

workspace:
  base: /drone
  path: /drone/src

steps:
  - name: build
    image: mhealthvn/node-builder:20.14
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
      DOCKER_USERNAME:
        from_secret: DOCKER_USERNAME
      DOCKER_PASSWORD:
        from_secret: DOCKER_PASSWORD
    commands:
      - git branch ${DRONE_SOURCE_BRANCH}
      - git checkout ${DRONE_SOURCE_BRANCH}
      - git merge main
      - yarn install
      - yarn build

  - name: test
    image: mhealthvn/node-tester:master
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
    commands:
      - pnpm lint
      - pnpm test

volumes:
  - name: dockersock
    host:
      path: /var/run/docker.sock

image_pull_secrets:
  - dockerconfig

trigger:
  event:
    - pull_request
  branch:
    - main

---
kind: pipeline
name: release-sandbox-web
type: docker

workspace:
  base: /drone
  path: /drone/src

steps:
  - name: build image
    image: docker:dind
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
      DOCKER_USERNAME:
        from_secret: DOCKER_USERNAME
      DOCKER_PASSWORD:
        from_secret: DOCKER_PASSWORD
    commands:
      - docker login -u $${DOCKER_USERNAME} -p $${DOCKER_PASSWORD}
      - docker build --rm -t mhealthvn/${DRONE_REPO_NAME}:${DRONE_COMMIT} . -f Dockerfile.sandbox.web --build-arg GIT_COMMIT=${DRONE_COMMIT} --build-arg GIT_BRANCH=${DRONE_BRANCH} --build-arg GIT_TOKEN=${GIT_TOKEN} --build-arg DRONE_REPO_NAME=${DRONE_REPO_NAME} --build-arg BUILD_TAG=${DRONE_TAG##v} --build-arg FIRE_ENV=sandbox

  - name: push to registry
    image: docker:dind
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
      DOCKER_USERNAME:
        from_secret: DOCKER_USERNAME
      DOCKER_PASSWORD:
        from_secret: DOCKER_PASSWORD
    commands:
      - docker login -u $${DOCKER_USERNAME} -p $${DOCKER_PASSWORD}
      - docker tag mhealthvn/${DRONE_REPO_NAME}:${DRONE_COMMIT} mhealthvn/${DRONE_REPO_NAME}:${DRONE_BRANCH}
      - docker push mhealthvn/${DRONE_REPO_NAME}:${DRONE_BRANCH}

  - name: redeploy service
    image: sinlead/drone-kubectl
    pull: if-not-exists
    settings:
      kubernetes_server:
        from_secret: k8s_server
      kubernetes_cert:
        from_secret: k8s_cert
      kubernetes_token:
        from_secret: k8s_token
    commands:
      - kubectl rollout restart deployment app-wellcare-vn -n web

trigger:
  branch:
    - sandbox
  event:
    - push

volumes:
  - name: dockersock
    host:
      path: /var/run/docker.sock

---
kind: pipeline
name: release-production-web
type: docker

workspace:
  base: /drone
  path: /drone/src

steps:
  - name: build image
    image: docker:dind
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
      DOCKER_USERNAME:
        from_secret: DOCKER_USERNAME
      DOCKER_PASSWORD:
        from_secret: DOCKER_PASSWORD
    commands:
      - docker login -u $${DOCKER_USERNAME} -p $${DOCKER_PASSWORD}
      - docker build --rm -t mhealthvn/${DRONE_REPO_NAME}:${DRONE_COMMIT} . -f Dockerfile.production.web --build-arg GIT_COMMIT=${DRONE_COMMIT} --build-arg GIT_BRANCH=${DRONE_BRANCH} --build-arg GIT_TOKEN=${GIT_TOKEN} --build-arg DRONE_REPO_NAME=${DRONE_REPO_NAME} --build-arg BUILD_TAG=${DRONE_TAG##v} --build-arg FIRE_ENV=production

  - name: push to registry
    image: docker:dind
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
      DOCKER_USERNAME:
        from_secret: DOCKER_USERNAME
      DOCKER_PASSWORD:
        from_secret: DOCKER_PASSWORD
    commands:
      - docker login -u $${DOCKER_USERNAME} -p $${DOCKER_PASSWORD}
      - docker tag mhealthvn/${DRONE_REPO_NAME}:${DRONE_COMMIT} mhealthvn/${DRONE_REPO_NAME}:$${DRONE_TAG##v}
      - docker push mhealthvn/${DRONE_REPO_NAME}:$${DRONE_TAG##v}
      - docker rmi mhealthvn/${DRONE_REPO_NAME}:${DRONE_COMMIT}

  - name: git commit new release
    image: mhealthvn/drone-git-push:staging
    pull: if-not-exists
    commands:
      - /bin/drone-git-push
      - cd /drone/app/release && git add . && git commit -m "new release" && git push
    settings:
      remote: https://github.com/Wellcare/kustomize.git
      token:
        from_secret: GIT_TOKEN
      username: tewnut
      envsubst: true
      sub_path: /web/app-wellcare-vn/base
      images: 'mhealthvn/${DRONE_REPO_NAME}:${DRONE_TAG##v}'

volumes:
  - name: dockersock
    host:
      path: /var/run/docker.sock

trigger:
  event:
    - tag
  ref:
    - refs/tags/*-web

---
kind: pipeline
type: docker
name: release-android

workspace:
  base: /drone
  path: /drone/src

steps:
  - name: release android
    image: docker:dind
    pull: if-not-exists
    volumes:
      - name: dockersock
        path: /var/run/docker.sock
    environment:
      GIT_TOKEN: ${GIT_TOKEN}
      DOCKER_USERNAME:
        from_secret: DOCKER_USERNAME
      DOCKER_PASSWORD:
        from_secret: DOCKER_PASSWORD
      TELEGRAM_CHAT: '-834364162'
      TELEGRAM_TOKEN: '**********:AAGe1Ft5Hj_dGmrkl_19fajaCpW49xu7fxU'
    commands:
      - docker login -u $${DOCKER_USERNAME} -p $${DOCKER_PASSWORD}
      - docker build --rm -t mhealthvn/${DRONE_REPO_NAME}:${DRONE_COMMIT} . -f Dockerfile.production.android --build-arg GIT_TOKEN=${GIT_TOKEN} --build-arg VERSION_NAME=${DRONE_SEMVER_SHORT} --build-arg=VERSION_CODE=${DRONE_SEMVER_BUILD} --build-arg=TELEGRAM_TOKEN=$${TELEGRAM_TOKEN} --build-arg=TELEGRAM_CHAT=$${TELEGRAM_CHAT}

trigger:
  event:
    - tag
  ref:
    - refs/tags/*-android+*

volumes:
  - name: dockersock
    host:
      path: /var/run/docker.sock
