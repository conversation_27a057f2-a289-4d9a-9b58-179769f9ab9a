<script setup lang="ts">
import { useProviderCalendar, navigateTo } from '#imports'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'
import type { BookingDayDto } from '~/models'

const { t } = useI18n({ useScope: 'local' })
const dayjs = useDayjs()
const { data, execute } = await useProviderCalendar()

const convertBookingDayToArray = (bookingDay?: BookingDayDto) => {
    const dayInWeek = []
    if (bookingDay?.mon) dayInWeek.push('mon')
    if (bookingDay?.tue) dayInWeek.push('tue')
    if (bookingDay?.wed) dayInWeek.push('wed')
    if (bookingDay?.thu) dayInWeek.push('thu')
    if (bookingDay?.fri) dayInWeek.push('fri')
    if (bookingDay?.sat) dayInWeek.push('sat')
    if (bookingDay?.sun) dayInWeek.push('sun')
    return dayInWeek
}

const availableCalendars = computed(() => {
    return data.value?.results
        .filter((item) => item.type !== 'outofoffice')
        .map((item) => ({
            ...item,
            dayInWeek: convertBookingDayToArray(item.booking?.day),
            from: dayjs(item.booking?.from?.date).format(t('day.format')),
            to: dayjs(item.booking?.to?.date).format(t('day.format')),
            never: item.booking?.to?.never,
            startTime: dayjs(item.startTime).format(t('time.format')),
            endTime: dayjs(item.endTime).format(t('time.format')),
            type: item.type,
        }))
})

const outOfOfficeCalendars = computed(() => {
    return data.value?.results
        .filter((item) => item.type === 'outofoffice')
        .map((item) => ({
            ...item,
            dayInWeek: convertBookingDayToArray(item.booking?.day),
            from: dayjs(item.booking?.from?.date).format(t('day.format')),
            to: dayjs(item.booking?.to?.date).format(t('day.format')),
            startTime: dayjs(item.startTime).format(t('time.format')),
            endTime: dayjs(item.endTime).format(t('time.format')),
            type: item.type,
        }))
})

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)

onMounted(async () => {
    execute()
})
</script>

<template>
    <SharedPageSection :title="t('title')" :pt="{ content: 'mx-4' }">
        <div class="mb-4">
            <div class="rounded-2xl bg-white dark:bg-surface-900">
                <div
                    v-for="(item, index) in availableCalendars"
                    :key="index"
                    class="border-b border-gray-100 p-3 last:border-b-0"
                >
                    <div class="mb-3 flex items-center justify-between">
                        <div class="flex items-center gap-1">
                            <p
                                v-if="item.never"
                                class="font-semibold capitalize"
                            >
                                {{ t('fixed') }}
                            </p>
                            <p v-else class="font-semibold capitalize">
                                {{ item.from }} - {{ item.to }}
                            </p>
                        </div>
                        <div class="flex items-center gap-2 rounded-lg">
                            <p v-if="item.allDay" class="font-semibold">
                                {{ t('all day') }}
                            </p>
                            <p v-else class="font-semibold">
                                {{ item.startTime + ' - ' + item.endTime }}
                            </p>
                        </div>
                    </div>
                    <div class="flex flex-wrap gap-2">
                        <template v-for="day in item.dayInWeek" :key="day">
                            <Tag
                                severity="secondary"
                                class="first-letter:uppercase"
                                :value="t(day)"
                            ></Tag>
                        </template>
                    </div>
                </div>
            </div>

            <Message
                v-for="(item, key) in outOfOfficeCalendars"
                :key="key"
                icon="pi pi-info-circle"
                severity="warn"
            >
                <span class="block">{{ t('Out of office', item) }}</span>
            </Message>

            <div class="flex flex-row items-center justify-end gap-2">
                <Button
                    v-if="!hideSupportContact"
                    :label="t('request-update')"
                    severity="secondary"
                    @click="
                        navigateTo('https://zalo.me/0366905905', {
                            external: true,
                            open: { target: '-blank' },
                        })
                    "
                />
            </div>
        </div>
    </SharedPageSection>
</template>

<i18n lang="json">
{
    "en": {
        "title": "My Schedule",
        "day.format": "MMM DD",
        "time.format": "HH:mm",
        "sun": "Sun",
        "mon": "Mon",
        "tue": "Tue",
        "wed": "Wed",
        "thu": "Thu",
        "fri": "Fri",
        "sat": "Sat",
        "all week": "all week",
        "all day": "all day",
        "fixed": "fixed",
        "Out of office": "Out of office from {from} to {to}",
        "request-update": "edit",
        "no-surcharge": "Normal",
        "sun-surcharge": "20% surcharge",
        "holiday-surcharge": "30% surcharge"
    },
    "vi": {
        "title": "Lịch Khám Từ Xa",
        "day.format": "DD MMM",
        "time.format": "HH:mm",
        "sun": "CN",
        "mon": "T2",
        "tue": "T3",
        "wed": "T4",
        "thu": "T5",
        "fri": "T6",
        "sat": "T7",
        "all week": "cả tuần",
        "all day": "cả ngày",
        "fixed": "cố định",
        "Out of office": "Đi vắng từ {from} đến hết {to}",
        "request-update": "sửa",
        "no-surcharge": "Ngày thường",
        "sun-surcharge": "20% surcharge",
        "holiday-surcharge": "30% surcharge"
    }
}
</i18n>
