<script lang="ts" setup>
import type { PropType } from 'vue'
import type { IItemPage } from '~/models'
import { useAudioPlayer } from '~/composables'

const props = defineProps({
    media: {
        type: Object as PropType<IItemPage>,
        required: true,
    },
})

const {
    duration,
    waveformRef,
    isPlaying,
    isLoading,
    currentTime,
    togglePlay,
    skipBackward,
    skipForward,
    refreshAudio,
    calculateIconPosition,
} = useAudioPlayer(props.media)

const isHeartFilled = ref(false)

const animateHeart = () => {
    isHeartFilled.value = !isHeartFilled.value
}
</script>

<template>
    <div class="mx-auto max-w-md bg-white p-4">
        <!-- Cover Image -->
        <div class="mb-6 overflow-hidden rounded-3xl">
            <NuxtImg
                src="https://storage.googleapis.com/cms-gallery-sandbox/6729e7fe2a44d839636d9dd9/podcast-small.png"
                :alt="media.name"
                class="h-[300px] w-full object-cover"
            />
        </div>

        <!-- Title -->
        <div class="mb-11">
            <h2 class="mb-1 text-lg font-semibold text-gray-900">
                {{ media.name }}
            </h2>
            <div class="mt-2">
                <p
                    v-dompurify-html="media?.content"
                    class="text-sm text-gray-600"
                />
            </div>
        </div>

        <!-- Waveform -->
        <template v-if="isLoading">
            <div class="loader -mt-6" />
        </template>
        <div class="relative h-[52px]">
            <div ref="waveformRef" class="absolute inset-0" />
            <div
                v-if="currentTime !== '0:00'"
                class="moving-icon absolute"
                :style="{ left: `${calculateIconPosition()}%` }"
            >
                <img
                    :src="media?.provider?.avatar?.url"
                    alt="Avatar"
                    class="-mt-4 h-8 w-8 shrink-0 -translate-x-1/2 rounded-full object-cover"
                />
            </div>
        </div>

        <!-- Time -->
        <div
            v-if="duration !== '0:00'"
            class="mb-6 flex justify-between text-sm text-gray-500"
        >
            <span>{{ currentTime }}</span>
            <span>{{ duration }}</span>
        </div>

        <!-- Controls -->
        <div
            class="border-top fixed bottom-0 left-0 right-0 z-50 flex items-center justify-between border bg-white p-3 shadow-xl sm:justify-center sm:gap-6"
        >
            <button
                class="p-3 text-gray-600 hover:text-gray-800"
                @click="refreshAudio"
            >
                <iconify-icon
                    class="text-xl"
                    icon="solar:refresh-bold-duotone"
                />
            </button>

            <button
                class="p-3 text-gray-600 hover:text-gray-800"
                @click="skipBackward"
            >
                <iconify-icon
                    class="text-xl"
                    icon="solar:rewind-back-bold-duotone"
                />
            </button>

            <button
                class="flex h-16 w-16 items-center justify-center rounded-full border-none bg-primary-400 text-white outline-none hover:bg-primary-300"
                @click="togglePlay"
            >
                <span class="text-4xl">
                    <template v-if="isPlaying">
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="28px"
                            height="28px"
                            viewBox="0 0 24 24"
                        >
                            <path
                                fill="currentColor"
                                d="M2 6c0-1.886 0-2.828.586-3.414S4.114 2 6 2s2.828 0 3.414.586S10 4.114 10 6v12c0 1.886 0 2.828-.586 3.414S7.886 22 6 22s-2.828 0-3.414-.586S2 19.886 2 18z"
                            />
                            <path
                                fill="currentColor"
                                d="M14 6c0-1.886 0-2.828.586-3.414S16.114 2 18 2s2.828 0 3.414.586S22 4.114 22 6v12c0 1.886 0 2.828-.586 3.414S19.886 22 18 22s-2.828 0-3.414-.586S14 19.886 14 18z"
                                opacity="0.5"
                            />
                        </svg>
                    </template>
                    <template v-else>
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="28px"
                            height="28px"
                            viewBox="0 0 24 24"
                        >
                            <path
                                fill="currentColor"
                                fill-rule="evenodd"
                                d="M23 12c0-1.035-.53-2.07-1.591-2.647L8.597 2.385C6.534 1.264 4 2.724 4 5.033V12z"
                                clip-rule="evenodd"
                            />
                            <path
                                fill="currentColor"
                                d="m8.597 21.615l12.812-6.968A2.99 2.99 0 0 0 23 12H4v6.967c0 2.31 2.534 3.769 4.597 2.648"
                                opacity="0.5"
                            />
                        </svg>
                    </template>
                </span>
            </button>

            <button
                class="p-3 text-gray-600 hover:text-gray-800"
                @click="skipForward"
            >
                <iconify-icon
                    class="text-xl"
                    icon="solar:rewind-forward-bold-duotone"
                />
            </button>

            <button class="p-3" @click="animateHeart">
                <span>
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                    >
                        <path
                            :fill="isHeartFilled ? '#e01c2b' : '#a5aab1'"
                            d="M2 9.137C2 14 6.02 16.591 8.962 18.911C10 19.729 11 20.5 12 20.5s2-.77 3.038-1.59C17.981 16.592 22 14 22 9.138S16.5.825 12 5.501C7.5.825 2 4.274 2 9.137"
                        />
                    </svg>
                </span>
            </button>
        </div>
    </div>
</template>

<style scoped>
.moving-icon {
    transition: left 0.1s ease;
    top: -20px;
}

.loader {
    width: 100%;
    height: 5px;
    border-radius: 20px;
    background-color: #51f7dc;
    background-image: linear-gradient(
        45deg,
        rgba(240, 240, 240, 0.803) 25%,
        transparent 25%,
        transparent 50%,
        rgba(236, 236, 236, 0.825) 50%,
        rgba(209, 209, 209, 0.619) 75%,
        transparent 75%,
        transparent
    );
    font-size: 30px;
    background-size: 1em 1em;
    box-sizing: border-box;
    animation: barStripe 1s linear infinite;
}

@keyframes barStripe {
    0% {
        background-position: 1em 0;
    }
    100% {
        background-position: 0 0;
    }
}
</style>
