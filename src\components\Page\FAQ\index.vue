<script lang="ts" setup>
// const router = useRouter()

defineProps({
    menuModel: {
        type: Array<any>,
        default: () => [],
    },
    category: {
        type: Object,
        default: () => ({}),
    },
    title: {
        type: String,
        default: '',
    },
    subCategories: {
        type: Object,
        default: () => ({}),
    },
})

const getCategoryHits = (category: any) =>
    category?.inner_hits?.['most_recent']?.hits?.hits

// const onFocus = () => {
//   setTimeout(() => {
//     router.push({
//       query: {
//         action: 'search',
//         target: '/ho-tro/cau-hoi-thuong-gap',
//       },
//     })
//   }, 100)
// }
</script>
<template>
    <div class="flex flex-col sm:flex-row">
        <!-- side menu -->
        <div
            class="relative hidden border-r-2 border-slate-200 px-4 sm:block dark:border-slate-500 dark:bg-surface-700"
        >
            <Menu class="sticky top-16 h-screen" :model="menuModel" />
        </div>
        <!-- content -->
        <div
            class="flex grow flex-col gap-12 self-stretch px-4 py-24 sm:px-16 dark:bg-slate-700"
        >
            <WHeroSection
                class="h-125 lg:h-80"
                :blocks="['title', 'sub-title', 'under']"
                :sub-title="category?.page?.properties?.Sapo"
            >
                <template #title>
                    <WSheet
                        tag="h1"
                        :class="`mb-4 text-5xl font-semibold leading-none tracking-tight text-slate-900 sm:text-6xl md:text-7xl lg:text-8xl`"
                    >
                        {{ title }}
                    </WSheet>
                </template>
                <!-- <template #under>
          <IconField icon-position="right" class="mx-[10vw]">
            <InputIcon class="pi pi-search" />
            <InputText v-model="search" placeholder="Search" @focus="onFocus" />
          </IconField>
        </template> -->
            </WHeroSection>

            <!-- Mobile content -->
            <TabView :scrollable="true" class="sm:hidden">
                <TabPanel
                    v-for="(item, index) in subCategories?.hits"
                    :key="index"
                    :header="item?.page?.properties?.Name"
                >
                    <AccordionQuestion
                        :items="getCategoryHits(item)"
                        is-mobile
                    />
                </TabPanel>
            </TabView>

            <!-- Computer content -->
            <div
                v-for="(item, index) in subCategories?.hits"
                :key="index"
                class="hidden sm:block"
            >
                <h2
                    :id="item?.page?.properties?.Slug"
                    class="mb-4 text-3xl font-semibold"
                >
                    {{ item?.page?.properties?.Name }}
                </h2>
                <AccordionQuestion :items="getCategoryHits(item)" />
            </div>
            <div class="flex flex-col items-center gap-4 px-4 py-4 text-center">
                <h2
                    :class="`text-5xl font-semibold leading-none tracking-tight text-slate-900 sm:text-6xl md:text-7xl lg:text-8xl`"
                >
                    Got more questions?
                </h2>
                <BlockContact :blocks="['email', 'call']" />
            </div>
        </div>
    </div>
</template>
