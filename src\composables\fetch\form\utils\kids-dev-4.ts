import { useRuntimeConfig } from '#imports'
import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => {
    const runtimeConfig = useRuntimeConfig()
    const appEnv = runtimeConfig.public.app.env

    return {
        objectId: appEnv === 'production' ? 'hRzRpX8SX4' : 'iDsIZ144qZ',
        pages: [
            {
                id: 'Starting Page',
                elements: [
                    {
                        id: 'm2iqq8a41s8',
                        type: 'display',
                        properties: {
                            display: {
                                component: 'html_block_content',
                                tag: 'p',
                                content:
                                    'Các mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. H<PERSON>y tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 4 tháng tuổi. <PERSON>ồ<PERSON> thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. Bác sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                            },
                        },
                    },
                    {
                        id: 'm2iqrjw90st',
                        type: 'display',
                        properties: {
                            display: {
                                component: 'html_block_media',
                                tag: 'img',
                                src: 'https://todaysparent.mblycdn.com/tp/resized/2019/08/1600x900/your-3-month-old-baby-milestones-1280x960.jpg',
                                alt: 'Nice picture',
                            },
                        },
                    },
                ],
                type: 'start',
            },
            {
                id: 'Giao tiếp xã hội và tình cảm',
                elements: [
                    {
                        id: 'm2iqw6u1yy',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Tự mỉm cười để thu hút sự chú ý của người lớn',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2iqw6u1yy',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2iqyzb4vw',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Cười khúc khích (chưa phải là cười to) khi người lớn cố làm cho bé cười',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2iqyzb4vw',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2iqz3jsnjc',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Nhìn theo cha mẹ, di chuyển hoặc tạo ra âm thanh để thu hút sự chú ý của cha mẹ',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2iqz3jsnjc',
                                fetch: {},
                            },
                        },
                    },
                ],
                type: 'input',
            },
            {
                elements: [
                    {
                        id: 'm2ir784xowe',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Phát ra âm thanh như “oooo”, “aahh” (thủ thỉ)',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2ir784xowe',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2ir7x6pefj',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Phát ra âm thanh đáp lại khi cha mẹ nói chuyện với bé',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2ir7x6pefj',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2ir7znd4x7',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Quay đầu về phía có giọng nói của cha mẹ',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2ir7znd4x7',
                                fetch: {},
                            },
                        },
                    },
                ],
                id: 'Ngôn ngữ và Giao tiếp',
                type: 'input',
            },
            {
                elements: [
                    {
                        id: 'm2irdzcyjor',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Nếu đói, bé sẽ há miệng khi nhìn thấy vú mẹ hoặc bình sữa',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2irdzcyjor',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2ire3g26m',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Nhìn bàn tay của mình một cách thích thú',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2ire3g26m',
                                fetch: {},
                            },
                        },
                    },
                ],
                id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
                type: 'input',
            },
            {
                elements: [
                    {
                        id: 'm2iribgbbk',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Giữ được đầu cổ khi bế đứng, mà không cần nâng đỡ',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2iribgbbk',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2iridd6gp',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Cầm được đồ chơi khi cha mẹ đặt vào tay',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2iridd6gp',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2irifereq',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Dùng tay để đung đưa đồ chơi',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2irifereq',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2iriguinsu',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Đưa tay lên miệng',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2iriguinsu',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2iriko2sdd',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Chống khuỷu tay/cẳng tay lên khi nằm sấp',
                                component: 'multiple_choice',
                                description: 'Select one option from the list.',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'option1',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'option2',
                                    },
                                    {
                                        value: 'option3',
                                        label: 'Chưa',
                                    },
                                ],
                                key: 'MultipleChoicem2iriko2sdd',
                                fetch: {},
                            },
                        },
                    },
                ],
                id: 'Vận động/Phát triển thể chất',
                type: 'input',
            },
        ],
        title: '4 tháng',
        submission: 'remote',
        status: 'draft',
        computes: hidden_fields.map((hidden_field: IHiddenField) => {
            return {
                key: hidden_field.key,
                value: hidden_field.value,
                component: 'hidden_field',
            }
        }),
    }
}
