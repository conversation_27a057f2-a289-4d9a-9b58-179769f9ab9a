<script setup lang="ts">
import type { PropType } from '#imports'
import { computed, useI18n } from '#imports'

import type { IProviderFilter, IProviderTag } from '~/models'

const props = defineProps({
    specialties: {
        type: Array as PropType<IProviderTag[]>,
        required: true,
    },
})

const filter = defineModel<IProviderFilter>('filter')

const route = useRoute()
const { t } = useI18n({ useScope: 'local' })

const titles = [
    { name: t('doctor'), key: 'Bs' },
    { name: t('psychologist-psychiatrist'), key: 'Tlg' },
]
const titleMap = Object.fromEntries(titles.map((t) => [t.key, t.name]))

const genderMap = Object.fromEntries(
    [
        { name: t('is-male'), key: 'Male' },
        { name: t('is-female'), key: 'Female' },
    ].map((s) => [s.key, s.name]),
)

const groups = [
    { name: t('adult'), key: 'adult' },
    { name: t('adult'), key: 'nguoi-lon' },
    { name: t('kid'), key: 'child' },
    { name: t('kid'), key: 'nhi' },
]
const groupMap = Object.fromEntries(groups.map((t) => [t.key, t.name]))

const specialtyMap = computed(() =>
    Object.fromEntries(props.specialties.map((s) => [s.key, s.name])),
)

const removeKeyword = () => {
    if (filter.value?.keyword) {
        filter.value.keyword = ''
    }
}

const removeTitle = () => {
    if (filter.value?.title) {
        filter.value.title = undefined
        filter.value.group = undefined
    }
}

const removeGender = () => {
    if (filter.value?.gender) {
        filter.value.gender = undefined
    }
}

const removeGroup = () => {
    if (filter.value?.group) {
        filter.value.group = undefined
        delete route.query?._group
    }
}

const removeSpecialty = (specialty: string) => {
    if (filter.value?.specialties) {
        filter.value.specialties = filter.value.specialties.filter(
            (i) => i !== specialty,
        )
    }
}
</script>

<template>
    <!-- Filter chips -->
    <div v-if="filter?.keyword || filter?.title || filter?.specialties?.length">
        <div
            class="col-span-full mt-4 flex min-h-9 flex-row flex-nowrap gap-2 overflow-x-auto sm:mb-4 sm:mt-0"
        >
            <!-- Doctor name -->
            <Chip
                v-if="filter.keyword?.length > 0"
                :label="filter.keyword"
                class="self-center text-nowrap text-sm"
                removable
                @remove="removeKeyword"
            />

            <!-- Doctor title -->
            <Chip
                v-if="filter.title !== undefined"
                :label="titleMap[filter.title]"
                class="self-center text-nowrap text-sm"
                removable
                @remove="removeTitle"
            />

            <!-- Gender -->
            <Chip
                v-if="filter.gender !== undefined"
                :label="genderMap[filter.gender]"
                class="self-center text-nowrap text-sm"
                removable
                @remove="removeGender"
            />

            <!-- Doctor group -->
            <Chip
                v-if="filter.group && groupMap[filter.group]"
                :label="groupMap[filter.group]"
                class="self-center text-nowrap text-sm"
                removable
                @remove="removeGroup"
            />

            <!-- Specialties -->
            <template
                v-if="filter?.specialties && filter?.specialties?.length > 0"
            >
                <Chip
                    v-for="specialty in filter?.specialties"
                    :key="specialty"
                    :label="specialtyMap[specialty]"
                    class="self-center text-nowrap text-sm"
                    removable
                    @remove="removeSpecialty(specialty)"
                />
            </template>
        </div>
    </div>
</template>

<i18n lang="yaml">
en:
    'doctor': 'Doctor'
    'psychologist-psychiatrist': 'Psychologist & Psychiatrist'
    'is-male': 'Is male'
    'is-female': 'Is female'
    'adult': 'Adult'
    'kid': 'Child'
vi:
    'doctor': 'Bác sĩ'
    'psychologist-psychiatrist': 'Tâm lý gia và Bác sĩ tâm thần kinh'
    'is-male': 'Nam'
    'is-female': 'Nữ'
    'adult': 'Người lớn'
    'kid': 'Trẻ em'
</i18n>
