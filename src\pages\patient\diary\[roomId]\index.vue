<script lang="ts" setup>
import { appRoutes } from '~/constants'
import { Capacitor } from '@capacitor/core'
import { useThrottleFn } from '@vueuse/core'
import {
    computed,
    definePageMeta,
    onBeforeUnmount,
    onMounted,
    ref,
    useActionToolkit,
    useConsultationSession,
    useConsultationTimer,
    useDayjs,
    useI18n,
    useNotivueNotifications,
    useRoute,
    useRouter,
    useRuntimeConfig,
    useSocketIo,
    useUserInfo,
    getPatientLimitUsage,
    // useWalletManagement,
    watch,
} from '#imports'
import type { ManipulateType } from 'dayjs'
import type { TActionState } from '~/models'
import { push } from 'notivue'

definePageMeta({
    middleware: 'auth-capacitor',
})

// Composables
const { t } = useI18n()
const dayjs = useDayjs()
const router = useRouter()
const { params, query }: any = useRoute()
const runtimeConfig: any = useRuntimeConfig()
const notifications = useNotivueNotifications()

const { user } = useUserInfo({ scope: ['_id'] })
// const {
//     getPersonalDoctorWallet,
//     formatRemainingTime,
//     refreshWallet
// } = useWalletManagement()

const {
    countdown,
    formattedCountdown,
    isCountdownInProgress,
    resetCountdown,
    startCountdown,
    stopCountdown,
} = useConsultationTimer()

// Constants
const BUFFER_TIME = {
    value:
        runtimeConfig.public?.app?.['personal-doctor']?.['remind-delay'] || 30,
    unit: 'minutes' as ManipulateType,
}
const MAX_LENGTH_REPLY = 50

// const DEFAULT_REMAINING_TIME = {
//     remainingTime: '0/120',
//     balance: 120,
//     balanceFormatted: '00:00',
//     usable: 0,
//     usableFormatted: '00:00',
//     used: 120,
//     usedFormatted: '00:00',
//     percentageUsed: 0,
// }

// State Management
const latestSession = ref<any>(null)
const isSessionCleared = ref<boolean>(false)

const consultation = ref<any>(null)
const loadingAction = ref<boolean>(false)
const actionState = ref<TActionState | null>(null)

const patient = ref<string>(query?.patient)
const provider = ref<string>(query?.provider)
const providerUser = ref<string>(query?.providerUser)

const partners = ref<any[]>([])
const newMessage = ref<any>(null)
const replyMessage = ref<string>('')
const selectedMessage = ref<any>(null)
const isShowModeReply = ref<boolean>(false)
const visibleMessageAction = ref<boolean>(false)

// Computed Properties
const conversation = computed(() => params.roomId)
const userId = computed(() => user.value?._id ?? '')
const isNative = computed(() => Capacitor.isNativePlatform())
const isReplyMessageOverLength = computed(
    () => replyMessage.value.replace(/\s/g, '').length > MAX_LENGTH_REPLY,
)
const isConsultationOverdue = computed(() =>
    dayjs().isAfter(dayjs(consultation.value?.time)),
)
const isAcceptReply = computed(() => {
    const message = selectedMessage.value
    const latestKey = latestSession.value?.lastMessage?.key

    return (
        message?.author !== userId.value &&
        message?.key === latestKey &&
        !message?.medias?.length &&
        !message?.action?.duration &&
        !message?.isFinal
    )
})

const { copySource } = useActionToolkit()

useAuthFirebase('')
const { sendMessage } = useFirebaseMessage()
const { updateMember, updateMemberStatus } = useFirebaseMember()
const { sendEventNewMessage } = useFirebaseEvent()

// Get WalletInfo the Patient
const { refresh: refreshWallet, extractWalletsPatientInfo } =
    await getPatientLimitUsage(providerUser, patient)

// API Hooks
const {
    getLatestConsultationSession,
    getConsultationSessionById,
    createConsultationSession,
    remindCallback,
} = useConsultationSession()

// Action Handlers
const onBack = () => router.push(appRoutes.patient.healthRecords.index)

const onStartConsultation = async () => {
    if (!userId.value) return
    loadingAction.value = true

    try {
        if (!extractWalletsPatientInfo.value) {
            throw new Error('No personal doctor wallet found')
        }

        const consultationTime = dayjs()
            .add(BUFFER_TIME.value, BUFFER_TIME.unit)
            .format()

        consultation.value = await createConsultationSession({
            reason: 'Personal Doctor',
            conversation: conversation.value,
            patient: patient.value,
            provider: provider.value,
            providerUser: providerUser.value,
            questions: [],
            user: userId.value,
            wallet: extractWalletsPatientInfo.value?._id,
            state: 'SCHEDULED',
            time: consultationTime,
        })

        latestSession.value = await getLatestConsultationSession(
            userId.value,
            conversation.value,
        )

        router.push({
            path: appRoutes.patient.form('request-callback'),
            query: {
                consultation: consultation.value?._id,
                conversation: conversation.value,
                patient: patient.value,
                provider: provider.value,
                providerUser: providerUser.value,
                sessionId: latestSession.value?._id,
                user: userId.value,
                wallet: extractWalletsPatientInfo.value?._id,
            },
        })
    } catch (error: any) {
        notifications.error({
            message: t(error?.message || 'Error starting consultation'),
            duration: 5000,
        })
    } finally {
        loadingAction.value = false
    }
}

const onRemind = async () => {
    loadingAction.value = true

    try {
        const results = await remindCallback(
            consultation.value?._id,
            dayjs().add(BUFFER_TIME.value, BUFFER_TIME.unit).format(),
        )

        const time = results?.time || consultation.value?.time

        startCountdown(time)
    } catch {
        notifications.error({
            message: t('Error sending reminder'),
            duration: 5000,
        })
    } finally {
        loadingAction.value = false
    }
}

const onRequestCallback = async () => {
    loadingAction.value = true

    try {
        router.push({
            path: appRoutes.patient.form('request-callback'),
            query: {
                consultation: consultation.value?._id,
                conversation: conversation.value,
                patient: patient.value,
                provider: provider.value,
                providerUser: providerUser.value,
                sessionId: latestSession.value?._id,
                user: userId.value,
                wallet: extractWalletsPatientInfo.value?._id,
            },
        })
    } catch {
        notifications.error({
            message: t('Error requesting callback'),
            duration: 5000,
        })
    } finally {
        loadingAction.value = false
    }
}

const handleChangeActionState = () => {
    if (actionState.value === 'start-consultation') {
        if (consultation.value?._id) {
            onRequestCallback()
        } else {
            onStartConsultation()
        }
    } else if (actionState.value === 'remind') {
        onRemind()
    } else {
        console.warn(`Unhandled action state: ${actionState.value}`)
    }
}

const handleNewMessage = async (message: any) => {
    newMessage.value = message
    await refreshWallet()
    await refreshAction()

    if (message?.action && !message?.action?.duration) {
        resetCountdown()
    }
}

const onUpdatePartners = (updatedPartners: any[]) => {
    partners.value = updatedPartners
}

const handlePressMessage = (message: any) => {
    selectedMessage.value = message
    visibleMessageAction.value = true
}

const handleCopyMessageContent = () => {
    copySource(
        selectedMessage.value?.content,
        () => {
            push.success({
                message: t('toast.copy.success'),
            })

            visibleMessageAction.value = false
        },
        () =>
            push.error({
                message: t('toast.copy.error'),
            }),
    )
}

const handleReplyMessage = () => {
    isShowModeReply.value = true
    visibleMessageAction.value = false
}

const sendReplyMessage = async () => {
    if (isReplyMessageOverLength.value) return

    const payload: any = {
        author: userId.value,
        content: replyMessage.value,
        host: 'patient/diary',
        time: dayjs().valueOf(),
        isHuman: true,
        isContinuous: false,
        sessionId: latestSession.value?._id,
        replyMessage: selectedMessage.value,
    }

    await updateMember(conversation.value, userId.value, { isTyping: true })

    const newMessage = await sendMessage(conversation.value, payload)

    if (newMessage.key) {
        const membersWithStatus = updateMemberStatus()
        const offlineMembers = membersWithStatus.filter(
            (member: any) => member.status === 'offScreen',
        )

        sendEventNewMessage(
            conversation.value,
            userId.value,
            newMessage,
            offlineMembers,
        )
        updateMember(conversation.value, userId.value, { isTyping: false })
    }

    replyMessage.value = ''
    isShowModeReply.value = false
}

// const refreshWalletData = async () => {
//     if (!user.value?._id) return
//     const updatedWallet = await refreshWallet(user.value._id)
//     if (updatedWallet) {
//         remainingCallTime.value = updatedWallet
//     }
// }

const refreshAction = useThrottleFn(async () => {
    loadingAction.value = true

    try {
        latestSession.value = await getLatestConsultationSession(
            userId.value,
            conversation.value,
        )

        if (!latestSession.value?._id) {
            actionState.value = 'start-consultation'
            return
        }

        const results = await getConsultationSessionById({
            session: latestSession.value._id,
            user: userId.value,
        })

        if (!Array.isArray(results) || results.length === 0) {
            actionState.value = 'start-consultation'
            isSessionCleared.value = true
            consultation.value = null
            return
        }

        const consultationData = results[0]
        consultation.value = consultationData
        patient.value = consultationData?.patient
        provider.value = consultationData?.provider
        providerUser.value = consultationData?.providerUser

        switch (consultationData.state) {
            case 'SCHEDULED':
                actionState.value = 'start-consultation'
                break
            case 'WAITING':
                actionState.value = 'remind'
                if (!isConsultationOverdue.value) {
                    startCountdown(consultation.value?.time)
                }
                break
            case 'INCONSULTATION':
                actionState.value = 'remind'
                resetCountdown()
                break
            default:
                actionState.value = null
        }
    } catch (error: any) {
        console.error('Error refreshing consultation session:', error)
        notifications.error({ message: t(error?.message), duration: 5000 })
    } finally {
        loadingAction.value = false
    }
}, 1000)

// Socket Management
const consultationSocket = useSocketIo({
    channel: '/Consultation',
    debugLevel: 'debug',
})

consultationSocket.on('updated', async () => {
    await refreshWallet()
    await refreshAction()
})

// Watchers
watch(
    [userId, () => consultationSocket.state.value.isConnected],
    async ([newUserId, isConnected]: any[]) => {
        if (newUserId && isConnected) {
            await consultationSocket.joinRoom({
                roomId: newUserId,
                userId: newUserId,
            })
        }
    },
    {
        immediate: true,
    },
)

watch(countdown, async () => {
    if (
        countdown.hours === 0 &&
        countdown.minutes === 0 &&
        countdown.seconds === 0
    ) {
        stopCountdown()
        await refreshAction()
    }
})

// watch(isReplyMessageOverLength, (newState: boolean) => {
//     if (newState) {
//         push.error({
//             message: t('toast.reply.max-length', {
//                 max_length: MAX_LENGTH_REPLY,
//             }),
//         })
//     }
// })

// Lifecycle Hooks
onMounted(async () => {
    await refreshWallet()
    await refreshAction()
})

onBeforeUnmount(() => {
    stopCountdown()
})
</script>

<template>
    <div>
        <w-chat-window
            v-if="userId"
            :user-id="userId"
            :room-id="conversation"
            :theme="query?._theme"
            :blocks="['header', 'body', 'footer']"
            hide-admin
            view="timelines"
            class="h-screen w-full text-black"
            @new-message="handleNewMessage"
            @update:partners="onUpdatePartners"
            @press-text-message="handlePressMessage"
            @press-call-message="handlePressMessage"
        >
            <template #header>
                <div>
                    <PagePatientMembershipPersonalDoctorDiaryHeader
                        @back="onBack"
                    />

                    <div
                        class="flex items-center gap-2 bg-primary-50 px-3 py-2"
                    >
                        <iconify-icon
                            icon="material-symbols:info-rounded"
                            class="text-2xl text-primary-500"
                        />

                        <div class="text-xs text-primary-500">
                            <p>{{ t('diary-info.first') }}</p>
                            <p>{{ t('diary-info.second') }}</p>
                        </div>
                    </div>
                </div>
            </template>

            <template #session-start="{ session }">
                <div class="flex items-center gap-2 px-2 pt-2">
                    <p class="font-semibold">
                        {{
                            dayjs(session?.startMessage?.time).format(
                                'HH:mm DD/MM/YYYY',
                            )
                        }}
                    </p>
                    <span>&#10072;</span>
                    <p>{{ t('session:title') }}</p>
                </div>
                <div class="px-2 pb-2">{{ session?.title }}</div>
            </template>

            <template #session-end="{ message }">
                <div
                    v-if="
                        (latestSession?.lastMessage?.key === message?.key &&
                            isSessionCleared) ||
                        message?.isFinal
                    "
                    class="ml-[-44px] flex items-center sm:mx-4 lg:ml-12 lg:mr-0"
                >
                    <Divider layout="horizontal" class="flex" align="center">
                        <span class="mx-2 min-w-fit flex-1 text-xs">
                            {{ t('session:clear') }}
                        </span>
                    </Divider>
                </div>
            </template>

            <template #footer>
                <div v-if="isShowModeReply" class="pt-2">
                    <div
                        class="flex items-center justify-between bg-primary-100/40"
                    >
                        <div class="relative ml-4 text-sm">
                            <p class="ml-3 pt-1 font-semibold text-gray-500">
                                &ldquo; {{ t('reply') }}
                            </p>
                            <p
                                v-dompurify-html="selectedMessage.content"
                                class="reply-message ml-3 line-clamp-2 pb-1 text-gray-500"
                            />
                        </div>

                        <Button
                            text
                            severity="secondary"
                            @click="isShowModeReply = false"
                        >
                            <template #icon>
                                <iconify-icon
                                    icon="material-symbols:close-rounded"
                                    class="text-2xl"
                                />
                            </template>
                        </Button>
                    </div>

                    <div
                        class="flex items-center justify-between gap-2 py-2 pl-4 pr-1"
                    >
                        <div class="relative flex-1">
                            <Textarea
                                v-model="replyMessage"
                                :class="[
                                    'max-h-32 w-full overflow-y-auto',
                                    {
                                        'focus:enabled:border-red-500':
                                            isReplyMessageOverLength,
                                    },
                                ]"
                                rows="1"
                                cols="30"
                                auto-resize
                                :placeholder="t('enter a reply message')"
                                @keydown.enter.prevent="sendReplyMessage"
                            />

                            <div
                                class="absolute -top-2 right-4 w-fit rounded bg-white px-2 text-sm"
                            >
                                <span
                                    :class="[
                                        {
                                            'text-red-500':
                                                isReplyMessageOverLength,
                                        },
                                    ]"
                                >
                                    {{ replyMessage.replace(/\s/g, '').length }}
                                </span>
                                /
                                {{ MAX_LENGTH_REPLY }}
                            </div>
                        </div>

                        <Button
                            text
                            rounded
                            :disabled="isReplyMessageOverLength"
                            aria-label="Send message"
                            @click="sendReplyMessage"
                        >
                            <template #icon>
                                <iconify-icon
                                    icon="fluent:send-24-filled"
                                    class="text-2xl"
                                />
                            </template>
                        </Button>
                    </div>
                </div>
                <div
                    v-else
                    class="border-t-2 shadow-lg"
                    :class="{ 'pb-safe': isNative }"
                >
                    <PagePatientMembershipPersonalDoctorDiaryTimerSection
                        :remaining-call-time="extractWalletsPatientInfo"
                    />
                    <PagePatientMembershipPersonalDoctorDiaryActionButton
                        :action-state="actionState"
                        :is-countdown-in-progress="isCountdownInProgress"
                        :formatted-countdown="formattedCountdown"
                        :loading="loadingAction"
                        @action="handleChangeActionState"
                    />
                </div>
            </template>
        </w-chat-window>

        <Dialog
            v-model:visible="visibleMessageAction"
            modal
            :header="t('dialog.message.action.header')"
            pt:root:class="w-4/5"
            pt:header:class="pb-0"
            pt:content:class="px-0 pb-2"
        >
            <Button
                text
                :label="t('dialog.message.action.copy')"
                severity="secondary"
                pt:root:class="w-full justify-start"
                @click="handleCopyMessageContent"
            >
                <template #icon>
                    <iconify-icon icon="stash:copy-duotone" class="text-4xl" />
                </template>
            </Button>

            <Button
                text
                :label="t('dialog.message.action.reply')"
                severity="secondary"
                :disabled="!isAcceptReply"
                pt:root:class="w-full justify-start"
                @click="handleReplyMessage"
            >
                <template #icon>
                    <iconify-icon icon="ic:round-reply" class="text-4xl" />
                </template>
            </Button>
        </Dialog>
    </div>
</template>

<style scoped>
.reply-message::before {
    content: '';
    position: absolute;
    left: 0px;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--p-primary-color);
    border-radius: 4px;
}
</style>

<i18n lang="json">
{
    "en": {
        "session:title": "Call Log",
        "session:clear": "Session completed",
        "dialog": {
            "message": {
                "action": {
                    "header": "Options",
                    "copy": "Copy",
                    "reply": "Reply"
                }
            }
        },
        "toast": {
            "copy": {
                "success": "Copied!",
                "error": "Copy not supported"
            },
            "reply": {
                "max-length": "Reply must be under {max_length} chars"
            }
        },
        "enter a reply message": "Enter a reply message",
        "reply": "Reply",
        "diary-info": {
            "first": "This Care Log is for clinical notes, not for general chat.",
            "second": "Every Doctor's response containing 50 to 160 characters will be counted as 1 minute of consultation."
        }
    },
    "vi": {
        "session:title": "Nhật Ký",
        "session:clear": "Đã hoàn tất phiên",
        "dialog": {
            "message": {
                "action": {
                    "header": "Tùy chọn",
                    "copy": "Sao chép",
                    "reply": "Trả lời"
                }
            }
        },
        "toast": {
            "copy": {
                "success": "Đã sao chép!",
                "error": "Không hỗ trợ sao chép"
            },
            "reply": {
                "max-length": "Phản hồi tối đa {max_length} ký tự"
            }
        },
        "enter a reply message": "Nhập tin nhắn trả lời",
        "reply": "Trả lời",
        "diary-info": {
            "first": "Nhật Ký này không phải là cửa sổ chat.",
            "second": "Mỗi tin nhắn chứa 50-160 ký tự của bác sĩ sẽ được tính là 1 phút tư vấn."
        }
    }
}
</i18n>
