export const robots = {
  groups: (
    process.env.ROBOTS || 'UserAgent:*,Disallow:/admin/,Disallow:/tests/'
  )
    .split(',')
    .reduce(
      (acc, rule) => {
        const [key, value] = rule.split(':')

        let groupIndex = acc.findIndex((group) => group.userAgent === key)

        if (groupIndex === -1) {
          acc.push({ userAgent: key, allow: [], disallow: [] })
          groupIndex = acc.length - 1
        }

        if (value === '/') {
          acc[groupIndex].allow.push(value)
        } else {
          acc[groupIndex].disallow.push(value)
        }

        return acc
      },
      [] as { userAgent: string; allow: string[]; disallow: string[] }[],
    ),
}
