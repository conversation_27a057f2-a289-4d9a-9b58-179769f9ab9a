// stores/userStore.ts
import { defineStore } from '#imports'

export const useOnboardingStore = defineStore('onboarding', {
    state: () => ({
        profile: {
            avatar: {
                url: '',
            },
            file: null,
            name: '',
            dob: '',
            gender: '',
            phone: '',
            email: '',
            username: '',
            countryCode: '',
            otp: '',
            validated: {
                email: false,
                phone: false,
            },
            password: ''
        },
    }),
    actions: {
        setProfile(data: any) {
            this.profile = data
        },
        setPhoneData(phone: string, countryCode: string) {
            this.profile.phone = phone
            this.profile.countryCode = countryCode
        },
        setOtp(otp: string) {
            this.profile.otp = otp
        },
        setAvatar(avatarUrl: string) {
            this.profile.avatar.url = avatarUrl
        },
        setName(name: string) {
            this.profile.name = name
        },
        setDob(dob: string) {
            this.profile.dob = dob
        },
        setGender(gender: string) {
            this.profile.gender = gender
        },
        setEmail(email: string) {
            this.profile.email = email
        },
        setUsername(username: string) {
            this.profile.username = username
        },
        setPassword(password: string) {
            this.profile.password = password
        },
        setValidated({ email, phone }: { email: boolean; phone: boolean }) {
            this.profile.validated.email = email
            this.profile.validated.phone = phone
        },
    },
})
