import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Form } from '~/models'
import { form as form2Months } from './kids-dev-2'
import { form as form4Months } from './kids-dev-4'
import { form as form6Months } from './kids-dev-6'
import { form as form9Months } from './kids-dev-9'
import { form as form15Months } from './kids-dev-15'
import { form as form18Months } from './kids-dev-18'
import { form as form24Months } from './kids-dev-24'
import { form as form36Months } from './kids-dev-36'
import { form as form48Months } from './kids-dev-48'
import { form as form60Months } from './kids-dev-60'

type MilestoneInfo = {
    month: number
    objectId: string
    label: string
}

export const milestoneFormFactory = {
    formGenerators: {
        2: form2Months,
        4: form4Months,
        6: form6Months,
        9: form9Months,
        15: form15Months,
        18: form18Months,
        24: form24Months,
        36: form36Months,
        48: form48Months,
        60: form60Months,
    },

    milestonesWithInfo: [
        { month: 2, label: '2 tháng' },
        { month: 4, label: '4 tháng' },
        { month: 6, label: '6 tháng' },
        { month: 9, label: '9 tháng' },
        { month: 12, label: '12 tháng' },
        { month: 15, label: '15 tháng' },
        { month: 18, label: '18 tháng' },
        { month: 24, label: '2 tuổi' },
        { month: 36, label: '3 tuổi' },
        { month: 48, label: '4 tuổi' },
        { month: 60, label: '5 tuổi' },
    ] as MilestoneInfo[],

    getFormByMonth(
        month: number,
        hiddenFields: IHiddenField[] = [],
    ): { form: MilestoneForm | null; label: string | null } {
        const milestone = this.milestonesWithInfo.find((m) => m.month === month)
        if (!milestone || !this.formGenerators[month]) {
            return { form: null, label: null }
        }
        return {
            form: this.formGenerators[month](hiddenFields),
            label: milestone.label,
        }
    },

    getNearestMilestone(currentMonth: number): MilestoneInfo | null {
        const milestones = this.milestonesWithInfo
            .filter((m) => m.month <= currentMonth)
            .sort((a, b) => b.month - a.month)

        return milestones.length > 0 ? milestones[0] : null
    },

    getNearestForm(
        currentMonth: number,
        hiddenFields: IHiddenField[] = [],
    ): { form: MilestoneForm | null; milestone: MilestoneInfo | null } {
        const nearestMilestone = this.getNearestMilestone(currentMonth)
        if (!nearestMilestone) {
            return { form: null, milestone: null }
        }

        return {
            form:
                this.formGenerators[nearestMilestone.month]?.(hiddenFields) ||
                null,
            milestone: nearestMilestone,
        }
    },

    formatToMonths(input: string): number {
        const match = input.match(/(month|year):(\d+)/)
        if (!match) {
            throw new Error(
                'Invalid input format. Expected "month:X" or "year:Y".',
            )
        }
        const [, type, value] = match
        const numericValue = parseInt(value, 10)
        return type === 'month' ? numericValue : numericValue * 12
    },
}
