<script setup lang="ts">
import {
    definePageMeta,
    ref,
    useI18n,
    usePagePatientEduHubArticleFirstAid,
    usePagePatientEduHubMediaCategory,
} from '#imports'

import Skeleton from 'primevue/skeleton'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

definePageMeta({
    layout: 'patient',
})

const layoutStore = useLayoutStore()
const { config } = useJsonConfigApp()
const { isMembership: checkMembership } = useMembership()

const isMembership = computed(() => {
    if (config.value?.pages?.eduhub?.checkMembership === false) return true
    else return checkMembership.value
})

const { t } = useI18n()
const { fullPath } = useRoute()

const topicId = useLocalStorage('edu-hub-media-topic-id', '')
topicId.value = null

const { categories, loading, aggregations } =
    usePagePatientEduHubMediaCategory()

const from = ref<number>(0)
const size = ref<number>(15)
const { articles, loading: loadingArticles } =
    usePagePatientEduHubArticleFirstAid(from, size)

const countTopics = (id: string) => {
    for (const bucket of (aggregations?.value as any)?.ids?.buckets || []) {
        if (bucket.key === id) {
            return bucket.doc_count
        }
    }
    return 0
}

onMounted(() => layoutStore.$reset())
</script>

<template>
    <div>
        <SharedDynamicHeader>
            <template #header>
                <h4
                    class="line-clamp-1 overflow-hidden overflow-ellipsis whitespace-normal break-words break-all"
                >
                    Edu-hub
                </h4>
            </template>
            <template #detailed>
                <div>
                    <h1 class="mx-6 mt-4">Edu-hub</h1>
                    <p class="mx-6 mt-2">
                        {{ t('overview') }}
                    </p>
                </div>
            </template>
        </SharedDynamicHeader>

        <SharedPageSection
            :title="t('section:1:title')"
            :pt="{
                content:
                    'flex flex-col sm:grid sm:grid-cols-1 sm:grid-flow-row gap-3 sm:gap-5 px-6 flex-nowrap snap-x overflow-x-auto',
            }"
        >
            <template v-if="loading">
                <div v-for="i in categories.length" :key="i" class="mt-4 flex">
                    <Skeleton size="8rem" class="mr-2" />
                    <div class="flex flex-col">
                        <Skeleton width="12rem" class="mb-2" />
                        <Skeleton width="5rem" class="mb-2" />
                        <Skeleton height=".6rem" width="12rem" />
                    </div>
                </div>
            </template>

            <transition-group v-else name="fade-up" tag="ul">
                <PagePatientEduHubCategoryCard
                    v-for="category in categories"
                    :key="category._id"
                    :category="category"
                    class="mt-4 first:mt-0"
                    :number-of-topics="countTopics(category._id)"
                />
            </transition-group>
        </SharedPageSection>

        <SharedPageSection
            :title="t('section:2:title')"
            :pt="{
                content:
                    'flex flex-row flex-nowrap overflow-x-auto snap-x px-6 sm:grid sm:grid-cols-4 sm:grid-flow-row gap-5 no-scrollbar',
            }"
            :view-all-to="`${fullPath}/article`"
        >
            <template v-if="loadingArticles">
                <div
                    v-for="i in 2"
                    :key="i"
                    class="mb-2 max-w-md rounded bg-surface-0 sm:gap-4 dark:bg-surface-900"
                >
                    <Skeleton width="8rem" height="8rem" class="mb-2" />
                    <Skeleton width="8rem" height="14px" />
                </div>
            </template>
            <PagePatientEduHubArticleCard
                v-for="article in articles"
                v-else
                :key="article._id"
                :article="article"
                :is-membership="isMembership"
                class="flex-none snap-center"
            />
        </SharedPageSection>
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "overview": "This collection features concise audio and video clips where our doctors provide in-depth answers to real-life patient queries",
        "section:1:title": "Resources",
        "section:2:title": "First Aid"
    },
    "vi": {
        "overview": "Các audio và video giải đáp của bác sĩ cho các câu hỏi thực tế từ bệnh nhân về nhiều chủ đề sức khỏe khác nhau",
        "section:1:title": "Audio & Video",
        "section:2:title": "Sơ Cứu"
    }
}
</i18n>
