<script setup lang="ts">
import { useFileSearch } from '~/composables'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const layoutStore = useLayoutStore()
const { t } = useI18n()
const route = useRoute()

const userId = computed(() => route?.params?.id as string)

const projectId = ref('emr')
const tags = ['medical']
const initialQuery = {
    skip: 0,
    limit: 50,
    count: true,
    fields: 'tags, description, url, medicationels, folder, state, progress, name, description, sprite, thumbnail, mimetype, blurhash, updatedAt, createdAt, capturedAt, duration, size',
    filter: {
        tags: tags,
    },
}

const { data: medications, refresh } = useFileSearch(
    'medical-files',
    userId,
    projectId,
    initialQuery,
)

const sortedMedications = computed(() => {
    return [...(medications.value || [])].sort(
        (a, b) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    )
})

const handleSuccess = () => {
    refresh()
}

nextTick(() => {
    layoutStore.setTitle(t('Medication and Supplement'))
})

onUnmounted(() => {
    layoutStore.$reset()
})
</script>

<template>
    <div class="mt-8 flex flex-col px-6 pb-10">
        <template v-if="sortedMedications?.length > 0">
            <PagePatientHealthRecordsAddFileCard
                project="emr"
                :user="userId"
                :tags="tags"
                :as-button="true"
                @upload-success="handleSuccess"
            >
                <template #default="{ handleClick, loading }">
                    <div class="flex w-full">
                        <Button
                            :disabled="loading"
                            severity="secondary"
                            class="ml-auto"
                            @click="handleClick"
                        >
                            <iconify-icon
                                icon="mynaui:plus-solid"
                                class="text-xl"
                            />
                            {{ t('upload') }}
                        </Button>
                    </div>
                </template>
            </PagePatientHealthRecordsAddFileCard>
            <div class="mt-5 flex flex-row flex-wrap gap-3">
                <template v-for="lab in sortedMedications" :key="lab?.url">
                    <SharedFileCard :file="lab" layout="Grid" />
                </template>
            </div>
        </template>
        <PagePatientHealthRecordsAddFileCard
            v-else-if="!loading && !sortedMedications?.length"
        />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "Medication and Supplement": "Medication and Supplement",
        "Add medication": "Add medication"
    },
    "vi": {
        "Medication and Supplement": "Thuốc và thực phẩm chức năng",
        "Add medication": "Thêm"
    }
}
</i18n>
