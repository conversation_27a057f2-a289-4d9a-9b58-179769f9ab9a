<script lang="ts" setup>
import { ref, useI18n, watch } from '#imports'

interface TimeInfo {
    remainingTime: string
    balance: number
    balanceFormatted: string
    usable: number
    usableFormatted: string
    used: number
    usedFormatted: string
    percentageUsed: number
}

interface Props {
    remainingCallTime: TimeInfo
    isShowAlert?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    isShowAlert: false,
})
const { t } = useI18n()
const isTimeExpanded = ref(false)
const showLowTimeAlert = ref(false)

// Theo dõi thay đổi của usable time
watch(
    () => props.remainingCallTime.usable,
    (newValue) => {
        if (newValue > 15 && props.isShowAlert) {
            showLowTimeAlert.value = true
        }
    },
    { immediate: true },
)

const handleBuyMore = () => {
    // Xử lý logic mua thêm thời gian ở đây
    showLowTimeAlert.value = false
}
</script>

<template>
    <div class="relative">
        <!-- Alert Message -->
        <Transition
            enter-active-class="transform transition duration-300 ease-out"
            enter-from-class="translate-y-[-100%] opacity-0"
            enter-to-class="translate-y-0 opacity-100"
            leave-active-class="transform transition duration-200 ease-in"
            leave-from-class="translate-y-0 opacity-100"
            leave-to-class="translate-y-[-100%] opacity-0"
        >
            <div
                v-if="showLowTimeAlert"
                class="fixed bottom-44 left-0 right-0 z-50 mx-auto max-w-md"
            >
                <div
                    class="mx-4 mt-4 flex items-center justify-between rounded-lg bg-yellow-50 px-4 py-3 shadow-lg"
                >
                    <div class="flex items-center space-x-2">
                        <i class="pi pi-exclamation-triangle text-yellow-600" />
                        <span class="text-sm font-medium text-yellow-700">
                            {{ t('low-time-warning') }}
                        </span>
                    </div>
                    <div class="flex items-center gap-2">
                        <button
                            class="rounded-md bg-yellow-600 px-3 py-1 text-sm font-medium text-white hover:bg-yellow-700"
                            @click="handleBuyMore"
                        >
                            {{ t('buy-more') }}
                        </button>
                        <button
                            class="text-yellow-600 hover:text-yellow-800"
                            @click="showLowTimeAlert = false"
                        >
                            <i class="pi pi-times" />
                        </button>
                    </div>
                </div>
            </div>
        </Transition>

        <!-- Rest of the existing code -->
        <button
            class="flex w-full items-center justify-between bg-gray-50 px-4 py-3 transition-colors hover:bg-gray-100"
            @click="isTimeExpanded = !isTimeExpanded"
        >
            <div class="flex items-center space-x-3">
                <div class="flex items-center">
                    <i class="pi pi-clock mr-2 text-primary-600" />
                    <span class="text-sm font-medium text-gray-900">
                        {{ t('remaining-time') }}
                    </span>
                </div>
                <div class="flex items-center">
                    <div
                        class="flex items-center rounded-full bg-primary-50 px-3 py-1"
                    >
                        <span class="text-sm font-semibold text-primary-600">
                            {{ remainingCallTime.usableFormatted }}
                        </span>
                    </div>
                    <span class="text-sm text-gray-500">/</span>
                    <div
                        class="flex items-center rounded-full bg-gray-100 px-3 py-1"
                    >
                        <span class="text-sm font-medium text-gray-600">
                            {{ remainingCallTime.balanceFormatted }}
                            {{ t('minutes') }}
                        </span>
                    </div>
                </div>
            </div>
            <i
                :class="[
                    'pi',
                    isTimeExpanded ? 'pi-chevron-down' : 'pi-chevron-up',
                    'text-gray-500',
                ]"
            />
        </button>

        <div
            v-if="isTimeExpanded"
            class="border-t border-gray-100 bg-gray-50 px-4 py-3"
        >
            <div class="space-y-2">
                <div
                    class="h-2 w-full overflow-hidden rounded-full bg-gray-200"
                >
                    <div
                        class="h-full bg-primary-600 transition-all duration-300"
                        :style="{
                            width: `${remainingCallTime.percentageUsed}%`,
                        }"
                    />
                </div>
                <!-- <div class="flex justify-between text-xs text-gray-500">
                    <span>00:00</span>
                    <span>{{ remainingCallTime.balanceFormatted }}</span>
                </div> -->
            </div>

            <div class="mt-4 grid auto-cols-max grid-flow-col gap-4">
                <div class="rounded-lg bg-gray-100 p-3">
                    <span class="text-xs text-gray-500">{{
                        t('total-time')
                    }}</span>
                    <div class="mt-1 text-sm font-medium text-gray-900">
                        {{ remainingCallTime.balanceFormatted }}
                        {{ t('minutes') }}
                    </div>
                </div>
                <div class="rounded-lg bg-primary-50 p-3">
                    <span class="text-xs text-primary-500">{{
                        t('remaining-time')
                    }}</span>
                    <div class="mt-1 text-sm font-medium text-primary-600">
                        {{ remainingCallTime.usableFormatted }}
                        {{ t('minutes') }}
                    </div>
                </div>
                <div class="rounded-lg bg-red-50 p-3">
                    <span class="text-xs text-red-500">{{
                        t('used-time')
                    }}</span>
                    <div class="mt-1 text-sm font-medium text-red-600">
                        {{ remainingCallTime.usedFormatted }} {{ t('minutes') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "start-consultation": "Start consultation",
        "remind": "Remind",
        "request-callback": "Request a callback",
        "minutes": "minute(s)",
        "total-time": "Total",
        "used-time": "Used",
        "remaining-time": "Remaining",
        "low-time-warning": "Less than 15 minutes remaining",
        "buy-more": "Buy More"
    },
    "vi": {
        "start-consultation": "Bắt đầu phiên tư vấn",
        "remind": "Nhắc gọi",
        "request-callback": "Gửi yêu cầu gọi lại",
        "minutes": "phút",
        "total-time": "Tổng thời gian",
        "used-time": "Đã sử dụng",
        "remaining-time": "Còn lại",
        "low-time-warning": "Còn dưới 15 phút",
        "buy-more": "Mua thêm"
    }
}
</i18n>
