// @ts-check
import withNuxt from './.nuxt/eslint.config.mjs'

export default withNuxt(
    // Your custom configs here
    {
        rules: {
            'vue/no-multiple-template-root': 'off',
            '@typescript-eslint/no-explicit-any': 'off',
            'vue/multi-word-component-names': 'off',
            'vue/html-self-closing': 'off',
            '@typescript-eslint/no-unused-expressions': [
                'error',
                {
                    allowShortCircuit: true,
                    // allowTernary: true,
                    // allowTaggedTemplates: true,
                },
            ],
        },
    },
    {
        ignores: ['**/vitest*'],
    },
)
