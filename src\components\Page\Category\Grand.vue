<!-- components/page/category -->
<script setup lang="ts">
import type { PropType } from '#imports'
import { usePageGrandCategory } from '~/composables/category-grand-page'
type LayoutType = 'list' | 'grid' | 'timeline'
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'

const props = defineProps({
  slug: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  layout: {
    type: String as PropType<LayoutType>,
    default: () => 'list',
  },
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
})
const { data, category, title, currentPage, pageSize } = usePageGrandCategory(
  props.slug,
  props.id,
)
</script>
<template>
  <div>
    <WHeroSection
      class="h-96"
      :blocks="['background-image', 'overlay', 'title', 'sub-title']"
      :background-image="{
        src: category?.page.cover.url,
        alt: category?.page.properties.Name,
      }"
      :title="title"
      :sub-title="category?.page.properties.Sapo"
      seo-title-tag="h1"
    />
    <div class="grid grid-cols-12 py-4">
      <div class="col-span-10 col-start-2 space-y-4">
        <PageCategoryArticleList
          :articles="data?.hits"
          :slug="props.slug"
          :layout="props.layout"
          :blocks="props.blocks"
        />
      </div>
    </div>

    <Paginator
      :rows="pageSize"
      :total-records="data?.total"
      :page-link-size="3"
      template="FirstPageLink PrevPageLink PageLinks NextPageLink"
      @page="currentPage = $event.page + 1"
    />
  </div>
</template>
