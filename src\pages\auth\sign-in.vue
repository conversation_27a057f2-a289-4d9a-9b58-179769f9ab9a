<template>
    <div
        class="flex min-h-screen flex-col items-center justify-between bg-gradient-to-br from-primary-50 via-primary-100/50 to-white p-4"
    >
        <!-- Header section -->
        <div
            class="mb-8 flex w-full items-center justify-between px-2 pb-4"
            :class="{
                'pt-safe': isNative,
                'pt-4': !isNative,
            }"
        >
            <Button
                icon="pi pi-angle-left"
                text
                :disabled="loading"
                rounded
                @click="goBack"
            />
            <div class="flex items-center gap-3">
                <Button
                    v-for="(link, index) in utilityLinks"
                    :key="index"
                    class="flex h-10 w-10 items-center justify-center rounded-full bg-white/80 text-gray-700 shadow-sm backdrop-blur-md transition-all hover:bg-gray-100 hover:shadow-md"
                    text
                    rounded
                    @click="link.command"
                >
                    <iconify-icon :icon="link.icon" class="text-xl" />
                    <span class="sr-only">{{ link.label }}</span>
                </Button>
            </div>
        </div>

        <div class="flex flex-col items-center space-y-4">
            <!-- Logo section with animation -->
            <img
                src="/images/logo.svg"
                alt="Wellcare Logo"
                class="h-auto w-24 drop-shadow-lg"
            />

            <!-- Sign in options -->
            <h1 class="mb-8 text-center text-3xl font-bold text-gray-800">
                {{ t('auth.welcome') }}
            </h1>
        </div>

        <div class="w-full max-w-md space-y-4">
            <div class="space-y-4">
                <Button
                    class="group flex w-full items-center justify-between gap-3 rounded-xl border-2 border-gray-200 bg-white/90 px-5 py-3 text-gray-700 shadow-sm backdrop-blur-sm transition-all hover:border-blue-200 hover:bg-blue-50/50 hover:shadow-md"
                    outlined
                    :loading="loading"
                    @click="signInWithGoogle"
                >
                    <iconify-icon
                        icon="flat-color-icons:google"
                        class="text-2xl"
                    />
                    <span class="flex-1 text-center">{{
                        t('auth.signInWithGoogle')
                    }}</span>
                    <div class="w-6"></div>
                </Button>

                <Button
                    v-if="isIOS"
                    class="group flex w-full items-center justify-between gap-3 rounded-xl border-2 border-gray-200 bg-white/90 px-5 py-3 text-gray-700 shadow-sm backdrop-blur-sm transition-all hover:border-gray-300 hover:bg-gray-50/50 hover:shadow-md"
                    outlined
                    :loading="loading"
                    @click="signInWithApple"
                >
                    <iconify-icon icon="mdi:apple" class="text-2xl" />
                    <span class="flex-1 text-center">{{
                        t('auth.signInWithApple')
                    }}</span>
                    <div class="w-6"></div>
                </Button>
            </div>

            <!-- Terms and Privacy -->
            <div class="text-center text-sm text-gray-600 pb-safe">
                <p class="mb-2">{{ t('auth.termsPrefix') }}</p>
                <p class="space-x-1">
                    <a
                        class="text-primary-600 transition-colors hover:text-primary-700 hover:underline"
                        @click="handleTerms"
                    >
                        {{ t('auth.terms') }}
                    </a>
                    <span>{{ t('auth.and') }}</span>
                    <a
                        class="text-primary-600 transition-colors hover:text-primary-700 hover:underline"
                        @click="handlePrivacy"
                    >
                        {{ t('auth.privacy') }}
                    </a>
                </p>
            </div>
        </div>

        <!-- Reusable components -->
        <SharedPopoverLang ref="popoverLangRef" />
        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="support"
        />
    </div>
</template>

<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

definePageMeta({
    pageTransition: {
        name: 'page',
        mode: 'out-in',
    },
})

const { t } = useI18n()
const router = useRouter()
const { openExternalLink } = useExternalLink()
const { signInIDP, loading } = useAppAuth()

const popoverLangRef = ref<any>(null)
const support = ref(false)

const utilityLinks = computed(() => [
    {
        label: t('auth.support'),
        href: '#',
        command: handleSupport,
        icon: 'material-symbols:support-agent',
    },
    {
        label: t('auth.language'),
        href: '#',
        command: togglePopoverLang,
        icon: 'material-symbols:translate',
    },
])

const isNative = computed(() => Capacitor.isNativePlatform())

const isIOS = computed(() => Capacitor.getPlatform() === 'ios')

const togglePopoverLang = (event: Event) => {
    if (popoverLangRef.value) {
        popoverLangRef.value.toggle(event)
    }
}

const handleSupport = () => {
    support.value = true
}

const handleTerms = () => {
    openExternalLink('https://wellcare.vn/ho-tro/dieu-khoan-su-dung')
}

const handlePrivacy = () => {
    openExternalLink('https://wellcare.vn/ho-tro/chinh-sach-bao-mat')
}

const signInWithGoogle = async () => {
    try {
        await signInIDP('google', {
            extraQueryParams: {
                prompt: 'select_account',
            },
        })
    } catch (error) {
        console.error('Google sign in failed:', error)
    }
}

const signInWithApple = async () => {
    try {
        await signInIDP('apple')
    } catch (error) {
        console.error('Apple sign in failed:', error)
    }
}

const goBack = () => {
    router.back()
}

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)
</script>

<style scoped>
:deep(.p-dropdown) {
    @apply rounded-lg border-none bg-white shadow-sm;
}

/* Native-like slide transitions */
.slide-right-enter-active,
.slide-right-leave-active {
    position: fixed;
    width: 100%;
    transition: all 0.25s cubic-bezier(0.33, 1, 0.68, 1);
}

.slide-right-enter-from {
    opacity: 0;
    transform: translateX(100%);
}

.slide-right-leave-to {
    opacity: 0;
    transform: translateX(-30%);
}

/* Left slide transition for back navigation */
.slide-left-enter-active,
.slide-left-leave-active {
    position: fixed;
    width: 100%;
    transition: all 0.25s cubic-bezier(0.33, 1, 0.68, 1);
}

.slide-left-enter-from {
    opacity: 0;
    transform: translateX(-100%);
}

.slide-left-leave-to {
    opacity: 0;
    transform: translateX(30%);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}
</style>

<i18n lang="json">
{
    "en": {
        "auth": {
            "welcome": "Sign in or create an account",
            "signInWithGoogle": "Continue with Google",
            "signInWithApple": "Continue with Apple",
            "termsPrefix": "By signing in, you agree to our",
            "terms": "Terms of Service",
            "and": "and",
            "privacy": "Privacy Policy"
        }
    },
    "vi": {
        "auth": {
            "welcome": "Đăng nhập hoặc tạo tài khoản",
            "signInWithGoogle": "Tiếp tục với Google",
            "signInWithApple": "Tiếp tục với Apple",
            "termsPrefix": "Bằng việc đăng nhập, bạn đồng ý với",
            "terms": "Điều khoản sử dụng",
            "and": "và",
            "privacy": "Chính sách bảo mật"
        }
    }
}
</i18n>
