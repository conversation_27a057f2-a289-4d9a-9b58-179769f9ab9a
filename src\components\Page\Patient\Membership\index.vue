<script setup lang="ts">
import { useIntervalFn } from '@vueuse/core'
import { appRoutes } from '~/constants'

const { user } = defineProps({
    user: {
        type: Object,
        properties: {
            name: { type: String },
            avatar: { type: String },
            email: { type: String },
        },
        default: () => {},
    },
})

const router = useRouter()
const { t } = useI18n({ useScope: 'local' })

const { isMembership } = useMembership()
const { bannerDirective } = useBanner({ position: 'top', size: 10 })

const currentImage = ref(0)
const bannerItems = computed(() => bannerDirective.value || [])

const bannerRef = ref<HTMLElement | null>(null)
const { direction } = useSwipe(bannerRef)

const benefits = [
    // { titleKey: 'benefit-title-1', descKey: 'benefit-description-1' },
    { titleKey: 'benefit-title-4', descKey: 'benefit-description-4' },
    { titleKey: 'benefit-title-2', desc<PERSON>ey: 'benefit-description-2' },
    { titleKey: 'benefit-title-3', descKey: 'benefit-description-3' },
]

const getAccentColor = (index: number) => {
    const colors = [
        'bg-purple-500',
        'bg-orange-400',
        'bg-green-500',
        'bg-blue-500',
    ]
    return colors[index % colors.length]
}

const getBackgroundColor = (index: number) => {
    const colors = [
        'bg-purple-50/80',
        'bg-orange-50/80',
        'bg-green-50/80',
        'bg-blue-50/80',
    ]
    return colors[index % colors.length]
}

function handleBannerClick(bannerItem: any) {
    if (bannerItem.target.startsWith('https')) {
        window.open(bannerItem.target, '_blank')
    } else {
        router.push({
            path: appRoutes.patient.checkout(bannerItem.target),
        })
    }
}

useIntervalFn(() => {
    currentImage.value = (currentImage.value + 1) % bannerItems.value.length
}, 3000)

watch(direction, () => {
    switch (direction.value) {
        case 'left':
            currentImage.value =
                (currentImage.value + 1) % bannerItems.value.length
            break
        case 'right':
            currentImage.value =
                (currentImage.value - 1 + bannerItems.value.length) %
                bannerItems.value.length
            break
        default:
            break
    }
})
</script>

<template>
    <div>
        <div
            v-if="bannerItems.length"
            class="relative h-40 sm:mx-6 sm:h-36 lg:h-48 xl:h-60"
        >
            <NuxtImg
                ref="bannerRef"
                class="h-full w-full object-cover sm:rounded"
                :src="bannerItems[currentImage]?.file"
                @click="handleBannerClick(bannerItems[currentImage])"
            />
            <div
                class="absolute -bottom-6 left-1/2 flex -translate-x-2/4 flex-row items-center gap-2"
            >
                <div
                    v-for="(_item, index) in bannerItems"
                    :key="index"
                    :class="[
                        'size-[10px] rounded-full transition',
                        currentImage == index
                            ? 'bg-primary'
                            : 'bg-zinc-300 bg-opacity-50',
                    ]"
                />
            </div>
        </div>

        <div v-if="isMembership" class="Membership mt-12">
            <SharedPageSection
                :title="t('membership-benefits')"
                :pt="{ content: 'mx-4' }"
            >
                <PagePatientMembershipCard
                    :user="user"
                    :is-member="isMembership"
                />

                <div>
                    <!-- Slider Container -->
                    <swiper
                        :slides-per-view="'auto'"
                        :space-between="12"
                        :grab-cursor="true"
                    >
                        <swiper-slide
                            v-for="(benefit, index) in benefits"
                            :key="index"
                            class="!w-[280px]"
                        >
                            <div
                                class="relative min-h-[160px] rounded-xl p-4"
                                :class="getBackgroundColor(index)"
                            >
                                <!-- Accent Bar -->
                                <div
                                    class="absolute left-0 top-4 h-8 w-1.5 rounded-r-full"
                                    :class="getAccentColor(index)"
                                />

                                <!-- Content -->
                                <div class="flex items-start justify-between">
                                    <div class="ml-2">
                                        <div class="mb-2 font-semibold">
                                            {{ t(benefit.titleKey) }}
                                        </div>
                                        <p class="text-[13px] text-zinc-500">
                                            {{ t(benefit.descKey) }}
                                        </p>
                                    </div>

                                    <!-- More Icon -->
                                    <IconMoreHorizontal class="text-gray-400" />
                                </div>
                            </div>
                        </swiper-slide>
                    </swiper>
                </div>
            </SharedPageSection>
        </div>
    </div>
</template>

<style scoped>
#accordion button[aria-expanded='true'] {
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
}
</style>

<i18n lang="json">
{
    "en": {
        "membership-details": "Healthcare benefits",
        "membership-benefits": "Membership Benefits",
        "Valid to": "VALID TO",
        "active-member": "Active Member",
        "benefit-title-1": "Personal Doctor",
        "benefit-title-2": "HealthGPT",
        "benefit-title-3": "EduHub",
        "benefit-title-4": "Health Program",
        "benefit-description-1": "Having a personal doctor who understands you and takes care of your health on an ongoing basis is priceless!",
        "benefit-description-2": "From symptom checks to mental health support, expert care at your fingertips 24/7.",
        "benefit-description-3": "Your doctor's audio and video library, answering real patient questions on wide range of health topics.",
        "benefit-description-4": "With expert guidance for pregnancy, parenting, chronic disease management, and more – Your health journey becomes truly simple and easy."
    },
    "vi": {
        "membership-details": "Quyền lợi chăm sóc sức khỏe",
        "membership-benefits": "Quyền lợi thành viên",
        "Valid to": "HIỆU LỰC ĐẾN",
        "renew": "Gia hạn",
        "active-member": "Đã kích hoạt",
        "benefit-title-1": "Bác sĩ riêng",
        "benefit-title-2": "HealthGPT",
        "benefit-title-3": "EduHub",
        "benefit-title-4": "Chương trình sức khỏe",
        "benefit-description-1": "Có được một bác sĩ hiểu mình và chăm sóc sức khỏe thường xuyên cho mình, là điều vô giá!",
        "benefit-description-2": "Từ tra cứu bệnh từ triệu chứng đến Chăm sóc sức khỏe tinh thần, hỗ trợ chuyên sâu 24/7.",
        "benefit-description-3": "Kho video và audio giải đáp của bác sĩ cho các câu hỏi thực tế từ bệnh nhân về nhiều chủ đề sức khỏe khác nhau.",
        "benefit-description-4": "Với hướng dẫn của chuyên gia cho thai kỳ, nuôi dạy con, quản lý bệnh mãn tính và nhiều hơn thế - Hành trình chăm sóc sức khỏe trở nên thật đơn giản và dễ dàng."
    }
}
</i18n>
