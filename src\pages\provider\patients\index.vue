<script setup lang="ts">
import { appRoutes } from '~/constants'
import { definePageMeta, ref } from '#imports'
import DynamicHeader from '~/components/Shared/DynamicHeader.vue'
import { useI18n } from 'vue-i18n'

definePageMeta({ layout: 'provider', middleware: 'auth-capacitor' })

const router = useRouter()
const { fullPath } = useRoute()
const { t } = useI18n()

const { user }: any = useUserInfo({ scope: '_id' })
const doctorId = computed(() => user.value._id)

const handleEmitPatientCard = (patient: any) => {
    const { relatedId } = patient
    if (patient?.conversationId) {
        router.push({
            path: appRoutes.provider.diary(patient?.conversationId),
            query: {
                providerUser: patient?.user, // Need to 'providerUser' instead of 'user',
                patient: patient?.relatedId,
            },
        })
    } else if (relatedId) {
        router.push(`${fullPath}/${relatedId}`)
    } else {
        router.push({
            path: appRoutes.provider.diary(patient?.conversationId),
            query: {
                providerUser: patient?.user, // Need to 'providerUser' instead of 'user',
                patient: patient?.relatedId,
            },
        })
    }
}

//state
const isSearchSidebarVisible = ref(false)
const searchQuery = ref('')

//methods
// const openSearchSidebar = () => {
//     isSearchSidebarVisible.value = true
// }
const performSearch = () => {
    // Implement your search logic here
    isSearchSidebarVisible.value = false
}

// const quickActions: IQuickAction[] = [
//     {
//         name: 'Copy link',
//         icon: 'pi pi-link',
//         important: true,
//     },
// ]
</script>

<template>
    <div>
        <DynamicHeader>
            <template #header>
                <h4
                    class="line-clamp-1 overflow-hidden overflow-ellipsis whitespace-normal break-words break-all"
                >
                    {{ t('My patients') }}
                </h4>
            </template>
        </DynamicHeader>

        <div class="container mx-auto px-4 py-2">
            <h3 class="my-6">
                {{ t('My patients') }}
            </h3>
            <WDashBoardPatient
                :user-id="doctorId"
                @patient-click="handleEmitPatientCard"
            />
        </div>

        <Sidebar
            v-model:visible="isSearchSidebarVisible"
            position="full"
            class="p-sidebar-lg"
            header="Search"
        >
            <div class="flex flex-row items-center gap-2">
                <IconField icon-position="left" class="w-full">
                    <InputIcon class="pi pi-search" />
                    <InputText
                        v-model="searchQuery"
                        placeholder="Search"
                        @submit="performSearch"
                    />
                </IconField>
                <Button label="Search" @click="performSearch" />
            </div>
        </Sidebar>
    </div>
</template>

<i18n lang="json">
{
    "vi": {
        "My patients": "Bệnh nhân của tôi"
    },
    "en": {
        "My Patients": "My Patients"
    }
}
</i18n>
