<script setup lang="ts">
import { useI18n } from '#imports'
const { locales, locale, setLocale, t } = useI18n()

const isCurrentLocale = (code: string) => code === locale.value
</script>

<template>
    <div class="w-full rounded-lg">
        <!-- Header -->
        <div class="p-3">
            <h2
                class="flex items-center gap-2 font-medium text-gray-700 dark:text-gray-200"
            >
                <span class="uppercase">{{ t('language') }}</span>
            </h2>
        </div>

        <!-- Language Options -->
        <div class="space-y-2 p-2">
            <Button
                v-for="_locale in locales"
                :key="_locale.code"
                class="relative w-full py-4 transition-colors last:mb-0"
                :severity="
                    isCurrentLocale(_locale.code) ? 'primary' : 'secondary'
                "
                outlined
                @click="setLocale(_locale.code)"
            >
                <div class="flex w-full items-center justify-between px-1">
                    <div class="flex items-center gap-2">
                        <span class="font-medium">{{ _locale.name }}</span>
                    </div>
                    <i
                        v-if="isCurrentLocale(_locale.code)"
                        class="pi pi-check text-primary-500"
                    >
                    </i>
                </div>
            </Button>
        </div>
    </div>
</template>

<style scoped>
.p-button {
    @apply transition-all duration-200;
}

.p-button:focus {
    @apply outline-none ring-2 ring-blue-200 ring-offset-2;
}

/* Dark mode adjustments */
:deep(.dark) .p-button:focus {
    @apply ring-offset-gray-800;
}
</style>
