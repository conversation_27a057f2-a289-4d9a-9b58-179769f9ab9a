<script lang="ts" setup>
// import type { PropType } from "#imports"
// import type { IUser } from "~/model"
const { avatarUrl } = defineProps({
    avatarUrl: {
        type: String,
        default: '',
    },
})
const { user } = useUserInfo({ scope: ['_id', 'isMember', 'isProvider', 'isAdmin', 'role'] })
console.log("🚀 ~ _user:", user)
</script>

<template>
    <Avatar :image="avatarUrl" shape="circle" size="large" class="bg-zinc-100 dark:bg-zinc-700" />
</template>
