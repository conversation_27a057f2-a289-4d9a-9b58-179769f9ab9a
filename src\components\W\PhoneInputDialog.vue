<template>
    <Dialog
        v-model:visible="visible"
        :header="t('title:change-outgoing-phone')"
        :draggable="false"
        modal
        position="bottom"
        :dismissable-mask="true"
        class="max-w-xl"
    >
        <div class="rounded-lg bg-orange-100 p-4 text-orange-500">
            {{ t('label:alert') }}
        </div>
        <div class="relative mx-auto mt-4 max-w-xl">
            <!-- Phone input with country code -->
            <div class="flex w-full items-center rounded-lg border p-2">
                <button
                    class="flex items-center gap-2 border-r pr-2"
                    @click="showDialog = true"
                >
                    <img
                        :src="selectedCountry.flag"
                        class="h-4 w-6"
                        :alt="selectedCountry.name"
                    />
                    <span>{{ selectedCountry.dialCode }}</span>
                    <i class="pi pi-chevron-down text-sm" />
                </button>
                <input
                    v-model="phoneNumber"
                    type="tel"
                    class="px-2 outline-none"
                    :placeholder="getExamplePhone('VN')"
                />
            </div>

            <!-- Country selector dialog -->
            <Dialog
                v-model:visible="showDialog"
                modal
                :style="{ width: '100%', maxWidth: '400px' }"
                position="bottom"
                class="p-0"
            >
                <template #header>
                    <div class="w-full pb-0 pt-4">
                        <h2 class="text-xl font-medium">Chọn mã quốc gia</h2>
                    </div>
                </template>
                <div class="relative">
                    <span class="absolute inset-y-0 right-4 flex items-center">
                        <i class="pi pi-search text-gray-400" />
                    </span>
                    <InputText
                        v-model="searchQuery"
                        class="w-full rounded-lg pr-10"
                        placeholder="Tìm kiếm"
                    />
                </div>
                <div class="mt-6 max-h-[400px] overflow-y-auto">
                    <button
                        v-for="country in filteredCountries"
                        :key="country.code"
                        class="flex w-full items-center gap-4 p-4 hover:bg-gray-50"
                        @click="selectCountry(country)"
                    >
                        <img
                            :src="country.flag"
                            class="h-4 w-6"
                            :alt="country.name"
                        />
                        <span>{{ country.name }}</span>
                        <span class="ml-auto text-gray-500">{{
                            country.dialCode
                        }}</span>
                    </button>
                </div>
            </Dialog>
        </div>
        <div class="mt-4 text-sm text-red-500">
            <div v-show="error">{{ error }}</div>
        </div>
        <div class="mt-4">
            <Button class="w-full" @click="save">{{ t('btn:save') }}</Button>
        </div>
    </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useCountries } from '~/composables/common/contries'
import {
    usePhoneFormat,
    type Country,
} from '~/composables/common/phone-validation'

const showDialog = ref(false)
const searchQuery = ref('')
const phoneNumber = ref('')
const { t } = useI18n()

const { countries } = useCountries()

const selectedCountry = ref<Country>({
    code: 'VN',
    dialCode: '+84',
    flag: 'https://flagcdn.com/w80/vn.png',
    name: 'Viet Nam',
})

const error = ref<string>('')

const { getExamplePhone, validatePhoneNumber, standardPhone } = usePhoneFormat(
    computed(() => phoneNumber.value),
    computed(() => selectedCountry.value.dialCode),
)

const filteredCountries = computed(() => {
    if (!searchQuery.value) return countries
    const query = searchQuery.value.toLowerCase()
    return countries.filter(
        (country) =>
            country.name.toLowerCase().includes(query) ||
            country.dialCode.includes(query),
    )
})

const selectCountry = (country: any) => {
    selectedCountry.value = country
    showDialog.value = false
}

const fullPhoneNumber = computed(() => {
    // return `+${selectedCountry.value.dialCode}${phoneNumber.value}`
    return {
        countryCode: selectedCountry.value.dialCode,
        phoneNumber: standardPhone.value,
    }
})

const visible = ref<boolean>(false)

function open() {
    visible.value = true
}

function close() {
    visible.value = false
}

// Optionally emit the value to parent
const emit = defineEmits(['on:save'])
function save() {
    if (!validatePhoneNumber(phoneNumber.value)) {
        error.value = t('error:invalid', {
            example: getExamplePhone(selectedCountry.value.code),
        })
        return
    }
    error.value = ''
    console.log(fullPhoneNumber.value)
    emit('on:save', fullPhoneNumber.value)
    phoneNumber.value = ''
}

defineExpose({
    open,
    close,
})
</script>
<i18n lang="yaml">
vi:
    'title:change-outgoing-phone': 'Đổi số điện thoại gọi đi'
    'label:alert': 'Thao tác này chỉ thay đổi số điện thoại gọi đi đối với cuộc tư vấn này, số điện thoại sổ khám của bạn sẽ không bị thay đổi'
    'error:invalid': 'Vui lòng điền số điện thoại hợp lệ. Vd: {example}'
en:
    'title:change-outgoing-phone': 'Change outgoing phone number'
    'label:alert': 'This operation only changes the outgoing phone number for this consultation. Your medical record phone number will not be changed'
    'error:invalid': 'Please enter a valid phone number. Ex: {example}'
</i18n>
