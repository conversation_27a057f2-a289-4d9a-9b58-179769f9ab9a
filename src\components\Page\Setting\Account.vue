<script setup lang="ts">
const { signOut } = useAppAuth()
const { user } = useUserInfo({ scope: ['name', 'avatar', 'email'] })

// const isAvatar = computed<boolean>(() => !!user.value?.avatar?.url)
const avatarUrl = computed<string>(() => user.value?.avatar?.url || `https://ui-avatars.com/api/?name=${user.value.name}`)
</script>

<template>
    <div class="flex flex-col items-center">
        <Avatar :image="avatarUrl" size="xlarge" label="S" class="mb-2" />
        <p>{{ user?.name }}</p>
        <small>{{ user?.email }}</small>
        <div class="mt-8 flex w-full flex-col items-center justify-center">
            <small>App version: 0.5</small>
            <Button
                label="Sign out"
                icon="pi pi-sign-out"
                text
                severity="danger"
                icon-pos="right"
                @click="signOut"
            />
        </div>
    </div>
</template>
