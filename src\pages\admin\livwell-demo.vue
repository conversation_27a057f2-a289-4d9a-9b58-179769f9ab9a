<script setup lang="ts">
import { computed, onMounted, useAppAuth } from '#imports'
import { App } from '@capacitor/app'
import { Browser } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'

const {
    account,
    checkAuthStatus,
    handleAppAuthUrlOpen,
    signOut,
    signIn,
    signInIDP,
} = useAppAuth()

const isLogged = computedAsync(async () => {
    return await checkAuthStatus()
}, false)

// handle initial logic here
const fullName = computed(() => {
    return account?.value?.search
})

const consultation = ref<string>('')

const signInApp = async () => {
    try {
        await signIn()
    } catch (error) {
        console.error('SSO error:', error)
    }
}

const chatWithBot = async () => {
    try {
        const url = `https://livwell-wellcare-vn.mhealthvn.com/debug?token=${account.value.access_token}`
        if (Capacitor.isNativePlatform()) {
            await Browser.open({
                url,
                windowName: '_self',
                presentationStyle: 'popover',
            })
        } else {
            window.location.href = url
        }
    } catch (error) {
        console.error('SSO error:', error)
    }
}

onMounted(() => {
    App.addListener('appUrlOpen', handleAppAuthUrlOpen)
})
</script>
<template>
    <div class="p-6">
        <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
            <button @click="navigateTo('/debug')">
                <span class="underline">Back to home</span>
            </button>
        </div>
        <h1>Livwell App Demo</h1>

        <!-- Login Card -->
        <div class="rounded-xl bg-surface-50 p-6 shadow-lg dark:bg-surface-700">
            <h2 class="mb-4 text-center text-2xl font-bold text-surface-900">
                Login
            </h2>
            <div class="space-y-4">
                <p class="mb-2 text-surface-600 dark:text-surface-400">
                    checkAuthStatus: {{ isLogged }}
                </p>

                <p
                    v-if="fullName"
                    class="mb-2 text-surface-600 dark:text-surface-400"
                >
                    Name: {{ fullName }}
                </p>

                <template v-if="isLogged">
                    <button
                        class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                        @click="chatWithBot"
                    >
                        HealthGPT
                    </button>
                    <button
                        class="w-full rounded-lg bg-red-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-red-600"
                        @click="signOut"
                    >
                        sign out
                    </button>
                </template>

                <template v-else>
                    <button
                        class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                        @click="signInApp"
                    >
                        Sign in App
                    </button>
                    <button
                        class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                        @click="signInIDP('livwell_oidc')"
                    >
                        Sign in by livwell idp
                    </button>
                </template>
            </div>
        </div>
        <div
            v-if="isLogged"
            class="mt-3 rounded-xl bg-surface-50 p-6 shadow-lg dark:bg-surface-700"
        >
            <h2 class="mb-4 text-center text-2xl font-bold text-surface-900">
                Consultation
            </h2>
            <div class="space-y-4">
                <label>Consultation id</label>
                <InputText v-model="consultation" class="w-full"></InputText>

                <button
                    class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                    @click="navigateTo('/patient/consultation/' + consultation)"
                >
                    View
                </button>
            </div>
        </div>
    </div>
</template>
