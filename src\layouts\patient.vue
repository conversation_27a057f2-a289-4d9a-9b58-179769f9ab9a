<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import { usePatientNavItems } from '~/composables/page/patient/nav-items'

const navItems = usePatientNavItems()
const { login: loginPush } = usePushNotification()
const { addNativeLogs } = useLogs()

const isNative = computed(() => Capacitor.isNativePlatform())

onMounted(async () => {
    if (Capacitor.isNativePlatform()) {
        console.log('login push')
        addNativeLogs({
            context: 'usePushNotification',
            message: 'login push',
            time: new Date().toLocaleString(),
        })
        await loginPush()
    }
})
</script>

<template>
    <LayoutUser
        :class="[
            {
                'pb-safe': isNative,
            },
        ]"
        :nav-items="navItems"
    >
        <template #sideNavStart="{ isCollapsed }">
            <div
                :class="`flex w-full flex-row items-center gap-2 ${isCollapsed ? 'px-2' : ''}`"
            >
                <NuxtImg src="/logo.svg" class="size-9" />

                <p v-if="!isCollapsed" class="m-0 text-lg font-semibold">
                    Wellcare
                </p>
            </div>
        </template>

        <template #sideNavEnd="{ isCollapsed }">
            <LayoutProfileButton
                :is-collapsed="isCollapsed"
                target-url="/patient/settings"
            />
        </template>

        <template #main>
            <SharedWrapperReloadPage>
                <LayoutPermissions>
                    <slot />
                </LayoutPermissions>
            </SharedWrapperReloadPage>
        </template>
    </LayoutUser>
</template>

<style scoped>
.page-enter-active {
    transition: opacity 0.3s ease-in;
}

.page-enter-from {
    opacity: 0;
    transition-delay: 0.1s;
}

.layout-enter-active,
.layout-leave-active {
    transition: all 0.3s ease;
}
.layout-enter-from {
    opacity: 0;
    transform: scale(0.95);
}
.layout-leave-to {
    opacity: 0;
    transform: scale(0.95) translateX(0);
}
</style>
