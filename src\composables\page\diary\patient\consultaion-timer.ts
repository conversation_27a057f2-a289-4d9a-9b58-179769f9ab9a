import { computed, onBeforeUnmount, reactive, useDayjs } from '#imports'

export const useConsultationTimer = () => {
    const dayjs = useDayjs()

    const countdown = reactive({
        hours: 0,
        minutes: 0,
        seconds: 0,
    })

    let interval: NodeJS.Timeout | null = null

    const startCountdown = (endTime: string) => {
        stopCountdown()
        const targetTime = dayjs(endTime).valueOf()

        interval = setInterval(() => {
            const now = dayjs().valueOf()
            const distance = targetTime - now

            if (distance <= 0) {
                clearInterval(interval!)
                interval = null
                resetCountdown()
                console.log('Countdown finished!')
                return
            }

            countdown.hours = Math.floor(
                (distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60),
            )
            countdown.minutes = Math.floor(
                (distance % (1000 * 60 * 60)) / (1000 * 60),
            )
            countdown.seconds = Math.floor((distance % (1000 * 60)) / 1000)
        }, 1000)
    }

    const stopCountdown = () => {
        if (interval) {
            clearInterval(interval)
            interval = null
            console.log('Countdown stopped.')
        }
    }

    const resetCountdown = () => {
        stopCountdown()
        countdown.hours = 0
        countdown.minutes = 0
        countdown.seconds = 0
        console.log('Countdown reset.')
    }

    const formattedCountdown = computed(
        () =>
            `${countdown.hours.toString().padStart(2, '0')}:${countdown.minutes
                .toString()
                .padStart(
                    2,
                    '0',
                )}:${countdown.seconds.toString().padStart(2, '0')}`,
    )

    const isCountdownInProgress = computed<boolean>(() =>
        Boolean(countdown.hours || countdown.minutes || countdown.seconds),
    )

    onBeforeUnmount(() => {
        stopCountdown()
    })

    return {
        countdown,
        startCountdown,
        stopCountdown,
        resetCountdown,
        formattedCountdown,
        isCountdownInProgress,
    }
}
