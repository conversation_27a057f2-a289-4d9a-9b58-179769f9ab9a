<template>
    <div class="p-4">
        <!-- Loading and Error States -->
        <div v-if="state.isLoading" class="mb-4">
            <p class="text-blue-600">Processing...</p>
        </div>

        <div v-if="state.error" class="mb-4 rounded bg-red-50 p-4 text-red-600">
            <p>Error: {{ state.error.message }}</p>
        </div>

        <!-- Result Card -->
        <Card class="mb-6">
            <template #title>
                <div class="text-xl font-semibold">Last Request Result</div>
            </template>
            <template #subtitle>
                <div class="text-sm text-gray-600">
                    {{ state.lastFunctionExecuted }}
                </div>
            </template>
            <template #content>
                <pre class="max-h-60 overflow-auto rounded-lg bg-gray-50 p-4">{{
                    state.lastFunctionContent
                }}</pre>
            </template>
        </Card>

        <!-- Testing Environment Controls -->
        <Card class="mb-6">
            <template #title>
                <div class="text-lg font-semibold">Testing Environment</div>
            </template>
            <template #content>
                <div class="flex flex-col gap-4">
                    <div class="flex items-center gap-4">
                        <label class="flex items-center gap-2">
                            <Checkbox
                                v-model="mockWebResults"
                                binary
                                @change="toggleMockWebResults"
                            />
                            Enable Web Mock Results
                        </label>
                    </div>
                    <div class="flex items-center gap-4">
                        <label class="flex items-center gap-2">
                            <Checkbox
                                v-model="simulatesAskToBuyInSandbox"
                                binary
                                @change="********************************"
                            />
                            Simulate "Ask to Buy" in Sandbox
                        </label>
                    </div>
                </div>
            </template>
        </Card>

        <!-- Action Buttons -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
            <Button
                v-for="(action, index) in actions"
                :key="index"
                :severity="action.severity || 'primary'"
                size="small"
                class="p-2"
                :disabled="state.isLoading"
                @click="action.handler"
            >
                {{ action.label }}
            </Button>

            <Button @click="navigateTo('/')"> Back to Home </Button>
            <Button @click="visibleMembershipPopup = true">
                Open membership
            </Button>
        </div>

        <SharedMembershipPopup
            v-model:visible="visibleMembershipPopup"
            @skip="visibleMembershipPopup = false"
            @update:visible="(val: boolean) => (visibleMembershipPopup = val)"
        />
    </div>
</template>

<script setup lang="ts">
import { navigateTo } from '#imports'
import { LOG_LEVEL } from '@revenuecat/purchases-capacitor'

const {
    state,
    mockWebResults,
    simulatesAskToBuyInSandbox,
    toggleMockWebResults,
    ********************************,
    getOfferings,
    getProducts,
    purchaseStoreProduct,
    purchasePackage,
    restorePurchases,
    getCustomerInfo,
    syncPurchases,
    checkTrialOrIntroductoryPriceEligibility,
    canMakePayments,
    isConfigured,
} = useRevenueCat({
    autoInit: true,
    logLevel: LOG_LEVEL.VERBOSE,
    onCustomerInfoUpdate: (customerInfo) => {
        console.log('Customer info updated:', customerInfo)
    },
    onError: (error) => {
        console.error('RevenueCat error:', error)
    },
})

const visibleMembershipPopup = ref(false)

// Actions Configuration
const actions = [
    {
        label: 'Get Offerings',
        handler: getOfferings,
    },
    {
        label: 'Get Products',
        handler: () =>
            getProducts(['annual_freetrial', 'monthly_subscription']),
    },
    {
        label: 'Purchase Store Product',
        handler: purchaseStoreProduct,
        severity: 'warning',
    },
    {
        label: 'Purchase Package',
        handler: purchasePackage,
        severity: 'warning',
    },
    {
        label: 'Restore Purchases',
        handler: restorePurchases,
        severity: 'help',
    },
    {
        label: 'Get Customer Info',
        handler: getCustomerInfo,
    },
    {
        label: 'Sync Purchases',
        handler: syncPurchases,
    },
    {
        label: 'Check Trial Eligibility',
        handler: () =>
            checkTrialOrIntroductoryPriceEligibility(['annual_freetrial']),
    },
    {
        label: 'Can Make Payments',
        handler: canMakePayments,
    },
    {
        label: 'Is Configured',
        handler: isConfigured,
    },
]
</script>
