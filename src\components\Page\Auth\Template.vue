<script setup lang="ts">
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

const { locale } = useI18n()
const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)

const visibleSupport = ref(false)
const visibleLang = ref(false)
</script>

<template>
    <div
        class="flex h-screen w-screen items-start justify-center md:items-center"
    >
        <Card class="w-full !shadow-none md:max-w-[450px] md:shadow-lg">
            <template #title>
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-1">
                        <Avatar image="/logo.svg" />
                        <span class="text-base">WELLCARE</span>
                    </div>
                    <div class="flex items-center space-x-1">
                        <Button
                            v-if="!hideSupportContact"
                            outlined
                            severity="secondary"
                            rounded
                            size="small"
                            @click="visibleSupport = true"
                        >
                            <iconify-icon
                                icon="material-symbols:support-agent"
                                class="text-xl"
                        /></Button>

                        <Button
                            outlined
                            severity="secondary"
                            rounded
                            size="small"
                            @click="visibleLang = true"
                            >{{ locale }}</Button
                        >
                    </div>
                </div>
                <Divider />
            </template>
            <template #content>
                <slot></slot>
            </template>
        </Card>

        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="visibleSupport"
        />
        <SharedPopoverLang v-model:visible="visibleLang" />
    </div>
</template>
