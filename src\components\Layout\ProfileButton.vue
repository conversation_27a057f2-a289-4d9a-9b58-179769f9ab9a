<script lang="ts" setup>
interface Props {
    targetUrl?: string
    command?: () => void
    isCollapsed?: boolean
}
const props = withDefaults(defineProps<Props>(), {
    targetUrl: undefined,
    command: undefined,
})

// const route = useRoute()
const { user } = useUserInfo({ scope: ['avatar', 'name', 'email'] })

// const isActive = computed(() => props.targetUrl === route.path)

const avatar = computedAsync(() => user.value?.avatar?.url || '')
const name = computedAsync(() => user.value?.name || '')
const email = computedAsync(() => user.value?.email || '')

const onProfileClick = () => {
    if (props.targetUrl) navigateTo(props.targetUrl)
    else if (props.command) props.command()
    else return
}
</script>

<template>
    <button
        class="flex w-full flex-row items-center gap-2 rounded p-2 transition hover:bg-surface-100"
        @click="onProfileClick"
    >
        <Avatar :image="avatar" :alt="name" shape="circle" />

        <div v-if="!isCollapsed" class="flex-auto text-start leading-none">
            <p class="font-medium">{{ name }}</p>
            <small>{{ email }}</small>
        </div>
    </button>
</template>
