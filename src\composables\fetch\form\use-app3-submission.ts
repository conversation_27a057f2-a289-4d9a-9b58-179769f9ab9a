import { computed } from 'vue'
import type { Ref, ComputedRef } from 'vue'

interface FetchResponse {
    data: Ref<any>
    status: Ref<string>
    error: Ref<any>
    refresh: () => Promise<void>
    execute: () => Promise<void>
}

export const useApp3Submission = (
    userId: Ref<string> | ComputedRef<string> | string,
    objectId: Ref<string> | ComputedRef<string>,
): FetchResponse => {
    if (!userId || !objectId) {
        throw new Error(
            'Both userId and objectId must be provided as Ref or ComputedRef.',
        )
    }

    const headers = {
        'X-Parse-Application-Id': 'form-parse-server',
        'X-Parse-REST-API-Key': 'REST_API_KEY',
    }

    const config = useRuntimeConfig()

    const query = computed(() => {
        const where = {
            'state.userId': unref(userId),
            form: {
                __type: 'Pointer',
                className: 'Form',
                objectId: objectId.value,
            },
        }

        const baseUrl =
            config?.public?.['nuxt3-module-data-layer']?.baseUrl || ''

        return `${baseUrl}/form-parse-server/classes/Submission?where=${encodeURIComponent(
            JSON.stringify(where),
        )}&limit=1&order=-updatedAt`
    })

    const { data, status, error, execute, refresh } = useFetch(query, {
        method: 'GET',
        headers,
    })

    return {
        data,
        error,
        status,
        execute,
        refresh,
    }
}
