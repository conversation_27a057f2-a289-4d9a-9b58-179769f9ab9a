<script setup lang="ts">
import { appRoutes } from '~/constants'
definePageMeta({
    middleware: 'auth-capacitor',
    layout: 'nested-page',
})

const route = useRoute()
const { t } = useI18n()

const userId = computed<string>(() => (route.params?.id as string) || '')
const gender = computed<string>(() => (route.query?.gender as string) || '')
const dob = computed<string>(() => (route.query?.dob as string) || '')
const typeChart = computed<string>(() => route.params?._typeChart as string)
const layoutStore = useLayoutStore()

onMounted(() => {
    layoutStore.setTitle(t(typeChart.value))
    layoutStore.setTarget(appRoutes.patient.healthRecords.index)
})
onUnmounted(() => {
    layoutStore.reset()
})
</script>

<template>
    <WChart
        v-if="userId && typeChart"
        :key="userId"
        class="p-2"
        :type="typeChart"
        :user-id="userId"
        :gender="gender"
        :dob="dob"
    />
</template>
