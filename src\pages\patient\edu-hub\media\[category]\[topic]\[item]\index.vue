<script lang="ts" setup>
import { usePagePatientEduHubMediaItem } from '#imports'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const { item, loading } = usePagePatientEduHubMediaItem()

const videoPlayComponent = defineAsyncComponent(
    () => import('~/components/Page/Patient/EduHub/VideoPlay.vue'),
)

const mediaMapping: any = {
    'Short Videos': videoPlayComponent,
    'Offline Events': videoPlayComponent,
    'Live Streams': videoPlayComponent,
    Podcasts: defineAsyncComponent(
        () => import('~/components/Page/Patient/EduHub/AudioPlay.vue'),
    ),
}

const layoutStore = useLayoutStore()

watch(
    item,
    () => {
        const name = item.value?.provider?.name
        if (name) {
            layoutStore.setTitle(name)
        }
    },
    {
        deep: true,
    },
)

onUnmounted(() => {
    layoutStore.$reset()
})
</script>

<template>
    <div>
        <Suspense v-if="!loading">
            <template #default>
                <div>
                    <component
                        :is="mediaMapping[item.delivery]"
                        :media="item"
                    />
                    <SharedDoctorBanner
                        :class="`${item.delivery === 'Podcasts' ? 'mb-20' : ''}`"
                    />
                </div>
            </template>
            <template #fallback>
                <NuxtLoadingIndicator />
            </template>
        </Suspense>
    </div>
</template>
