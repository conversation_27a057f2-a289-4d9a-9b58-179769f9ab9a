/* Bad coding practices */

/* Background Classes */
button:focus {
    outline: none;
    /* box-shadow: none; */
}
.bg-primary\/30 {
    background-color: rgba(0, 150, 136, 0.3) !important;
}
.bg-primary-50 {
    background-color: var(--p-primary-50) !important;
}
.bg-primary-50-50 {
    background-color: rgba(236, 253, 252, 0.5) !important;
}
.bg-primary-100 {
    background-color: var(--p-primary-100) !important;
}
.bg-primary-200 {
    background-color: var(--p-primary-200) !important;
}
.bg-primary-300 {
    background-color: var(--p-primary-300) !important;
}
.bg-primary-400 {
    background-color: var(--p-primary-400) !important;
}
.bg-primary-500,
.bg-primary {
    background-color: var(--p-primary-500) !important;
}
.bg-primary-600 {
    background-color: var(--p-primary-600) !important;
}
.bg-primary-700 {
    background-color: var(--p-primary-700) !important;
}
.bg-primary-800 {
    background-color: var(--p-primary-800) !important;
}
.bg-primary-900 {
    background-color: var(--p-primary-900) !important;
}
.bg-primary-950 {
    background-color: var(--p-primary-950) !important;
}

.bg-surface-0 {
    background-color: var(--p-surface-0) !important;
}
.bg-surface-50 {
    background-color: var(--p-surface-50) !important;
}
.bg-surface-100 {
    background-color: var(--p-surface-100) !important;
}
.bg-surface-200 {
    background-color: var(--p-surface-200) !important;
}
.bg-surface-300 {
    background-color: var(--p-surface-300) !important;
}
.bg-surface-400 {
    background-color: var(--p-surface-400) !important;
}
.bg-surface-500 {
    background-color: var(--p-surface-500) !important;
}
.bg-surface-600 {
    background-color: var(--p-surface-600) !important;
}
.bg-surface-700 {
    background-color: var(--p-surface-700) !important;
}
.bg-surface-800 {
    background-color: var(--p-surface-800) !important;
}
.bg-surface-900 {
    background-color: var(--p-surface-900) !important;
}
.bg-surface-950 {
    background-color: var(--p-surface-950) !important;
}

/* TextColor Classes */
.text-primary-50 {
    color: var(--p-primary-50) !important;
}
.text-primary-100 {
    color: var(--p-primary-100) !important;
}
.text-primary-200 {
    color: var(--p-primary-200) !important;
}
.text-primary-300 {
    color: var(--p-primary-300) !important;
}
.text-primary-400 {
    color: var(--p-primary-400) !important;
}
.text-primary-500,
.text-primary {
    color: var(--p-primary-500) !important;
}
.text-primary-600 {
    color: var(--p-primary-600) !important;
}
.text-primary-700 {
    color: var(--p-primary-700) !important;
}
.text-primary-800 {
    color: var(--p-primary-800) !important;
}
.text-primary-900 {
    color: var(--p-primary-900) !important;
}
.text-primary-950 {
    color: var(--p-primary-950) !important;
}

.text-surface-0 {
    color: var(--p-surface-0) !important;
}
.text-surface-50 {
    color: var(--p-surface-50) !important;
}
.text-surface-100 {
    color: var(--p-surface-100) !important;
}
.text-surface-200 {
    color: var(--p-surface-200) !important;
}
.text-surface-300 {
    color: var(--p-surface-300) !important;
}
.text-surface-400 {
    color: var(--p-surface-400) !important;
}
.text-surface-500 {
    color: var(--p-surface-500) !important;
}
.text-surface-600 {
    color: var(--p-surface-600) !important;
}
.text-surface-700 {
    color: var(--p-surface-700) !important;
}
.text-surface-800 {
    color: var(--p-surface-800) !important;
}
.text-surface-900 {
    color: var(--p-surface-900) !important;
}
.text-surface-950 {
    color: var(--p-surface-950) !important;
}
