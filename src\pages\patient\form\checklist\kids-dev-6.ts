import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'QlsLHSzoDN',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2irrjj7ix8',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON>c mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 6 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. B<PERSON><PERSON> sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2irs25fkhk',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_media',
                            tag: 'img',
                            src: 'https://cdn-assets-eu.frontify.com/s3/frontify-enterprise-files-eu/**********************************************************************************************************************************************************************************************************************************************:ihh-healthcare-berhad:WIpoAUWbt8587tMs-8wbFtetdQr-My8rjGFIuL7sAl0?format=webp',
                            alt: 'Nice picture',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2iruffn5vo',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Thích ngắm mình trong gương',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2iruffn5vo',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2irurasbal',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết người quen',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2irurasbal',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2irusnnuik',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cười',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2irusnnuik',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2is23716cg',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Phối hợp với cha mẹ, thay phiên nhau tạo ra âm thanh',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2is23716cg',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2is250z8u',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Phun mưa (lè lưỡi và thổi nước bọt)',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2is250z8u',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2is26ozgna',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Hú hét chói tai',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2is26ozgna',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2is6n3gy5k',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cho đồ vật vào miệng để khám phá',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2is6n3gy5k',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2is6pm49g',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Với lấy món đồ chơi mà mình muốn',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2is6pm49g',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jvx0v8a2m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Mím chặt môi tỏ ý không muốn ăn thêm',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jvx0v8a2m',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2isadjhezs',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Lật ngửa từ tư thế nằm sấp',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isadjhezs',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2isafd15z',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chống đẩy với cánh tay thẳng khi nằm sấp',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isafd15z',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2isagmcrr',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chống tay đỡ người khi ngồi',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isagmcrr',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    title: '6 tháng',
    submission: 'remote',
    status: 'draft',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
})
