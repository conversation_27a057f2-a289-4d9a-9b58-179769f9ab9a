import type { IDate } from '../../transform'
import type { NotionWebsite } from '../hit'
import type { INotionPage } from './page'

export interface NotionContributors {
  _id?: string
  'Meta Description'?: string
  Avatar?: {
    url?: string
    [key: string]: any
  }
  Gender?: string
  Joined?: IDate
  Locale?: string
  Name?: string
  Profile?: string
  Slug?: string
  Specialties?: any[]
  Status?: string
  Title?: string
  Website?: INotionPage<NotionWebsite>
  [key: string]: any
}
