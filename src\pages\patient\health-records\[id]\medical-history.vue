<script setup lang="ts">
import { definePageMeta } from '#imports'
import { useMedicalHistory } from '~/composables/page/patient/health-records/medical-history'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const { medicalHistory } = useMedicalHistory()
</script>

<template>
    <div class="flex flex-col px-6">
        <h2>Medical History</h2>

        <PagePatientHealthRecordsMedicalHistoryCard
            v-for="meet in medicalHistory"
            :key="meet.diagnosisDate"
            :medical-history="meet"
            :detailed="true"
            class="mb-3"
        />
    </div>
</template>
