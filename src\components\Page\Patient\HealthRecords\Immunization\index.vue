<script setup lang="ts">
import type { Relationship } from '~/models'

interface Props {
    title?: string
    to?: string
    memberActivated?: Relationship
    isReadOnly: boolean
}

const props = defineProps<Props>()

const memberId = computed(() => props.memberActivated?.related?._id || '')
</script>

<template>
    <SharedPageSection
        :title="title"
        :view-all-to="to"
        :pt="{ content: 'flex flex-col gap-3 px-6' }"
    >
        <WVaccination
            v-if="memberId"
            :user-id="memberId"
            :is-read-only="isReadOnly"
        />
    </SharedPageSection>
</template>
