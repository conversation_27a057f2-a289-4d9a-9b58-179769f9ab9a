<script lang="ts" setup>
definePageMeta({
    colorMode: 'light',
})
const { t } = useI18n()
const { openExternalLink } = useExternalLink()
type FooterCommand = 'terms' | 'support' | 'language' | 'starter' | 'policy'
const footerLinks = computed(() => [
    {
        label: t('starter.footer.terms'),
        href: '#',
        command: 'terms' as FooterCommand,
    },
    {
        label: t('starter.footer.policy'),
        href: '#',
        command: 'policy' as FooterCommand,
    },
])

const handleLinkClick = (command: FooterCommand, event: Event) => {
    event.preventDefault()
    if (command === 'terms')
        openExternalLink('https://wellcare.vn/ho-tro/dieu-khoan-su-dung')
    else if (command === 'policy')
        openExternalLink('https://wellcare.vn/ho-tro/chinh-sach-bao-mat')
}
</script>

<template>
    <div class="mt-20 mt-safe">
        <ShutDownNotice />

        <WellcareTimelines />

        <FarewellMessage />

        <FeaturedStats />

        <TestimonialBlock />

        <!-- <OurFamily /> -->
        <div
            class="flex w-full flex-col items-center gap-2 border-t py-2 md:hidden"
        >
            <div class="text-sm text-surface-500">
                © 2015 - 2024 • Wellcare • All Rights Reserved
            </div>
            <div class="flex flex-wrap items-center justify-center gap-4">
                <a
                    v-for="link in footerLinks"
                    :key="link.command"
                    :href="link.href"
                    class="text-sm text-surface-600 transition-colors hover:text-primary hover:underline"
                    @click="handleLinkClick(link.command, $event)"
                >
                    {{ link.label }}
                </a>
            </div>
        </div>
    </div>
</template>
