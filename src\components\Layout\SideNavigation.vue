<template>
    <Menu
        :model="mappedNavItems"
        class="!hidden h-full !flex-col overflow-hidden sm:!flex"
        :pt="{
            root: `!border-none !rounded-none transform transition-all duration-300 ease-in-out ${isCollapsed ? '!min-w-[40px]' : '!min-w-[200px]'}`,
            end: 'p-4',
            start: 'px-4 py-[18px]',
            list: 'flex-auto space-y-3',
        }"
    >
        <template #start>
            <div
                :class="[
                    'flex',
                    isCollapsed ? 'flex-col' : '',
                    'items-center',
                    'gap-2',
                ]"
            >
                <slot name="start" :is-collapsed="isCollapsed" />
            </div>
        </template>

        <template #item="{ item, props: itemProps }">
            <a
                v-bind="itemProps.action"
                :class="[
                    activeTab === item.label
                        ? 'border-l-4 border-teal-500 bg-teal-50 text-teal-700 dark:bg-teal-900/20 dark:text-teal-300'
                        : 'border-l-4 border-transparent text-surface-700 hover:bg-surface-100 dark:text-surface-300 dark:hover:bg-surface-800',
                    'group relative flex items-center rounded-r-lg p-3 transition-all duration-200',
                    isCollapsed ? 'justify-center' : 'justify-start',
                ]"
            >
                <iconify-icon
                    :icon="
                        activeTab === item.label
                            ? item.activeIcon
                            : item.defaultIcon
                    "
                    class="flex-shrink-0 transition-all duration-200 group-hover:scale-110"
                    :class="[
                        isCollapsed ? 'text-2xl' : 'text-xl',
                        activeTab === item.label
                            ? 'text-teal-500'
                            : 'text-surface-500 dark:text-surface-400',
                    ]"
                />
                <span
                    class="ml-3 overflow-hidden whitespace-nowrap text-sm font-medium transition-all duration-200"
                    :class="
                        isCollapsed ? 'w-0 opacity-0' : 'w-auto opacity-100'
                    "
                >
                    {{ item.label }}
                </span>
                <Badge
                    v-if="item.badge"
                    :value="item.badge"
                    class="absolute right-3 ml-auto text-xs font-semibold transition-all duration-200"
                    :class="isCollapsed ? 'opacity-0' : 'opacity-100'"
                />
            </a>
        </template>

        <template #end>
            <Button
                class="my-2 w-full"
                :icon="`pi pi-angle-double-${isCollapsed ? 'right' : 'left'}`"
                outlined
                @click="collapse"
            />

            <slot name="end" :is-collapsed="isCollapsed" />
        </template>
    </Menu>
</template>

<script setup lang="ts">
import type { NavItem } from '~/models'

interface Props {
    navItems: NavItem[]
}

const props = defineProps<Props>()
const router = useRouter()
const route = useRoute()
const isCollapsed = ref(true)

const activeTab = computed(() => {
    return props.navItems.find((el) => el.to === route.path)?.label
})

const collapse = () => {
    isCollapsed.value = !isCollapsed.value
    return isCollapsed.value
}

const mappedNavItems = computed(() => {
    return props.navItems
        ?.filter((navItem) => !navItem.hideOnWeb)
        .map((element) => ({
            ...element,
            command: () => router.push(element.to),
        }))
})

defineExpose({
    collapse,
})
</script>

<style scoped>
.menu-item {
    transition: all 0.2s ease-in-out;
}

.menu-item:hover {
    transform: translateX(5px);
}

@media (max-width: 640px) {
    .menu-item:hover {
        transform: none;
    }
}
</style>
