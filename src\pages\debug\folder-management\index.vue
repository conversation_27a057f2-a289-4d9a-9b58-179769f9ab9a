<script setup lang="ts">
import { ref, computed } from 'vue'
import _ from 'lodash'
import { useRouter } from 'vue-router'

const router = useRouter()
// const { user } = await useUserInfo({ scope: ['_id'] });
const userId = ref('6566f2cc6f0cc288dd3d2c42')
const projectId = ref('emr')
const project: string = 'emr'
const initialQuery = {
    skip: 0,
    limit: 10,
    count: true,
    fields: 'tags, description, url, labels, folder, state, progress, name, description, sprite, thumbnail, mimetype, blurhash, updatedAt, createdAt, capturedAt, duration, size',
    filter: { folder: { $ne: '/' } },
    sort: '-updatedAt',
}

const { data } = useFileSearch('folder', userId, projectId, initialQuery)

const groupedData = computed(() => _.groupBy(data.value, 'folder'))

const navigateToFolder = (folderName: string) => {
    router.push(`/folder-management${folderName}`)
}
</script>

<template>
    <div class="p-6">
        <!-- Header -->
        <div class="mb-4 rounded-lg p-4">
            <div class="mb-2 text-xl font-bold text-primary-600">
                Sổ Khám Sức Khỏe Điện Tử
            </div>
            <p class="text-gray-600">
                Quản lý hồ sơ sức khỏe của bạn một cách dễ dàng và hiệu quả
            </p>
        </div>

        <!-- Folder and Files -->
        <div v-for="(files, folder) in groupedData" :key="folder" class="mb-4">
            <!-- Folder -->
            <div
                class="mb-4 flex cursor-pointer items-center hover:text-primary"
                @click="navigateToFolder(folder)"
            >
                <div class="mr-2 flex items-center justify-center rounded-md">
                    <iconify-icon
                        icon="flat-color-icons:folder"
                        class="text-3xl"
                    />
                </div>
                <span class="text-xl font-medium">{{
                    folder.replace(/^\/|\/$/g, '')
                }}</span>
            </div>

            <!-- Files -->
            <!-- <ul class="ml-14 space-y-2">
                <li
                    v-for="file in files"
                    :key="file.name"
                    class="flex items-center text-gray-700"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        class="mr-2 h-5 w-5 text-gray-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6z"
                        />
                    </svg>
                    {{ file.name }}
                </li>
            </ul> -->
        </div>
        <WFileDrive :user="user?._id" :project="project" />
    </div>
</template>
