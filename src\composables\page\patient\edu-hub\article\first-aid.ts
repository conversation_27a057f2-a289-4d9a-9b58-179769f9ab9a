import type { HitNotionWebsite, IArticleFirstAidCard } from '~/models'
import { ElasticIndex } from '~/models'
import type { ComputedRef, Ref } from '#imports'
import { useFetchElastic } from '#imports'
import { useRuntimeConfig } from '#build/imports'

export function usePagePatientEduHubArticleFirstAid(
    from: Ref<number> | ComputedRef<number>,
    size: Ref<number> | ComputedRef<number>,
) {
    const runtimeConfig: any = useRuntimeConfig()

    const { page } = runtimeConfig.public
    const status = page.status.split(',')

    const { hits, loading, total } = useFetchElastic<HitNotionWebsite>(
        ElasticIndex.NOTION_WEBSITE,
        {
            from,
            size,
            filters: [
                {
                    term: {
                        'page.properties.Parent item.id.keyword':
                            '4a2c35d9-4a0a-4299-a714-d84de765adbf', // <PERSON><PERSON> cứu
                    },
                },
                {
                    terms: {
                        'page.properties.Status.keyword': status,
                    },
                },
            ],
            sort: [{ 'page.properties.Order': 'asc' }],
        },
    )

    const articles: ComputedRef<IArticleFirstAidCard[]> = computed(() => {
        return hits.value.map((hit: HitNotionWebsite) => ({
            _id: hit._id || '',
            name: hit.page.properties.Name || '',
            slug: hit.page.properties.Slug || '',
            cover: hit.page.cover?.url || '',
            content: hit?.html || '',
            updatedAt: hit.page.last_edited_time || '',
        }))
    })

    return { articles, loading, total }
}
