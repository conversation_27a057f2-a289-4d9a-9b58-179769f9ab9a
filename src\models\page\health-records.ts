export interface IVaccination {
  name: string
  doses: number
  requiredDoses: number
  scheduledDoses: { doseNumber: number; date: string }[]
  lastDoseDate: string
}

export interface IVitalSign {
  label: string
  icon: string
  value: string
  lastUpdated: string
  unit: string
  bgColor: string
  iconColor: string
}

export interface ILabTest {
  files: string[]
  date: string
}

export type PrescriptionRoute = 'Oral' | 'Injection' | 'Other'
export interface IPrescription {
  route: PrescriptionRoute
  name: string
  description: string
}
export interface IMedication {
  date: string
  label?: string
  files: { url: string }[]
}

export interface IMedicalHistory {
  condition: string
  diagnosisDate: string
  treatment: string
  doctor: string
  clinic: string
  notes: string
}
export interface IHabitsData {
  lastUpdated: string
  exercise: string
  exerciseFrequency: string
  tobacco: string
  alcohol: string
  streetDrugs: string
  caffeine: string
  troubleSleeping: boolean
  eatingHabits: string
  eatOutMoreThanTwiceAWeek: boolean
}

export type Severity = 'Mild' | 'Moderate' | 'Severe'
export interface IAllergy {
  name: string // Name of the allergy (e.g., Pollen, Nuts)
  severity: Severity // Severity level (e.g., mild, moderate, severe)
  lastReaction: string // Date or description of the last allergic reaction
  medications: string[] // Medications taken for this allergy
  hasEpiPen: boolean // Whether the patient has an EpiPen for this allergy
}
