export interface IDrug {
    _id?: string;
    name?: string;
    slug?: string;
    state?: string;
    unit?: string;
}

export interface IMedicalReferences {
    _id?: string;
    slug?: string;
    name: {
        vi: string;
        en: string
    }
}
// export interface IPrescription {
//     _id?: string;
//     drug?: IDrug;
//     route?: IMedicalReferences;
//     take?: number;
//     unit?: string;
//     duration?: number;
//     frequency?: IMedicalReferences;
//     note?: string;
//     followDirection?: boolean;
// }
