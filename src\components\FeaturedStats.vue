<script lang="ts" setup>
import { onMounted } from 'vue'

interface Stat {
  icon: string
  value: string
  label: string
  color: string
}

const stats: Stat[] = [
  {
    icon: 'heroicons:user-group-20-solid',
    value: '150K+',
    label: 'Người dùng',
    color: 'blue',
  },
  {
    icon: 'ph:phone-call-fill',
    value: '930K+',
    label: 'Phút tư vấn',
    color: 'green',
  },
  {
    icon: 'solar:calendar-bold',
    value: '31K+',
    label: 'Ca khám',
    color: 'orange',
  },
  {
    icon: 'lets-icons:star-fill',
    value: '99.6%',
    label: 'Rating 5★',
    color: 'yellow',
  },
]

const getColorClasses = (color: string) => ([
  {
    blue: { bg: 'bg-blue-100', text: 'text-blue-500', border: 'border-blue-500' },
    green: { bg: 'bg-green-100', text: 'text-green-500', border: 'border-green-500' },
    orange: { bg: 'bg-orange-100', text: 'text-orange-500', border: 'border-orange-500' },
    yellow: { bg: 'bg-yellow-100/50', text: 'text-yellow-400', border: 'border-yellow-500' },
  }[color] || { bg: 'bg-gray-100', text: 'text-gray-500' },
])[0]

onMounted(() => {
  if (window.gsap) {
    const gsap = window.gsap
    const ScrollTrigger = window.ScrollTrigger

    gsap.registerPlugin(ScrollTrigger)

    gsap.from('.stats-card', {
      opacity: 0,
      y: 50,
      duration: 1,
      stagger: 0.3,
      ease: 'power2.out',
      scrollTrigger: {
        trigger: '.stats-grid',
        start: 'top 90%',
        end: 'top 30%',
        scrub: true,
      },
    })
  }
  else {
    console.error('GSAP chưa được tải từ CDN.')
  }
})
</script>

<template>
  <div class="container mx-auto px-12 py-16 mt-4 rounded-xl">
    <div class="text-center mb-12">
      <WSparkleText
        text="Thống kê nổi bật"
        :colors="{ first: '#9E7AFF', second: '#c1ff71' }"
        class="text-center inline"
      />
    </div>

    <div class="stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 sm:gap-12">
      <div
        v-for="(stat, index) in stats"
        :key="index"
        class="stats-card p-6 rounded-2xl flex flex-col items-center text-center hover:border transition-shadow duration-200 border border-transparent [background:linear-gradient(theme(colors.white),theme(colors.zinc.50))_padding-box,linear-gradient(120deg,theme(colors.zinc.200),theme(colors.zinc.100),theme(colors.zinc.200))_border-box]"
      >
        <div
          :class="[getColorClasses(stat.color).bg, getColorClasses(stat.color).border, 'border w-12 h-12 rounded-xl flex items-center justify-center mb-4']"
        >
          <div :class="getColorClasses(stat.color).text">
            <iconify-icon
              class="text-2xl"
              :icon="stat.icon"
            />
          </div>
        </div>
        <div class="mt-4 text-5xl font-bold text-gray-700">
          {{ stat.value }}
        </div>
        <p class="text-md mt-3">
          {{ stat.label }}
        </p>
      </div>
    </div>
  </div>
</template>
