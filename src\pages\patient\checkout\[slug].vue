<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import {
    ref,
    definePageMeta,
    computed,
    onMounted,
    onUnmounted,
    useI18n,
    onBeforeMount,
    useRoute,
    useRouter,
    useAppAuth,
    useLayoutStore,
    useUserInfo,
    useRuntimeConfig,
} from '#imports'
import { InAppBrowser } from '@capgo/inappbrowser'
import {
    DOMAIN_CHECKOUT_PRODUCTION,
    DOMAIN_CHECKOUT_SANDBOX,
    appRoutes,
} from '~/constants'
import PhoneVerification from '~/components/Shared/Dialog/PhoneVerification.vue'

definePageMeta({
    layout: 'checkout',
    middleware: 'auth-capacitor',
})

const layoutStore = useLayoutStore()
const runtimeConfig: any = useRuntimeConfig()
const route = useRoute()
const router = useRouter()
const { t } = useI18n()
const { getAccount, refreshTokenOidc } = useAppAuth()
const healthRecordStore = useHealthRecordStore()
const { user, refresh } = useUserInfo({ scope: ['phone', '_id'] })

const paymentCompleted = ref<boolean>(false)
const orderId = ref<string>('')
const idp = ref<string>('internal-checkout-wellcare-vn-oidc')
const isLoading = ref<boolean>(false)
const showPhoneVerification = ref(false)

const isNative = Capacitor.isNativePlatform()

const domainCheckout = computed(() => {
    const appEnv = runtimeConfig.public?.app?.env || 'sandbox'
    return appEnv === 'production'
        ? DOMAIN_CHECKOUT_PRODUCTION
        : DOMAIN_CHECKOUT_SANDBOX
})

const isWebviewOpen = ref(false)

const openCheckout = async () => {
    try {
        isLoading.value = true
        const account = await getAccount()

        if (
            !user.value?.phone &&
            !(route.query?.bypass as string)?.split(',')?.includes('phone')
        ) {
            showPhoneVerification.value = true
            return
        }

        const refreshToken = account?.refresh_token
        if (!refreshToken) {
            console.error('Refresh token is not available')
            return
        }

        const slug = route.params.slug as string
        const redirectUrl = btoa(`${window.location.origin}`)

        const searchParams = new URLSearchParams({
            slug,
            refresh_token: refreshToken,
            redirect_url: redirectUrl,
            idp: idp.value,
        })

        Object.entries(route.query).forEach(([key, value]) => {
            if (value) {
                if (Array.isArray(value)) {
                    value.forEach((v) => v && searchParams.append(key, v))
                } else {
                    searchParams.append(key, value)
                }
            }
        })

        const url = `${domainCheckout.value}/checkout/sso-internal?${searchParams.toString()}`
        if (isNative) {
            await InAppBrowser.open({
                url,
                // title: t('Service'),
            })
            isWebviewOpen.value = true
        } else {
            window.open(url as unknown as string, '_blank')
            isWebviewOpen.value = true
        }
    } catch (error) {
        console.error('Error opening checkout:', error)
    } finally {
        isLoading.value = false
    }
}

const handleNavigationEvent = async (event: any) => {
    if (event.url.includes('/membership/')) {
        if (event.url.includes('/health-programs/baby-development')) {
            router.push(
                appRoutes.patient.healthRecords.membership?.healthProgram
                    ?.babyDevelopment,
            )
        }
        if (event.url.includes('/health-programs/pregnancy-diary')) {
            router.push(
                appRoutes.patient.healthRecords.membership?.healthProgram
                    ?.pregnancyDiary,
            )
        }
    } else if (event.url.includes('/payment/order')) {
        const url = new URL(event.url)
        const pathSegments = url.pathname.split('/')
        orderId.value = pathSegments[pathSegments.indexOf('order') + 1]

        if (event.url.includes('/success')) {
            paymentCompleted.value = true
            await refresh()

            if (isNative) {
                InAppBrowser.close()
            }

            setTimeout(() => {
                console.log(`Order ${orderId.value} completed successfully!`)
            }, 1000)
        }
    }
}

const handleBrowserClose = async () => {
    if (!paymentCompleted.value && isWebviewOpen.value) {
        await refresh()
        isWebviewOpen.value = false
        router.back()
    }
}

const goToHome = () => {
    router.push(appRoutes.patient.healthRecords.index)
}

const handleVerificationComplete = async () => {
    healthRecordStore.$reset()
    await refreshTokenOidc()
    await refresh()
    openCheckout()
}

onBeforeMount(() => {
    const unwatch = router.beforeEach(() => {
        if (isWebviewOpen.value && !paymentCompleted.value) {
            isWebviewOpen.value = false
            return false
        }
        return true
    })

    onUnmounted(() => {
        unwatch()
    })
})

const handleClose = () => {
    router.back()
}

onBeforeMount(async () => {
    await openCheckout()
    if (isNative) {
        await InAppBrowser.addListener('urlChangeEvent', handleNavigationEvent)
        await InAppBrowser.addListener('closeEvent', handleBrowserClose)
    } else {
        // Handle web browser
        window.addEventListener('message', (event) => {
            if (event.data.type === 'PAYMENT_COMPLETED') {
                paymentCompleted.value = true
                orderId.value = event.data.orderId
                handleNavigationEvent({
                    url: `/payment/order/${orderId.value}/success`,
                })
            }
        })
        // Check if the browser is closed
        const checkInterval = setInterval(() => {
            if (isWebviewOpen.value && !paymentCompleted.value) {
                handleBrowserClose()
            }
        }, 1000)

        onUnmounted(() => {
            clearInterval(checkInterval)
        })
    }
})

onMounted(() => layoutStore.setTitle(t('Service')))

onUnmounted(() => {
    if (isNative) {
        InAppBrowser.removeAllListeners()
    } else {
        window.removeEventListener('message', () => {})
    }
})
</script>

<template>
    <div
        class="flex h-full items-center justify-center bg-gradient-to-br from-blue-50 to-green-50 p-4"
    >
        <!-- Background decorative elements -->
        <div class="absolute inset-0 top-16 overflow-hidden">
            <div
                class="absolute -right-20 -top-20 h-40 w-40 rounded-full bg-blue-100 opacity-20 blur-3xl md:-right-40 md:-top-40 md:h-80 md:w-80"
            ></div>
            <div
                class="absolute -bottom-20 -left-20 h-40 w-40 rounded-full bg-green-100 opacity-20 blur-3xl md:-bottom-40 md:-left-40 md:h-80 md:w-80"
            ></div>
        </div>

        <div class="relative z-10 w-full max-w-md text-center">
            <div v-if="!paymentCompleted" class="space-y-4">
                <!-- Title -->
                <h2 class="text-2xl font-bold text-gray-800 md:text-3xl">
                    {{ t('checkout-title') }}
                </h2>

                <!-- Subtitle -->
                <p class="text-sm text-gray-600 md:text-base">
                    {{ t('checkout-subtitle') }}
                </p>

                <!-- Reopen Checkout Button -->
                <Button
                    class="w-full"
                    :loading="isLoading"
                    @click="openCheckout"
                >
                    {{ t('reopen-checkout') }}
                </Button>
            </div>

            <div v-else class="w-full md:p-8">
                <!-- Success Icon -->
                <div class="relative mb-6">
                    <div
                        class="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-br from-primary-400 to-primary-600 shadow-lg"
                    >
                        <i class="pi pi-check text-4xl text-white"></i>
                    </div>
                </div>

                <!-- Success Message -->
                <h2
                    class="mb-4 bg-clip-text text-3xl font-bold text-primary text-transparent"
                >
                    {{ t('payment-successful') }}
                </h2>

                <!-- Success Subtitle -->
                <p class="mb-6 text-gray-600">
                    {{ t('payment-success-subtitle') }}
                </p>

                <!-- Action Button -->
                <Button class="mb-4 w-full" @click="goToHome">
                    {{ t('go-to-Home') }}
                </Button>

                <!-- Order ID -->
                <div
                    class="flex items-center justify-center space-x-2 text-sm text-gray-500"
                >
                    <span>{{ t('order-id') }}:</span>
                    <span class="font-mono font-medium">{{ orderId }}</span>
                </div>
            </div>
        </div>
    </div>

    <PhoneVerification
        v-model:visible="showPhoneVerification"
        :user-id="user._id"
        @verification-complete="handleVerificationComplete"
        @close="handleClose"
    />
</template>

<i18n lang="json">
{
    "en": {
        "Service": "Wellcare Service",
        "checkout-title": "Continue Payment",
        "checkout-subtitle": "The checkout window was closed. Click below to reopen and complete your payment.",
        "reopen-checkout": "Reopen Checkout",
        "payment-successful": "Payment Successful",
        "payment-success-subtitle": "Your appointment has been confirmed. Please return to the health records page to view your medical record.",
        "go-to-Home": "View Health Records",
        "order-id": "Order ID"
    },
    "vi": {
        "Service": "Dịch vụ Wellcare",
        "checkout-title": "Tiếp Tục Thanh Toán",
        "checkout-subtitle": "Cửa sổ thanh toán đã đóng. Vui lòng nhấn nút bên dưới để mở lại và hoàn tất thanh toán.",
        "reopen-checkout": "Mở Lại Thanh Toán",
        "payment-successful": "Thanh Toán Thành Công",
        "payment-success-subtitle": "Đặt hẹn của bạn đã được xác nhận. Vui lòng quay về trang hồ sơ sức khoẻ để xem bệnh án.",
        "go-to-Home": "Hồ sơ sức khoẻ",
        "order-id": "Mã đơn"
    }
}
</i18n>
