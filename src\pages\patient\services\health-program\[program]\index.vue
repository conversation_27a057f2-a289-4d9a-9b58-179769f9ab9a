<script lang="ts" setup>

import { appRoutes } from '~/constants';
definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const { params }: any = useRoute()
const { t } = useI18n()
const layoutStore = useLayoutStore()

const asyncComponents = {
    pregnancyDiary: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/Services/HealthProgram/PregnancyDiary/index.vue'
            ),
    ),
    babyDevelopment: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/Services/HealthProgram/BabyDevelopment/index.vue'
            ),
    ), // Add your component imports here
}

const computedComponent = computed(() => {
    switch (params.program) {
        case 'pregnancy-diary':
            return asyncComponents.pregnancyDiary
        case 'baby-development':
            return asyncComponents.babyDevelopment
        default:
            return null
    }
})

onMounted(() => {
    const programs: Record<string, any> = {
        'pregnancy-diary': {
            title: t('title:1'),
        },
        'baby-development': {
            title: t('title:2'),
        },
    }

    layoutStore.setTitle(programs[params?.program]?.title)
    layoutStore.setTarget(appRoutes.patient.services.index)
})

onUnmounted(() => {
    layoutStore.$reset() // Reset store on unmounting the component
})
</script>

<template>
    <component :is="computedComponent" />
</template>

<i18n lang="json">
{
    "en": {
        "title:1": "Pregnancy Package",
        "title:2": "Raise them right"
    },
    "vi": {
        "title:1": "Thai kỳ khoẻ mạnh",
        "title:2": "Cùng con khôn lớn"
    }
}
</i18n>
