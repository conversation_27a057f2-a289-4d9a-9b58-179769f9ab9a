import { ref } from '#imports'
import {
    CapacitorPluginPermissions,
    type PermissionType,
    type PermissionsStatus,
} from '@wellcare/capacitor-permissions'

const _p = ref<PermissionsStatus>({})

export function usePermission() {
    const checkPermissions = async (permissions: PermissionType[]) => {
        for (const permission of permissions) {
            if (!CapacitorPluginPermissions?.checkPermission) continue

            const status = await CapacitorPluginPermissions.checkPermission({
                permission: permission,
            })
            console.log(
                '[debug] permission status result ' +
                    permission +
                    ' - ' +
                    status[permission],
            )
            _p.value = { ..._p.value, ...status }
        }
        return _p.value
    }

    const requestPermissions = async (permissions: PermissionType[]) => {
        for (const permission of permissions) {
            if (!CapacitorPluginPermissions?.requestPermission) continue

            const status = await CapacitorPluginPermissions.requestPermission({
                permission: permission,
            })
            _p.value = { ..._p.value, ...status }
        }
        return _p.value
    }

    const request = async (permissions: PermissionType | PermissionType[]) => {
        const _permissions = Array.isArray(permissions)
            ? permissions
            : [permissions]
        return checkPermissions(_permissions).then((status) => {
            for (const _permission of _permissions) {
                if (
                    status[_permission] === 'prompt' ||
                    status[_permission] === 'prompt-with-rationale'
                ) {
                    return requestPermissions(_permissions)
                }
            }
            return status
        })
    }

    const check = async (permissions: PermissionType | PermissionType[]) => {
        const _permissions = Array.isArray(permissions)
            ? permissions
            : [permissions]
        return checkPermissions(_permissions)
    }

    return {
        permissions: _p,
        check,
        request,
    }
}
