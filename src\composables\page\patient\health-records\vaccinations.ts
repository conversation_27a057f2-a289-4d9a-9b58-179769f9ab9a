import type { IVaccination } from '~/models/page/health-records'

/**
 * Returns a reactive reference to an array of vaccinations, including the
 * name, number of doses received, the required number of doses, the date of
 * the last dose, and an array of scheduled doses.
 *
 * @returns {Ref<IVaccination[]>} A reactive reference to an array of
 * vaccinations.
 */

export function useVaccinations() {
  const vaccinations: Ref<IVaccination[]> = ref([
    {
      name: 'COVID-19 Vaccine',
      doses: 2, // Updated doses based on lastDoseDate
      requiredDoses: 2,
      lastDoseDate: '2023-06-15',
      scheduledDoses: [
        {
          doseNumber: 3,
          date: '2024-06-15', // A scheduled booster dose
        },
      ],
    },
    {
      name: 'Influenza Vaccine',
      doses: 1,
      requiredDoses: 1,
      lastDoseDate: '2023-10-01',
      scheduledDoses: [],
    },
    {
      name: 'Hepatitis B Vaccine',
      doses: 3,
      requiredDoses: 3,
      lastDoseDate: '2023-08-23',
      scheduledDoses: [
        {
          doseNumber: 4,
          date: '2024-02-15',
        },
      ],
    },
    {
      name: 'Tdap Vaccine',
      doses: 1,
      requiredDoses: 1,
      lastDoseDate: '2022-12-10',
      scheduledDoses: [],
    },
    {
      name: 'MMR Vaccine',
      doses: 2,
      requiredDoses: 2,
      lastDoseDate: '2021-05-20',
      scheduledDoses: [],
    },
  ])

  return { vaccinations }
}
