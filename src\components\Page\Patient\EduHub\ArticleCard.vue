<script setup lang="ts">
import type { IArticleFirstAidCard } from '~/models'

const props = defineProps({
    article: {
        type: Object as PropType<IArticleFirstAidCard>,
        required: true,
    },
    isMembership: {
        type: Boolean,
        default: false,
    },
})

// const dayjs = useDayjs()

const router = useRouter()
const visibleMembershipPopup = ref<boolean>(false)

const goToArticle = (slug: string) => {
    if (!props.isMembership) {
        visibleMembershipPopup.value = true
    } else {
        router.push(`/patient/edu-hub/article/${slug}`)
    }
}
</script>

<template>
    <div class="flex w-40 flex-col">
        <div
            class="link text-lg font-semibold"
            @click="goToArticle(article.slug)"
        >
            <NuxtImg
                alt="Thumbnail"
                class="h-40 w-full rounded-2xl bg-surface-200 object-cover"
                sizes="160px"
                :src="article.cover"
            />
        </div>

        <div class="py-2">
            <!-- <p class="mb-1 text-xs text-surface-500 dark:text-surface-100">
                {{ dayjs(article.updatedAt).format('DD/MM/YYYY') }}
            </p> -->
            <NuxtLinkLocale :to="`/patient/edu-hub/article/${article.slug}`">
                <p class="font-bold">{{ article.name }}</p>
            </NuxtLinkLocale>
        </div>

        <SharedMembershipPopup
            v-model:visible="visibleMembershipPopup"
            @skip="visibleMembershipPopup = false"
            @update:visible="(val: boolean) => (visibleMembershipPopup = val)"
        />
    </div>
</template>
