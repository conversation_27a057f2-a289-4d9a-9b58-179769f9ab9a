<script setup lang="ts">
import type { IMedicalHistory } from '~/models/page/health-records'

interface Props {
    medicalHistory: IMedicalHistory
    detailed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    detailed: false,
})

const medicalHistoryMapped = computed(() => ({
    date: props.medicalHistory.diagnosisDate,
    datas: [
        {
            label: 'Notes',
            data: props.medicalHistory.notes,
            icon: 'material-symbols:notes-rounded',
        },
        {
            label: 'Clinic',
            data: props.medicalHistory.clinic,
            icon: 'material-symbols:location-on-rounded',
        },
        {
            label: 'Doctor',
            data: props.medicalHistory.doctor,
            icon: 'material-symbols:stethoscope-rounded',
        },
    ],
}))
</script>

<template>
    <div
        v-if="!detailed"
        class="mb-4 rounded-xl bg-surface-50 dark:bg-surface-900"
    >
        <p class="text-sm">
            {{ medicalHistory.notes }}
        </p>
    </div>
    <div
        v-else
        class="-mt-1 mb-6 rounded-xl bg-surface-50 p-2 dark:bg-surface-900"
    >
        <div class="flex flex-row items-center justify-between">
            <p class="text-lg font-bold">{{ medicalHistory.diagnosisDate }}</p>
            <Button text plain icon="pi pi-pencil" size="small" />
        </div>

        <template
            v-for="(section, index) in medicalHistoryMapped.datas"
            :key="section.label"
        >
            <div
                class="flex flex-row items-center gap-2 text-slate-500 dark:text-slate-400"
            >
                <iconify-icon :icon="section.icon" />
                <p class="font-bold">{{ section.label }}</p>
            </div>
            <p class="text-lg">{{ section.data }}</p>
            <Divider
                v-if="index < medicalHistoryMapped.datas.length - 1"
                class="!my-2"
            />
        </template>

        <!--
    <p class="text-base font-bold">{{ medicalHistory.diagnosisDate }}</p>
    <Divider
      align="left"
      class="!my-2"
      :pt="{
        content:
          'flex flex-row px-2 z-10 items-center gap-1 bg-white rounded -ml-3 text-lg',
      }"
    >
      <Icon name="material-symbols:notes-rounded" />
      <p class="font-semibold">Notes</p>
    </Divider>
 <p class="mx-2">{{ medicalHistory.notes }}</p>
    <Divider
      align="left"
      class="!my-2"
      :pt="{
        content:
          'flex flex-row px-2 z-10 items-center gap-1 bg-white rounded -ml-3 text-lg',
      }"
    >
      <p class="font-semibold">Clinic</p>
    </Divider>
    <p class="mx-2">{{ medicalHistory.clinic }}</p>
    <Divider
      align="left"
      class="!my-2"
      :pt="{
        content:
          'flex flex-row px-2 z-10 items-center gap-1 bg-white rounded -ml-3 text-lg',
      }"
    >
      <p class="font-semibold">Doctor</p>
    </Divider>
    <p class="mx-2">{{ medicalHistory.doctor }}</p> -->
    </div>
</template>
