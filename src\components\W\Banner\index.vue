<script setup lang="ts">
import { type PropType, useBanner } from '#imports'

// declare components
import type { BannerPosition } from '~/types'
import Bottom from './Position/Bottom.vue'
import Left from './Position/Left.vue'
import Popup from './Position/Popup.vue'
import Right from './Position/Right.vue'
import Top from './Position/Top.vue'
// import type { BannerPosition, HitNotionBanners } from '~/models'
const { position } = defineProps({
  position: {
    type: String as PropType<BannerPosition>,
    required: true,
  },
  // size: {
  //   type: Number,
  //   default: 1,
  // },
})
const list = {
  popup: Popup,
  bottom: Bottom,
  top: Top,
  left: Left,
  right: Right,
}
const { banner, click, impress, banners } = useBanner({ position, size: 1 })
</script>

<template>
  <div>
    <component
      :is="list[position]"
      :banner="banner"
      :banners="banners"
      v-bind="$attrs"
      @click="click"
      @impress="impress"
    />
  </div>
</template>
