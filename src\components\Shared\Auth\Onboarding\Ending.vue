<script setup lang="ts">
const { t } = useI18n()
</script>

<template>
    <div class="flex min-h-[60vh] items-center justify-center px-4">
        <div class="flex flex-col items-center justify-center">
            <!-- Success checkmark animation -->
            <div
                class="mx-auto mb-6 flex h-20 w-20 items-center justify-center rounded-full bg-primary-100"
            >
                <iconify-icon
                    icon="icon-park-outline:success"
                    class="text-6xl text-primary-600"
                />
            </div>

            <h1 class="mb-4 text-2xl font-bold text-primary-600 sm:text-3xl">
                {{ t('onboarding.ending.title') }}!
            </h1>

            <p class="mb-8 text-center text-lg text-surface-600">
                {{ t('onboarding.ending.description') }}
            </p>
        </div>
    </div>
</template>

<style scoped>
@keyframes scale {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
</style>
