<template>
    <div class="w-full">
        <h1 class="text-4xl font-bold lg:text-6xl">
            🔥 Welcome to <span class="text-teal-500">wellcare</span> app
        </h1>
        <div class="my-4 h-2 w-20 bg-teal-500"/>
        <p class="mb-10 text-xl">
            Lorem ipsum dolor sit amet consectetur adipisicing elit. Recusandae
            maiores neque eaque ea odit placeat, tenetur illum distinctio nulla
            voluptatum a corrupti beatae tempora aperiam quia id aliquam
            possimus aut.
        </p>
    </div>
</template>

<script setup lang="ts">
const submit = () => {
    return true
}

defineExpose({
    submit,
})
</script>

<style scoped>
/* Typing text fade-in and slide-in animation */
h1 {
    opacity: 0;
    animation: fadeInSlideIn 1s ease-in-out forwards;
}

/* Custom typing effect */
@keyframes fadeInSlideIn {
    from {
        opacity: 0;
        transform: translateX(-30px); /* Slide from left */
    }
    to {
        opacity: 1;
        transform: translateX(0); /* Stop at the final position */
    }
}

/* Add elegant shadow to text for better contrast */
h1 {
    text-shadow: 2px 4px 6px rgba(0, 0, 0, 0.2);
}
</style>
