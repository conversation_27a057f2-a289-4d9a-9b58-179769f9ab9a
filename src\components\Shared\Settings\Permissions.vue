<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import OneSignal from 'onesignal-cordova-plugin'
import type { PermissionState } from '@capacitor/core'
import type { PermissionType } from '@wellcare/capacitor-permissions'
import {
    AndroidSettings,
    IOSSettings,
    NativeSettings,
} from 'capacitor-native-settings'
import { ref } from '#build/imports'

interface Props {
    requiredPermissions?: PermissionType[]
}

const props = withDefaults(defineProps<Props>(), {
    requiredPermissions: () => [],
})

const emit = defineEmits<{
    'update:permissions': [granted: boolean]
}>()

const { t } = useI18n()
const { permissions, request, check } = usePermission()

// const POLLING_INTERVAL = 3000 // 3 seconds
// const MAX_RETRIES = 20 // Maximum number of retries (1 minute total)

const platform = Capacitor.getPlatform()
const isNative = Capacitor.isNativePlatform()

const defaultPermissions: PermissionType[] = [
    'camera',
    'microphone',
    'notifications',
]
const permissionsRequire = computed(() => {
    if (props.requiredPermissions.length > 0) {
        return props.requiredPermissions
    }
    return isNative ? defaultPermissions : []
})

// const isRequesting = ref(false)
const retryCount = ref(0)
const pollingTimeout = ref<NodeJS.Timeout | null>(null)

const deniedPermissions = ref<Partial<Record<PermissionType, boolean>>>({
    camera: false,
    microphone: false,
    notifications: false,
})

const settings = {
    ios: {
        camera: IOSSettings.App,
        photos: IOSSettings.Photos,
        microphone: IOSSettings.App,
        geolocation: IOSSettings.LocationServices,
        notifications: IOSSettings.Notifications,
        'clipboard-read': IOSSettings.App,
        'clipboard-write': IOSSettings.App,
        contacts: IOSSettings.App,
        calendar: IOSSettings.App,
        location: IOSSettings.LocationServices,
        phone: IOSSettings.Phone,
        sensors: IOSSettings.App,
        sms: IOSSettings.App,
        storage: IOSSettings.Store,
    },
    android: {
        camera: AndroidSettings.ApplicationDetails,
        photos: AndroidSettings.ApplicationDetails,
        microphone: AndroidSettings.ApplicationDetails,
        geolocation: AndroidSettings.Location,
        notifications: AndroidSettings.AppNotification,
        'clipboard-read': AndroidSettings.ApplicationDetails,
        'clipboard-write': AndroidSettings.ApplicationDetails,
        contacts: AndroidSettings.ApplicationDetails,
        calendar: AndroidSettings.ApplicationDetails,
        location: AndroidSettings.Location,
        phone: AndroidSettings.ApplicationDetails,
        sensors: AndroidSettings.ApplicationDetails,
        sms: AndroidSettings.ApplicationDetails,
        storage: AndroidSettings.Storage,
    },
    web: {
        camera: 'chrome://settings/content/camera',
        photos: 'chrome://settings/content/images',
        microphone: 'chrome://settings/content/microphone',
        geolocation: 'chrome://settings/content/location',
        notifications: 'chrome://settings/content/notifications',
        'clipboard-read': 'chrome://settings/content/clipboard',
        'clipboard-write': 'chrome://settings/content/clipboard',
        contacts: 'chrome://settings/content/siteDetails',
        calendar: 'chrome://settings/content/siteDetails',
        location: 'chrome://settings/content/location',
        phone: 'chrome://settings/content/siteDetails',
        sensors: 'chrome://settings/content/sensors',
        sms: 'chrome://settings/content/siteDetails',
        storage: 'chrome://settings/content/siteDetails',
    },
}

const hasUngrantedPermissions = computed(() =>
    permissionsRequire.value.some(
        (permission) => permissions.value[permission] !== 'granted',
    ),
)

const getButtonLabel = computed(
    () => (permissionType: PermissionType) =>
        permissionType.charAt(0).toUpperCase() + permissionType.slice(1),
)

const permissionStates = ref<Partial<Record<PermissionType, PermissionState>>>(
    {},
)

const isPermissionGranted = computed(() => (permissionType: PermissionType) => {
    const currentState =
        permissionStates.value[permissionType] ||
        permissions.value[permissionType]
    return currentState === 'granted'
})

watch(hasUngrantedPermissions, (hasUngranted) => {
    emit('update:permissions', !hasUngranted)
})

// const startPolling = () => {
//     if (pollingTimeout.value) return

//     const checkPermissions = async () => {
//         if (retryCount.value >= MAX_RETRIES) {
//             stopPolling()
//             return
//         }

//         const currentPermissions = { ...permissions.value }
//         const hasUngranted = permissionsRequire.value.some(
//             (permission) => currentPermissions[permission] !== 'granted',
//         )

//         if (!hasUngranted) {
//             emit('update:permissions', true)
//             stopPolling()
//             return
//         }

//         retryCount.value++
//         pollingTimeout.value = setTimeout(checkPermissions, POLLING_INTERVAL)
//     }
//     checkPermissions()
// }

const stopPolling = () => {
    if (pollingTimeout.value) {
        clearTimeout(pollingTimeout.value)
        pollingTimeout.value = null
    }
    retryCount.value = 0
}

// const currentPermissionIndex = ref(0)

const resetPermissionStates = () => {
    permissionStates.value = {}
    permissionsRequire.value.forEach((permission) => {
        permissionStates.value[permission] = permissions.value[permission]
        deniedPermissions.value[permission] =
            permissions.value[permission] !== 'granted'
    })
}

// const requestAllPermissions = async () => {
//     if (permissionsRequire.value.length === 0) return

//     isRequesting.value = true
//     currentPermissionIndex.value = 0
//     resetPermissionStates()
//     await processNextPermission()
// }

// const processNextPermission = async () => {
//     if (currentPermissionIndex.value >= permissionsRequire.value.length) {
//         isRequesting.value = false
//         startPolling()
//         return
//     }

//     const currentPermission =
//         permissionsRequire.value[currentPermissionIndex.value]

//     try {
//         await handlePermissionRequest(currentPermission)
//     } catch {
//         // Không cần xử lý lỗi ở đây vì đã xử lý trong handlePermissionRequest
//     } finally {
//         currentPermissionIndex.value++
//         setTimeout(() => {
//             processNextPermission()
//         }, 0)
//     }
// }

// const handlePermissionRequest = async (permissionType: PermissionType) => {
//     try {
//         const result = await check(permissionType)
//         const state = result[permissionType]
//         deniedPermissions.value[permissionType] = false
//         permissionStates.value[permissionType] = state

//         if (state !== 'granted') {
//             deniedPermissions.value[permissionType] = true
//             await request(permissionType)
//             // return Promise.reject('Permission denied')
//             return Promise.resolve()
//         }

//         return Promise.resolve()
//     } catch (error) {
//         deniedPermissions.value[permissionType] = true
//         permissionStates.value[permissionType] = 'denied'
//         return Promise.reject(error)
//     }
// }

const count = ref(0)

const openSetting = async (permissionType: PermissionType) => {
    console.log('[debug] open setting permission ' + permissionType)
    const res = await check(permissionType)
    const state = res[permissionType]
    // Exit early if the permission is already granted
    // Update permission states
    permissionStates.value[permissionType] = 'granted'
    deniedPermissions.value[permissionType] = false
    if (state === 'granted') return

    // iOS-specific logic
    if (platform === 'ios') {
        if (state === 'prompt') {
            if (permissionType === 'notifications')
                await OneSignal.Notifications.requestPermission(true)
            else await request(permissionType)
        } else if (state === 'denied') {
            NativeSettings.openIOS({ option: settings.ios[permissionType] })
        }
        return
    }

    // Android-specific logic
    if (platform === 'android') {
        if (permissionType === 'notifications') {
            // Handle notification permissions
            console.log('[debug] permission notification click ' + count.value)
            if (count.value === 0) {
                // await request(permissionType)
                count.value++
                await OneSignal.Notifications.requestPermission(true)
            } else {
                NativeSettings.openAndroid({
                    option: settings.android[permissionType],
                })
            }
        } else {
            // Handle other permissions
            if (state === 'prompt') {
                await request(permissionType)
            } else if (state === 'denied') {
                NativeSettings.openAndroid({
                    option: settings.android[permissionType],
                })
            }
        }
    }
    // const allGranted =
    //     deniedPermissions.value.camera === false &&
    //     deniedPermissions.value.microphone === false &&
    //     deniedPermissions.value.notifications === false
    // if (allGranted) emit('update:permissions', allGranted)
}

const showTeleconsultation = computed(() =>
    permissionsRequire.value.some((p) => ['camera', 'microphone'].includes(p)),
)

const showNotifications = computed(() =>
    permissionsRequire.value.includes('notifications'),
)

onMounted(() => {
    resetPermissionStates()
    // requestAllPermissions()
    // startPolling()
})

onUnmounted(() => {
    stopPolling()
})

// watch(
//     permissions,
//     (newPermissions: Partial<Record<PermissionType, PermissionState>>) => {
//         Object.entries(newPermissions).forEach(([key, value]) => {
//             const permKey = key as PermissionType
//             if (permissionsRequire.value.includes(permKey)) {
//                 permissionStates.value[permKey] = value
//                 deniedPermissions.value[permKey] = value === 'denied'
//             }
//         })
//     },
//     { deep: true },
// )
</script>

<template>
    <div>
        <div class="mb-4 grid place-items-center">
            <div
                class="mb-4 flex h-16 w-16 transform items-center justify-center rounded-full bg-primary-500 transition-transform duration-300 sm:h-20 sm:w-20"
            >
                <i class="pi pi-shield text-3xl text-white sm:text-4xl" />
            </div>
            <h1 class="mb-2 text-center text-2xl font-bold sm:text-3xl">
                {{ t('permissions.title') }}
            </h1>
            <p class="max-w-md text-center text-sm text-surface-600">
                {{ t('permissions.description') }}
            </p>
        </div>

        <div class="mb-4 grid">
            <!-- Notifications Section -->
            <div v-if="showNotifications" class="p-4 duration-300 sm:p-6">
                <h2
                    class="mb-3 flex items-center text-lg font-semibold sm:text-xl"
                >
                    <i class="pi pi-bell mr-2" />
                    {{ t('permissions.notification.title') }}
                </h2>
                <p class="mb-4 text-sm text-surface-600">
                    {{ t('permissions.notification.description') }}
                </p>
                <div class="grid grid-cols-2 gap-3 sm:grid-cols-2">
                    <div class="flex flex-col gap-2">
                        <Button
                            :label="getButtonLabel('notifications')"
                            icon="pi pi-bell"
                            :outlined="!isPermissionGranted('notifications')"
                            @click="openSetting('notifications')"
                        />
                        <!-- <div
                            v-if="deniedPermissions.notifications"
                            class="text-center text-sm text-red-500"
                        >
                            {{ t('permissions.denied') }}
                        </div> -->
                    </div>
                </div>
            </div>
            <!-- Teleconsultation Section -->
            <div
                v-if="showTeleconsultation"
                class="p-4 transition-shadow duration-300 sm:p-6"
            >
                <h2
                    class="mb-3 flex items-center text-lg font-semibold sm:text-xl"
                >
                    <i class="pi pi-video mr-2" />
                    {{ t('permissions.teleconsultation.title') }}
                </h2>
                <p class="mb-4 text-sm text-surface-600">
                    {{ t('permissions.teleconsultation.description') }}
                </p>
                <div class="grid grid-cols-2 gap-3 md:grid-cols-3">
                    <!-- Camera Permission -->
                    <div
                        v-if="permissionsRequire.includes('camera')"
                        class="flex flex-col gap-2"
                    >
                        <Button
                            :label="getButtonLabel('camera')"
                            icon="pi pi-camera"
                            :outlined="!isPermissionGranted('camera')"
                            @click="openSetting('camera')"
                        />
                        <!-- <div
                            v-if="deniedPermissions.camera"
                            class="text-center text-sm text-red-500"
                        >
                            {{ t('permissions.denied') }}
                        </div> -->
                    </div>

                    <!-- Microphone Permission -->
                    <div
                        v-if="permissionsRequire.includes('microphone')"
                        class="flex flex-col gap-2"
                    >
                        <Button
                            :label="getButtonLabel('microphone')"
                            icon="pi pi-microphone"
                            :outlined="!isPermissionGranted('microphone')"
                            @click="openSetting('microphone')"
                        />
                        <!-- <div
                            v-if="deniedPermissions.microphone"
                            class="text-center text-sm text-red-500"
                        >
                            {{ t('permissions.denied') }}
                        </div> -->
                    </div>
                </div>
            </div>

            <div class="px-4">
                <p class="text-sm text-surface-600">
                    {{ t('permissions.alert.description') }}
                </p>
            </div>
        </div>
    </div>
</template>
