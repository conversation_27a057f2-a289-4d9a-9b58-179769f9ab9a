import { ElasticIndex } from '~/models'
import type {
    HitNotionProviders,
    HitNotionWebsite,
    IProviderCard,
    IProviderFilter,
    IProviderTag,
} from '~/models'

export function usePageProviderSearch(
    quantity: {
        provider: {
            from: Ref<number>
            size: Ref<number>
        }
    },
    filter: Ref<IProviderFilter>,
    providerSlug: Ref<string> | ComputedRef<string>,
) {
    const dayjs = useDayjs()

    const specialtyFilters = computed(() => {
        const filters: any[] = [
            {
                prefix: {
                    'page.properties.Labels.keyword': 'specialty:',
                },
            },
        ]

        const { group } = filter.value
        if (group) {
            filters.push({
                term: {
                    'page.properties.Parent item.properties.Slug.keyword':
                        group,
                },
            })
        }
        return filters
    })

    const { hits: specialtyHits } =
        useFetchElasticWithDefault<HitNotionWebsite>(
            ElasticIndex.NOTION_WEBSITE,
            {
                size: 100,
                filters: specialtyFilters,
                _source: {
                    excludes: [],
                    includes: [
                        'page.properties.Name',
                        'page.properties.Slug',
                        'page.properties.Labels',
                    ],
                },
            },
        )

    const specialties = computed(() => {
        return specialtyHits.value?.map((specialty: any) => {
            const slug = specialty.page?.properties?.Slug
            const name = specialty.page?.properties?.Name

            return { key: slug, name }
        })
    })

    const validSpecialties = ref<string[]>([])
    const providerFilters = computed(() => {
        const { title, gender } = filter.value || {}

        const filters: any[] = []

        if (title) {
            const titleKeywords = (() => {
                switch (title) {
                    case 'Bs':
                        return ['Bs']
                    case 'Tlg':
                        return ['Tlg', 'Bs']
                    default:
                        return null
                }
            })()

            if (titleKeywords) {
                filters.push({
                    terms: {
                        'page.properties.Title.keyword': titleKeywords,
                    },
                })
            }
        }

        if (gender) {
            filters.push({
                term: {
                    'page.properties.Gender.keyword': gender,
                },
            })
        }

        if (validSpecialties.value.length) {
            filters.push({
                terms: {
                    'page.properties.Specialties.properties.Slug.keyword':
                        validSpecialties.value,
                },
            })
        }

        return filters
    })

    const providerMust = computed(() => {
        if (filter.value.keyword) {
            return [
                {
                    multi_match: {
                        fields: [
                            'page.properties.Name^2',
                            'page.properties.Specialties.properties.Name',
                        ],
                        query: filter.value.keyword,
                    },
                },
            ]
        }

        return []
    })

    const providerMustNot = computed(() => {
        const output: any[] = []

        if (providerSlug.value) {
            output.push({
                match_phrase: {
                    'page.properties.Slug.keyword': providerSlug.value,
                },
            })
        }

        return output
    })

    const providerHighlight = computed(() => {
        if (filter.value.keyword) {
            return {
                pre_tags: ['<mark>'],
                post_tags: ['</mark>'],
                fields: {
                    'page.properties.Name': {},
                    'page.properties.Specialties.properties.Name': {},
                },
            }
        }

        return {}
    })

    const providerSort = computed(() => {
        const rank = [{ 'page.properties.Rating': 'desc' }, '_score']

        if (filter.value.keyword) {
            return rank.reverse() as any
        }

        return rank
    })

    const {
        hits: providerHits,
        loading: providerLoading,
        total: providerTotal,
        execute: searchProviders,
    } = useFetchElasticWithDefault<HitNotionProviders>(ElasticIndex.PROVIDERS, {
        from: quantity.provider.from,
        size: quantity.provider.size,
        filters: providerFilters,
        must: providerMust,
        must_not: providerMustNot,
        highlight: providerHighlight,
        sort: providerSort,
    })

    const providers: ComputedRef<IProviderCard[]> = computed(() => {
        // Helper to retrieve matched specialties as a comma-separated string
        const getSpecialties = (specialties: any[]): string => {
            return (
                specialties
                    ?.map((specialty: any) => {
                        const slug = specialty?.properties?.Slug
                        const match = specialtyHits.value.find(
                            (hit: any) => hit.page?.properties?.Slug === slug,
                        )
                        return match?.page?.properties?.Name || null
                    })
                    .filter(Boolean)
                    .join(', ') || ''
            )
        }

        // Helper to calculate experience years
        const calculateExperience = (startDate?: string): string =>
            startDate ? `${dayjs().diff(dayjs(startDate), 'year')}` : '-'

        // Create a set of specialty slugs from `specialtyHits`
        const specialtySlugs = new Set(
            specialtyHits.value.map((hit) => hit.page?.properties?.Slug),
        )

        // Transform provider hits into the desired format
        return providerHits.value
            .filter((providerHit: HitNotionProviders) =>
                providerHit.page.properties.Specialties?.some(
                    (specialty: any) =>
                        specialtySlugs.has(specialty?.properties?.Slug),
                ),
            )
            .map((provider: any) => ({
                _id: provider._id,
                slug: provider.page.properties?.Slug || '',
                avatar: provider.page.properties?.Avatar?.url || '',
                name:
                    provider._highlight?.page?.properties?.Name?.[0] ||
                    provider.page.properties.Name,
                title: provider.page.properties?.Title || '',
                specialties:
                    provider._highlight?.page?.properties?.Specialties?.properties?.Name?.join(
                        ', ',
                    ) || getSpecialties(provider.page.properties.Specialties),
                metrics: [
                    {
                        name: 'experience',
                        icon: 'ri-medal-fill',
                        label: calculateExperience(
                            provider.page.properties?.StartWork?.start,
                        ),
                        unit: 'years',
                    },
                    {
                        name: 'average length',
                        icon: 'ri-time-fill',
                        label: provider.output?.meta?.minuteAvg
                            ? Math.round(provider.output.meta.minuteAvg)
                            : '-',
                        unit: 'mins',
                    },
                    {
                        name: 'satisfaction',
                        icon: 'ri-thumb-up-fill',
                        label: provider.output?.meta?.ratingGood
                            ? Math.round(provider.output.meta.ratingGood * 100)
                            : '-',
                        unit: '%',
                    },
                ],
            }))
    })

    watch(
        () => filter.value.specialties,
        async (newSpecialties) => {
            validSpecialties.value = newSpecialties?.length
                ? newSpecialties
                : specialties.value.map(
                      (specialty: IProviderTag) => specialty.key,
                  )

            await searchProviders()
        },
    )

    return { specialties, providers, providerLoading, providerTotal }
}
