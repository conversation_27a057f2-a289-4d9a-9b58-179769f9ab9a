import { $fetch } from '@nuxt/test-utils/e2e'
import { describe, expect, it } from 'vitest'
import { useHtml } from '~/tests/utils/html'
import { expectNoClientErrors } from '../../utils'
const urls = ['/dich-vu']

export const category = async () => {
  urls.forEach((url) => {
    describe(url, async () => {
      describe('layout', async () => {
        it('should have viewport configured for responsive design', async () => {
          const html = await $fetch(url)
          expect(html.match('meta name="viewport"').length).toEqual(1)
          await expectNoClientErrors('/')
        })
      })

      describe('multi-language', async () => {
        it('should contains at least 3 hreflang links', async () => {
          const html = await $fetch(url)
          const { hreflangs } = useHtml(html)
          expect(hreflangs.length).toBeGreaterThanOrEqual(3)
        })
      })

      describe('image', async () => {
        it('should have only CDN enabled sources', async () => {
          const html = await $fetch(url)
          const { imgSrcs } = useHtml(html)

          imgSrcs.forEach((url: string) => {
            expect(url).toMatch(/^\/images|^https:\/\/storage\.googleapis\.com/)
          })
        })
      })

      describe('title', () => {
        it('should be less than 60 characters', async () => {
          const html = await $fetch(url)
          const { title } = useHtml(html)
          expect(title.split('|')[0].length).toBeLessThanOrEqual(60)
        })
      })

      // describe('meta description', async () => {
      //   it('should be 50-160 characters', async () => {
      //     const html = await $fetch(url)
      //     const { metaDescription } = useHtml(html)
      //     expect(metaDescription.length).toBeLessThanOrEqual(160)
      //     expect(metaDescription.length).toBeGreaterThanOrEqual(50)
      //   })
      // })
      describe('headings', async () => {
        it('should have only 1 <h1> tag', async () => {
          const html = await $fetch(url)
          const { h1s } = useHtml(html)
          expect(h1s).toHaveLength(1)
        })
        it('should have h2-headings less than 60 characters', async () => {
          const html = await $fetch(url)
          const { h2s } = useHtml(html)
          h2s.forEach((tag: string) => {
            expect(tag?.length)?.toBeLessThanOrEqual(60)
          })
        })
      })
      describe('links', async () => {
        it('should have nofollow attributes for all external links', async () => {
          const html = await $fetch(url)
          const { links } = useHtml(html)
          links
            .filter((i) => !i.includes('khamtuxa.wellcare.vn'))
            .forEach((link: string) => {
              expect(link).toContain('rel="nofollow"')
            })
        })
        // it('should not have noindex for internal links', async () => {})
      })

      describe('pagination', async () => {
        it('should support page number and show it on title and description', async () => {
          const html = await $fetch(`${url}?page=2`)
          const { h1s, metaDescription } = useHtml(html)
          expect(h1s[0]).toContain('2')
          expect(metaDescription).toContain('2')
        })
        it('should show 404 if not existed', async () => {
          const html = await $fetch(`${url}?page=999999999999999`)
          expect(html).toContain('404')
        })
        it('should have canonical URL to be the paginated page', async () => {
          const html = await $fetch(`${url}?page=2`)
          const { headLinks } = useHtml(html)
          const canonicalLinks = headLinks.filter((link) =>
            link.includes('canonical'),
          )
          canonicalLinks.forEach((link) => {
            expect(link.includes('?page=2')).toBeTruthy()
          })
        })
        it('should use rel=next on first page', async () => {
          const html = await $fetch(url)
          const { headLinks } = useHtml(html)
          const nextLink = headLinks.find((link) => link.includes('canonical'))
          const preLink = headLinks.find((i) => i.includes('prev'))
          expect(nextLink).toBeTruthy()
          expect(preLink).toBeFalsy()
        })
        // it('should use rel=next/prev on second page', async () => {
        //   const html = await $fetch(`${url}?page=2`)
        //   const { headLinks } = useHtml(html)
        //   const nextLink = headLinks.find((i) => i.includes('rel="next"'))
        //   const preLink = headLinks.find((i) => i.includes('rel="prev"'))
        //   expect(nextLink).toBeTruthy()
        //   expect(preLink).toBeFalsy()
        //   expect(preLink.includes('page=1')).toBe(false)
        //   expect(nextLink.includes('page=3')).toBe(true)
        // })
        // it('should not use rel=prev on the root page')
        // it('should not use rel=next on the last page')
        // it('should link the first page without ?page=1 query')
      })

      // describe('content', async () => {
      //   it('should appear only on the first page')
      // })

      // describe('structured data', async () => {
      //   it('should signal pagination', async () => {})
      // })
    })
  })
}
export default category
