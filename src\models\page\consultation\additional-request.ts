export interface AdditionalRequest {
    _id: string;
    title: string;
    description: string;
    state: "REQUESTED" | "SUBMITED" | "APPROVED";
    category: string;
    consultation?: string;
    createdBy?: {
        _id: string;
        name: string;
    };
    approvedBy?: {
        _id: string;
        name: string;
    };
    updatedBy?: {
        _id: string;
        name: string;
    };
}

export enum AdditionalRequestCategory {
    ALL = "all",
    CHIEF_COMPLAINT = "chief-complaint",
    DESCRIPTION = "description",
    QUESTION = "question",
    BODY_INDEX = "body-index",
    VACCINE = "vaccine",
    LABS = "labs",
    FILES = "files",
}
