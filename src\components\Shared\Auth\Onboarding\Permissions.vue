<template>
    <div class="h-[80vh] overflow-y-auto pb-64 md:pb-0">
        <SharedSettingsPermissions
            @update:permissions="handlePermissionsUpdate"
        />

        <!-- Thêm Scroll Indicator -->
        <Transition
            enter-active-class="transition-opacity duration-300 ease-out"
            leave-active-class="transition-opacity duration-200 ease-in"
            enter-from-class="opacity-0"
            leave-to-class="opacity-0"
        >
            <div
                v-if="showScrollIndicator"
                class="fixed bottom-[20vh] left-0 right-0 z-40 transform animate-bounce md:bottom-[20vh]"
            >
                <div class="flex flex-col items-center gap-2 text-center">
                    <Button
                        variant="text"
                        rounded
                        icon="pi pi-angle-double-down"
                        @click="scrollToBottom"
                    />
                </div>
            </div>
        </Transition>
    </div>
</template>

<script setup lang="ts">
const isValid = ref<boolean>(false)
const showScrollIndicator = ref(false)

const emit = defineEmits(['update:permissions'])

const handlePermissionsUpdate = (granted: boolean) => {
    console.log(granted)
    isValid.value = granted
    emit('update:permissions', granted)
}

const checkHeight = () => {
    showScrollIndicator.value = window.innerHeight < 800 && window.scrollY < 100
}

onMounted(() => {
    checkHeight()
    window.addEventListener('resize', checkHeight)
    window.addEventListener('scroll', checkHeight)
})

onUnmounted(() => {
    window.removeEventListener('resize', checkHeight)
    window.removeEventListener('scroll', checkHeight)
})

const submit = async () => {
    return true
}

const scrollToBottom = () => {
    window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: 'smooth',
    })
}

defineExpose({
    submit,
})
</script>

<style scoped>
@keyframes bounce {
    0%,
    100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.animate-bounce {
    animation: bounce 1s infinite;
}
</style>
