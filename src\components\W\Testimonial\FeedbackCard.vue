<script setup lang="ts">
import { useClipboard } from '@vueuse/core'
import { useRoute } from 'nuxt/app'
import { useDateDiffWithLocale } from '~/composables/component/feedback-card'
import type { IRating } from '~/models'

//Use
const { t } = useI18n({ useScope: 'local' })

//states
const props = defineProps({
    rating: {
        type: Object as PropType<IRating>,
        default: () => {},
    },
    pt: {
        type: Object,
        default: () => {},
    },
    showTime: {
        type: Boolean,
        default: false,
    },
})
const dateFromNow = useDateDiffWithLocale(props.rating.dateCreated)
const stars: Ref<number> = ref(props.rating.stars)

//See more
const isShowMore = ref<string[]>([])
const onShowMore = (rating: any) => {
    const _id = rating?._id
    console.log('_id:', _id)
    if (!_id) return
    const _isShowMore = new Set(isShowMore.value)
    if (_isShowMore.has(_id)) {
        _isShowMore.delete(_id)
    } else {
        _isShowMore.add(_id)
    }
    isShowMore.value = Array.from(_isShowMore)
}

// Copy to clipboard
const source = ref('Link is null')
const { copy, copied, isSupported } = useClipboard({ source })
onMounted(() => {
    source.value = props.rating.notionLink
})

// editor
const route = useRoute()
const isEditor = route.query.editor == 'true'

// Avatar
const img = useImage()

const src = computed(() => {
    if (props.rating.avatar) {
        return img(
            props.rating.avatar,
            { format: 'webp' },
            {
                preset: 'avatar',
            },
        )
    } else {
        const femaleAvatar = img(
            'https://storage.googleapis.com/cms-gallery/61e64af5003d0a9e5b2ed091/8ca19fbe-f6ff-5c84-9a7f-44ab52ce7cdf.png',
            { width: 216, format: 'webp' },
        )

        const maleAvatar = img(
            'https://storage.googleapis.com/cms-gallery/61e64af4a5fbe579b6491310/82360227-a55b-5450-a884-41a082398a49.png',
            { width: 221, format: 'webp' },
        )

        switch (props.rating.authorGender.substring(0).toUpperCase()) {
            case 'F':
                return femaleAvatar
            case 'M':
                return maleAvatar
            default:
                return femaleAvatar
        }
    }
})
</script>

<template>
    <div
        :class="[
            'mt-3 min-h-40 w-full rounded-md bg-slate-50 p-4 text-start dark:bg-surface-900',
            pt?.item,
        ]"
    >
        <div :class="['mb-2 flex flex-row items-center', pt?.itemHeader]">
            <Avatar
                class="mr-2"
                :image="src"
                shape="circle"
                size="normal"
                :alt="`profile image ${rating.authorName}`"
            />
            <div>
                <div
                    :class="[
                        'mb-[3px] flex items-center justify-between gap-1.5 text-sm sm:text-base',
                        pt?.commentHeader,
                    ]"
                >
                    <span
                        :class="[
                            'font-semibold text-surface-700',
                            pt?.commentName,
                        ]"
                    >
                        {{ rating.authorName }}
                    </span>
                    <div v-if="showTime" class="flex items-center">
                        <span class="h-1 w-1 rounded-full bg-surface-300" />
                        <span
                            :class="[
                                'text-xs text-surface-400 sm:text-sm',
                                pt?.commentTime,
                            ]"
                        >
                            {{ dateFromNow }}
                        </span>
                    </div>
                </div>
                <Rating v-model="stars" readonly :cancel="false" />
            </div>
        </div>
        <blockquote :class="['text-sm sm:text-base', pt?.commentContent]">
            {{
                rating.halfComment
                    ? isShowMore.includes(rating._id)
                        ? rating.fullComment
                        : rating.halfComment
                    : rating.fullComment
            }}
        </blockquote>
        <template v-if="isEditor">
            <Button
                v-if="isSupported"
                :label="!copied ? 'Copy Notion Link' : 'Copied!'"
                icon="pi pi-clipboard"
                size="small"
                @click="copy()"
            />
            <p v-else>clipboard not supported</p>
            <p>Link to notion: "{{ rating.notionLink }}"</p>
        </template>

        <div class="flex justify-end">
            <Button
                v-if="rating.isOverlap && !isShowMore.includes(rating._id)"
                plain
                text
                size="small"
                class="mt-2"
                @click="onShowMore(rating)"
            >
                {{ t('see-more') }}
            </Button>
        </div>
    </div>
</template>

<i18n lang="yaml">
en:
    'see-more': 'See more'
vi:
    'see-more': 'Xem thêm'
</i18n>
