import { computed, reactive, useAsyncData, useNuxtApp } from '#imports'

export function useFileSearch(
    key: string = 'files',
    userId: Ref<string> | ComputedRef<string>,
    projectId: Ref<string> | ComputedRef<string>,
    initialQuery: any = { skip: 0, limit: 10 },
) {
    const query = reactive(initialQuery)
    const { $fetchWellcare } = useNuxtApp()

    const { data, status, refresh } = useAsyncData(
        key,
        () =>
            $fetchWellcare(
                `/file-management/project/${projectId.value}/user/${userId.value}`,
                { query },
            ),
        {
            immediate: true,
            transform: (res) => res.results,
        },
    )

    const total = computed(() => data.value?.total ?? 0)

    return {
        query,
        data,
        total,
        status,
        refresh,
    }
}
