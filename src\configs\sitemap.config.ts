import type { ModuleOptions } from '@nuxtjs/sitemap'

export const sitemap: Partial<ModuleOptions> = {
  debug: true,
  sitemaps: {
    static: {
      includeAppSources: true,
      include: ['/**'],
      exclude: ['/admin/**', '/en/admin/**'],
    },
    category: {
      sources: ['/api/__sitemap__/Category?size=100'],
    },
    'article-0-10k': {
      sources: ['/api/__sitemap__/Article?size=10000'],
    },
    'person-0-10k': {
      sources: ['/api/__sitemap__/Person?size=10000'],
    },
  },
}
