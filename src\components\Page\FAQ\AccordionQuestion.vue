<script setup lang="ts">
defineProps({
  items: {
    type: Array<any>,
    required: true,
    default: () => [],
  },
  isMobile: {
    type: Boolean,
    default: () => false,
  },
})
</script>

<template>
  <Accordion expand-icon="pi pi-angle-down" collapse-icon="pi pi-angle-up">
    <AccordionTab
      v-for="(item, index) in items"
      :key="index"
      :multiple="true"
      :header="item?.page?.properties?.Name"
      :pt="
        isMobile
          ? {
              headerTitle: '!leading-6',
            }
          : {
              root: {
                class:
                  '!mb-0 dark:border-slate-800 md:hover:bg-slate-100 md:dark:hover:bg-slate-900 last:border-b-0',
              },
              headerAction: {
                class: 'px-0 py-0 !text-lg',
              },
              headerTitle: {
                class: '!leading-6 sm:leading-none',
              },
            }
      "
    >
      {{ item?.page?.properties?.Sapo }}
    </AccordionTab>
  </Accordion>
</template>
