<template>
    <div class="relative mx-auto max-w-xl">
        <!-- Phone input with country code -->
        <div
            class="flex w-full items-center rounded-lg border p-2"
            :class="{ 'border-red-500': showError }"
        >
            <button
                class="flex shrink-0 items-center gap-2 border-r pr-2"
                @click="showDialog = true"
            >
                <div
                    class="h-4 w-6"
                    :class="`flag:${selectedCountry.code}`"
                ></div>

                <span>+{{ selectedCountry.dialCode }}</span>
                <i class="pi pi-chevron-down text-sm" />
            </button>
            <input
                v-model="phoneNumber"
                type="tel"
                class="w-full min-w-0 px-2 outline-none"
                :placeholder="getExamplePhone(selectedCountry.code)"
                @input="handleInputChange"
            />
        </div>

        <!-- Error message -->
        <span v-if="showError" class="mt-1 text-sm text-red-500">
            {{ errorMessage }}
        </span>

        <!-- Country selector dialog -->
        <Dialog
            v-model:visible="showDialog"
            modal
            :style="{ width: '100%', maxWidth: '400px' }"
            position="bottom"
            class="p-0"
        >
            <template #header>
                <div class="w-full pb-0 pt-4">
                    <h2 class="text-xl font-medium">
                        {{ t('select_country_code') }}
                    </h2>
                </div>
            </template>
            <div class="relative">
                <span class="absolute inset-y-0 right-4 flex items-center">
                    <i class="pi pi-search text-gray-400" />
                </span>
                <InputText
                    v-model="searchQuery"
                    class="w-full rounded-lg pr-10"
                    :placeholder="t('search')"
                />
            </div>
            <div class="mt-6 max-h-[400px] overflow-y-auto">
                <button
                    v-for="country in filteredCountries"
                    :key="country.code"
                    class="flex w-full items-center gap-4 p-4 hover:bg-gray-50"
                    @click="selectCountry(country)"
                >
                    <div :class="`flag:${country.code}`"></div>
                    <span>{{ country.name }}</span>
                    <span class="ml-auto text-gray-500">{{
                        country.dialCode
                    }}</span>
                </button>
            </div>
        </Dialog>
    </div>
</template>

<script setup lang="ts">
import { useCountries } from '~/composables/common/contries'
import {
    usePhoneFormat,
    type Country,
} from '~/composables/common/phone-validation'

const props = defineProps<{
    modelValue: string
}>()

const emit = defineEmits(['update:modelValue', 'change', 'change:country-code'])
const { t } = useI18n()

const showDialog = ref(false)
const searchQuery = ref('')
const showError = ref(false)
const errorMessage = ref('')
const phoneNumber = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
})

const { countries } = useCountries()

const selectedCountry = ref<Country>({
    code: 'VN',
    dialCode: '84',
    flag: 'https://flagcdn.com/w80/vn.png',
    name: 'Viet Nam',
})

const {
    getExamplePhone,
    validatePhoneNumber,
    standardPhone,
    formatPhoneNumber,
} = usePhoneFormat(
    computed(() => phoneNumber.value),
    computed(() => selectedCountry.value.dialCode),
)

const filteredCountries = computed(() => {
    let sortedCountries = countries

    // Sort the list with Vietnam at the top
    sortedCountries = [
        ...countries.filter((country) => country.code === 'VN'),
        ...countries.filter((country) => country.code !== 'VN'),
    ]

    if (!searchQuery.value) return sortedCountries

    const query = searchQuery.value.toLowerCase()

    // Filter and reapply the sorting
    return sortedCountries.filter(
        (country) =>
            country.name.toLowerCase().includes(query) ||
            country.dialCode.includes(query),
    )
})

const debouncedEmitChangeIfValid = useDebounceFn(() => {
    const valid = validatePhoneNumber(phoneNumber.value)

    showError.value = Boolean(phoneNumber.value) && !valid
    errorMessage.value = valid ? '' : t('invalid_phone')

    if (valid) {
        emit('change', {
            countryCode: selectedCountry.value.dialCode,
            phoneNumber: standardPhone.value,
        })
    }
}, 500)

const selectCountry = (country: Country) => {
    country.dialCode = country.dialCode.replace('+', '')
    selectedCountry.value = country
    showDialog.value = false
    emit('change:country-code', country)
    debouncedEmitChangeIfValid()
}

const handleInputChange = () => {
    debouncedEmitChangeIfValid()
}

const isValid = () => {
    if (!phoneNumber.value) {
        showError.value = true
        errorMessage.value = t('required_phone')
        return false
    }

    const valid = validatePhoneNumber(phoneNumber.value)

    showError.value = !valid
    errorMessage.value = valid ? '' : t('invalid_phone')

    return valid
}

defineExpose({
    selectedCountry,
    validatePhoneNumber,
    formatPhoneNumber,
    isValid,
})
</script>

<i18n lang="json">
{
    "en": {
        "invalid_phone": "Invalid phone number",
        "required_phone": "Phone number is required",
        "select_country_code": "Select country code",
        "search": "Search"
    },
    "vi": {
        "invalid_phone": "Số điện thoại không hợp lệ",
        "required_phone": "Vui lòng nhập số điện thoại",
        "select_country_code": "Chọn mã quốc gia",
        "search": "Tìm kiếm"
    }
}
</i18n>
