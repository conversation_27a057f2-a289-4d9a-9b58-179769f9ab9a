<script setup lang="ts">
import { computed } from '#imports'
import { Clipboard } from '@capacitor/clipboard'
import { Capacitor } from '@capacitor/core'
import { appRoutes } from '~/constants'

definePageMeta({
    layout: 'patient',
})
const router = useRouter()
const { isMembership } = useMembership()
const { user }: any = useUserInfo({
    scope: ['avatar', 'name', 'email', 'phone'],
})
const { t } = useI18n({})
const visible = ref(false)
const appStore = useAppStore()
const notification = useNotivueNotifications()

const healthGptContext = useLocalStorage('health-gpt-context', {})

const avatarUrl = computed<string>(
    () =>
        user.value?.avatar?.url ||
        `https://ui-avatars.com/api/?name=${user.value.name}`,
)
const copyPhone = async () => {
    try {
        await Clipboard.write({ string: '+' + user.value.phone })
        // Optionally, you can show a toast or notification here to indicate successful copy
        notification.success({
            title: t('notification'),
            message: t('phone-copied'),
        })
    } catch (e: any) {
        notification.error({
            title: t('notification'),
            message: t('copy-not-supported'),
        })
        console.log(
            'copyToClipboard',
            JSON.stringify({ error: e.message, stack: e.stack }),
        )
    }
}

const copyEmail = async () => {
    try {
        await Clipboard.write({ string: user.value.email })
        // Optionally, you can show a toast or notification here to indicate successful copy
        notification.success({
            title: t('notification'),
            message: t('email-copied'),
        })
    } catch (e: any) {
        notification.error({
            title: t('notification'),
            message: t('copy-not-supported'),
        })
        console.log(
            'copyToClipboard',
            JSON.stringify({ error: e.message, stack: e.stack }),
        )
    }
}

const onSignOut = () => {
    visible.value = true
    healthGptContext.value = {}
}
</script>

<template>
    <div class="flex flex-col gap-3 md:container md:mx-auto">
        <SharedDynamicHeader class="col-span-full">
            <template #detailed>
                <div
                    class="rounded-lg bg-surface-50 p-6 shadow-sm dark:bg-surface-800"
                >
                    <div
                        class="flex flex-col items-start gap-2 md:flex-row md:items-center"
                    >
                        <!-- Profile Image Section with Edit Button -->
                        <div
                            class="flex w-full items-start justify-between gap-4"
                        >
                            <div
                                class="group relative cursor-pointer transition-transform hover:scale-105"
                                @click="
                                    router.push(
                                        appRoutes.patient.settings.profile,
                                    )
                                "
                            >
                                <Avatar
                                    :image="avatarUrl"
                                    :alt="user?.name"
                                    shape="circle"
                                    size="xlarge"
                                    class="bg-surface-100 ring-2 ring-primary-200 dark:bg-surface-700 dark:ring-primary-700"
                                />
                            </div>

                            <div>
                                <Button
                                    icon="pi pi-pencil"
                                    rounded
                                    outlined
                                    :label="t('edit')"
                                    @click="
                                        router.push(
                                            appRoutes.patient.settings.profile,
                                        )
                                    "
                                />
                            </div>
                        </div>

                        <!-- User Info Section -->
                        <div class="flex flex-1 flex-col gap-2">
                            <h4
                                class="text-2xl font-semibold text-surface-900 dark:text-surface-50"
                            >
                                {{ user.name }}
                            </h4>

                            <!-- Contact Information -->
                            <div class="flex flex-col gap-1.5">
                                <div
                                    v-if="user.phone"
                                    class="group flex cursor-pointer items-center gap-2"
                                    @click="copyPhone()"
                                >
                                    <i class="pi pi-phone text-surface-500" />
                                    <span
                                        class="text-surface-600 transition-colors group-hover:text-primary-500"
                                    >
                                        +{{ user.phone }}
                                    </span>
                                </div>

                                <div
                                    v-if="user.email"
                                    class="group flex cursor-pointer items-center gap-2"
                                    @click="copyEmail()"
                                >
                                    <i
                                        class="pi pi-envelope text-surface-500"
                                    />
                                    <span
                                        class="text-surface-600 transition-colors group-hover:text-primary-500"
                                    >
                                        {{ user.email }}
                                    </span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <template #header>
                <div class="flex flex-row items-center gap-4">
                    <div
                        class="flex flex-col items-center"
                        @click="router.push(appRoutes.patient.settings.profile)"
                    >
                        <Avatar
                            :image="avatarUrl"
                            shape="circle"
                            class="bg-zinc-100 dark:bg-zinc-700"
                        />
                    </div>

                    <div class="flex flex-col">
                        <h4
                            class="line-clamp-1 overflow-hidden overflow-ellipsis whitespace-normal break-words break-all"
                        >
                            {{ user.name }}
                        </h4>
                    </div>
                </div>
            </template>
        </SharedDynamicHeader>

        <SharedPageSection v-if="isMembership" :pt="{ content: 'mx-4' }">
            <PagePatientMembershipCard :is-member="isMembership" :user="user" />
        </SharedPageSection>

        <SharedSettingsMobile />

        <div v-if="Capacitor.isNativePlatform()" class="text-center">
            App Version: {{ appStore.version }}
        </div>

        <div class="mb-6 mt-8 flex justify-center">
            <Button
                icon="pi pi-sign-out"
                severity="secondary"
                :label="t('sign-out')"
                outlined
                text
                @click="onSignOut"
            />
        </div>

        <SharedAuthSignOutDialog v-model:visible="visible" />
    </div>
</template>
