<script setup lang="ts">
import { SUPPORT_CONTACTS } from '~/constants'

defineProps<{
    visible: boolean
}>()

const emit = defineEmits<{
    (e: 'update:visible', value: boolean): void
}>()

const { t } = useI18n()
const { display } = useDisplay()

const isMobile = computed<boolean>(() => display?.breakpoint?.isMobile)

const handleContactClick = (type: keyof typeof SUPPORT_CONTACTS) => {
    const url = SUPPORT_CONTACTS[type]
    window.open(url, '_blank')
}
</script>

<template>
    <Dialog
        :visible="visible"
        :modal="true"
        :draggable="false"
        dismissable-mask
        :class="[
            'support-dialog',
            { 'w-full md:w-[32rem]': isMobile, 'w-[32rem]': !isMobile },
        ]"
        :header="t('support.title')"
        :position="isMobile ? 'bottom' : 'center'"
        @update:visible="(val: boolean) => emit('update:visible', val)"
    >
        <div class="flex flex-col gap-6">
            <!-- Chat Support Section -->
            <div class="space-y-6">
                <!-- Chat Options Grid -->
                <div class="grid grid-cols-1 gap-4">
                    <!-- Zalo Contact -->
                    <div
                        class="group flex cursor-pointer items-center gap-4 rounded-xl border-2 border-gray-100 bg-white p-5 transition-all duration-200 hover:border-primary hover:bg-primary-50"
                        @click="handleContactClick('ZALO')"
                    >
                        <div
                            class="flex h-12 w-12 items-center justify-center rounded-xl bg-blue-50 transition-colors group-hover:bg-white"
                        >
                            <img
                                src="/icons/zalo.svg"
                                alt="Zalo"
                                class="h-7 w-7 object-contain"
                            />
                        </div>
                        <div class="flex-1">
                            <h6 class="mb-1 font-semibold text-gray-900">
                                Zalo
                            </h6>
                            <p class="text-sm text-gray-600">+84366905905</p>
                        </div>
                        <i
                            class="pi pi-arrow-right text-lg text-gray-400 transition-colors group-hover:text-primary"
                        ></i>
                    </div>

                    <!-- WhatsApp Contact -->
                    <div
                        class="group flex cursor-pointer items-center gap-4 rounded-xl border-2 border-gray-100 bg-white p-5 transition-all duration-200 hover:border-primary hover:bg-primary-50"
                        @click="handleContactClick('WHATSAPP')"
                    >
                        <div
                            class="flex h-12 w-12 items-center justify-center rounded-xl bg-green-50 transition-colors group-hover:bg-white"
                        >
                            <img
                                src="/icons/whatsapp.svg"
                                alt="WhatsApp"
                                class="h-7 w-7 object-contain"
                            />
                        </div>
                        <div class="flex-1">
                            <h6 class="mb-1 font-semibold text-gray-900">
                                WhatsApp
                            </h6>
                            <p class="text-sm text-gray-600">+84366905905</p>
                        </div>
                        <i
                            class="pi pi-arrow-right text-lg text-gray-400 transition-colors group-hover:text-primary"
                        ></i>
                    </div>
                </div>
            </div>
        </div>
    </Dialog>
</template>

<style scoped>
.support-dialog :deep(.p-dialog-header) {
    @apply border-b border-gray-200 bg-white;
}

.support-dialog :deep(.p-dialog-content) {
    @apply bg-gray-50 p-0;
}
</style>
