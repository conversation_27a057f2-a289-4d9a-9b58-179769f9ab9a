// stores/device.ts
import { defineStore } from 'pinia'
import { type DeviceInfo, type BatteryInfo, Device } from '@capacitor/device'
import type { ISafeArea } from './../models/common/device-info.interface'

export interface IDeviceInfo {
  name: string
  model: string
  platform: 'ios' | 'android' | 'web'
  operatingSystem: 'ios' | 'android' | 'windows' | 'mac' | 'unknown'
  osVersion: string
  manufacturer: string
  isVirtual: boolean
}

interface State {
  deviceInfo: DeviceInfo | IDeviceInfo | null
  batteryInfo: BatteryInfo | null
  deviceId: string | null
  languageCode: string | null
  languageTag: string | null
  safeArea: ISafeArea
  signature: string
}

export const useDeviceStore = defineStore('device', {
  state: (): State => ({
    deviceInfo: null,
    batteryInfo: null,
    deviceId: null,
    languageCode: null,
    languageTag: null,
    safeArea: {
      top: 0,
      right: 0,
      bottom: 0,
      left: 0,
      statusBarHeight: 0,
    },
    signature: '',
  }),
  actions: {
    async fetchDeviceInfo() {
      this.deviceInfo = await Device.getInfo()
    },
    async fetchBatteryInfo() {
      this.batteryInfo = await Device.getBatteryInfo()
    },
    async fetchDeviceId() {
      const { identifier } = await Device.getId()
      this.deviceId = identifier
    },
    async fetchLanguageCode() {
      const { value } = await Device.getLanguageCode()
      this.languageCode = value
    },
    async fetchLanguageTag() {
      const { value } = await Device.getLanguageTag()
      this.languageTag = value
    },
  },
  getters: {
    deviceName: (state) => state.deviceInfo?.name || '',
    isIos: (state) => state.deviceInfo?.platform === 'ios',
    isAndroid: (state) => state.deviceInfo?.platform === 'android',
    isWeb: (state) => state.deviceInfo?.platform === 'web',
  },
})
