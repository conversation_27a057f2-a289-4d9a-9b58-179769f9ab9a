<script setup lang="ts">
import * as yup from 'yup'

const { t } = useI18n()
const onboardingStore = useOnboardingStore()

const gender = ref('')

// Initialize gender from store on component mount
onMounted(() => {
    if (onboardingStore.profile.gender) {
        // Convert store value to display value
        gender.value =
            onboardingStore.profile.gender === 'F' ? 'female' : 'male'
    }
})

// Watch for store changes
watch(
    () => onboardingStore.profile.gender,
    (newGender) => {
        if (newGender) {
            // Convert store value to display value
            gender.value = newGender === 'F' ? 'female' : 'male'
        }
    },
)

const { errors, defineField, validate } = useForm({
    validationSchema: yup.object({
        gender: yup.string().required(t('required')),
    }),
})

const [genderField, genderAttrs] = defineField('gender')

// Set initial value from gender ref
watch(
    gender,
    (newValue) => {
        genderField.value = newValue
    },
    { immediate: true },
)

// Update store when gender changes
watch(genderField, async (newGender) => {
    const result = await validate()
    if (result.valid) {
        // Convert display value to store value (F/M)
        const storeGenderValue = newGender === 'female' ? 'F' : 'M'
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            gender: storeGenderValue,
        })
    }
})

const submit = async () => {
    const result = await validate()
    return result.valid
}

defineExpose({
    submit,
})
</script>

<template>
    <div class="flex min-h-[80vh] pt-9 md:items-center md:justify-center">
        <div class="w-full max-w-2xl rounded-lg">
            <div class="mb-6 w-full space-y-4 text-center">
                <h1>
                    {{ t('onboarding.gender.description') }}
                </h1>
                <!-- Gender Selection -->
                <div>
                    <div class="grid grid-cols-1 gap-4" v-bind="genderAttrs">
                        <div
                            class="gender-box"
                            :class="{
                                'gender-box-selected': genderField === 'male',
                            }"
                            @click="genderField = 'male'"
                        >
                            <div class="gender-icon male">
                                <i class="pi pi-mars"></i>
                            </div>
                            <span>{{ t('onboarding.gender.male') }}</span>
                        </div>
                        <div
                            class="gender-box"
                            :class="{
                                'gender-box-selected': genderField === 'female',
                            }"
                            @click="genderField = 'female'"
                        >
                            <div class="gender-icon female">
                                <i class="pi pi-venus"></i>
                            </div>
                            <span>{{ t('onboarding.gender.female') }}</span>
                        </div>
                    </div>
                    <small
                        v-if="errors.gender"
                        class="mt-1 block text-center text-red-500"
                        >{{ errors.gender }}</small
                    >
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.gender-box {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border: 2px solid #e2e8f0;
    border-radius: 1rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.gender-box-selected {
    border-color: var(--p-primary-color);
    background-color: #e1fffe;
    transform: translateY(-2px);
    box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1);
}

.gender-icon {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: #eff6ff;
}

.gender-icon.male {
    color: #2563eb;
}

.gender-icon.female {
    color: #db2777;
}

.gender-box-selected .gender-icon.male {
    background-color: var(--p-primary-color);
    color: white;
}

.gender-box-selected .gender-icon.female {
    background-color: var(--p-secondary-color);
    color: white;
}

.gender-box span {
    font-weight: 500;
    color: #1f2937;
}
</style>
