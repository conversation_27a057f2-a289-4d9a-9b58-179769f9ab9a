.lightbox-container {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1500; /* Sit on top */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: rgba(0, 0, 0, 0.9); /* Black w/ opacity */
    transition: ease;
    transition-duration: 250ms;
}
/* ACTION */
.lightbox-action-container {
    padding-top: var(--safe-area-inset-top);
    display: flex;
    justify-content: end;
}
.lightbox-action-btn {
    background-color: black;
    color: white;
    border: none;
    cursor: pointer;
    font-size: large;
    margin: 5px;
    border-radius: 3px;
    font-weight: bold;
}
/* CONTENT */
.lightbox-content {
    width: 90%;
    height: 90%;
    /* background-color: black; */
    display: grid;
    place-items: center;
    overflow: hidden;
    margin: 10px auto ;
}
/* WAY 1: render image as background */
.lightbox-image-container {
    width: 100%;
    height: 100%;
    /* background-image: url("https://picsum.photos/600"); */
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    overflow: hidden;
    transition: ease;
    transition-duration: 150ms;
}
/* WAY 2: render use img tag */
/* .lightbox-content > div {
  max-width: 350px;
  height: 100%;
  overflow: hidden;
}
.lightbox-content > div > img {
  width: 100%;
  object-fit: contain;
} */
.lightbox-video-container {
    display: flex;
    flex-direction: column;
    justify-items: center;
}
.lightbox-video-container > video {
    max-width: 500px;
}
/* PAGINATION */
.lightbox-pagination {
    display: flex;
    justify-content: center;
}
.lightbox-pagination-btn {
    background-color: black;
    color: white;
    border: none;
    cursor: pointer;
    border-radius: 50%;
    font-size: larger;
    width: 35px;
    height: 35px;
    margin-left: 10px;
    margin-right: 10px;
}
#lightbox-pagination-prev::after {
    content: '\2039';
}
#lightbox-pagination-next::after {
    content: '\203A';
}
/* BOTTOM */
.lightbox-bottom {
    position: absolute;
    bottom: 0;
    width: 100%;
    transition: ease;
    transition-duration: 250ms;
}
.lightbox-gallery-container {
    padding: 5px;
    background-color: black;
    display: grid;
    grid-template-columns: repeat(auto-fill, 75px);
    gap: 5px;
    /* display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: center; */
    justify-content: center;
    justify-items: center;
}
.lightbox-gallery-item {
    width: 100%;
    height: 75px;
    border: 3px solid grey;
    border-radius: 5px;
    background-color: lightgrey;
    cursor: pointer;
    justify-self: center;
    align-self: center;
    /* background-image: url("https://picsum.photos/600"); */
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
    display: flex;
    justify-content: center; /* Centers horizontally */
    align-items: center;
}
.lightbox-gallery-item > span {
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.5);
    width: 50px;
    height: 50px;
}
.lightbox-gallery-item-selected {
    border: 3px solid white;
}
.lightbox-gallery-action {
    display: flex;
    justify-content: end;
}
.lightbox-gallery-btn {
    border: none;
    color: white;
    background-color: black;
    font-weight: bold;
    cursor: pointer;
    width: 45px;
    height: 45px;
    font-size: x-large;
}
