<script setup lang="ts">
import timelineData from '../assets/data/timelineData.json'

const isLoading = ref(true)

onMounted(() => {
  setTimeout(() => {
    isLoading.value = false
  }, 1000)

  if ((window as any).gsap) {
    const gsap = (window as any).gsap
    const ScrollTrigger = (window as any).ScrollTrigger

    gsap.registerPlugin(ScrollTrigger)

    gsap.utils.toArray('.box').forEach((box, _index) => {
      gsap.fromTo(
        box,
        { opacity: 0, y: 50 },
        {
          opacity: 1,
          y: 0,
          duration: 1,
          ease: 'power2.out',
          scrollTrigger: {
            trigger: box,
            start: 'top 100%',
            end: 'top 0%',
            scrub: true,
          },
        },
      )
    })

    gsap.to('.timeline-bg', {
      backgroundPosition: '0% 100%',
      scrollTrigger: {
        trigger: '.timeline-bg',
        start: 'top top',
        end: 'bottom bottom',
        scrub: 2,
      },
    })
  }
  else {
    console.error('GSAP chưa đư<PERSON><PERSON> tải từ CDN.')
  }
})
</script>

<template>
  <div
    class="w-full p-6 timeline-bg"
    style="background-image: url('/images/timeline-bg.png'); background-position: center; transition: background 0.5s;"
  >
    <div class="text-center relative mt-4 sm:mt-20 font-bold w-full mx-auto flex items-center flex-col box">
      <h1
        class="relative z-10 sm:mb-4 text-3xl md:mb-5 md:text-4xl xl:mb-6 xl:text-5xl 2xl:text-6xl tracking-wider"
        style="font-family: 'Imperial Script'"
      >
        Khép lại hành trình 10 năm
      </h1>
      <img
        src="https://storage.googleapis.com/cms-gallery-sandbox/67ad6bf7a3987a9bb38b84c4/leaf-pattern.png"
        class="max-w-[800px] w-full object-cover absolute -top-20 sm:-top-44 opacity-50 z-0"
      />
    </div>
    <div class="relative mt-4 sm:mt-8 max-w-5xl w-full mx-auto">
      <!-- Vertical line -->
      <div
        class="absolute bottom-0 left-4 md:left-10 top-0 w-0 border-l-2 border-dashed border-primary sm:block"
        style="border-image: repeating-linear-gradient(to bottom, #00bcaa, #00bcaa 5px, transparent 5px, transparent 12px) 1;"
      >
      </div>
      <!-- Timeline items -->
      <div
        v-for="(item, index) in timelineData"
        :key="index"
        class="relative mb-8 last:mb-0 box transition-all duration-200"
      >
        <!-- Dot -->
        <div
          class="absolute left-4 md:left-10 -translate-x-1/2 mt-1 sm:mt-4 sm:flex h-8 w-8 sm:h-12 sm:w-12 items-center justify-center rounded-full bg-[#ffdfa5]"
        >
          <!-- <div class="h-2 w-2 rounded-full bg-white"></div> -->
        </div>
        <div class="w-12 sm:w-16 md:w-24 absolute left-4 md:left-10 -translate-x-1/2 sm:flex md:-top-1">
          <img
            :src="item.image"
            :alt="item.title"
            class="w-full h-full object-cover"
          />
        </div>
        <!-- Content section -->
        <div class="ml-10 sm:ml-16 md:ml-40 lg:ml-60 flex flex-col">
          <div class="w-full">
            <h1
              class="text-primary mb-1 sm:mb-3 pl-3 tracking-wider"
              style="font-family: 'Imperial Script'"
            >
              {{ item.year }}
            </h1>
            <div class="w-full rounded-lg pl-3">
              <div class="flex items-start gap-4">
                <h3 class="text-xl font-semibold text-gray-900">
                  {{ item.title }}
                </h3>
              </div>
              <div
                v-if="isLoading"
                class="mt-2 ml-6 space-y-2 animate-pulse transition-all"
              >
                <div class="h-2.5 w-3/4 bg-[#fbf5e5] rounded"></div>
                <div class="h-2.5 w-3/4 bg-[#fbf5e5] rounded"></div>
                <div class="h-2.5 w-2/3 bg-[#fbf5e5] rounded"></div>
                <div class="h-2.5 w-2/3 bg-[#fbf5e5] rounded"></div>
              </div>
              <div
                v-else
                v-dompurify-html="item.description"
                class="mt-2 ml-6 text-gray-600 transition-all"
              ></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
