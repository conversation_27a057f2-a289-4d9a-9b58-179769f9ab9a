import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'JNQTJy3XrV',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2it1cev.lz',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON>c mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 15 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. <PERSON><PERSON><PERSON> sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2it1qzr.bc',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'img',
                            src: 'https://www.readingrockets.org/sites/default/files/styles/share_image/public/article/hp-16actions.jpg?itok=Wm6wkH-B',
                            alt: 'Nice picture',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2it2qla.pl',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Bắt chước trẻ khác khi chơi, thích lấy đồ chơi ra khỏi thùng khi thấy đứa trẻ khác làm vậy',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2it2qlapl',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2it2s47.hno',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cho cha mẹ xem món đồ mình thích',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2it2s47hno',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2it2tr2.ns',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Vỗ tay khi phấn khích',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2it2tr2ns',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2it2w8m.ejn',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Ôm ấp búp bê nhồi bông hoặc món đồ chơi khác',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2it2w8mejn',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2it4xj8.23b',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Thể hiện tình cảm với cha mẹ (ôm, âu yếm hoặc hôn)',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2it4xj823b',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jui29y.wgi',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cố gắng nói một hoặc hai từ ngoài “ba ba” hoặc “me me” như “ca ca” cho con cá hoặc “bong bong” cho trái bóng',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jui29ywgi',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jui3r8.bep',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhìn vào một vật quen thuộc cha mẹ khi gọi tên món đồ đó',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jui3r8bep',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jui549.f',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Làm theo hướng dẫn, bằng cả cử chỉ và lời nói. Ví dụ, bé sẽ đưa cho mẹ một món đồ chơi khi mẹ đưa tay ra và nói: “Đưa đồ chơi cho mẹ”.',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jui549f',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jui6bx.min',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết chỉ tay để có thứ mình muốn hoặc để được giúp đỡ',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jui6bxmin',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jumb2a.bxd',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cố gắng sử dụng mọi thứ đúng cách, như điện thoại, ly cốc, hoặc cuốn sách',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jumb2abxd',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jumkbk.yna',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Xếp chồng lên nhau được ít nhất hai món đồ nhỏ, như các khối gỗ vuông',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jumkbkyna',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2juozzw.kp',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự mình bước đi được một vài bước',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2juozzwkp',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jup186.e',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Dùng ngón tay để bốc và tự ăn',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jup186e',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    createdBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: 'ou3LIcv6ES',
    },
    updatedBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    title: '15 tháng',
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
})
