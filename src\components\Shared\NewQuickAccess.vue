<script lang="ts" setup>
import type { IQuickAction } from '~/models/component/quick-action'

interface Props {
    quickActions: IQuickAction[]
}

defineProps<Props>()
</script>

<template>
    <div
        class="no-scrollbar flex flex-row gap-3 overflow-x-auto overflow-y-hidden px-6 py-2"
    >
        <NuxtLink
            v-for="action in quickActions"
            :key="action.name"
            :to="action.to"
            class="card flex flex-none cursor-pointer flex-col items-center justify-center gap-2 transition hover:bg-[var(--p-surface-200)] active:scale-90 dark:hover:bg-[var(--p-surface-700)]"
        >
            <i :class="[action.icon, 'text-3xl']" />
            <p class="m-0 text-sm">{{ action.name }}</p>
        </NuxtLink>
    </div>
</template>
