import { ElasticIndex } from '~/models'
import type {
    HitNotionContentHealthProgram,
    IHealthProgramCalendar,
} from '~/models'

export function usePagePatientServicesHealthProgramsPregnancyDiary(
    label: Ref<string> | ComputedRef<string>,
) {
    const dayjs = useDayjs()
    const { t } = useI18n()

    const { user } = useUserInfo({
        scope: ['meta'],
    })

    const user_preg_date = computed(() => user.value?.meta?.preg_date)

    const pregnancyWeek: ComputedRef<number> = computed(() => {
        const today = dayjs().startOf('day')
        const pregnancyStart = dayjs(user_preg_date.value)

        const weeksElapsed = pregnancyStart.diff(today, 'week')
        const currentWeek = 41 - Math.max(0, weeksElapsed)

        return currentWeek
    })

    const parseLabel = (label: string) => {
        const [unit, valueStr] = label.split(':')

        if (!unit || !valueStr) {
            throw new Error('Invalid label format. Expected format: "week:1".')
        }

        const value = parseInt(valueStr, 10)

        return { unit, value }
    }

    const transformData = (
        articles: HitNotionContentHealthProgram[],
        currentWeek: number,
    ): IHealthProgramCalendar[] => {
        const sortedArticles = articles
            .map(({ page }) => {
                const { unit, value } = parseLabel(
                    page?.properties?.Labels || '',
                )
                return {
                    unit,
                    value,
                    label: t(page?.properties?.Labels || ''),
                }
            })
            .sort((a, b) => a.value - b.value)

        const activeIndex = sortedArticles.findIndex(
            (article) => article.value >= currentWeek,
        )

        return sortedArticles.map((item, index) => ({
            ...item,
            isActive: index <= activeIndex,
            isToday: index === activeIndex,
        }))
    }

    const { hits: articles, loading } =
        useFetchElasticWithDefault<HitNotionContentHealthProgram>(
            ElasticIndex.NOTION_CONTENT_HEALTH_PROGRAM,
            {
                filters: [
                    {
                        term: {
                            'page.properties.Program.properties.Key.keyword':
                                'pregnancy-diary',
                        },
                    },
                ],
                size: 50,
            },
        )

    const times: ComputedRef<IHealthProgramCalendar[]> = computed(() =>
        transformData(articles.value, pregnancyWeek.value),
    )

    const replaceWebmWithMp4 = (url: string) => {
        if (url?.endsWith('.webm')) {
            return url?.replace(/\.webm$/, '.mp4')
        }
        return url
    }

    const { hit } = useFetchElasticWithDefault<HitNotionContentHealthProgram>(
        ElasticIndex.NOTION_CONTENT_HEALTH_PROGRAM,
        {
            size: 1,
            filters: computed(() => [
                {
                    term: {
                        'page.properties.Slug.keyword': `nktk-tuan-${label.value.split(':')[1]}-short`,
                    },
                },
            ]),
            _source: {
                includes: ['page', 'blockstring'],
            },
        },
    )

    const shortArticle = computed(() => {
        return {
            name: hit.value?.page?.properties?.Name || '',
            cover: replaceWebmWithMp4(hit.value?.page?.cover?.url || ''),
            blocks: hit.value?.page?.blocks as any,
        }
    })

    return { times, loading, shortArticle }
}
