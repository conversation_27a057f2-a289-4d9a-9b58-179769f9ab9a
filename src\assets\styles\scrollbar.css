/*!* <PERSON><PERSON><PERSON> cài đặt mặc định cho màn hình lớn hơn *!*/
/*@media (min-width: 640px) {*/
/*    !* Webkit browsers (Chrome, Safari, newer versions of Opera) *!*/
/*    ::-webkit-scrollbar {*/
/*        width: calc(var(--scrollbar-width) + 2 * var(--scrollbar-padding));*/
/*    }*/

/*    ::-webkit-scrollbar-track {*/
/*        background: var(--scrollbar-track-color);*/
/*        border-radius: var(--scrollbar-border-radius);*/
/*    }*/

/*    ::-webkit-scrollbar-thumb {*/
/*        background: var(--scrollbar-thumb-color);*/
/*        border-radius: var(--scrollbar-border-radius);*/
/*        border: var(--scrollbar-padding) solid var(--scrollbar-track-color);*/
/*    }*/

/*    ::-webkit-scrollbar-thumb:hover {*/
/*        background: var(--scrollbar-thumb-hover-color);*/
/*    }*/
/*}*/

/*!* Mobile *!*/
/*@media (max-width: 639px) {*/
/*    !* Webkit browsers (Chrome, Safari, newer versions of Opera) *!*/
/*    ::-webkit-scrollbar {*/
/*        width: calc(var(--scrollbar-width) + 2 * var(--scrollbar-padding));*/
/*    }*/

/*    ::-webkit-scrollbar-track {*/
/*        background: transparent; !* Track trong suốt *!*/
/*    }*/

/*    ::-webkit-scrollbar-thumb {*/
/*        background: transparent; !* Thumb trong suốt *!*/
/*        border-radius: var(--scrollbar-border-radius);*/
/*    }*/

/*    ::-webkit-scrollbar-thumb:hover {*/
/*        background: transparent; !* Hover cũng trong suốt để tránh thay đổi màu *!*/
/*    }*/

/*    !* Firefox *!*/
/*    * {*/
/*        scrollbar-color: transparent transparent;*/
/*        scrollbar-width: thin;*/
/*    }*/
/*}*/

/* html,
body {
    overscroll-behavior: none !important;
    -webkit-overflow-scrolling: touch !important;
}

html {
    height: 100%;
    overflow: hidden !important;
    position: relative !important;
    user-select: none !important;
}

body {
    height: 100%;
    overflow: auto !important;
    position: relative !important;
} */

@media (min-width: 480px) {
    ::-webkit-scrollbar {
        /* width: 0 !important;
        background: transparent !important;
        display: none !important; */
        display: none;
    }
}

/* Mobile */
@media (max-width: 480px) {
    * {
        scrollbar-color: transparent transparent;
        scrollbar-width: thin;
    }
}
