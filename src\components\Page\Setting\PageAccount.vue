<script lang="ts" setup>
import { WUserProfileFormContainer } from '#components'
import { push } from 'notivue'

interface Props {
    isReadOnly?: boolean
    isProvider?: boolean
}

const props = withDefaults(defineProps<Props>(), {
    isReadOnly: false,
    isProvider: false,
})

const dayjs = useDayjs()
const { t } = useI18n()
const { key } = useRerender()
const { user, refresh } = useUserInfo()

const profileFormRef = ref<InstanceType<
    typeof WUserProfileFormContainer
> | null>(null)

const profile = computed(() => {
    const isProvider = props.isProvider

    const profile = isProvider
        ? {
              ...user.value,
              ...user.value?.provider,
          }
        : user.value

    return profile
})

const maxDate = computed(() => {
    return dayjs().subtract(16, 'years').toDate()
})

const minDate = computed(() => dayjs().subtract(120, 'year').toDate())

const handleSubmitForm = () => {
    profileFormRef.value?.submitFormDataFn('update')
    refresh()
}

const hanldeSubmitFormSuccess = () => {
    push.success({
        message: t('update-success'),
        duration: 3000,
    })
}

const hanldeSubmitFormError = () => {
    push.error({
        message: t('update-error'),
        duration: 3000,
    })
}
</script>

<template>
    <WUserProfileFormContainer
        :key="key"
        ref="profileFormRef"
        is-require-avatar
        :max-date="maxDate"
        :min-date="minDate"
        :text-btn-submit="t('update')"
        :default-data="profile"
        :is-read-only="isReadOnly"
        @on:submit-form="handleSubmitForm"
        @on:submit-success="hanldeSubmitFormSuccess"
        @on:submit-error="hanldeSubmitFormError"
    />
</template>
