import type {
    IChoice,
    IValidation,
    IFetchElement,
    EComponentType,
} from '../element'

export interface IChoosePatient {
    id: number
    label: string // Choose baby or Choose patient
    component: EComponentType //multiple_choice'
    description: string // Select one option
    choices: IChoice[]
    fetch?: IFetchElement
    validations?: IValidation[]
}

// Example:
// {
//   "id": 4,
//   "label": "MultipleChoice",
//   "component": "multiple_choice",
//   "description": "Select one option.",
//   "choices": [
//     {
//       "label": "<PERSON><PERSON><PERSON>, 22 tuổi (me)",
//       "value": "1"
//     },
//     {
//       "label": "<PERSON><PERSON><PERSON>, 3 tuổi (my daughter)",
//       "value": "2"
//     },
//     {
//       "label": "<PERSON>uy<PERSON>, 1 tuổi (my son)",
//       "value": "3"
//     }
//   ]
// }
