import { computed, useLazyAsyncData } from '#imports'
import { GenericEndpoint } from '~/models'

const BALANCE = 150

export async function getPatientLimitUsage(
    providerUserId: Ref<string> | ComputedRef<string>,
    patientId: Ref<string> | ComputedRef<string>,
) {
    const { $fetchWellcare } = useNuxtApp()
    const { data, status, execute, refresh } = useLazyAsyncData(
        () =>
            $fetchWellcare(
                `${GenericEndpoint.PERSONAL_DOCTOR_USER_DOCTOR_RELATIONSHIP}/${providerUserId.value}/patient/${patientId.value}`,
                {
                    query: {
                        populate: JSON.stringify([
                            { path: 'related' },
                            { path: 'provider' },
                        ]),
                        count: true,
                        pageSize: 5,
                        pageIndex: 1,
                    },
                },
            ),
        {
            immediate: false,
        },
    )

    await execute()

    const extractWalletsPatientInfo: any = computed(() => {
        const { balance, type, usable, _id } =
            data.value?.results?.wallets[0] ?? {}
        const formattedTime = formatRemainingTime(usable ?? 0, BALANCE) // Default balance set to 150 minutes
        return {
            _id,
            balance,
            type,
            usable,
            ...formattedTime, // Include formatted time properties
        }
    })

    const formatTimeString = (minutes: number) => {
        const totalSeconds = minutes * 60
        const formattedMinutes = Math.floor(totalSeconds / 60)
        const seconds = Math.floor(totalSeconds % 60)
        return {
            minutes: formattedMinutes,
            seconds,
            formatted: `${formattedMinutes.toString().padStart(2, '0')}:${seconds
                .toString()
                .padStart(2, '0')}`,
        }
    }

    const formatRemainingTime = (
        usableMinutes: number,
        balanceMinutes: number = BALANCE,
    ) => {
        const usableTime = formatTimeString(usableMinutes)
        const balanceTime = formatTimeString(balanceMinutes)
        const usedMinutes = balanceMinutes - usableMinutes
        const usedTime = formatTimeString(usedMinutes)
        return {
            remainingTime: `${usableMinutes}/${balanceMinutes}`,
            balance: balanceMinutes,
            balanceFormatted: balanceTime.formatted,
            usable: usableMinutes,
            usableFormatted: usableTime.formatted,
            used: usedMinutes,
            usedFormatted: usedTime.formatted,
            percentageUsed: (usableMinutes / balanceMinutes) * 100,
        }
    }

    return { data, status, execute, refresh, extractWalletsPatientInfo }
}
