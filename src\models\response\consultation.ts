import type { IBaseResponse } from '../../types/base'

export const TYPE_ENUM = [
    'indepth',
    'question',
    'home-treatment',
    'group',
    'request',
    'simple',
    'virtual',
    'ondemand',
    'quickcall',
    'onsite',
    'review-healthgpt',
] as const
export const MEDIUM_ENUM = [
    'phone',
    'video',
    'chat',
    'question',
    'none',
    'inperson',
] as const
export const CLOSED_STATES = [
    'COMPLETED',
    'FREE',
    'CANCELLED',
    'REJECTED',
] as const
export const STATE_ENUM = [
    'SCHEDULED',
    'WAITING',
    'INCONSULTATION',
    ...CLOSED_STATES,
] as const
export const FOLLOW_UP_QUESTION_TYPE_ENUM = ['illness', 'medication', 'disease']
export const FOLLOW_UP_QUESTION_STATE_ENUM = [
    'waiting',
    'replied',
    'cancelled',
    'rejected',
] as const
export const CANCELLED_BY_ROLE_ENUM = [
    'admin',
    'provider',
    'user',
    'patient',
    'system',
] as const
export const GET_ALL_API_DEFAULT_FIELDS = [
    'reason',
    'chiefComplaint',
    'state',
    'user',
    'patient',
    'provider',
    'providerUser',
    'questions',
    'medium',
    'shortLink',
    'files',
    'tags',
] as const
export const GET_ALL_QUESTION_API_DEFAULT_FIELDS = [
    'reason',
    'chiefComplaint',
    'type',
    'state',
    'user',
    'patient',
    'provider',
    'providerUser',
    'medium',
    'note',
] as const
export const NORMAL_STATE_FLOW = {
    SCHEDULED: ['WAITING', 'INCONSULTATION', 'CANCELLED', 'REJECTED'],
    WAITING: ['INCONSULTATION', 'CANCELLED', 'REJECTED'],
    INCONSULTATION: ['COMPLETED', 'FREE'],
} as const
export const CREATE_STATE_ENUM = ['SCHEDULED', 'WAITING'] as const
export const UPDATE_BY_PATIENT_STATE_ENUM = [
    'WAITING',
    'INCONSULTATION',
] as const

interface IBaseCancelledByDto {
    user?: string
    role?: (typeof CANCELLED_BY_ROLE_ENUM)[number]
    reason?: string
    comment?: string
}

// interface IBaseRejectDto {
//   comment: string
//   reason: string
// }
// interface IBaseCancelDto {
//   comment: string
//   reason: string
// }

// interface IBaseCompleteDto {
//   state: string
// }

interface IBaseTransferredDto {
    isTransfer?: boolean
    isOwner?: boolean
    by?: string
}

interface IBaseShortLinkDto {
    provider?: string
    providerCode?: string
    patient?: string
    patientCode?: string
}

// interface IBaseUpdateByPatientDto {
//   chiefComplaint?: string
//   reason?: string
//   questions?: string[]
// }
// interface IBaseUpdateByProviderDto {
//   note?: string
//   symptom?: string
// }

interface IBaseMetadataDto {
    minuteAvg?: number
    yearsofEXP?: number
    privateDoctorReply?: string
    ratingGood?: number
    registerQuickcall?: boolean
}

interface IBaseFollowUpQuestionDto {
    type?: string
    question?: string
    answer?: string
    state?: string
}

interface IBaseEvaluationDto {
    relevance?: boolean
    accuracy?: boolean
    completeness?: boolean
    respectForPatientAutonomy?: boolean
}
interface IBaseReferenceDto {
    name?: string
    url?: string
    authors?: string[]
    retrievedAt?: string
}

interface IBaseNoteDto {
    doctor?: string
    patient?: string
    ai?: string
    medicalTeam?: string
}

interface IBaseExpectationAnswerDto {
    text?: string
    audio?: string
}

interface IExpectationDto {
    key?: string
    order?: number
    tags?: string[]
    answer?: IBaseExpectationAnswerDto
}

export interface IResponseConsultation extends IBaseResponse {
    source?: string
    test?: boolean
    tags?: string[]
    type?: string
    state?: string
    closedAt?: Date
    metadata?: IBaseMetadataDto
    noneDrug?: boolean
    provider?: string
    providerUser?: string
    user?: string
    patient?: string
    medium?: string
    consultTime?: number
    shortLink?: IBaseShortLinkDto
    orderItems?: string[]
    orderItem?: string
    order?: string
    treatmentEndedAt?: Date
    chiefComplaint?: string
    symptom?: string
    diagnosis?: string[]
    altDiagnoses?: string[]
    note?: string
    conversation?: string
    followUpQuestion?: IBaseFollowUpQuestionDto
    reason?: string
    questions?: string[]
    noteAudio?: string[]
    transferred?: IBaseTransferredDto
    cancelledBy?: IBaseCancelledByDto
    comment?: string
    slot?: number
    evaluation?: IBaseEvaluationDto
    references?: IBaseReferenceDto[]
    notes?: IBaseNoteDto
    expectations?: IExpectationDto[]
}
