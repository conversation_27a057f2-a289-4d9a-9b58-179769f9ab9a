# Build stage
FROM mhealthvn/node-builder:20.14 as builder
ARG GIT_TOKEN
ENV GIT_TOKEN=$GIT_TOKEN
WORKDIR /usr/src/app
COPY package.json ./
RUN yarn
COPY . .
COPY .env.sandbox.web .env
RUN yarn generate

# Production stage
FROM nginx:alpine as final
RUN rm /etc/nginx/conf.d/default.conf
COPY nginx.conf /etc/nginx/conf.d/default.conf
COPY --from=builder /usr/src/app/.output/public /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
