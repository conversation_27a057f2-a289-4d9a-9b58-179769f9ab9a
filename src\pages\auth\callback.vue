<script setup lang="ts">
definePageMeta({
    layout: 'blank',
})

const { error, loading, processAuthCallback, retryAuthentication } =
    useAuthCallback()

onMounted(async () => {
    await processAuthCallback()
})
</script>

<template>
    <div
        class="flex h-screen w-full flex-col items-center justify-center text-center"
    >
        <div v-if="loading" class="mb-4">
            <div
                class="mx-auto h-12 w-12 animate-spin rounded-full border-4 border-blue-500 border-t-transparent"
            />
        </div>
        <p v-if="error" class="mt-4 break-words font-bold text-red-600">
            {{ error }}
        </p>
        <button
            v-if="error"
            class="mt-4 rounded bg-blue-500 px-4 py-2 font-bold text-white transition duration-300 ease-in-out hover:bg-blue-600"
            @click="retryAuthentication"
        >
            Retry Login
        </button>
    </div>
</template>
