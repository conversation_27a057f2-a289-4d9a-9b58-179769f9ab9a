<script setup lang="ts">
import { appRoutes } from '~/constants'
import type { HealthProgramCard } from '~/models/component/health-program.interface'

defineProps({
    program: {
        type: Object as PropType<HealthProgramCard>,
        required: true,
    },
})

const router = useRouter()
const { user } = useUserInfo({
    scope: ['meta'],
    refresh: true,
})

const { isMembership } = useMembership()

const visibleMembershipPopup = ref<boolean>(false)

const handleRedirect = (program: HealthProgramCard) => {
    const meta = user.value?.meta

    switch (program.key) {
        case 'pregnancy-diary':
            if (!isMembership.value) {
                visibleMembershipPopup.value = true
                return
            }

            router.push(
                meta?.preg_subscribe && meta?.preg_date
                    ? '/patient/services/health-program/pregnancy-diary'
                    : appRoutes.patient.checkout('pregnancy-diary'),
            )
            return

        case 'baby-development': {
            if (!isMembership.value) {
                visibleMembershipPopup.value = true
                return
            }

            router.push(
                meta?.baby_subscribe && meta?.baby_id
                    ? '/patient/services/health-program/baby-development'
                    : appRoutes.patient.checkout(
                          'hanh-trinh-cung-con-khon-lon',
                      ),
            )
            return
        }

        default:
            break
    }
}

onBeforeMount(() => {
    useUserInfo({ refresh: true })
})
</script>

<template>
    <div class="h-32" @click="handleRedirect(program)">
        <NuxtImg
            :src="program?.cover"
            :alt="program?.name"
            class="h-full w-32"
            loading="lazy"
        />
        <div class="flex h-full flex-1 flex-col pr-1">
            <div class="flex items-stretch justify-between font-semibold">
                <span>{{ program?.name }}</span>
                <i class="pi pi-angle-right mt-1" aria-hidden="true" />
            </div>
            <div class="line-clamp-5 flex-1 pr-1 text-sm">
                {{ program.description }}
            </div>
        </div>

        <SharedMembershipPopup
            v-model:visible="visibleMembershipPopup"
            @skip="visibleMembershipPopup = false"
            @update:visible="(val: boolean) => (visibleMembershipPopup = val)"
        />
    </div>
</template>
