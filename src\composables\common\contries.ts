import {
    type CountryCode,
    getCountries,
    getCountryCallingCode,
} from 'libphonenumber-js'
import type { Country } from './phone-validation'

export const useCountries = () => {
    const countries: Array<Country> = getCountries().map((country) => {
        const countryCode = country as CountryCode
        const phoneCode = getCountryCallingCode(countryCode)

        return {
            code: countryCode,
            name:
                new Intl.DisplayNames(['en'], { type: 'region' }).of(country) ||
                country,
            flag: `https://flagcdn.com/w80/${countryCode.toLowerCase()}.png`,
            dialCode: `+${phoneCode}`,
        }
    })

    const filterCountries = (query: string) => {
        const searchQuery = query.toLowerCase()
        return countries.filter(
            (country) =>
                country.name.toLowerCase().includes(searchQuery) ||
                country.dialCode.includes(searchQuery) ||
                country.code.toLowerCase().includes(searchQuery),
        )
    }

    return { countries, filterCountries }
}
