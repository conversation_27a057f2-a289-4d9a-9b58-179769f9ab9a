import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'G8sDB5GXP9',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2jsjgplma',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON>c mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 3 tuổi. <PERSON><PERSON>ng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. Bác sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2jsk6h0xxn',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'img',
                            src: 'https://www.storypod.com/cdn/shop/articles/kazuend-geG4C3qyiG0-unsplash.jpg?v=1675908945&width=1100',
                            alt: '',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2jsnsoenl8',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Lấy lại bình tĩnh trong vòng 10 phút sau khi cha mẹ rời đi, như khi đi trẻ chẳng hạn',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsnsoenl8',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jsnumrhap',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Để ý đến những đứa trẻ khác và tham gia chơi cùng',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có ',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsnumrhap',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jst8jm3w',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Trò chuyện với cha mẹ và có ít nhất hai lượt hồi đáp qua lại',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jst8jm3w',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jsuc11r6',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Đặt được câu hỏi “ai”, “cái gì”, “ở đâu” hoặc “tại sao”, như “Ba (mẹ) đâu rồi?”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsuc11r6',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jsuzww7ds',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cho biết được chuyện gì đang xảy ra khi xem một bức tranh hoặc một cuốn sách khi được hỏi, chẳng hạn như “đang chạy”, “đang ăn” hoặc “đang chơi”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'nnot_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsuzww7ds',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jsvsuiaz',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói tên mình khi được hỏi',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsvsuiaz',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jsxvs53rf',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói chuyện đủ tốt cho người khác hiểu mình, đa phần là như vậy.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jsxvs53rf',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jszia0er',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Vẽ được một vòng tròn khi được chỉ cho cách thực hiện',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jszia0er',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jt08s8b3',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết tránh chạm vào các vật nóng, như bếp lò, khi được cha mẹ nhắc nhở',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jt08s8b3',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jt14hk21f',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết chơi xâu chuỗi các món đồ lại với nhau, như các hạt nhựa lớn chẳng hạn.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jt14hk21f',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jt1fvgyyo',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự mặc được một số loại quần áo, như quần rộng hoặc áo khoác',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jt1fvgyyo',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jt20ydqwu',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Dùng nĩa để ăn',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jt20ydqwu',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    title: '3 tuổi',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
})
