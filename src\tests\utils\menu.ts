import { menu } from '../../configs'

export function useMenu() {
  const [...menuValues] = Object.values(menu)
  const menuItems = menuValues.reduce((previous, current) => {
    return previous.concat(current)
  }, [])
  const linkSet: Set<string> = new Set()
  menuItems.forEach((i) => {
    linkSet.add(i.route)
    if (i.children) {
      i.children.forEach((child) => {
        linkSet.add(child.route)
      })
    }
  })
  const links = Array.from(linkSet).filter((i) => i !== '#')

  return { links }
}
