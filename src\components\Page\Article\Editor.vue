<script setup lang="ts">
import { onMounted, ref, useRoute } from '#imports'
import type { PropType } from 'vue'
import type { INotionPage } from '~/models'

defineProps({
  page: {
    type: Object as PropType<INotionPage>,
    required: true,
  },
})
const { query } = useRoute()
const href = ref()
const open = ref(!!query?.editor)
onMounted(() => {
  href.value = window.location.href
})
</script>

<template>
  <Sidebar v-model:visible="open" header="Editor">
    <div
      class="align-items-center justify-content-between flex flex-wrap gap-3"
    >
      <div class="align-items-center flex gap-2">
        <NuxtLink :to="page.url" target="_blank">
          <Button icon="pi pi-pencil" rounded outlined />
        </NuxtLink>
        <NuxtLink
          :to="`https://sitechecker.pro/app/main/seo-report?pageUrl${href}`"
          target="_blank"
        >
          <Button icon="pi pi-list-check" rounded outlined />
        </NuxtLink>
      </div>
      <span class="p-text-secondary"
        >Updated {{ $dayjs(page.last_edited_time).format('LL') }}</span
      >
      <Divider />
      <p>Meta Image</p>
      <NuxtImg :src="page.cover?.url" />
      <p>Meta Description:</p>
      <p>{{ page.properties['Meta Description'] || '-' }}</p>
    </div>
  </Sidebar>
</template>
