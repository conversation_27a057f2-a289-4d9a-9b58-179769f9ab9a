<script setup lang="ts">
import { appRoutes } from '~/constants'
import { ElasticIndex } from '~/models'
import type { IDataSection, IProviderProfile } from '~/models'
import { push } from 'notivue'
import {
    computed,
    definePageMeta,
    ref,
    useActionToolkit,
    useI18n,
    useLayoutStore,
    usePagePatientServicesProviderProduct,
    usePagePatientServicesProviderProfile,
    useRoute,
    useRouter,
    useScroll,
    watch,
} from '#imports'

definePageMeta({
    layout: 'nested-page',
})

const layoutStore = useLayoutStore()

const router = useRouter()
const { params, query }: any = useRoute()

const { t } = useI18n({ useScope: 'local' })

const providerSlug = computed(() => params.profile)

const { profile, loading } = usePagePatientServicesProviderProfile(providerSlug)

const transformSpecialties = (profile: IProviderProfile) => {
    return profile.specialties
        ?.map((specialty: any) => specialty?.name)
        ?.join(', ')
}

const onCopySuccess = () => {
    push.info({ message: t('toast:copied-to-clipboard') })
}

const onCopyFailure = () => {
    push.error({
        message: t('toast:copy-not-supported'),
    })
}

const { shareSource } = useActionToolkit()

const onShareProfile = () => {
    shareSource(
        'Share profile',
        'Check out this doctor on wellcare',
        `https://khamtuxa.wellcare.vn/bac-si/${params.profile}`,
        onCopySuccess,
        onCopyFailure,
    )
}

const { allProducts, getAllProducts } = usePagePatientServicesProviderProduct()

watch(profile, async () => {
    const { _id, name } = profile.value || {}

    if (_id) {
        await getAllProducts(_id)
        if (!allProducts.value) {
            push.error({
                message: 'No products available',
            })
        }
    }

    if (name) {
        layoutStore.setTitle(name)
    }
})

const navigateToAppointment = (productSlug: string) => {
    const route = {
        path: appRoutes.patient.checkout(productSlug),
        query,
    }

    router.push(route)
}

const isShowActionFooter = ref(false)

const getArticle = (provider: any) => provider.blocks

const ratingFilters = computed(() => {
    if (!profile.value?.name) return []
    return [
        { term: { 'page.properties.IsFeature': true } },
        { term: { 'page.properties.Provider.keyword': profile.value?.name } },
        {
            term: {
                'page.properties.Sentiment.keyword': 'positive',
            },
        },
    ]
})

const profileRef = ref<HTMLElement | null>(null)
const { y } = useScroll(profileRef)

watch(
    y,
    () => {
        isShowActionFooter.value = y.value > 400
    },
    {
        immediate: true,
        deep: true,
    },
)

const moreActionMenuRef = ref()
const items = ref([
    {
        label: t('menu:label'),
        items: [
            {
                label: t('btn:share-profile'),
                icon: 'pi pi-share-alt',
                command: onShareProfile,
            },
        ],
    },
])

const toggle = (event: any) => {
    moreActionMenuRef.value.toggle(event)
}
</script>

<template>
    <div
        ref="profileRef"
        class="flex h-full flex-col items-start overflow-y-scroll pb-20"
    >
        <!-- Skeleton loader when loading -->
        <template v-if="loading">
            <div class="relative mb-40 h-60 w-full sm:mb-28 sm:h-[21.5rem]">
                <!-- Cover image skeleton -->
                <Skeleton width="100%" height="90%" />
            </div>

            <div
                class="mb-6 flex flex-col items-end justify-center gap-4 sm:flex-row"
            >
                <!-- Profile avatar skeleton -->
                <Skeleton shape="circle" size="4rem" class="mr-4" />

                <!-- Doctor info skeleton -->
                <div>
                    <Skeleton width="10rem" height="2rem" class="mb-2" />
                    <Skeleton width="15rem" height="1.5rem" />
                </div>
            </div>

            <!-- Action buttons skeleton -->
            <div class="mb-4 flex flex-row gap-2 p-2">
                <Skeleton width="8rem" height="2rem" />
                <Skeleton width="8rem" height="2rem" />
            </div>
        </template>

        <template v-else>
            <div
                class="relative w-full bg-gradient-to-b from-[#ababab75] via-[#f5f5f5] to-[#ffff] dark:via-black dark:to-black"
            >
                <div
                    class="mx-auto -mb-14 aspect-[2/1] max-w-[80rem] sm:mb-0 sm:aspect-[5/2]"
                >
                    <NuxtImg
                        :src="profile.cover"
                        :alt="profile.name"
                        placeholder="https://placehold.co/1600x400/f5f5f5/FFF?text=\n"
                        class="h-full w-full object-cover shadow-[0_8px_24px_rgba(149,157,165,0.2)]"
                    />

                    <div
                        class="app-padding-x flex w-full -translate-y-[50px] flex-col items-start justify-start gap-2 md:-translate-y-[36px] md:flex-row md:items-end md:pl-8 md:pr-0 lg:-translate-y-[50px]"
                    >
                        <div
                            class="profile-avatar flex flex-none items-center justify-center before:h-[132px] before:w-[132px] before:md:h-[164px] before:md:w-[164px]"
                        >
                            <NuxtImg
                                :src="profile.avatar"
                                :alt="profile.name"
                                sizes="500px md:800px"
                                class="relative z-50 size-32 self-center rounded-full bg-white object-cover md:size-40 md:self-start"
                            />
                        </div>

                        <div
                            class="flex w-full flex-col py-2 text-start md:flex-row md:items-center md:justify-between"
                        >
                            <div>
                                <b class="text-lg text-black">
                                    {{ profile.name }}
                                </b>
                                <p class="mb-2">
                                    {{ transformSpecialties(profile) }}
                                </p>
                            </div>
                            <ClientOnly>
                                <div class="flex items-start justify-between">
                                    <div
                                        v-if="allProducts.length"
                                        class="flex flex-row flex-wrap gap-2"
                                    >
                                        <Button
                                            v-for="product in allProducts"
                                            :key="product._id"
                                            :label="t(`btn:${product.sku}`)"
                                            @click="
                                                navigateToAppointment(
                                                    product.slug,
                                                )
                                            "
                                        />
                                    </div>

                                    <Button
                                        v-if="!allProducts.length"
                                        severity="secondary"
                                        icon="pi pi-share-alt"
                                        :label="t('btn:share-profile')"
                                        @click="onShareProfile"
                                    />

                                    <Button
                                        v-else
                                        type="button"
                                        icon="pi pi-ellipsis-v"
                                        severity="secondary"
                                        aria-haspopup="true"
                                        aria-controls="overlay_menu"
                                        @click="toggle"
                                    />
                                    <Menu
                                        id="overlay_menu"
                                        ref="moreActionMenuRef"
                                        :model="items"
                                        :popup="true"
                                    />
                                </div>
                            </ClientOnly>
                        </div>
                    </div>
                </div>
            </div>

            <Transition
                enter-from-class="opacity-0 translate-y-[100%]"
                leave-to-class="opacity-0 translate-y-[100%]"
                enter-active-class="transition duration-500"
                leave-active-class="transition duration-500"
            >
                <div
                    v-if="isShowActionFooter"
                    class="fixed bottom-0 left-0 z-20 flex w-full items-start justify-between gap-3 border-t-2 bg-white p-4 md:hidden dark:border-gray-700 dark:bg-surface-900"
                >
                    <div
                        v-if="allProducts.length"
                        class="flex flex-row flex-wrap gap-2"
                    >
                        <Button
                            v-for="product in allProducts"
                            :key="product._id"
                            :label="t(`btn:${product.sku}`)"
                            @click="navigateToAppointment(product.slug)"
                        />
                    </div>

                    <Button
                        v-if="!allProducts.length"
                        severity="secondary"
                        icon="pi pi-share-alt"
                        :label="t('btn:share-profile')"
                        @click="onShareProfile"
                    />

                    <Button
                        v-else
                        type="button"
                        icon="pi pi-ellipsis-v"
                        severity="secondary"
                        aria-haspopup="true"
                        aria-controls="overlay_menu"
                        @click="toggle"
                    />
                    <Menu
                        id="overlay_menu"
                        ref="moreActionMenuRef"
                        :model="items"
                        :popup="true"
                    />
                </div>
            </Transition>

            <SharedProviderProfileDataSection
                v-for="section in new Array<IDataSection>(
                    {
                        title: t('about:title'),
                        content: profile.highlight,
                        contentType: 'Text',
                    },
                    {
                        title: t('specialties'),
                        content: profile.specialties,
                        contentType: 'Chips',
                    },
                    {
                        title: t('conditions'),
                        content: profile.conditions,
                        contentType: 'Chips',
                    },
                    {
                        title: t('metrics'),
                        content: profile.metrics,
                        contentType: 'Metrics',
                    },
                )"
                :key="section.title"
                :title="section.title"
                :content="section.content"
                :content-type="section.contentType"
            />

            <WArticleBody
                class="mb-4 max-w-[80rem] px-4 md:px-8 lg:px-12 xl:px-20 2xl:px-32"
                :blocks="getArticle(profile)"
            />

            <div
                class="mb-6 max-w-full px-6 sm:mx-auto sm:w-full sm:max-w-[80rem]"
            >
                <WTestimonial
                    v-if="ratingFilters?.length > 0"
                    :elastic-index="ElasticIndex.NOTION_RATINGS"
                    :title="t('testimonial:title')"
                    :filters="ratingFilters"
                    :path-to-all-rating="
                        appRoutes.patient.services.provider.rating(providerSlug)
                    "
                />
            </div>
        </template>
    </div>
</template>

<style>
.profile-avatar::before {
    position: absolute;
    content: '';
    background: #ffffff;
    border-radius: 1000px;
    z-index: 20;
}
</style>

<i18n lang="yaml">
en:
    'menu:label': 'Options'
    'btn:share-profile': 'Share'
    'btn:consultation-indepth': 'Teleconsultation'
    'btn:membership-personal-doctor': 'Personal Doctor'
    'toast:copied-to-clipboard': 'Profile copied to clipboard'
    'toast:copy-not-supported': 'Clipboard functionality is not supported in your browser'
    'about:title': 'About'
    'specialties': 'Specialties'
    'conditions': 'Conditions and diseases'
    'metrics': 'Metrics'
    'testimonial:title': 'Feedback'
vi:
    'menu:label': 'Lựa chọn'
    'btn:share-profile': 'Chia sẻ'
    'btn:consultation-indepth': 'Khám từ xa'
    'btn:membership-personal-doctor': 'Bác sĩ riêng'
    'toast:copied-to-clipboard': 'Link của bác sĩ đã được sao chép'
    'toast:copy-not-supported': 'Chức năng sao chép không được hỗ trợ trong trình duyệt của bạn'
    'about:title': 'Giới Thiệu'
    'specialties': 'Chuyên khoa'
    'conditions': 'Vấn đề và bệnh lý'
    'metrics': 'Thống kê'
    'testimonial:title': 'Nhận xét'
</i18n>
