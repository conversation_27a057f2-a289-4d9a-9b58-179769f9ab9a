<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import { appRoutes } from '~/constants'
import type { IProviderFilter, TProviderTitle } from '~/models'

definePageMeta({
    layout: 'nested-page',
})

const route = useRoute()
const router = useRouter()
const { t } = useI18n({ useScope: 'local' })

const layoutStore = useLayoutStore()
const isNative = computed(() => Capacitor.isNativePlatform())

const { _group, _target }: any = route.query

const getProviderTitle = (group: string): TProviderTitle => {
    const mapping: Record<string, TProviderTitle> = {
        adult: 'Bs',
        'nguoi-lon': 'Bs',
        child: 'Bs',
        nhi: 'Bs',
        'mental-health': 'Tlg',
        'suc-khoe-tinh-than': 'Tlg',
    }

    return mapping[group]
}

const searchInputModel = ref<string>('')
const visibleMDF = ref<boolean>(false) // Visible mobile dialog filter

const providerFrom = ref<number>(0)
const providerSize = ref<number>(20)
const filter: Ref<IProviderFilter> = ref({
    keyword: '',
    group: _group,
    specialties: [],
    title: getProviderTitle(_group),
})

const visibleBtnSeeMore: ComputedRef<boolean> = computed(
    () =>
        providers.value?.length > 0 &&
        providerTotal.value !== undefined &&
        providerSize.value < providerTotal.value,
)

const hasFilters: ComputedRef<boolean> = computed(() => {
    return (
        (filter.value?.specialties?.length ?? 0) > 0 ||
        (filter.value?.keyword?.length ?? 0) > 0 ||
        filter.value?.title !== undefined
    )
})

const { specialties, providers, providerLoading, providerTotal } =
    usePagePatientServicesProviderSearch(
        {
            provider: {
                from: providerFrom,
                size: providerSize,
            },
        },
        filter,
    )

const onClickProviderCard = (providerSlug: string) => {
    const route = {
        path: appRoutes.patient.services.provider.profile(providerSlug),
        query: _target ? { target: _target } : undefined,
    }

    router.push(route)
}

const clearFilter = () => {
    filter.value.keyword = ''
    filter.value.specialties = []
    filter.value.title = undefined
}

watchDebounced(
    searchInputModel,
    (value) => {
        filter.value.keyword = value
    },
    {
        debounce: 400,
    },
)

watch(
    () => filter.value.keyword,
    (value) => {
        if (!value) {
            searchInputModel.value = ''
        }
    },
)

onMounted(() => {
    layoutStore.setTitle(t('layout:title'))
})
</script>

<template>
    <div>
        <div class="flex flex-col items-center justify-center">
            <h1 class="my-3">
                {{ t('page:title') }}
            </h1>

            <SharedSearchInputText
                v-model="searchInputModel"
                :placeholder="t('input:search')"
                class="mb-3"
            />

            <!-- Filter button -->
            <div class="xl:hidden">
                <Button
                    :label="t('btn:filter')"
                    icon="pi pi-filter"
                    outlined
                    rounded
                    aria-label="show filter"
                    @click="visibleMDF = true"
                />
            </div>
        </div>

        <div
            class="app-padding-x flex flex-col gap-6 pb-12 text-start xl:flex-row xl:px-12"
        >
            <!-- Filter on computer -->
            <SharedProviderFilter
                v-model="filter"
                :specialties="specialties"
                class="hidden w-80 flex-none xl:block"
            />

            <!-- Filter on mobile -->
            <Sidebar
                v-model:visible="visibleMDF"
                position="full"
                block-scroll
                :header="t('sidebar:filter')"
                :pt:header:class="[
                    {
                        'pt-safe': isNative,
                    },
                ]"
            >
                <SharedProviderFilter
                    v-model="filter"
                    :specialties="specialties"
                />
            </Sidebar>

            <div class="flex-flex-col flex-1">
                <SharedProviderFilterChips
                    class="no-scrollbar"
                    :filter="filter"
                    :specialties="specialties"
                />
                <div
                    class="grid-cols mt-4 grid grid-cols-1 gap-4 md:grid-cols-2"
                >
                    <PagePatientServicesProviderCard
                        v-for="provider in providers"
                        :key="provider.slug"
                        :provider="provider"
                        @on-click-card="onClickProviderCard(provider.slug)"
                    />

                    <template v-if="providerLoading">
                        <Skeleton
                            v-for="i in Array(6)"
                            :key="i"
                            height="10rem"
                        />
                    </template>

                    <div
                        v-if="visibleBtnSeeMore"
                        class="col-span-full mt-4 flex flex-row justify-center p-4"
                    >
                        <Button
                            class="block w-28 font-medium"
                            severity="secondary"
                            @click="providerSize += 20"
                        >
                            <div
                                v-if="providerLoading"
                                class="rounded-inherit mx-auto h-4 w-4 animate-spin rounded-full border-2 border-surface-800 border-r-transparent border-opacity-10"
                            />
                            <span v-else>
                                {{ t('btn:show-more') }}
                            </span>
                        </Button>
                    </div>

                    <Transition
                        enter-active-class="transition ease-out duration-300"
                        enter-from-class="opacity-0 transform scale-95"
                        enter-to-class="opacity-100 transform scale-100"
                        leave-active-class="transition ease-in duration-200"
                        leave-from-class="opacity-100 transform scale-100"
                        leave-to-class="opacity-0 transform scale-95"
                    >
                        <template
                            v-if="
                                !providerLoading &&
                                hasFilters &&
                                providers.length <= 0
                            "
                        >
                            <div
                                class="col-span-full flex flex-col items-center p-4"
                            >
                                <i
                                    class="pi pi-compass my-8 text-9xl text-surface-600 dark:text-surface-300"
                                />
                                <h3>{{ t('search:no-results') }}</h3>
                                <Button class="mt-2" @click="clearFilter">
                                    {{ t('btn:clear-filter') }}
                                </Button>
                            </div>
                        </template>
                    </Transition>
                </div>
            </div>
        </div>
    </div>
</template>

<i18n lang="yaml">
en:
    'btn:clear-filter': 'Clear filters'
    'btn:filter': 'Filter'
    'btn:show-more': 'See more'
    'input:search': 'Search by name or specialty'
    'layout:title': 'Find your specialist'
    'page:title': 'Our doctors'
    'search:no-results': 'No results'
    'sidebar:filter': 'Filter'
vi:
    'btn:clear-filter': 'Xóa bộ lọc'
    'btn:filter': 'Bộ lọc'
    'btn:show-more': 'Xem thêm'
    'input:search': 'Tìm theo tên hoặc chuyên khoa'
    'layout:title': 'Tìm kiếm bác sĩ'
    'page:title': 'Bác sĩ của chúng tôi'
    'search:no-results': 'Không tìm thấy kết quả'
    'sidebar:filter': 'Bộ lọc'
</i18n>
