import Knock, { type Feed, type FeedEventPayload, type FeedStoreState } from "@knocklabs/client";
import { ref } from '#imports'

export function useActivityFeed() {
  // const config = useRuntimeConfig()
  // const app_id = config.public.onesignal.app_id
  const KNOCK_API_KEY = 'pk_test_Ci3Ov6SPGMXDZ8tZ6Y2_GP1SVAUeCdUc7F1EduZPDwc';
  const KNOCK_FEED_CHANNEL_ID = '34edced8-544b-4f08-9693-a34f1a031d83';
  const client = ref<Feed>();
  const knockClient = new Knock(KNOCK_API_KEY);
  const feed = ref<FeedStoreState>();

  const init = (currentUser: { id: string, token?: string }) => {
    knockClient.authenticate(
      // The id of the user you want to authenticate against
      currentUser.id,
      // You only need this in production environments
      currentUser.token,
    );

    // Initialize the feed
    client.value = knockClient.feeds.initialize(
      KNOCK_FEED_CHANNEL_ID,
      // Optionally you can provide a default scope here:
      // { tenant: "jurassic-park", source: "new-comment" },
      {
        // Turn on the automatic connection manager
        auto_manage_socket_connection: true,
        // Optionally, customize the delay amount in milliseconds. Defaults to 2000ms or 2s
        auto_manage_socket_connection_delay: 2500,
      }
    );

    // Connect to the real-time socket
    client.value.listenForUpdates();

    // Setup a callback for when a batch of items is received (including on first load and subsequent page load)
    client.value.on("items.received.page", () => {
      if (client.value) feed.value = client.value.getState();
    });

    // Setup a callback for when new items arrive in real-time
    client.value.on("items.received.realtime", (payload: FeedEventPayload) => {
      console.log('items.received.realtime', payload)
      if (client.value) feed.value = client.value.getState();
      payload.items.forEach((item) => {
        console.log(item);
        // if (item.data && item.data.showToast) {
          // toast({
          //   title: `📨 New feed item at ${new Date(
          //     item.inserted_at
          //   ).toLocaleString()}`,
          //   description: "Snap! This real-time feed is mind-blowing 🤯",
          // });
        // }
      });
    });

    // Listen to all received items
    client.value.on("items.received.*", () => {
      if (client.value) feed.value = client.value.getState();
    });
  }



  const fetchFeed = () => {

    // Fetch the feed items
    client.value?.fetch({
      // Fetch a particular status only (defaults to all)
      // status: "all" | "unread" | "unseen",
      // // Pagination options
      // after: lastItem.__cursor,
      // before: firstItem.__cursor,
      // // Defaults to 50
      // page_size: 10,
      // // Filter by a specific source
      // source: "notification-key",
      // // Filter by a specific tenant
      // tenant: "jurassic-park",
    });

  }
  const teardown = () => {
    client.value?.teardown();
  }

  // // Mark one or more items as read
  // knockFeed.markAsRead(feedItemOrItems);
  // // Mark one or more items as seen
  // knockFeed.markAsSeen(feedItemOrItems);
  // // Mark one or more items as archived
  // knockFeed.markAsArchived(feedItemOrItems);

  // // Mark one or more items as unread
  // knockFeed.markAsUnread(feedItemOrItems);
  // // Mark one or more items as unseen
  // knockFeed.markAsUnseen(feedItemOrItems);
  // // Mark one or more items as unarchived
  // knockFeed.markAsUnarchived(feedItemOrItems);
  return {
    init,
    client,
    feed,
    fetchFeed,
    teardown
  }
}
