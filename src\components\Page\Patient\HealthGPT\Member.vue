<script lang="ts" setup>
import { appRoutes } from '~/constants'
import type { Relationship } from '~/models'

const props = defineProps({
    member: {
        type: Object as PropType<Relationship>,
        required: true,
    },
})

const emit = defineEmits(['update-member'])

const visible = defineModel<boolean>()

const { t } = useI18n()

const { user } = useUserInfo({
    scope: ['_id'],
})

const components = {
    Medication: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/HealthRecords/Medication/index.vue'
            ),
    ),
    LabTest: defineAsyncComponent(
        () =>
            import('~/components/Page/Patient/HealthRecords/LabTest/index.vue'),
    ),
    Vitals: defineAsyncComponent(
        () =>
            import('~/components/Page/Patient/HealthRecords/Vitals/index.vue'),
    ),
    Immunization: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/HealthRecords/Immunization/index.vue'
            ),
    ),
}

const medicalSections = computed(() => [
    {
        label: 'medication',
        icon: 'material-symbols:medication',
        to: appRoutes.patient.healthRecords.medicalSection(
            props.member.related?._id ?? '',
            'prescriptions',
        ),
        component: components.Medication,
    },
    {
        label: 'lab',
        icon: 'material-symbols:science',
        to: appRoutes.patient.healthRecords.medicalSection(
            props.member.related?._id ?? '',
            'lab',
        ),
        component: components.LabTest,
    },
    {
        label: 'vitals',
        icon: 'mdi:clipboard-vitals',
        component: components.Vitals,
    },
    {
        label: 'immunization',
        icon: 'material-symbols:vaccines-outline-rounded',
        component: components.Immunization,
    },
])
</script>

<template>
    <Dialog
        v-model:visible="visible"
        modal
        :header="t('header', { name: member?.related?.name })"
        pt:root:class="w-full h-full"
        pt:header:class="pb-0"
        pt:title:class="text-lg"
        pt:content:class="px-0 flex flex-col"
    >
        <div class="overflow-y-auto" style="height: calc(100% - 48px)">
            <div v-for="section in medicalSections" :key="section.label">
                <Suspense>
                    <template #default>
                        <component
                            :is="section.component"
                            :member-activated-id="member.related?._id"
                            :member-activated="member"
                            :title="t(`${section.label}`)"
                            :to="section.to"
                            :gender="member.related?.gender"
                            :dob="member.related?.dob"
                            :user-id="user._id"
                        />
                    </template>
                    <template #fallback>
                        <Skeleton
                            height="200px"
                            width="100%"
                            class="rounded-xl"
                        />
                    </template>
                </Suspense>
            </div>
        </div>
        <div class="mx-4 mt-auto">
            <Button
                :label="t('update-profile')"
                class="w-full"
                @click="emit('update-member', member)"
            >
                <template #icon>
                    <iconify-icon icon="bi:pencil-square" />
                </template>
            </Button>
        </div>
    </Dialog>
</template>

<i18n lang="json">
{
    "en": {
        "header": "{name}'s information",
        "update-profile": "Update Profile"
    },
    "vi": {
        "header": "Thông tin của {name}",
        "update-profile": "Cập nhật hồ sơ"
    }
}
</i18n>
