<script lang="ts" setup>
definePageMeta({
    layout: 'content',
    middleware: 'auth-capacitor',
})

const layoutStore = useLayoutStore()

const { article, loading } = usePagePatientServicesHealthProgramsSlug()

watch(
    article,
    () => {
        layoutStore.setTitle(article.value.name)
        layoutStore.setCover(article.value.cover)
    },
    {
        deep: true,
        immediate: true,
    },
)
</script>

<template>
    <div v-if="loading">
        <Skeleton width="100%" height="200px" />
        <Skeleton width="90%" height="20px" class="ml-4 mt-4" />
        <Skeleton width="60%" height="16px" class="ml-4 mt-4" />
        <Skeleton width="80%" height="16px" class="ml-4 mt-2" />
        <Skeleton width="70%" height="16px" class="ml-4 mt-2" />
        <Skeleton width="80%" height="16px" class="ml-4 mt-2" />
    </div>
    <div v-else>
        <WArticleBody class="mt-4 px-6" :blocks="article?.blocks" />
        <SharedDoctorBanner class="mt-6" />
    </div>
</template>
