import { useOidc, useRouter } from '#imports'
import { <PERSON><PERSON><PERSON> } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'
import { jwtDecode } from 'jwt-decode'
import type { CreateSigninRequestArgs, UserProfile } from 'oidc-client-ts'
import { User } from 'oidc-client-ts'
import { appRoutes, deepLink } from '~/constants'
import { push } from 'notivue'

export const useAppAuth = () => {
    const {
        getOidcClient,
        getUserManager,
        checkAuthStatus,
        refreshToken: refreshTokenOidc,
    } = useOidc()
    const router = useRouter()
    const authStore = useAuthStore()

    const { clear } = usePreferences()

    const loading = computed(() => authStore.isLoading)

    const account = computedAsync(
        async () => await getUserManager.getUser(),
        null,
    )

    const refreshToken = computedAsync<string>(
        async () => account.value?.refresh_token || '',
    )

    const accessToken = computedAsync<string>(
        async () => account.value?.access_token || '',
    )

    const executeAuthOperation = async (
        operation: () => Promise<void>,
        errorMessage: string,
    ) => {
        authStore.setIsLoading(true)
        try {
            await operation()
        } catch (error) {
            console.error(`${errorMessage}:`, error)
        } finally {
            authStore.setIsLoading(false)
        }
    }

    const getAccount = async (): Promise<User | null> => {
        return await getUserManager.getUser()
    }

    const signIn = async (args?: CreateSigninRequestArgs) =>
        await executeAuthOperation(async () => {
            if (Capacitor.isNativePlatform()) {
                const request = await getOidcClient.createSigninRequest({
                    ...args,
                    redirect_uri: deepLink.oauth2redirect,
                    request_type: 'si:r',
                })

                await Browser.open({
                    url: request.url,
                    windowName: '_self',
                    presentationStyle: 'fullscreen',
                })
            } else {
                await getUserManager.signinRedirect(args)
            }
        }, 'Sign in error')

    const signInIDP = (
        idp?: string,
        _args?: Partial<CreateSigninRequestArgs>,
    ) =>
        // 1. https://keycloak-wellcare-vn.mhealthvn.com/realms/customer/protocol/openid-connect/auth?client_id=chat-wellcare-vn&redirect_uri=http%3A%2F%2Flocalhost%3A3000%2Fauth%2Fcallback&response_type=code&scope=openid+profile+email&state=1d30b444deb640809edb375bd03ad827&code_challenge=9a1w4gcDM99kmGQu6bxE_qCuXCoPuqvb7J1oUjmQw8I&code_challenge_method=S256&kc_idp_hint=livwell_oidc
        // 2. https://keycloak-wellcare-vn.mhealthvn.com/realms/customer/broker/livwell_oidc/login?session_code=yrUJmdCUZrrgwBZQyXmfwvj8xe7pmWNcWul5i9P96fk&client_id=chat-wellcare-vn&tab_id=UTolsNnsAYo
        // // livwell
        // 3. https://keycloak.wellcare.vn/realms/customer/protocol/openid-connect/auth?scope=openid&state=V9OnmCA89H0_PyQtyq2XIdMaqaSIn_etHUHEByOhGbE.UTolsNnsAYo.nPdjuOSMTy6BHuAqInTwtQ&response_type=code&client_id=livwell&redirect_uri=https%3A%2F%2Fkeycloak-wellcare-vn.mhealthvn.com%2Frealms%2Fcustomer%2Fbroker%2Flivwell_oidc%2Fendpoint&nonce=xKKTeGHnGhcqzL3rY4DLKw
        // 4. https://keycloak.wellcare.vn/realms/customer/login-actions/authenticate?session_code=bdF9Z8RvpHLVNwyuoSC0o7FDyR6WMClQe9HOiY-9ZZ4&execution=6b4c0fe7-56d3-46b2-86fc-430934c592a3&client_id=livwell&tab_id=dUiTqFdDq54
        // // wellcare
        // 5. https://keycloak-wellcare-vn.mhealthvn.com/realms/customer/broker/livwell_oidc/endpoint?state=V9OnmCA89H0_PyQtyq2XIdMaqaSIn_etHUHEByOhGbE.UTolsNnsAYo.nPdjuOSMTy6BHuAqInTwtQ&session_state=5b7050f3-e480-4dda-a175-668005d0f238&code=a2384bf6-7e7c-41ec-9d7b-7c0d3c93dc7b.5b7050f3-e480-4dda-a175-668005d0f238.8e3c1f2c-f1f4-4c8f-975b-aa6363424f40
        // 6. http://localhost:3000/auth/callback?state=1d30b444deb640809edb375bd03ad827&session_state=cb369243-d377-48d7-a30d-aec706c377e8&code=686b6490-2aa4-4e56-a509-38d14333b179.cb369243-d377-48d7-a30d-aec706c377e8.9cf763b8-e48c-4f2e-811e-e02a2274f0b5
        // 7. https://keycloak-wellcare-vn.mhealthvn.com/realms/customer/protocol/openid-connect/token
        // 8. https://keycloak-wellcare-vn.mhealthvn.com/realms/customer/protocol/openid-connect/userinfo
        executeAuthOperation(async () => {
            if (Capacitor.isNativePlatform()) {
                const request = await getOidcClient.createSigninRequest({
                    request_type: 'si:r',
                    redirect_uri: deepLink.oauth2redirect,
                    extraQueryParams: {
                        kc_idp_hint: idp || '',
                        prompt: 'select_account',
                    },
                })
                await Browser.open({
                    url: request.url,
                    windowName: '_self',
                    presentationStyle: 'popover',
                })
            } else {
                if (idp) {
                    await getUserManager.signinRedirect({
                        extraQueryParams: {
                            kc_idp_hint: idp,
                            prompt: 'select_account',
                        },
                    })
                }
            }
        }, 'Sign in error')

    const signInRefreshToken = (
        refreshToken: string,
        _args?: Partial<CreateSigninRequestArgs>,
    ) =>
        //   {
        //     "access_token": "**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",
        //     "expires_at": 1723995985,
        //     "refresh_expires_in": 604800,
        //     "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI3NjkyZjE4YS1mYTUzLTQzYzctODA2NC0wMzQxYmVjMGU1OGMifQ.eyJleHAiOjE3MjQ1OTk4ODUsImlhdCI6MTcyMzk5NTA4NSwianRpIjoiMTlmODY5ZTgtNGQ2Zi00NGUxLWJmZjAtMDllYWVmNTg3NjJjIiwiaXNzIjoiaHR0cHM6Ly9rZXljbG9hay53ZWxsY2FyZS52bi9yZWFsbXMvY3VzdG9tZXIiLCJhdWQiOiJodHRwczovL2tleWNsb2FrLndlbGxjYXJlLnZuL3JlYWxtcy9jdXN0b21lciIsInN1YiI6IjU2NTIxODdlLWRjNzUtNDAyNi1hZTdlLWVkNjQ5YmY3YmE0ZiIsInR5cCI6Ik9mZmxpbmUiLCJhenAiOiJ3ZWxsY2FyZSIsInNlc3Npb25fc3RhdGUiOiJjZWI2OTJkNy02M2U1LTQzMTgtOGJiZi02YjUwYmQxODkyYmEiLCJzY29wZSI6InByb2ZpbGUgb2ZmbGluZV9hY2Nlc3MgZW1haWwiLCJzaWQiOiJjZWI2OTJkNy02M2U1LTQzMTgtOGJiZi02YjUwYmQxODkyYmEifQ.wyZ5J_UTlNUOe6h1Co8hNtBX1ONBlZYgYVGH9Mus578",
        //     "token_type": "Bearer",
        //     "not-before-policy": 0,
        //     "session_state": "ceb692d7-63e5-4318-8bbf-6b50bd1892ba",
        //     "scope": "profile offline_access email"
        // }
        executeAuthOperation(async () => {
            const profile: UserProfile = jwtDecode(refreshToken)
            const user = new User({
                refresh_token: refreshToken,
                access_token: refreshToken,
                token_type: 'Bearer',
                profile,
            })
            await getUserManager.storeUser(user)
            await getUserManager.signinSilent()
        }, 'Sign in error')

    const signOut = async () => {
        await executeAuthOperation(async () => {
            await clear()

            if (Capacitor.isNativePlatform()) {
                const request = await getOidcClient.createSignoutRequest({
                    id_token_hint: account.value?.id_token,
                    post_logout_redirect_uri: deepLink.signOut,
                })

                await Browser.open({
                    url: request.url,
                    windowName: '_self',
                    presentationStyle: 'popover',
                })
            } else {
                await getUserManager.signoutRedirect({
                    id_token_hint: account.value?.id_token,
                })
            }
        }, 'Sign out error')
    }

    const handleAppAuthUrlOpen = async (event: { url: string }) => {
        const url = event?.url
        if (
            url?.startsWith(deepLink.oauth2redirect) &&
            url?.includes('state=') &&
            (url?.includes('error=') || url?.includes('code='))
        ) {
            const notification = push.promise('Processing authentication...')
            // try {
            await getUserManager.signinCallback(url)
            await Browser.close()
            router.push(appRoutes.auth.callback)
            notification.clear()
            // } catch (error) {
            //     console.error('Sign in callback error:', error)
            //     notification.reject('Sign in failed')
            //     await Browser.close()
            //     throw error
            // }
        }

        if (url?.startsWith(deepLink.signOut)) {
            const notification = push.promise('Signing out...')
            try {
                await Browser.close()
                router.push('/')
                notification.clear()
            } catch {
                notification.reject('Sign out failed')
            }
        }

        if (url?.startsWith(deepLink.resetPassword)) {
            executeAuthOperation(async () => {
                await Browser.close()
                return
            }, 'reset password error')
        }
    }

    return {
        account,
        checkAuthStatus,
        loading,
        signIn,
        signInIDP,
        signInRefreshToken,
        signOut,
        handleAppAuthUrlOpen,
        getUserManager,
        refreshToken,
        accessToken,
        getAccount,
        refreshTokenOidc,
    }
}
