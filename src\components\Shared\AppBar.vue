<template>
    <header
        :class="[
            'sticky inset-0 z-50 flex w-full items-center justify-center px-4 transition-all duration-300 ease-in-out',
            elevated ? 'shadow-sm' : '',
            color ? `bg-${color}` : 'bg-white',
            dark ? 'text-white' : 'text-surface-800',
            height ? `h-${height}` : 'h-12',
        ]"
    >
        <slot name="left" />

        <div class="flex-grow text-center">
            <slot name="title">
                <h2 class="mb-0 text-xl font-semibold">{{ title }}</h2>
            </slot>
        </div>

        <slot name="right" />
    </header>
</template>

<script setup lang="ts">
interface Props {
    title?: string
    elevated?: boolean
    color?: string
    dark?: boolean
    height?: string
}

withDefaults(defineProps<Props>(), {
    title: 'App Title',
    elevated: false,
    color: 'white',
    dark: false,
    height: '16',
})
</script>
