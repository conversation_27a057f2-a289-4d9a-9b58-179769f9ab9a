import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: '5H05MlAFAe',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2jrk2tt.d7',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON>c mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 4 tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. Bác sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.\n',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2jrl4x7.uw',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Giả vờ đóng vai nhân vật khác khi chơi (cô giáo, siêu anh hùng, con chó)',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrl4x7uw',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrlwuy.3go',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Đòi đi chơi với trẻ khác, nếu không có trẻ nào xung quanh, chẳng hạn như “Con có thể sang chơi với An An được không?”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrlwuy3go',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrmhno.ufq',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'An ủi người khác đang bị đau hoặc buồn bã, như đến ôm một bạn đang khóc',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrmhnoufq',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrmzph.rtr',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết tránh nguy hiểm, như không nhảy từ trên cao xuống sân chơi',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrmzphrtr',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrnsgc.kh8',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Thích làm “người giúp đỡ” người khác',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrnsgckh8',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrolv3.wt',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Thay đổi hành vi khi đến các địa điểm khác nhau, như nơi thờ cúng, thư viện, sân chơi',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrolv3wt',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jrpq80.04f',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói được câu có 4 từ trở lên',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrpq8004f',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrprn9.rek',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói được một số từ trong bài hát, câu chuyện hoặc bài đồng dao',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrprn9rek',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrpt6h.ay',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói về ít nhất một sự việc đã xảy ra trong ngày, chẳng hạn như "Con chơi bóng đá đó"',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrpt6hay',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrpuoh.yz',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Trả lời những câu hỏi đơn giản như “Áo khoác dùng để làm gì?” hoặc “Bút chì dùng để làm gì?”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrpuohyz',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jrscqo.nw',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Kể được một số màu sắc của đồ vật',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrscqonw',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrsed8.3rr',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Kể được những gì sẽ xảy ra tiếp theo trong một câu chuyện quen thuộc',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrsed83rr',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrsfos.9xj',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Vẽ một người có ba bộ phận cơ thể trở lên',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrsfos9xj',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jrua5v.x9',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Hầu như luôn bắt trúng được trái bóng lớn',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrua5vx9',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jruber.v0a',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự lấy được thức ăn hoặc rót nước khi có người lớn giám sát',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not-',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jruberv0a',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrucla.g59',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Mở được một vài nút (áo, quần)',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jruclag59',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrudw7.uz',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Giữ bút màu hoặc bút chì giữa các ngón tay và ngón cái (không phải bằng cách nắm cả bàn tay lại)',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrudw7uz',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    createdBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    updatedBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    title: '4 tuổi',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
    createdAt: '2024-10-22T01:24:21.722Z',
    updatedAt: '2024-10-22T02:13:00.506Z',
})
