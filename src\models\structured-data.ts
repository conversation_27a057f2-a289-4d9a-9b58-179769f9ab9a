export type TJsonLD =
  | 'NewsArticle'
  | 'FAQ'
  | 'Article'
  | 'WebPage'
  | 'VideoObject'
  | 'Recipe'
  | 'Course'
  | 'HowTo'
  | 'HowToSection'
  | 'HowToStep'
  | 'TechArticle'
  | 'APIReference'
  | 'BlogPosting'
  | 'DiscussionForumPosting'
  | 'LiveBlogPosting'
  | 'SocialMediaPosting'
  | 'Blog'
  | 'Question'
  | 'Answer'
  | 'Review'
  | 'ClaimReview'
  | 'CreativeWork'
  | 'ScholarlyArticle'
  | 'Report'
  | 'Thesis'
  | 'Comment'
  | 'Dataset'
  | 'ImageObject'
  | 'MediaObject'
  | 'AudioObject'
  | 'DataCatalog'
  | 'DataDownload'
  | 'DataFeed'
  | 'DataFeedItem'
  | 'DataSheet'
  | 'PresentationDigitalDocument'
  | 'Table'
  | 'Painting'
  | 'Photograph'
  | 'Sculpture'
  | 'SoftwareApplication'
  | 'MobileApplication'
  | 'VideoGame'
  | 'WebApplication'
  | 'Atlas'
  | 'ComicStory'
  | 'Map'
  | 'Photograph'
  | 'Recipe'
  | 'SatiricalArticle'
  | 'ScholarlyArticle'
  | 'SocialMediaPosting'
  | 'WebPage'
  | 'AboutPage'
  | 'CheckoutPage'
  | 'CollectionPage'
  | 'ImageGallery'
  | 'VideoGallery'
  | 'ContactPage'
  | 'ItemPage'
  | 'ProfilePage'
  | 'SearchResultsPage'
  | 'WebPageElement'
  | 'WPAdBlock'
  | 'WPFooter'
  | 'WPHeader'
  | 'WPSideBar'

export interface IJsonLD {
  enabled: boolean
  type?: TJsonLD
}
