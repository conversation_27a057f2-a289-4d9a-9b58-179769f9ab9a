/** @type {import('tailwindcss').Config} */
import primeui from 'tailwindcss-primeui'
import defaultTheme from 'tailwindcss/defaultTheme'
import safeArea from 'tailwindcss-safe-area-capacitor'

module.exports = {
    content: [
        './src/assets/presets/**/*.{js,vue,ts}',
        './src/components/**/*.{js,vue,ts}',
        './src/doc/**/*.{js,vue,ts}',
        './src/layouts/**/*.vue',
        './src/pages/**/*.vue',
        './src/plugins/**/*.{js,ts}',
        './src/nuxt.config.{js,ts}',
        './src/app.vue',
        './src/error.vue',
    ],
    theme: {
        extend: {
            screens: {
                ...defaultTheme.screens,
                xxs: '368px',
                xs: '480px',
            },
        },
    },
    plugins: [primeui, safeArea],
}
