package vn.wellcare.capacitor;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.util.Log;

import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.getcapacitor.BridgeActivity;

import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

import vn.wellcare.plugins.capacitor.agora.VideoCallActivity;
import vn.wellcare.plugins.capacitor.agora.VideoCallAgoraActivity;
import vn.wellcare.plugins.capacitor.agora.VoiceCallAcitivity;
import vn.wellcare.plugins.capacitor.agora.VoiceCallAgoraActivity;
import vn.wellcare.plugins.capacitor.agora.ui.SocketManager;
import vn.wellcare.plugins.capacitor.agora.util.ApiCallback;

//import vn.wellcare.plugins.capacitor.app_auth.CapacitorAppAuthPlugin;

public class MainActivity extends BridgeActivity {
    static final String TAG = "WellcareVn";
    private final BroadcastReceiver receiver = new BroadcastReceiver() {
        static final String TAG = "WellcareVn";

        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.d(TAG, "action: " + action);

            if (action.equals("action_call_accept")) {
                String additionalData = intent.getStringExtra("extra_additional_data");
                startAgoraCall(additionalData, context);
            }
        }

        ;
    };

    @Override
    public void load() {
        super.load();
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("action_call_accept");
        LocalBroadcastManager.getInstance(this).registerReceiver(receiver, new IntentFilter("action_call_accept"));
        SharedPreferences sharedPreferences = getSharedPreferences("tmp_callkeep_file", Context.MODE_PRIVATE);
        String callData = sharedPreferences.getString("extra_additional_data", "{}");
//    String callData = "{\"mode\":\"app\",\"callback\":{\"url\":\"https://api.mhealthvn.com/callcenter/app/event\",\"data\":{}},\"token\":\"007eJxTYHCovhL879qEt15PVVNCjRUNHLYsLFsb2G39La8ntlv7RoQCQ1KiiZmJcZpRknGioYlpmpGlqblpkoW5hXGKcaJparKhlaZKekMgI4P7ZUkmRgYIBPFZGEpSi0sYGAAzEB4m\",\"appId\":\"ba4643f2b3a145f29575b8783d3a5ec1\",\"room\":\"test\",\"user\":{\"name\":\"User Test\",\"avatar\":{\"url\":\"https://i.pravatar.cc/300\"}},\"from\":{\"role\":\"user\",\"user\":\"a0\"},\"to\":{\"role\":\"user\",\"user\":\"a1\"}}";
        if (!callData.equals("{}")) {
            sharedPreferences.edit().remove("extra_additional_data").apply();
            startAgoraCall(callData, this);
        }
    }

    @Override
    public void onDestroy() {
        super.onDestroy();
        LocalBroadcastManager.getInstance(this).unregisterReceiver(receiver);
    }

    void addNativeLog(Context context, JSONObject data) {
        // Get the current date and time
        Date currDate = new Date();
        SimpleDateFormat dateFormatter = new SimpleDateFormat("dd/MM/yyyy HH:mm:ss", Locale.getDefault());

        // Format the current date and time
        String formattedDate = dateFormatter.format(currDate);

        // Add the formatted date and time to the data JSON object
        try {
            data.put("time", formattedDate);
        } catch (Exception e) {
            e.printStackTrace();
        }

        // Access the shared preferences
        SharedPreferences sharedPreferences = context.getSharedPreferences("CapacitorStorage", Context.MODE_PRIVATE);
        SharedPreferences.Editor editor = sharedPreferences.edit();

        // Get the saved logs as a string
        String savedString = sharedPreferences.getString("debug-log", "[]");

        // Convert the saved string to a JSON array
        JSONArray logs;
        try {
            logs = new JSONArray(savedString);
        } catch (Exception e) {
            logs = new JSONArray();
        }

        // Add the new log entry to the logs array
        logs.put(data);

        // Save the updated logs array back to shared preferences
        editor.putString("debug-log", logs.toString());
        editor.apply();
    }

    void startAgoraCall(String additionalData, Context context) {
        try {
            Log.d(TAG, "data: " + additionalData);
            JSONObject jsonObject = new JSONObject(additionalData);
//      SharedPreferences sharedPreferences = context.getSharedPreferences("CapacitorStorage", Context.MODE_PRIVATE);
//      String userString = sharedPreferences.getString("user", "{}");
//      JSONObject userData = new JSONObject(userString);
//            room: data.cname,
//            uid: 0,
//            user: userCall as any,
//            token: data.token,
//            appId: data.appid,
//            mode: 'audio',
//            authId: user.value._id as string
            String env = jsonObject.optString("env");
            if(env.equals("sandbox") || env.equals("production")) {
                SocketManager.INSTANCE.setEnv(env);
            }
            SocketManager.INSTANCE.initialize();
            String mode = jsonObject.optString("mode");
            ApiCallback.INSTANCE.setUrl(jsonObject.getString("callback"));
            ApiCallback.INSTANCE.setData(jsonObject);
            JSONObject dataJoinChannel = new JSONObject();
            dataJoinChannel.put("token", jsonObject.optString("token"));
            dataJoinChannel.put("appid", jsonObject.optString("appid"));
            dataJoinChannel.put("mode", mode);
            dataJoinChannel.put("channel", jsonObject.optString("conversation"));
            Object remaining = jsonObject.opt("remaining");
            if (remaining instanceof Integer) {
                dataJoinChannel.put("duration", remaining);
            } else if (remaining instanceof Double) {
                dataJoinChannel.put("duration", ((Double) remaining).intValue());
            }
            JSONObject toUser = jsonObject.optJSONObject("to");
            JSONObject fromUser = jsonObject.optJSONObject("from");

            JSONObject localUser = new JSONObject();
            JSONObject remoteUser = new JSONObject();

            JSONObject provider = jsonObject.optJSONObject("provider");
            JSONObject patient = jsonObject.optJSONObject("patient");
            String providerName = String.format("%s %s", provider.optString("title"), provider.optString("name"));
            if (fromUser.optString("role").equals("doctor")) {
//        dataJoinChannel.put("to", jsonObject.optJSONObject("provider"));
                remoteUser.put("name", providerName);
                remoteUser.put("_id", fromUser.optString("user"));
                remoteUser.put("avatar", provider.optJSONObject("avatar").optString("url"));

                localUser.put("name", patient.optString("name"));
                localUser.put("_id", toUser.optString("user"));
                localUser.put("avatar", patient.optJSONObject("avatar").optString("url"));
            } else {
                remoteUser.put("name", patient.optString("name"));
                remoteUser.put("_id", fromUser.optString("user"));
                remoteUser.put("avatar", patient.optJSONObject("avatar").optString("url"));

                localUser.put("name", providerName);
                localUser.put("_id", toUser.optString("user"));
                localUser.put("avatar", provider.optJSONObject("avatar").optString("url"));
            }
            dataJoinChannel.put("from", localUser);
            dataJoinChannel.put("to", remoteUser);
            JSONObject log = new JSONObject();
            log.put("message", "join agora channel - app opened");
            log.put("context", "CONFERENCE");
            log.put("data", dataJoinChannel);
            addNativeLog(this.getApplicationContext(), log);
            if (mode.equals("app")) {
//        Intent i = new Intent(context, VoiceCallAgoraActivity.class);
//        i.putExtra("joinroom", dataJoinChannel.toString());
//        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                Intent i = new Intent(context, VoiceCallAcitivity.class);
                i.putExtra("data", dataJoinChannel.toString());
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(i);
            } else {
//        Intent i = new Intent(context, VideoCallAgoraActivity.class);
//        i.putExtra("joinroom", dataJoinChannel.toString());
//        i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                Intent i = new Intent(context, VideoCallActivity.class);
                i.putExtra("data", dataJoinChannel.toString());
                i.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                context.startActivity(i);
            }
        } catch (JSONException e) {
            e.printStackTrace();
//      throw new RuntimeException(e);
        }
    }
}
