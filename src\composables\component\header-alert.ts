export interface IMessageHeaderAlert {
    severity: 'warn' | 'error' | 'success'
    content: string
    id?: string
    life?: number
    icon?: string
    closeIcon?: string
    closable?: boolean
}
export const useHeaderAlert = createGlobalState(() => {
    const messages = reactive(new Map<string, IMessageHeaderAlert>())

    const add = (msg: IMessageHeaderAlert) => {
        msg.id = msg.id || `${Date.now()}${messages.size}`
        messages.set(msg.id, msg)
        return msg.id
    }
    const update = (msg: IMessageHeaderAlert & { id: string }) => {
        messages.set(msg.id, msg)
        return msg.id
    }
    const remove = (id: string) => {
        messages.delete(id)
        return id
    }
    const clear = () => {
        messages.clear()
    }

    const getLast = (size: number) => {
        const messagesArray = Array.from(messages.values()); // Chuyển Map thành mảng
        return messagesArray.slice(size); // Lấy 3 phần tử cuối cùng
    };

    return {
        messages,
        add,
        update,
        remove,
        clear,
        getLast,
    }
})
