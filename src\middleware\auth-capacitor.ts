import {
    createError,
    defineNuxtRouteMiddleware,
    navigateTo,
    useAppAuth,
    usePreferences,
} from '#imports'

export default defineNuxtRouteMiddleware(async (to) => {
    const { checkAuthStatus } = useAppAuth()
    const { set } = usePreferences()

    try {
        const isLogged = await checkAuthStatus()

        if (!isLogged) {
            await set({ key: 'intendedRoute', value: to.fullPath })
            return navigateTo('/')
        }
    } catch (e: any) {
        throw createError({
            statusCode: 500,
            message: e || e?.message || 'Expect error',
        })
    }
})
