<template>
    <div>
        <div
            ref="topEl"
            :style="computedHeight"
            :class="
                isOnTopOfPage
                    ? 'bg-white shadow-none dark:bg-black'
                    : 'bg-surface-50 shadow dark:bg-surface-800'
            "
            class="sticky left-0 right-0 top-0 z-40 overflow-hidden transition-all duration-300 sm:hidden"
            @click="y = 0"
        >
            <div ref="headerEl" class="flex flex-row px-4 pb-4 pt-6">
                <slot name="headerTop" />
            </div>

            <Transition
                appear
                enter-active-class="transition-opacity delay-100 duration-500"
                leave-active-class="transition-opacity"
                enter-from-class="opacity-0"
                leave-to-class="opacity-0"
            >
                <div v-show="isTabsVisible" ref="headerBottomEl" class="w-full">
                    <slot name="headerBottom" />
                </div>
            </Transition>
        </div>
        <div
            class="mx-auto h-[3px] w-40 rounded-full bg-surface-300 sm:hidden"
        />
        <div ref="mainEl" :class="[activeTopMargin, 'p-4']">
            <slot name="main" />
        </div>
    </div>
</template>
<script setup lang="ts">
//import statements
import { computed, nextTick, onMounted, type Ref, ref, watch } from '#imports'
import {
    useElementSize,
    useSwipe,
    useWindowScroll,
    useWindowSize,
} from '@vueuse/core'

//element references
const topEl = ref<HTMLElement | null>(null)
const mainEl = ref<HTMLElement | null>(null)
const headerEl = ref<HTMLElement | null>(null)
const headerBottomEl = ref<HTMLElement | null>(null)

//vueuse
const { y } = useWindowScroll()
const { isSwiping: isTopSwiping, direction: topSwipeDirection } = useSwipe(
    topEl,
    { threshold: 1 },
)
const { isSwiping: isMainSwiping, direction: mainDirection } = useSwipe(
    mainEl,
    { threshold: 30 },
)
const { height: topElHeight } = useElementSize(topEl)
const { width } = useWindowSize()

//constant
const INITIAL_HEIGHT: Ref<string> = ref()
const FOCUS_HEIGHT: Ref<string> = ref()

//computed values
const isOnTopOfPage = computed(() => y.value <= 0)
const computedHeight = computed(() => ({ height: `${activeHeight.value}` }))

//reactive states
const activeTopMargin = computed(() => `mt-${topElHeight.value}`)
const activeHeight = ref()
const isTabsVisible = ref(true)

//watchers
watch(
    activeHeight,
    () => (isTabsVisible.value = activeHeight.value == FOCUS_HEIGHT.value),
)

// watch for swipes
watch(isMainSwiping, () => {
    if (!isMainSwiping.value) return
    if (mainDirection.value == 'down' && isOnTopOfPage.value)
        activeHeight.value = FOCUS_HEIGHT.value
    if (mainDirection.value == 'up') activeHeight.value = INITIAL_HEIGHT.value
})

watch(isTopSwiping, () => {
    if (!isTopSwiping.value) return
    if (topSwipeDirection.value == 'down' && isOnTopOfPage.value)
        activeHeight.value = FOCUS_HEIGHT.value

    if (topSwipeDirection.value == 'up')
        activeHeight.value = INITIAL_HEIGHT.value
})

//watch if on top of page
watch(isOnTopOfPage, () => {
    if (!isOnTopOfPage.value) activeHeight.value = INITIAL_HEIGHT.value
})

watch(width, () => updateHeights())

// Function to update heights
const updateHeights = () => {
    if (headerEl.value && headerBottomEl.value) {
        INITIAL_HEIGHT.value = `${headerEl.value.offsetHeight}px`
        FOCUS_HEIGHT.value = `${headerEl.value.offsetHeight + headerBottomEl.value.offsetHeight}px`
        activeHeight.value = INITIAL_HEIGHT.value
    }
}

// Set up initial heights after component is mounted and slots are populated
onMounted(() => {
    nextTick(() => {
        updateHeights()
    })
})
</script>
