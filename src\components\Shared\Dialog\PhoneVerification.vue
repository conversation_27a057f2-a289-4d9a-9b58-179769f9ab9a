<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import Phone from '../Auth/Onboarding/Phone.vue'
import VerifyPhone from '../Auth/Onboarding/VerifyPhone.vue'
import { wellcareUrl } from '~/constants'
import { useWindowSize } from '@vueuse/core'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

defineProps<{
    visible: boolean
    userId: string
}>()

const emit = defineEmits<{
    'update:visible': [value: boolean]
    close: []
    'verification-complete': [
        data: { phone: string; countryCode: string; otp: string },
    ]
}>()

const { user } = useUserInfo({ scope: '_id' })

const step = ref<'phone' | 'verify'>('phone')
const componentRef = ref()
const otpSentSuccess = ref(false)

// Centralized state
const verificationData = ref<{
    phone: string
    countryCode: string
    otp: string
}>({
    phone: '',
    countryCode: '',
    otp: '',
})
const popoverLang = ref(false)
const support = ref(false)
const checkPhoneData = ref()
const errorSendOtpMessage = ref('')
const typeVerify = ref<'update' | 'merge'>('update')

const { t } = useI18n()
const { $fetchWellcare } = useNuxtApp()
const { account } = useAppAuth()
const { height } = useWindowSize()
const { checkPhone } = useOnboarding()

const isSmallScreen = computed(() => height.value < 750)
const isNative = computed(() => Capacitor.isNativePlatform())
const avatarUrl = computed(() => checkPhoneData.value?.avatar?.url || '')

const {
    execute: executeMergeUser,
    error: errorMergeUser,
    status: statusMergeUser,
} = useAsyncData(
    'verification-data',
    () => {
        return $fetchWellcare(
            wellcareUrl.mergeUser(account.value?.profile?.sub as string),
            {
                method: 'POST',
                body: verificationData.value,
            },
        )
    },
    {
        immediate: false,
    },
)

const {
    execute: executeUpdatePhone,
    error: errorUpdatePhone,
    status: statusUpdatePhone,
} = useAsyncData(
    'verification-data',
    () => {
        return $fetchWellcare(wellcareUrl.updatePhone(user.value?._id), {
            method: 'PUT',
            body: verificationData.value,
        })
    },
    {
        immediate: false,
    },
)

const {
    execute: executeSendOtp,
    error: errorSendOtp,
    status: statusSendOtp,
} = useAsyncData(
    'verification-data',
    () => {
        return $fetchWellcare(wellcareUrl.sendOtp(), {
            method: 'POST',
            body: {
                phone: verificationData.value.phone,
                countryCode: verificationData.value.countryCode,
            },
        })
    },
    {
        immediate: false,
    },
)

const isLoading = computed(
    () =>
        statusMergeUser.value === 'pending' ||
        statusSendOtp.value === 'pending' ||
        statusUpdatePhone.value === 'pending',
)

const utilityLinks = computed(() => [
    {
        label: t('auth.support'),
        href: '#',
        command: handleSupport,
        icon: 'material-symbols:support-agent',
    },
    {
        label: t('auth.language'),
        href: '#',
        command: togglePopoverLang,
        icon: 'material-symbols:translate',
    },
])

const togglePopoverLang = () => {
    popoverLang.value = true
}

const handleSupport = () => {
    support.value = true
}

// Single submit handler for both steps
const handleSubmit = async () => {
    try {
        const isValid = await componentRef.value?.submit()
        if (!isValid) return

        if (step.value === 'phone') {
            const { data, success } = await checkPhone({
                t: t,
                phone: verificationData.value.phone,
                countryCode: verificationData.value.countryCode,
                disabledNotification: true,
            })

            const code = data?.value?.code

            if (success || data?.value) {
                await executeSendOtp()

                if (errorSendOtp.value) {
                    errorSendOtpMessage.value = t(
                        errorSendOtp.value.data.message,
                    )
                    otpSentSuccess.value = false
                } else {
                    errorSendOtpMessage.value = ''
                    otpSentSuccess.value = true
                }
                checkPhoneData.value = data?.value.results

                switch (code) {
                    case 200:
                        typeVerify.value = 'merge'
                        break
                    case 404:
                        typeVerify.value = 'update'
                        break
                    default:
                        break
                }

                step.value = 'verify'
            }
        }
    } catch (error) {
        console.error(error)
    }
}

// Event handlers for component emissions
const onPhoneSubmit = (data: { countryCode: string; phoneNumber: string }) => {
    verificationData.value = {
        ...verificationData.value,
        countryCode: data.countryCode,
        phone: data.phoneNumber,
    }
}

const onOtpSubmit = async (otp: string) => {
    verificationData.value.otp = otp
    const noti = push.promise(t('phone-verification.verify.loading'))
    try {
        switch (typeVerify.value) {
            case 'merge':
                await executeMergeUser()
                if (errorMergeUser.value) {
                    throw errorMergeUser.value
                }
                break
            case 'update':
                await executeUpdatePhone()
                if (errorUpdatePhone.value) {
                    throw errorUpdatePhone.value
                }
                break
            default:
                break
        }

        emit('verification-complete', verificationData.value)
        emit('update:visible', false)
        resetDialog()
        noti.success({
            message: t('phone-verification.verify.success'),
            duration: 3000,
        })
    } catch (error: any) {
        console.error(error)
        const errorMessages = {
            'otp not match': t('otp-not-match'),
            'otp is used': t('otp-is-used'),
            'otp was expired': t('otp-was-expired'),
            'missing otp': t('missing-otp'),
        }
        noti.error({
            message: t(
                errorMessages[error?.data?.message as ErrorMessageKey] ??
                    error?.data?.message ??
                    'phone-verification.verify.error',
            ),
            duration: 3000,
        })
    }
}

const resetDialog = () => {
    step.value = 'phone'
    otpSentSuccess.value = false
    verificationData.value = {
        phone: '',
        countryCode: '',
        otp: '',
    }
}

const handleHide = () => {
    emit('close')
    emit('update:visible', false)
    resetDialog()
}

const handleBack = () => {
    step.value = 'phone'
}

const handleClose = () => {
    emit('update:visible', false)
    resetDialog()
    emit('close')
}

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)

// Add type for error messages
type ErrorMessageKey =
    | 'otp not match'
    | 'otp is used'
    | 'otp was expired'
    | 'missing otp'
</script>

<template>
    <Dialog
        :visible="visible"
        :modal="true"
        :closable="false"
        class="p-dialog-maximized w-full max-w-lg"
        :pt="{
            content: { class: 'p-0' },
        }"
        @hide="handleHide"
    >
        <template #header>
            <div class="flex w-full items-center justify-between">
                <div class="flex w-full items-center gap-2">
                    <Button
                        icon="pi pi-angle-left"
                        text
                        rounded
                        :disabled="isLoading"
                        @click="handleClose"
                    />
                </div>

                <div class="flex items-center gap-2">
                    <Button
                        v-for="(link, index) in utilityLinks"
                        :key="index"
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-white/80 text-gray-700 shadow-sm backdrop-blur-md transition-all hover:bg-gray-100 hover:shadow-md"
                        text
                        rounded
                        @click="link.command"
                    >
                        <iconify-icon :icon="link.icon" class="text-xl" />
                        <span class="sr-only">{{ link.label }}</span>
                    </Button>
                </div>
            </div>
        </template>

        <div
            class="flex h-full flex-col items-center justify-between"
            :class="{ '!h-[150vh]': isSmallScreen }"
        >
            <Avatar
                shape="circle"
                image="/logo.svg"
                alt="Logo"
                class="mb-2 h-auto w-14 drop-shadow-lg"
            />
            <div class="relative flex-1 px-4">
                <transition name="fade" mode="out-in">
                    <Phone
                        v-if="step === 'phone'"
                        ref="componentRef"
                        :title="t('phone-verification.phone.title')"
                        :description="t('phone-verification.phone.description')"
                        :placeholder="t('phone-verification.phone.placeholder')"
                        :use-store="false"
                        @phone-submit="onPhoneSubmit"
                    />
                    <div v-else class="flex w-full flex-col items-center">
                        <VerifyPhone
                            ref="componentRef"
                            :title="t('phone-verification.verify.title')"
                            :phone-number="verificationData.phone"
                            :avatar="avatarUrl"
                            :use-store="false"
                            @otp-submit="onOtpSubmit"
                        />
                    </div>
                </transition>
            </div>

            <div></div>

            <div
                class="fixed bottom-0 left-0 right-0 w-full space-y-4 bg-white px-4"
                :class="{
                    'pb-safe': isNative,
                    'pb-4': !isNative,
                }"
            >
                <Button
                    :label="t('phone-verification.continue')"
                    class="w-full"
                    :loading="isLoading"
                    :disabled="isLoading"
                    @click="handleSubmit"
                />

                <Button
                    v-if="step === 'verify'"
                    class="w-full"
                    :label="t('phone-verification.back')"
                    text
                    :disabled="isLoading"
                    :loading="isLoading"
                    @click="handleBack"
                />
            </div>
        </div>

        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="support"
        />
        <SharedPopoverLang v-model:visible="popoverLang" />
    </Dialog>
</template>

<i18n lang="json">
{
    "en": {
        "phone-verification": {
            "title": "Phone Verification",
            "continue": "Continue",
            "cancel": "Cancel",
            "back": "Back",
            "phone": {
                "title": "Verify your phone number",
                "description": "Enter your active phone number below:",
                "placeholder": "Enter phone number"
            },
            "verify": {
                "title": "Verify Phone Number with OTP",
                "loading": "Verifying phone number...",
                "success": "Phone number verified successfully",
                "error": "Failed to verify phone number",
                "otp-sent": "OTP has been sent to {phone}. You will receive a call shortly.",
                "call-support": "For assistance, please contact our support hotline: 1800-XXXX"
            }
        },
        "You are limited to requesting 1 otp in a day": "You are limited to requesting 1 otp in a day",
        "otp-not-match": "OTP does not match",
        "otp-is-used": "OTP has already been used",
        "otp-was-expired": "OTP has expired",
        "missing-otp": "OTP is required"
    },
    "vi": {
        "phone-verification": {
            "title": "Xác thực Số điện thoại",
            "continue": "Tiếp tục",
            "cancel": "Huỷ",
            "back": "Quay lại",
            "phone": {
                "title": "Xác thực số điện thoại",
                "description": "Nhập số điện thoại đang sử dụng bên dưới:",
                "placeholder": "Nhập số điện thoại"
            },
            "verify": {
                "title": "Xác thực số điện thoại bằng OTP",
                "loading": "Xác thực số điện thoại...",
                "success": "Xác thực số điện thoại thành công",
                "error": "Xác thực số điện thoại thất bại",
                "otp-sent": "OTP đã được gửi đến {phone}. Bạn sẽ nhận được cuộc gọi trong giây lát.",
                "call-support": "Để được hỗ trợ, vui lòng liên hệ tổng đài: 1800-XXXX"
            }
        },
        "You are limited to requesting 1 otp in a day": "Chỉ được yêu cầu 1 OTP trong ngày",
        "otp-not-match": "Mã OTP không đúng",
        "otp-is-used": "Mã OTP đã được sử dụng",
        "otp-was-expired": "Mã OTP đã hết hạn",
        "missing-otp": "Vui lòng nhập mã OTP"
    }
}
</i18n>
