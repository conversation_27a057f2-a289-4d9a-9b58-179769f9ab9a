<script setup lang="ts">
import { yupResolver } from '@primevue/forms/resolvers/yup'
import * as yup from 'yup'

interface Props {
    isLoading?: boolean
    redirectBack?: string
}

const props = withDefaults(defineProps<Props>(), {
    isLoading: false,
    redirectBack: '',
})

const emit = defineEmits<{
    'on:submit': [value: string]
}>()

const { t } = useI18n()

const password = ref('')
const confirmPassword = ref('')
const showPassword = ref(false)
const showConfirmPassword = ref(false)

const schema = yup.object().shape({
    password: yup
        .string()
        .min(6, t('validation.password-min'))
        .required(t('validation.password-required')),
    confirmPassword: yup
        .string()
        .oneOf([yup.ref('password')], t('validation.passwords-must-match'))
        .required(t('validation.confirm-password-required')),
})

const resolver = yupResolver(schema)

const initialValues = {
    password: '',
    confirmPassword: '',
}

const togglePassword = (field: 'password' | 'confirmPassword') => {
    if (field === 'password') {
        showPassword.value = !showPassword.value
    } else {
        showConfirmPassword.value = !showConfirmPassword.value
    }
}

const onFormSubmit = ({ valid, values }: any) => {
    if (valid) {
        emit('on:submit', values.password)
    }
}

const back = () => {
    if (props.redirectBack) {
        navigateTo(String(props.redirectBack), {
            external: true,
        })
    }
}
</script>

<template>
    <Form
        v-slot="$form"
        :initial-values="initialValues"
        :resolver="resolver"
        class="mt-2 flex flex-col gap-8"
        @submit="onFormSubmit"
    >
        <h6 class="self-center font-semibold">
            {{ t('password-setting') }}
        </h6>
        <div>
            <FloatLabel>
                <IconField class="w-full">
                    <InputText
                        id="password"
                        v-model="password"
                        name="password"
                        :type="showPassword ? 'text' : 'password'"
                        class="w-full"
                        size="large"
                        :class="{ 'p-invalid': $form.password?.invalid }"
                    />
                    <InputIcon
                        :class="showPassword ? 'pi pi-eye-slash' : 'pi pi-eye'"
                        @click="togglePassword('password')"
                        style="cursor: pointer"
                    />
                </IconField>
                <label for="password">{{ t('enter-password') }}</label>
            </FloatLabel>
            <Message
                v-if="$form.password?.invalid"
                severity="error"
                size="small"
                variant="simple"
                >{{ $form.password.error.message }}</Message
            >
        </div>

        <div>
            <FloatLabel>
                <IconField class="w-full">
                    <InputText
                        id="confirmPassword"
                        v-model="confirmPassword"
                        name="confirmPassword"
                        :type="showConfirmPassword ? 'text' : 'password'"
                        class="w-full"
                        size="large"
                        :class="{
                            'p-invalid': $form.confirmPassword?.invalid,
                        }"
                    />
                    <InputIcon
                        :class="
                            showConfirmPassword
                                ? 'pi pi-eye-slash'
                                : 'pi pi-eye'
                        "
                        @click="togglePassword('confirmPassword')"
                        style="cursor: pointer"
                    />
                </IconField>
                <label for="confirmPassword">{{ t('confirm-password') }}</label>
            </FloatLabel>
            <Message
                v-if="$form.confirmPassword?.invalid"
                severity="error"
                size="small"
                variant="simple"
                >{{ $form.confirmPassword.error.message }}</Message
            >
        </div>
        <div class="space-y-4">
            <Button
                type="submit"
                :disabled="isLoading"
                :loading="isLoading"
                class="w-full"
                :label="t('continue')"
            />
            <Button
                v-if="redirectBack"
                outlined
                severity="secondary"
                :disabled="isLoading"
                :loading="isLoading"
                class="w-full"
                :label="t('back')"
                @click="back"
            />
        </div>
    </Form>
</template>

<i18n lang="json">
{
    "en": {
        "password-setting": "Password setting",
        "enter-password": "Enter password",
        "confirm-password": "Confirm password",
        "continue": "Continue",
        "back": "Back",
        "validation": {
            "password-required": "Password is required",
            "password-min": "Password must be at least 6 characters",
            "confirm-password-required": "Please confirm your password",
            "passwords-must-match": "Passwords must match"
        }
    },
    "vi": {
        "password-setting": "Cài mật khẩu",
        "enter-password": "Nhập mật khẩu",
        "confirm-password": "Nhập lại mật khẩu",
        "continue": "Tiếp tục",
        "back": "Quay lại",
        "validation": {
            "password-required": "Vui lòng nhập mật khẩu",
            "password-min": "Mật khẩu phải có ít nhất 6 ký tự",
            "confirm-password-required": "Vui lòng xác nhận mật khẩu",
            "passwords-must-match": "Mật khẩu không khớp"
        }
    }
}
</i18n>
