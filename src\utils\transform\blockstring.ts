// utils/transform/blockstring.ts
import type { IHitNotion } from '../../models'

export const blockstring = (item: IHitNotion): IHitNotion => {
    try {
        ;(item as any).page.blocks = Object.values(
            JSON.parse(JSON.parse(item.blockstring as string)),
        )
        delete item.blockstring
    } catch {
        ;(item as any).page = {
            ...item.page,
            blocks: [],
        }
    }
    return item
}
