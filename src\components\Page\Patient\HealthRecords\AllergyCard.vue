<script setup lang="ts">
import type { IAllergy } from '~/models/page/health-records'

interface Props {
    allergy: IAllergy
    detailed?: boolean
}

withDefaults(defineProps<Props>(), {
    detailed: false,
})
</script>

<template>
    <div class="card flex flex-row items-center gap-2">
        <p class="font-semibold">{{ allergy.name }}</p>
        <Tag
            v-if="allergy.severity == 'Mild'"
            :value="allergy.severity"
            severity="secondary"
        />
        <Tag
            v-else-if="allergy.severity == 'Moderate'"
            :value="allergy.severity"
            severity="warning"
        />
        <Tag
            v-else-if="allergy.severity == 'Severe'"
            :value="allergy.severity"
            severity="danger"
        />
        <iconify-icon
            v-if="detailed"
            icon="material-symbols:edit-rounded"
            class="ml-auto text-slate-500"
        />
    </div>
</template>
