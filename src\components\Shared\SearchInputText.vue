<script setup lang="ts">
import { useI18n } from '#imports'

const { t } = useI18n()

const onKeydown = (event: any) => {
    if (event.key == 'Enter') {
        submit()
    }
}
const submit = () => {
    emit('execute')
}

const attrs = useAttrs()
const placeholder: ComputedRef<string> = computed(
    () => (attrs.placeholder as string) || t('search'),
)

defineProps<{
    modelValue: string
}>()

const emit = defineEmits(['update:modelValue', 'execute'])

const updateModelValue = (event: Event) => {
    const payload = (event.target as HTMLInputElement).value
    emit('update:modelValue', payload)
}
</script>

<template>
    <IconField icon-position="right" class="mx-auto">
        <InputIcon
            class="pi pi-search !-mt-3.5 cursor-pointer rounded-full bg-surface-100 p-1.5 transition-all hover:brightness-95 dark:bg-surface-700"
            @click="submit()"
        />
        <InputText
            :value="modelValue"
            :placeholder="placeholder"
            size="large"
            class="ml-1 min-w-[324px]"
            @input="updateModelValue"
            @keydown="onKeydown"
        />
    </IconField>
</template>
