<script setup lang="ts">
import { usePermission } from '#imports'
import { Capacitor } from '@capacitor/core'
import {
  NativeSettings,
  AndroidSettings,
  IOSSettings,
} from 'capacitor-native-settings'

const _platform = Capacitor.getPlatform()
const { permissions } = usePermission()

const model = defineModel<boolean>()

const openAndroidSetting = async (setting: AndroidSettings) => {
  NativeSettings.openAndroid({ option: setting })
}

const openIosSetting = async (setting: IOSSettings) => {
  NativeSettings.openIOS({ option: setting })
}
</script>

<template v-if="model">
  <Dialog
    v-model:visible="model"
    :style="{ width: '20rem' }"
    :modal="true"
    position="center"
    :draggable="false"
    aria-label="permissions"
  >
    <p><span class="font-semibold">Camera:</span> {{ permissions.camera }}</p>

    <p>
      <span class="font-semibold">Micro:</span> {{ permissions.microphone }}
    </p>

    <p>
      <span class="font-semibold">Push:</span> {{ permissions.notifications }}
    </p>

    <template
      v-if="
        permissions.camera !== 'granted' || permissions.microphone !== 'granted'
      "
    >
      <span class="text-surface-500 dark:text-surface-400 mb-8 block"
        >Camera or Microphone was denied</span
      >
      <div class="flex justify-end gap-2">
        <Button
          v-if="_platform === 'android'"
          type="button"
          label="Allow"
          severity="secondary"
          @click="openAndroidSetting(AndroidSettings.ApplicationDetails)"
        />
        <Button
          v-if="_platform === 'ios'"
          type="button"
          label="Allow"
          severity="secondary"
          @click="openIosSetting(IOSSettings.App)"
        />
      </div>
    </template>

    <template v-else-if="permissions.notifications !== 'granted'">
      <span class="text-surface-500 dark:text-surface-400 mb-8 block"
        >Push notification was denied</span
      >
      <div class="flex justify-end gap-2">
        <Button
          v-if="_platform === 'android'"
          type="button"
          label="Allow"
          severity="secondary"
          @click="openAndroidSetting(AndroidSettings.ApplicationDetails)"
        />
        <Button
          v-if="_platform === 'ios'"
          type="button"
          label="Allow"
          severity="secondary"
          @click="openIosSetting(IOSSettings.App)"
        />
      </div>
    </template>
  </Dialog>
</template>
