<script setup lang="ts">
// import { definePageMeta } from '#imports'
import BMICard from '~/components/Page/Patient/HealthRecords/BMICard.vue'
import { useBodyIndex } from '~/composables/page/patient/health-records/body-index'
import { useVitalSigns } from '~/composables/page/patient/health-records/vital-signs'

definePageMeta({
    //   middleware: 'auth-capacitor',
    layout: 'nested-page',
})

const { bodyIndexMapped } = useBodyIndex()
const { vitalSigns } = useVitalSigns()
const layoutStore = useLayoutStore()

onMounted(() => layoutStore.setTitle('Vital signs'))
</script>

<template>
    <div class="px-6 pb-6 pt-6">
        <h4>Vital Signs</h4>
        <div class="grid grid-flow-row grid-cols-2 gap-3">
            <PagePatientHealthRecordsVitalSignCard
                v-for="vitalSign in vitalSigns"
                :key="vitalSign.label"
                :vital-sign="vitalSign"
            />
        </div>
        <div class="flex flex-col">
            <Button label="Update" class="mt-3 self-end" />
        </div>
        <h4 class="mt-8">Body Index</h4>
        <BMICard :body-index-mapped="bodyIndexMapped" />
        <div class="flex flex-col">
            <Button label="Update" class="mt-3 self-end" />
        </div>
    </div>
</template>
