<script setup lang="ts">
import type { IVaccination } from '~/models/page/health-records'

interface Props {
    detailed?: boolean
    edit?: boolean
    vaccination: IVaccination
}

withDefaults(defineProps<Props>(), { detailed: false, edit: false })
</script>

<template>
    <button v-if="!detailed" class="card text-start">
        <div class="flex flex-row justify-between">
            <p class="m-0 grow text-xl font-semibold">{{ vaccination.name }}</p>

            <p class="font-bold">
                {{ vaccination.doses
                }}<span class="font-normal text-slate-400"> / </span
                >{{ vaccination.requiredDoses }}
            </p>
        </div>
    </button>
    <button v-else class="card flex flex-col text-start">
        <div class="flex w-full flex-row justify-between gap-2">
            <p class="m-0 grow text-xl font-semibold">{{ vaccination.name }}</p>

            <p class="font-bold">
                {{ vaccination.doses
                }}<span class="font-normal text-slate-400"> / </span
                >{{ vaccination.requiredDoses }}
            </p>
        </div>
        <p>Last Dose: {{ vaccination.lastDoseDate }}</p>
        <p>
            Scheduled Doses:
            {{
                vaccination.scheduledDoses.length > 0
                    ? vaccination.scheduledDoses.map((s) => s.date).join(', ')
                    : 'None'
            }}
        </p>
        <Button
            size="small"
            class="self-end"
            icon="pi pi-pencil"
            label="Edit"
            severity="secondary"
        />
    </button>
</template>
