export enum GenericEndpoint {
    // CONSULTATION-SERVER
    CONSULTATION = '/consultation-server/generic/consultation',
    PROVIDER = '/consultation-server/generic/provider',
    RATING = '/consultation-server/generic/rating',
    PRESCRIPTION = '/consultation-server/generic/prescription',
    DIAGNOSIS = '/consultation-server/generic/diagnosis',
    PATIENTNOTE = '/consultation-server/generic/patientnote',

    //   PHR
    VACCINATION = '/consultation-server/generic/vaccination',
    OBSERVATION = '/consultation-server/generic/observation',

    // CALENDAR
    CALENDAR = '/calendar/calendar',
    CALENDAR_ITEM = '/calendar/calendar-item',

    // PERSONAL DOCTOR
    PERSONAL_DOCTOR = '/user-management/personal-doctor/user',
    PERSONAL_DOCTOR_USER_DOCTOR_RELATIONSHIP = '/user-management/personal-doctor/user/doctor',
    PERSONAL_DOCTOR_DOCTOR_PATIENTS_RELATIONSHIP = '/user-management/personal-doctor/doctor/patient',

    // PUSH
    NOTIFICATION = '/notification',

    // FORMSIBLE
    GET_FORM = '/form-parse-server/classes/Form/',
}
