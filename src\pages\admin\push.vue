<script setup lang="ts">
import { ref, usePushNotification, onMounted } from '#imports'
import { Capacitor } from '@capacitor/core'

const {
    login: loginPush,
    logout: logoutPush,
    getExternalId,
    getOnesignalId,
} = usePushNotification()

const enabled = ref(false)
const externalId = ref('')
const onesignalId = ref('')

const retriveId = async () => {
    getExternalId().then((id) => {
        console.log('retriveId', JSON.stringify({ id }))
        enabled.value = !!id
        externalId.value = id!
    })
    getOnesignalId().then((id) => (onesignalId.value = id!))
}

const onLoginPush = async () => {
    if (Capacitor.isNativePlatform()) {
        await loginPush()
        await retriveId()
    }
}

const onLogoutPush = async () => {
    if (Capacitor.isNativePlatform()) {
        await logoutPush()
        await retriveId()
    }
}

onMounted(async () => {
    await retriveId()
})
</script>

<template>
    <div
        class="flex min-h-screen items-center justify-center bg-gray-100 p-4 dark:bg-gray-900"
    >
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <!-- BackToHome -->
            <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
                <button @click="navigateTo('/debug')">
                    <span class="underline">Back to home</span>
                </button>
            </div>

            <!-- Main Info Card -->
            <div
                class="rounded-xl bg-surface-50 p-6 shadow-lg dark:bg-surface-700"
            >
                <h2
                    class="mb-4 text-center text-2xl font-bold text-surface-900"
                >
                    Push Notification
                </h2>
                <p class="mb-2 text-surface-600 dark:text-surface-400">
                    external_id: {{ externalId }}
                </p>
                <p class="mb-2 text-surface-600 dark:text-surface-400">
                    onesignal_id: {{ onesignalId }}
                </p>
                <div class="space-y-4">
                    <InputGroup>
                        <InputText
                            v-model="externalId"
                            placeholder="Enter external id"
                        />
                    </InputGroup>
                    <button
                        v-if="enabled"
                        class="w-full rounded-lg bg-red-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-red-600"
                        @click="onLogoutPush"
                    >
                        logout push
                    </button>
                    <button
                        v-else
                        class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                        @click="onLoginPush"
                    >
                        login push
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
