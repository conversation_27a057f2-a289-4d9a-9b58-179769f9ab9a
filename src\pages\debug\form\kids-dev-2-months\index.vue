<script setup lang="ts">
import kids_dev_2m from './index.json'
import { ref, reactive, onMounted } from 'vue'
// const router = useRouter()

// const headers = {
//     'X-Parse-Application-Id': 'form-parse-server',
//     'X-Parse-REST-API-Key': 'REST_API_KEY',
//     'Content-Type': 'application/json',
// }

// To debug, you need to add ?_debug=true to the query parameters .
const state = reactive({
    user: '<PERSON><PERSON><PERSON>',
    conversation: '12345678910ID',
})

const form = ref(kids_dev_2m)
onMounted(() => {
    window.addEventListener('message', (event) => {
        if (event.data.event === 'submit') {
            console.log('Form submitted:', event.data.state)
        } else if (event.data.event === 'state') {
            const newState = event.data.state
            Object.assign(state, newState)
            console.log('State updated:', state)
        }
    })
})
const handleFormSubmit = () => {
    // router.push('/patient/health-records')
    // console.log('🚀 ~ handleFormSubmit ~ handleFormSubmit:', submittedState)
}
</script>

<template>
    <div class="mx-auto w-full max-w-7xl p-4 md:p-8">
        <F :form="form" :state="state" @submit="handleFormSubmit" />
    </div>
</template>
