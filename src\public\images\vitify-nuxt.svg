<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 37 37" fill="none">
  <g transform="rotate(180 18.778 13.7)">
    <path class="spa-loading-path"
      d="M24.236 22.006h10.742L25.563 5.822l-8.979 14.31a4 4 0 0 1-3.388 1.874H2.978l16-27.713 6 10.392" />
  </g>
  <style>
    .spa-loading-path {
      fill: none;
      stroke: #248fe4;
      stroke-width: 4px;
      stroke-linecap: round;
      stroke-linejoin: round;
      /* Stroke-dasharray property */
      stroke-dasharray: 128;
      stroke-dashoffset: 128;
      animation: nuxt-spa-loading-move 1.5s linear;
      animation-fill-mode: forwards
    }

    @keyframes nuxt-spa-loading-move {
      100% {
        stroke-dashoffset: 0;
      }
    }
  </style>
</svg>