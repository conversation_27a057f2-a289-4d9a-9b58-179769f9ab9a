require_relative '../../node_modules/.pnpm/@capacitor+ios@6.2.0_@capacitor+core@6.2.0/node_modules/@capacitor/ios/scripts/pods_helpers'

platform :ios, '14.0'
use_frameworks!

# workaround to avoid Xcode caching of Pods that requires
# Product -> Clean Build Folder after new Cordova plugins installed
# Requires CocoaPods 1.6 or newer
install! 'cocoapods', :disable_input_output_paths => true

def capacitor_pods
  pod 'Capacitor', :path => '../../node_modules/.pnpm/@capacitor+ios@6.2.0_@capacitor+core@6.2.0/node_modules/@capacitor/ios'
  pod 'CapacitorCordova', :path => '../../node_modules/.pnpm/@capacitor+ios@6.2.0_@capacitor+core@6.2.0/node_modules/@capacitor/ios'
  pod 'CapacitorApp', :path => '../../node_modules/.pnpm/@capacitor+app@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/app'
  pod 'CapacitorBrowser', :path => '../../node_modules/.pnpm/@capacitor+browser@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/browser'
  pod 'CapacitorClipboard', :path => '../../node_modules/.pnpm/@capacitor+clipboard@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/clipboard'
  pod 'CapacitorDevice', :path => '../../node_modules/.pnpm/@capacitor+device@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/device'
  pod 'CapacitorPreferences', :path => '../../node_modules/.pnpm/@capacitor+preferences@6.0.3_@capacitor+core@6.2.0/node_modules/@capacitor/preferences'
  pod 'CapacitorSplashScreen', :path => '../../node_modules/.pnpm/@capacitor+splash-screen@6.0.3_@capacitor+core@6.2.0/node_modules/@capacitor/splash-screen'
  pod 'CapawesomeCapacitorAppUpdate', :path => '../../node_modules/.pnpm/@capawesome+capacitor-app-update@6.1.0_@capacitor+core@6.2.0/node_modules/@capawesome/capacitor-app-update'
  pod 'CapgoInappbrowser', :path => '../../node_modules/.pnpm/@capgo+inappbrowser@6.15.0_@capacitor+core@6.2.0/node_modules/@capgo/inappbrowser'
  pod 'RevenuecatPurchasesCapacitor', :path => '../../node_modules/.pnpm/@revenuecat+purchases-capacitor@10.3.8_@capacitor+core@6.2.0/node_modules/@revenuecat/purchases-capacitor'
  pod 'WellcareCapacitorAppAuth', :path => '../../node_modules/.pnpm/@wellcare+capacitor-app-auth@0.0.9_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-app-auth'
  pod 'WellcareCapacitorCallkeep', :path => '../../node_modules/.pnpm/@wellcare+capacitor-callkeep@6.0.6_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-callkeep'
  pod 'WellcareCapacitorEditor', :path => '../../node_modules/.pnpm/@wellcare+capacitor-editor@6.0.0_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-editor'
  pod 'WellcareCapacitorPermissions', :path => '../../node_modules/.pnpm/@wellcare+capacitor-permissions@6.0.0-permission.3_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-permissions'
  pod 'WellcareCapacitorPluginAgora', :path => '../../node_modules/.pnpm/@wellcare+capacitor-plugin-agora@6.0.6-socket_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-plugin-agora'
  pod 'WellcareCapacitorStringee', :path => '../../node_modules/.pnpm/@wellcare+capacitor-stringee@6.0.1-hangup.2_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-stringee'
  pod 'CapacitorNativeSettings', :path => '../../node_modules/.pnpm/capacitor-native-settings@6.0.6_@capacitor+core@6.2.0/node_modules/capacitor-native-settings'
  pod 'CapacitorPluginSafeArea', :path => '../../node_modules/.pnpm/capacitor-plugin-safe-area@3.0.4_@capacitor+core@6.2.0/node_modules/capacitor-plugin-safe-area'
  pod 'CapacitorScreenOrientation', :path => '../../node_modules/.pnpm/@capacitor+screen-orientation@6.0.3_@capacitor+core@6.2.0/node_modules/@capacitor/screen-orientation'
  pod 'CordovaPlugins', :path => '../capacitor-cordova-ios-plugins'
  pod 'CordovaPluginsStatic', :path => '../capacitor-cordova-ios-plugins'
  pod 'CordovaPluginsResources', :path => '../capacitor-cordova-ios-plugins'
end

target 'App' do
  capacitor_pods
  # Add your Pods here
end

post_install do |installer|
  assertDeploymentTarget(installer)
  bitcode_strip_path = `xcrun --find bitcode_strip`.chop!

  def strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
    framework_path = File.join(Dir.pwd, framework_relative_path)
    command = "#{bitcode_strip_path} #{framework_path} -r -o #{framework_path}"
    puts "Stripping bitcode: #{command}"
    system(command)
  end
  # Strip bitcode from AgoraDav1d framework
  framework_paths = [
    "Pods/AgoraRtcEngine_iOS/AgoraDav1d.xcframework/ios-arm64_armv7/AgoraDav1d.framework/AgoraDav1d",
    "Pods/AgoraRtm_iOS/AgoraRtmKit.xcframework/ios-arm64_armv7/AgoraRtmKit.framework/AgoraRtmKit"
  ]

  framework_paths.each do |framework_relative_path|
    strip_bitcode_from_framework(bitcode_strip_path, framework_relative_path)
  end
end

target 'OneSignalNotificationServiceExtension' do
  pod 'OneSignalXCFramework', '>= 5.0', '< 6.0'
end