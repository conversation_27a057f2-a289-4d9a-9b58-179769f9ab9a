<template>
    <div
        v-if="shouldRender && Capacitor.isNativePlatform()"
        class="bg-teal-100 p-4 text-primary pt-safe"
    >
        <div class="flex items-center space-x-2">
            <div class="grow">
                <div class="font-bold">
                    {{ t('app-version-title', { version: availableVersion }) }}
                </div>
                <div class="text-sm">
                    {{
                        t('app-version-subtitle', { current: appStore.version })
                    }}
                </div>
            </div>
            <div>
                <Button @click="onUpdateApp">{{ t('btn-update') }}</Button>
            </div>
        </div>
        <div v-if="isRequire" class="text-sm text-red-400">
            *{{ t('update-required') }}
        </div>
    </div>
</template>
<script setup lang="ts">
import { AppUpdate } from '@capawesome/capacitor-app-update'
import { Capacitor } from '@capacitor/core'
import { UpgradeType, useSemverCompare } from '~/composables/use-semver-compare'
import { onMounted } from '#build/imports'

const { t } = useI18n()

const availableVersion = ref('1.0.0')
const shouldRender = ref<boolean>(false)
const appStore = useAppStore()
const getCurrentAppVersion = async () => {
    const result = await AppUpdate.getAppUpdateInfo()
    if (Capacitor.getPlatform() === 'android') {
        return result.currentVersionCode
    } else {
        return result.currentVersionName
    }
}
const getAvailableAppVersion = async () => {
    const result = await AppUpdate.getAppUpdateInfo()
    if (Capacitor.getPlatform() === 'android') {
        return result.availableVersionCode
    } else {
        return result.availableVersionName
    }
}

const { upgradeType } = useSemverCompare(appStore.version, availableVersion)
const isRequire = ref<boolean>(false)

const updateApp = async () => {
    try {
        if (!Capacitor.isNativePlatform()) return
        const curr = await getCurrentAppVersion()
        const available = (await getAvailableAppVersion()) as string
        appStore.version = curr
        availableVersion.value = available

        const iosImmediateUpdate = [
            UpgradeType.Major,
            UpgradeType.Minor,
        ].includes(upgradeType.value)
        const iosFlexibleUpdate = UpgradeType.Patch === upgradeType.value
        if (Capacitor.getPlatform() === 'ios') {
            if (iosFlexibleUpdate || iosImmediateUpdate) {
                shouldRender.value = true
            }
            if (iosImmediateUpdate) isRequire.value = true
        } else if (Capacitor.getPlatform() === 'android') {
            if (Number(curr) < Number(available)) {
                shouldRender.value = true
                isRequire.value = true
            }
        }
    } catch (error) {
        console.error(error)
    }
}

const onUpdateApp = async () => {
    if (Capacitor.getPlatform() === 'ios') await AppUpdate.openAppStore()
    else if (Capacitor.getPlatform() === 'android')
        window.open('https://play.google.com/store/apps/details?id=vn.wellcare')
}

onMounted(() => {
    updateApp()
})
</script>
<i18n lang="json">
{
    "vi": {
        "app-version-title": "Đã có phiên bản mới ({version}), cập nhật ngay !",
        "app-version-subtitle": "Phiên bản hiện tại: {current}",
        "btn-update": "CẬP NHẬT",
        "update-required": "Đây là bản nâng cấp quan trọng, hãy cập nhật trước khi sử dụng"
    },
    "en": {
        "app-version-title": "New update available ({version}), download now !",
        "app-version-subtitle": "Current version: {current}",
        "btn-update": "UPDATE",
        "update-required": "This is a critical and required upgrade, please install now"
    }
}
</i18n>
