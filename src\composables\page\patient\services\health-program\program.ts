import { ElasticIndex } from '~/models'
import type { HealthProgramCard, HitNotionHealthProgram } from '~/models'

export function usePagePatientServicesHealthProgramsProgram() {
    const { hits } = useFetchElasticWithDefault<HitNotionHealthProgram>(
        ElasticIndex.NOTION_HEALTH_PROGRAM,
        {
            filters: [{ term: { 'page.properties.Status.keyword': 'Done' } }],
            _source: {
                excludes: ['blockstring'],
                includes: [
                    'page.cover.url',
                    'page.properties.Key',
                    'page.properties.Name',
                    'page.properties.Description',
                ],
            },
        },
    )

    const programs: ComputedRef<HealthProgramCard[]> = computed(() => {
        return hits.value.map((hit: HitNotionHealthProgram) => {
            return {
                cover: hit.page?.cover?.url || '',
                key: hit.page?.properties.Key || '',
                name: hit.page?.properties.Name || '',
                description: hit.page?.properties.Description || '',
            }
        })
    })

    return { programs }
}
