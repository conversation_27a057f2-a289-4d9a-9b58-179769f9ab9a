<script setup lang="ts">
import { useHead, useLocaleHead } from '#imports'
const localeHead = useLocaleHead({
    addDirAttribute: true,
    identifierAttribute: 'id',
    addSeoAttributes: true,
})

useHead({
    htmlAttrs: {
        lang: localeHead.value.htmlAttrs.lang,
        dir: localeHead.value.htmlAttrs.dir,
    },
    link: localeHead.value.link,
    meta: localeHead.value.meta,
})

</script>

<template>
    <div
        class="h-screen overflow-y-auto"
    >
        <slot />
    </div>
</template>
