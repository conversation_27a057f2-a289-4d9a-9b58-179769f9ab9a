import { afterAll, beforeAll, it } from 'vitest'
import { createPage } from '@nuxt/test-utils/e2e'
import { expect, type ElementHandle, type Page } from '@playwright/test'

// function generateRandomString(length) {
//   const characters =
//     'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
//   let result = ''
//   for (let i = 0; i < length; i++) {
//     result += characters.charAt(Math.floor(Math.random() * characters.length))
//   }
//   return result
// }

export const DescribeNavSearch = async () => {
  let page: Page
  beforeAll(async () => {
    page = await createPage('/')
  })

  it('Upon clicking the search button, the page should display the search box.', async () => {
    const page = await createPage('/')
    await page.locator('#nav-search-btn').click()
    await expect(page.locator('#nav-search-dialog')).toBeVisible()
  })

  it('The search input should be editable.', async () => {
    await page.locator('#nav-search-btn').click()
    const searchContainer = await page.$('div[data-role="nav-search-input"]')
    const inputContainer = (await searchContainer.$(
      'input[type="text"]',
    )) as ElementHandle<HTMLInputElement>
    const isEditable = await inputContainer.isEditable()
    expect(isEditable).toBe(true)
  })

  // it(
  //   "Search click or Enter should lead to 'timkiem' page with 'q' query as keyword.",
  //   async () => {
  //     const searchContainer = await page.$('div[data-role="nav-search-input"]')
  //     const inputContainer = (await searchContainer.$(
  //       'input[type="text"]',
  //     )) as ElementHandle<HTMLInputElement>
  //     const keyword = generateRandomString(6)
  //     inputContainer.fill(keyword)
  //     const action = ['click', 'enter']
  //     const randomIndex = Math.floor(Math.random() * 2)
  //     if (action[randomIndex] === 'click') {
  //       const btnSearch = await searchContainer.$(
  //         'div[class="v-field__append-inner"]',
  //       )
  //       await btnSearch.click()
  //       // page.locator('nav-search-btn').click()
  //     } else {
  //       await inputContainer.press('Enter')
  //     }
  //     const regex = new RegExp(`^\\/timkiem\\?q=${keyword}$`)
  //     await page.waitForURL('**/timkiem')
  //     // expect(page).toHaveURL(regex, { timeout: 10000 })
  //     expect(true).toBe(true)
  //   },
  //   {
  //     timeout: 15000,
  //   },
  // )
  afterAll(async () => {
    await page.close()
  })

  // it('should filter search results by entering text', async () => {
  //   await page.locator('#search-input').fill('suc khoe')
  //   await page.locator('#search-input').press('Enter')
  //   await expect(page.locator('#nav-search-dialog')).toContainText(
  //     'Filtered Results',
  //   )
  // })

  // it('should handle no search results', async () => {
  //   await page.route('/api/**', async (route) => {
  //     await route.fulfill({
  //       json: {
  //         body: {
  //           took: 0,
  //           timed_out: false,
  //           hits: {
  //             total: {
  //               value: 0,
  //               relation: 'eq',
  //             },
  //             max_score: null,
  //             hits: [],
  //           },
  //         },
  //         statusCode: 200,
  //       },
  //     })
  //   })
  //   await page.locator('#search-input').fill('nonexistent term')
  //   await page.locator('#search-input').press('Enter')
  //   await expect(page.locator('#nav-search-dialog')).toContainText(
  //     'No Results Found',
  //   )
  // })

  // it('should allow user interaction with search results', async () => {
  //   await page.route('/api/**', async (route) => {
  //     await route.fulfill({
  //       json: {
  //         body: {
  //           hits: {
  //             total: {
  //               value: 1,
  //               relation: 'eq',
  //             },
  //             hits: [
  //               {
  //                 _id: '3565aee4-f39c-409f-8121-9538fcf73841',
  //                 html: '<h1>Y tế</h1>',
  //                 page: {
  //                   id: '3565aee4-f39c-409f-8121-9538fcf73841',
  //                   properties: {
  //                     Created: '2023-09-08T08:05:00.000Z',
  //                     Keywords: [],
  //                     'Last edited time': '2024-03-11T08:48:00.000Z',
  //                     Locale: 'vi',
  //                     'Meta Description': '',
  //                     Name: 'Y tế',
  //                     Sapo: '',
  //                     Site: 'khamtuxa.wellcare.vn',
  //                     Slug: '',
  //                   },
  //                 },
  //                 _highlight: {},
  //               },
  //             ],
  //           },
  //         },
  //       },
  //     })
  //   })
  //   await page.locator('#search-input').fill('example')
  //   await page.locator('#search-input').press('Enter')
  //   await page.locator('.search-result').first().click()
  //   await expect(page.locator('#details-page')).toExist()
  // })

  // it('should navigate to the search results page on Enter', async () => {
  //   const keyword = 'example'
  //   await page.locator('#search-input').fill(keyword)
  //   await page.locator('#search-input').press('Enter')
  //   const expectedUrl = `/tim-kiem?q=${encodeURIComponent(keyword)}`
  //   await page.waitForURL(expectedUrl)
  //   const currentUrl = page.url()
  //   expect(currentUrl).toContain(expectedUrl)
  // })

  // it('should clear the search input', async () => {
  //   await page.locator('#search-input').fill('example')
  //   await page.locator('#clear-search-btn').click()
  //   await expect(page.locator('#search-input')).toHaveValue('')
  // })

  // it('should handle accessibility features', async () => {
  //   const searchInputLabel = await page
  //     .locator('#search-input-label')
  //     .innerText()
  //   expect(searchInputLabel).toContain('Search:')

  //   const searchButtonLabel = await page.locator('#nav-search-btn').innerText()
  //   expect(searchButtonLabel).toContain('Search')
  // })
}

export default DescribeNavSearch
