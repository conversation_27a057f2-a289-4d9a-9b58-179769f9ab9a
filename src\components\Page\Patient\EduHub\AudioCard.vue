<script lang="ts" setup>
import type { IItemCard } from '~/models'

defineProps({
    media: {
        type: Object as PropType<IItemCard>,
        required: true,
    },
})
</script>

<template>
    <div class="mx-2 my-4 rounded-lg bg-zinc-100/60">
        <div class="relative rounded-xl">
            <div class="flex flex-col p-4 pb-0">
                <h3 class="line-clamp-5 text-base font-medium leading-snug">
                    {{ media.name }}
                </h3>
                <div
                    class="mt-3 flex items-center justify-between text-sm text-gray-500"
                >
                    <div class="flex items-center gap-2">
                        <template v-if="media.patient.avatar">
                            <Avatar
                                size="large"
                                shape="circle"
                                class="h-7 w-7"
                                :image="media.patient.avatar"
                            />
                        </template>
                        <template v-else>
                            <div
                                class="flex h-7 w-7 items-center justify-center rounded-full bg-gray-200"
                            >
                                <iconify-icon
                                    icon="solar:user-bold"
                                />
                            </div>
                        </template>
                        <span class="text-xs">
                            {{ media.patient.name }}
                        </span>
                    </div>
                    <span class="text-xs">
                        {{ media.publishedAt }}
                    </span>
                </div>
            </div>
            <div class="mt-3 border-t border-gray-200/70">
                <div class="mt-1 flex items-center justify-between p-3">
                    <div class="flex">
                        <Avatar
                            size="large"
                            shape="circle"
                            class="rounded-ful h-12 w-12"
                            :image="media.provider.avatar"
                        />
                        <div class="ml-2">
                            <div class="text-sm font-medium text-gray-700">
                                {{
                                    media.provider.title +
                                    '. ' +
                                    media.provider.name
                                }}
                            </div>
                            <div
                                class="mt-1 inline-block rounded-lg bg-surface-100 px-2 py-1 text-xs"
                            >
                                {{ media.duration }}
                            </div>
                        </div>
                    </div>
                    <button
                        class="flex h-6 w-6 items-center justify-center rounded-full bg-primary-400"
                    >
                        <iconify-icon
                            class="text-xl text-white"
                            icon="bitcoin-icons:podcast-filled"
                        />
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>
