<script setup lang="ts">
import { useAppAuth, navigateTo } from '#imports'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'
import { appRoutes } from '~/constants'

definePageMeta({
    middleware: [
        async function() {
            const { getConfig } = useJsonConfigApp()
            const config = await getConfig()
            if(config.global?.mode === 'farewell') return navigateTo('/farewell')
            return
        },
        async function () {
            const isLogged = await useAppAuth().checkAuthStatus()
            if (isLogged) {
                return navigateTo(appRoutes.auth.callback)
            }
            return
        },
    ],
})

const { openExternalLink } = useExternalLink()

const popoverLangRef = ref<any>(null)
const support = ref(false)

// Composable để quản lý debug mode
const isDebugMode = ref(false)
const clickCount = ref(0)
const clickTimeout = ref<NodeJS.Timeout | null>(null)

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)
const mode = computed(() => config.value.global?.mode)


onMounted(() => {
    document.addEventListener('click', handleClick)
    // setTimeout(() => {
    //     if(mode.value === 'farewell') navigateTo('/farewell')        
    // }, 1500);
})

onUnmounted(() => {
    document.removeEventListener('click', () => {})
    if (clickTimeout.value) {
        clearTimeout(clickTimeout.value)
    }
})

const handleClick = (event: MouseEvent) => {
    if (event.clientX <= 100 && event.clientY <= 100) {
        if (clickTimeout.value) {
            clearTimeout(clickTimeout.value)
        }

        clickCount.value++

        clickTimeout.value = setTimeout(() => {
            clickCount.value = 0
        }, 1500)

        if (clickCount.value === 5) {
            isDebugMode.value = !isDebugMode.value
            clickCount.value = 0
        }
    }
}

const togglePopoverLang = (event: Event) => {
    if (popoverLangRef.value) {
        popoverLangRef.value.toggle(event)
    }
}

const onSupport = () => {
    support.value = true
}

const getStarter = () => {
    if (mode.value === 'farewell') navigateTo('/farewell')
    else navigateTo(appRoutes.auth.checkPhone)
    // await signIn()
}

// Debug actions
const debugRoutes = ref([
    { name: 'Sign In', path: '/auth/sign-in' },
    { name: 'Callback', path: '/auth/callback' },
    { name: 'Debug page', path: '/debug' },

    // Thêm routes khác nếu cần
])
</script>

<template>
    <div>
        <Dialog
            v-model:visible="isDebugMode"
            header="Debug Panel"
            class="p-dialog-maximized"
            :draggable="false"
            :closable="true"
        >
            <div class="space-y-4">
                <Panel header="Quick Actions" toggleable>
                    <div class="flex flex-wrap gap-2">
                        <Button
                            v-for="route in debugRoutes"
                            :key="route.path"
                            :label="'Go to ' + route.name"
                            severity="info"
                            @click="navigateTo(route.path)"
                        />
                    </div>
                </Panel>
            </div>
        </Dialog>

        <PageStarter
            @on:starter="getStarter"
            @on:language="togglePopoverLang"
            @on:support="onSupport"
            @on:terms="
                openExternalLink(
                    'https://wellcare.vn/ho-tro/dieu-khoan-su-dung',
                )
            "
            @on:policy="
                openExternalLink(
                    'https://wellcare.vn/ho-tro/chinh-sach-bao-mat',
                )
            "
        />
        <SharedPopoverLang ref="popoverLangRef" />
        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="support"
        />
    </div>
</template>
