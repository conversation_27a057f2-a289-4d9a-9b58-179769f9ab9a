<script lang="ts" setup>
import { Capacitor } from '@capacitor/core'
const router = useRouter()

const layoutStore = useLayoutStore()
const { title } = storeToRefs(layoutStore)

const isNative = computed(() => Capacitor.isNativePlatform())

const goBack = async () => {
    router.back()
}
</script>

<template>
    <div class="min-h-screen overflow-y-scroll">
        <!-- Fixed AppBar với blur effect -->
        <div
            class="fixed left-0 right-0 top-0 z-50 flex items-center border-b border-gray-200/80 bg-white/80 px-4 pb-2 backdrop-blur-md"
            :class="{ 'pt-safe': isNative, 'pt-2': !isNative }"
        >
            <Button
                rounded
                icon="pi pi-angle-left"
                aria-label="Return"
                class="!mr-3 !border-none !bg-transparent text-black hover:!bg-black/5"
                @click="goBack"
            />
            <h1 class="line-clamp-1 text-base font-medium text-gray-800">
                {{ title }}
            </h1>
        </div>

        <!-- Main Content Container -->
        <div>
            <!-- Article Content -->
            <article
                class="mx-auto h-full max-w-3xl overflow-y-auto pb-20 pt-16"
            >
                <slot />
            </article>
        </div>

        <!-- Scroll to Top Button -->

        <ScrollTop />
    </div>
</template>
