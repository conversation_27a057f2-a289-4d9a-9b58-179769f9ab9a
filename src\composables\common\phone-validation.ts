import { computed } from 'vue'
import {
    parsePhoneNumber,
    getExampleNumber,
    type CountryCode,
    formatNumber,
} from 'libphonenumber-js'
import examples from 'libphonenumber-js/examples.mobile.json'

export interface Country {
    code: CountryCode
    name: string
    dialCode: string
    flag: string
    [key: string]: any
}

type PhoneFormatter = (phone: string) => string
type PhoneValidator = (phone: string) => boolean
type ExamplePhoneGetter = (countryCode: CountryCode) => string

interface PhoneFormatComposable {
    standardPhone: ComputedRef<string>
    formatPhoneNumber: PhoneFormatter
    validatePhoneNumber: PhoneValidator
    getExamplePhone: ExamplePhoneGetter
}

export const usePhoneFormat = (
    phone: MaybeRef<string>,
    dialCode: MaybeRef<string>,
): PhoneFormatComposable => {
    const formatPhoneNumber = (inputPhone: string): string => {
        if (!inputPhone) return ''

        let cleanPhone = inputPhone.toString().replace(/[^\d+]/g, '')
        const unwrappedDialCode = unref(dialCode).replace(/\+/g, '') // Remove + from dialCode

        // Special handling for Vietnam numbers
        if (
            unwrappedDialCode === '84' &&
            cleanPhone.startsWith('84') &&
            cleanPhone.length > 9
        ) {
            return cleanPhone
        }

        // Remove dialCode if it exists at the start of the phone number
        if (cleanPhone.startsWith(unwrappedDialCode)) {
            cleanPhone = cleanPhone.slice(unwrappedDialCode.length)
        }

        // Format the full number
        const fullNumber = `+${unwrappedDialCode}${cleanPhone}`

        try {
            const formatted = formatNumber(fullNumber, 'INTERNATIONAL')
            return formatted.replace(/[^\d]/g, '') // Remove all non-digits
        } catch (error) {
            console.error('Error formatting number:', error)
            return cleanPhone
        }
    }

    const standardPhone = computed(() => {
        const rawPhone = unref(phone)
        return formatPhoneNumber(rawPhone)
    })

    const validatePhoneNumber = (inputPhone: string): boolean => {
        if (!inputPhone) return false

        try {
            const formattedPhone = formatPhoneNumber(inputPhone)
            const phoneObj = parsePhoneNumber(`+${formattedPhone}`)
            return phoneObj?.isValid() ?? false
        } catch (error) {
            console.error('Error validating number:', error)
            return false
        }
    }

    const getExamplePhone = (countryCode: CountryCode): string => {
        if (!countryCode) return ''

        try {
            return (
                getExampleNumber(countryCode, examples)?.formatNational() ?? ''
            )
        } catch (error) {
            console.error('Error getting example number:', error)
            return ''
        }
    }

    return {
        standardPhone,
        formatPhoneNumber,
        validatePhoneNumber,
        getExamplePhone,
    }
}
