<script setup lang="ts">
import { definePageMeta } from '#imports'

import { ref } from 'vue'
definePageMeta({ layout: 'nested-page', middleware: 'auth-capacitor' })

const patientData = ref({
    name: '<PERSON>',
    appointmentTime: '2:30 PM',
    symptoms: 'I have a headache',
})
</script>

<template>
    <div>
        <h1>Consultation with {{ patientData.name }}</h1>

        <p>Patient Information</p>
        <div class="p-fluid space-y-4">
            <div class="field space-x-2">
                <label for="name">Name</label>
                <InputText id="name" v-model="patientData.name" disabled />
            </div>
            <div class="field space-x-2">
                <label for="appointmentTime">Appointment Time</label>
                <InputText
                    id="appointmentTime"
                    v-model="patientData.appointmentTime"
                    disabled
                />
            </div>
            <div class="flex flex-col space-y-2">
                <label for="symptoms">Reported Symptoms</label>
                <Textarea
                    id="symptoms"
                    v-model="patientData.symptoms"
                    rows="3"
                    disabled
                />
            </div>
        </div>
    </div>
</template>
