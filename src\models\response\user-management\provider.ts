import type { IUserBase } from './user'

interface ISpecialty {
    en: {
        title: string[]
    }
    fr: {
        title: string[]
    }
    slug: string[]
    vi: {
        title: string[]
    }
}

interface IProfile {
    diagnosis?: string[]
    education?: string
    experience?: string
    experiencesDescription?: string
    portrait?: string
    startWork?: string
    tagLine?: string
}

export interface IProvider extends IUserBase {
    title?: string
    user?: string
    slug?: string
    highlight?: string
    profile?: IProfile
    minuteAvg?: number
    order?: number
    ratingGood?: number
    slot?: number[]
    specialty?: ISpecialty
    spoken?: string[]
    yearsofEXP?: number
    specialties?: string[]
    group?: string
    keyword?: string
    gender?: 'Female' | 'Male'
    averageRating?: number | string
    isPersonalDoctor?: boolean
}
