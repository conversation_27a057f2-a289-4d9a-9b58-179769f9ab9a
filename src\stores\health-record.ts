import type { Relationship } from "~/models"

interface HealthRecordState {
    memberActivated: Relationship
}

export const useHealthRecordStore = defineStore('health-record', {
    state: (): HealthRecordState => ({
        memberActivated: {},
    }),
    actions: {
        setMemberActivated(relationship: Relationship) {
            this.memberActivated = relationship
        },
        clearMemberActivated() {
            this.memberActivated = {}
        }
    }
})