<script setup lang="ts">
import { useLifestyle } from '~/composables/page/patient/health-records/lifestyle'
const { lifeStyle } = useLifestyle()
interface Props {
    title: string
    to: string
}

defineProps<Props>()
</script>

<template>
    <div>
        <SharedPageSection
            v-if="lifeStyle"
            :title="title"
            :pt="{ content: 'flex flex-col px-6' }"
            :view-all-to="to"
        >
            <PagePatientHealthRecordsLifestyleCard :life-style="lifeStyle" />
        </SharedPageSection>
        <PagePatientHealthRecordsAddInfo
            v-else
            title="Add Lifestyle Details"
            class="mx-6 mt-8"
            icon="material-symbols:sports-gymnastics-rounded"
            description="Record your lifestyle habits and activities."
        />
    </div>
</template>
