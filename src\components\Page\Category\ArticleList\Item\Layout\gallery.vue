<script setup lang="ts">
import { type PropType, computed } from '#imports'
import type { Hit, INotionPage } from '~/models'
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
const props = defineProps({
    blocks: {
        type: Array as PropType<BlockType[]>,
        default: () => ['title', 'sapo'],
    },
    article: {
        type: Object as PropType<Hit<INotionPage>>,
        required: true,
    },
    slug: {
        type: String,
        default: '',
    },
})
const placeholderSource = computed<string>(() => {
    const name = (props.article as any).page?.properties?.Name
    return (
        'https://placehold.co/1920x1080?text=' +
        name.split(' ').slice(0, 3).join(' ') +
        ' ...'
    )
})
</script>
<template>
    <article class="flex flex-col gap-4 md:gap-5">
        <slot v-if="blocks.includes('thumbnail')" name="thumbnail">
            <template v-if="(article as Hit<INotionPage>).page?.cover?.url">
                <div class="overflow-hidden rounded-xl hover:cursor-pointer">
                    <NuxtImg
                        :src="(article as Hit<INotionPage>).page?.cover?.url"
                        :alt="
                            (article as Hit<INotionPage>).page?.properties?.Name
                        "
                        :placeholder="placeholderSource"
                        loading="lazy"
                        preset="thumbnail"
                        sizes="364px md:500px"
                        format="webp"
                        height="auto"
                        blur="20px"
                        lazy
                        class="duration-800 transform transition ease-in hover:scale-105"
                    />
                </div>
            </template>
        </slot>

        <slot v-if="blocks.includes('title')" name="title">
            <!-- <NuxtLink :to="`${slug}/${article.page.properties.Slug}`"> -->
            <div
                class="mb-2 text-lg font-bold tracking-tight text-surface-900 hover:text-primary md:text-xl"
            >
                {{ article?.page?.properties?.Name }}
            </div>
            <!-- </NuxtLink> -->
        </slot>

        <slot v-if="blocks.includes('sapo')" name="sapo">
            <p class="mb-3 font-normal text-surface-700 dark:text-surface-400">
                {{ article?.page?.properties?.Sapo }}
            </p>
        </slot>
        <slot name="readtime">
            <p />
        </slot>
    </article>
</template>
