<script setup lang="ts">
import {
    reactive,
    onMounted,
    definePageMeta,
    useRouter,
    useRoute,
    useLayoutStore,
    useI18n,
    useConsultationSession,
    computed,
} from '#imports'
import { appRoutes } from '~/constants'
import { form } from './form'

definePageMeta({
    layout: 'nested-page',
})

const { t } = useI18n()
const router = useRouter()
const { query }: any = useRoute()
const layoutStore = useLayoutStore()

const state = reactive({
    user: query?.user,
    sessionId: query?.sessionId,
    conversation: query?.conversation,
})

const { updateConsultationSession } = useConsultationSession()

const handleFormSubmit = async () => {
    if (query?.consultation) {
        await updateConsultationSession(query?.consultation, {
            state: 'INCONSULTATION',
        })
    }

    router.push({
        path: appRoutes.provider.diary(query?.conversation),
        query: {
            provider: query?.provider,
            providerUser: query?.providerUser,
            patient: query?.patient,
        },
    })
}

const hidden_fields = computed(() => {
    return [
        {
            key: 'user',
            value: query?.user,
        },
        {
            key: 'sessionId',
            value: query?.sessionId,
        },
        {
            key: 'conversation',
            value: query?.conversation,
        },
    ]
})

onMounted(() => {
    window.addEventListener('message', async (event) => {
        if (event.data.event === 'submit') {
            console.log('Form submitted:', event.data.state)
        } else if (event.data.event === 'state') {
            const newState = event.data.state
            Object.assign(state, newState)
            console.log('State updated:', state)
        }
    })
    layoutStore.setTitle(t('Nhắn tin'))
})
</script>

<template>
    <div class="mx-auto w-full max-w-7xl p-4 md:p-8">
        <F
            :form="form(hidden_fields)"
            :state="state"
            @submit="handleFormSubmit"
        />
    </div>
</template>
