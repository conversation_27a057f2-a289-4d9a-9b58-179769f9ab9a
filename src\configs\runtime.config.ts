// runtime.config.ts
import { firebase } from './firebase.config'
import { gtm } from './gtm.config'

/*
 * Nuxt provides a runtime config API to expose configuration and secrets within your application.
 * See more: https://nuxt.com/docs/guide/going-further/runtime-config
 */

export const runtimeConfig: any = (env: any) => ({
    // configs and secrets that available on server only
    consola: {
        level: env.NUXT_CONSOLA_LEVEL,
    },

    // configs that available on client side
    public: {
        // APP
        app: {
            env: env.APP_ENV,
            'personal-doctor': {
                'remind-delay':
                    env.NUXT_PUBLIC_APP_PERSONAL_DOCTOR_REMIND_DELAY,
            },
        },

        // REVENUE CAT
        revenueCat: {
            apiKey: env.NUXT_PUBLIC_REVENUE_CAT_API_KEY,
            // appUserID: env.NUXT_PUBLIC_REVENUE_CAT_APP_USER_ID,
        },

        // FORMSIBLE
        cloudEndpoint: env.NUXT_PUBLIC_CLOUD_ENDPOINT,
        parseAppId: env.NUXT_PUBLIC_PARSE_APP_ID,
        parseServerUrl: env.NUXT_PUBLIC_PARSE_SERVER_URL,

        // CLARITY_PROJECT_ID
        clarityProjectId: env.NUXT_PUBLIC_CLARITY_PROJECT_ID,

        // SOCKET
        socketEndPoint: env.NUXT_PUBLIC_SOCKET_ENDPOINT,

        // UPLOAD
        uploadEndPoint: env.NUXT_PUBLIC_UPLOAD_ENDPOINT,

        // AUTH
        auth: {
            // Keycloak config
            redirectUri: env.NUXT_PUBLIC_AUTH_REDIRECT_URI,
            keycloakClientId: env.NUXT_PUBLIC_AUTH_KEYCLOAK_CLIENT_ID,
            keycloakIssuer: env.NUXT_PUBLIC_AUTH_KEYCLOAK_ISSUER,
        },

        'nuxt3-module-data-layer': {
            accountBaseUrl:
                env.NUXT_PUBLIC_NUXT3_MODULE_DATA_LAYER_ACCOUNT_BASE_URL,
            baseUrl: env.NUXT_PUBLIC_NUXT3_MODULE_DATA_LAYER_BASE_URL,
            xTenantId: env.NUXT_PUBLIC_NUXT3_MODULE_DATA_LAYER_X_TENANT_ID,
        },
        'nuxt3-module-consultation': {
            env: env.NUXT_PUBLIC_NUXT3_MODULE_CONSULTATION_ENV,
        },
        'nuxt3-module-chat': {
            firebaseEnv: env?.FIREBASE_ENV,
            firebaseConfig: firebase[env?.FIREBASE_ENV],
        },
        page: {
            status: env.NUXT_PUBLIC_PAGE_STATUS,
            site: env.NUXT_PUBLIC_PAGE_SITE,
        },
        editor: {
            enabled: env.NUXT_PUBLIC_EDITOR_ENABLED,
        },
        gtm,
        onesignal: {
            endpoint: env.NUXT_PUBLIC_ONESIGNAL_ENDPOINT,
            isTest: env.NUXT_PUBLIC_ONESIGNAL_TEST,
            app_id: env.NUXT_PUBLIC_ONESIGNAL_APP_ID,
            voip: env.NUXT_PUBLIC_ONESIGNAL_VOIP,
        },
        'nuxt3-module-conference': {
            baseUrl: env.NUXT_PUBLIC_NUXT3_MODULE_CONFERENCE_BASE_URL,
            ringingAudio: '/audio/ringing.mp3',
            connectingAudio: '/audio/connecting.mp3',
            socketEndPoint:
                env.NUXT_PUBLIC_NUXT3_MODULE_CONFERENCE_SOCKET_ENDPOINT,
        },
    },
})
