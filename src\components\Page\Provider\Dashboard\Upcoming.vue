<script setup lang="ts">
const { userId, isProvider } = defineProps({
    userId: {
        type: String,
        required: true,
    },
    isProvider: {
        type: Boolean,
        default: false,
    },
    phone: {
        type: String,
        default: '',
    },
})
const { t } = useI18n()
const callHandle = useCallHandle()
const upConsultationRef = ref<InstanceType<
    typeof WProviderUpcomingConsultations
> | null>(null)

const shareConsultationCount = useState('share-provider-consultation-count')

function openMr(consultation: any) {
    navigateTo(`/provider/consultation/${consultation._id}`)
}

const callVideoHandler = (data: any) => {
    callHandle.open('video', data)
}
const callVoiceHandler = (data: any) => {
    callHandle.open('voice', data)
}

const refreshConsultations = () => {
    if (upConsultationRef.value) {
        upConsultationRef.value?.refreshConsultations()
    }
}

const consultationSocket = useSocketIo({
    channel: '/Consultation',
    debugLevel: 'debug',
})

const debouncedRefresh = useDebounceFn(() => {
    refreshConsultations()
    console.log('[socket] refreshSearch')
}, 500)

consultationSocket.on('created', debouncedRefresh)
consultationSocket.on('updated', debouncedRefresh)
consultationSocket.on('removed', debouncedRefresh)

watch(
    [() => userId, () => consultationSocket.state.value.isConnected],
    async ([newUserId, isConnected]) => {
        if (newUserId && isConnected) {
            await consultationSocket.joinRoom({
                roomId: newUserId,
                userId: newUserId,
            })
        }
    },
    { immediate: true },
)
</script>
<template>
    <SharedPageSection
        v-show="shareConsultationCount > 0"
        :title="t('teleconsultation')"
        :pt="{ content: 'mx-4' }"
    >
        <WProviderUpcomingConsultations
            ref="upConsultationRef"
            :user-id="userId"
            :is-provider="isProvider"
            @click:call="callVoiceHandler"
            @click:open-mr="openMr"
            @click:call-video="callVideoHandler"
        />
        <!-- <CallHandle
            ref="call-handle"
            default-route="provider"
            :from-id="userId"
            :user-phone="phone"
        /> -->
    </SharedPageSection>
</template>
