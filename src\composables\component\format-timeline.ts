import dayjs from 'dayjs'

export const formatTimeline = (
    date: string,
    formatType: 'MonthYear' | 'FullDate' = 'MonthYear',
): string => {
    const currentYear = dayjs().year()
    const timelineYear = dayjs(date).year()

    let format = ''

    if (formatType === 'MonthYear') {
        format = timelineYear === currentYear ? 'YYYY-MM' : 'YYYY-MM'
    } else if (formatType === 'FullDate') {
        format = timelineYear === currentYear ? 'YYYY-MM-DD' : 'YYYY-MM-DD'
    }

    return dayjs(date).format(format)
}
