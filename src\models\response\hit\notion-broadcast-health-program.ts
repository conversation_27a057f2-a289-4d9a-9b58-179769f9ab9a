import type { INotionPage } from '../notion'
import type { NotionHealthProgram } from '../notion/health-program'
import type { IHitNotion } from './notion'

export interface NotionBroadcastHealthProgram {
  'Parent item'?: INotionPage<NotionBroadcastHealthProgram>[]
  'Sub-item'?: INotionPage<NotionBroadcastHealthProgram>[]
  Labels?: string
  Name?: string
  Program?: INotionPage<NotionHealthProgram>[]
  Status?: string
  Tags?: string[]
  Target?: string[]
}

export interface HitNotionBroadcastHealthProgram extends IHitNotion {
  page?: INotionPage<NotionBroadcastHealthProgram>
}
