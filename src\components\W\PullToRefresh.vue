<!-- PullToRefresh.vue -->
<template>
    <div
        ref="pullContainer"
        class="relative overflow-hidden"
        @touchstart="handleTouchStart"
        @touchmove="handleTouchMove"
        @touchend="handleTouchEnd"
    >
        <!-- Loading indicator -->
        <div
            class="absolute left-0 right-0 flex transform items-center justify-center transition-transform duration-300"
            :style="{
                top: '-60px',
                transform: `translateY(${pullDistance}px)`,
            }"
        >
            <div
                class="h-8 w-8 rounded-full border-4 border-blue-500 border-t-transparent"
                :class="{ 'animate-spin': isRefreshing }"
            ></div>
        </div>

        <!-- Main content -->
        <div
            :style="{ transform: `translateY(${pullDistance}px)` }"
            class="transition-transform"
        >
            <slot></slot>
        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'

const props = defineProps({
    onRefresh: {
        type: Function,
        required: true,
    },
    threshold: {
        type: Number,
        default: 60,
    },
})

const pullContainer = ref(null)
const startY = ref(0)
const pullDistance = ref(0)
const isRefreshing = ref(false)

const handleTouchStart = (e) => {
    if (pullContainer.value.scrollTop <= 0) {
        startY.value = e.touches[0].clientY
    }
}

const handleTouchMove = (e) => {
    if (startY.value === 0) return

    const touch = e.touches[0]
    const distance = touch.clientY - startY.value

    if (distance > 0 && pullContainer.value.scrollTop <= 0) {
        e.preventDefault()
        pullDistance.value = Math.min(distance * 0.5, props.threshold * 1.5)
    }
}

const handleTouchEnd = async () => {
    if (pullDistance.value >= props.threshold) {
        isRefreshing.value = true
        pullDistance.value = props.threshold

        try {
            await props.onRefresh()
        } catch (error) {
            console.error('Refresh failed:', error)
        }

        isRefreshing.value = false
    }

    pullDistance.value = 0
    startY.value = 0
}
</script>
