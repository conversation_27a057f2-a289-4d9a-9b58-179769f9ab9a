<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { Autoplay, Pagination } from 'swiper/modules'
import { ref, computed, onMounted, onUnmounted } from 'vue'
import 'swiper/css'
import 'swiper/css/pagination'
import { Capacitor } from '@capacitor/core'

type FooterCommand = 'terms' | 'support' | 'language' | 'starter' | 'policy'
type EmitEvents = `on:${FooterCommand}`
interface Props {
    isLoading?: boolean
}

defineProps<Props>()

const emit = defineEmits<{
    (e: EmitEvents, event?: Event): void
}>()

const { t } = useI18n()
const { display } = useDisplay()
const { logout: logoutCallkeep } = useCallkeep()
const { logout: logoutPush } = usePushNotification()

const isMobile = computed(() => display.breakpoint.isMobile)
const showScrollIndicator = ref(false)

const footerLinks = computed(() => [
    {
        label: t('starter.footer.terms'),
        href: '#',
        command: 'terms' as FooterCommand,
    },
    {
        label: t('starter.footer.policy'),
        href: '#',
        command: 'policy' as FooterCommand,
    },
])

const utilityLinks = computed(() => [
    {
        label: t('starter.footer.support'),
        href: '#',
        command: 'support' as FooterCommand,
        icon: 'material-symbols:support-agent',
    },
    {
        label: t('starter.footer.language'),
        href: '#',
        command: 'language' as FooterCommand,
        icon: 'material-symbols:globe',
    },
])

const swiperSlides = computed(() => [
    {
        image: `https://storage.googleapis.com/cms-gallery-sandbox/664abe22562b0140e403f051/ba-bau.jpg`,
        title: t('starter.slides.pregnancy.title'),
        description: t('starter.slides.description'),
    },
    {
        image: `https://storage.googleapis.com/cms-gallery-sandbox/664dbb59562b01804a03f25a/mevabe2.png`,
        title: t('starter.slides.parent.title'),
        description: t('starter.slides.description'),
    },
    {
        image: `https://storage.googleapis.com/cms-gallery-sandbox/66544960c45c25078c690052/benh-man-tinh.jpg`,
        title: t('starter.slides.convenience.title'),
        description: t('starter.slides.description'),
    },
    {
        image: `https://storage.googleapis.com/cms-gallery-sandbox/66544941c45c254a59690049/tri-lieu-tam-ly-2.png`,
        title: t('starter.slide.mental-health.title'),
        description: t('starter.slides.description'),
    },
    {
        image: `https://storage.googleapis.com/cms-gallery-sandbox/664aeac5562b0165cf03f060/hr-1.png`,
        title: t('starter.slides.employees.title'),
        description: t('starter.slides.description'),
    },
])

const handleLinkClick = (command: FooterCommand, event: Event) => {
    event.preventDefault()
    emit(`on:${command}`, event)
}

const checkHeight = () => {
    showScrollIndicator.value = window.innerHeight < 750 && window.scrollY < 100
}

const signoutCallkeep = async () => {
    if (Capacitor.isNativePlatform()) {
        try {
            await logoutCallkeep()
        } catch (error) {
            console.error(error)
        }
        try {
            await logoutPush()
        } catch (error) {
            console.error(error)
        }
    }
}

const scrollToBottom = () => {
    window.scrollTo({
        top: document.documentElement.scrollHeight,
        behavior: 'smooth',
    })
}

onMounted(() => {
    signoutCallkeep()
    checkHeight()
    window.addEventListener('resize', checkHeight)
    window.addEventListener('scroll', checkHeight)
})

onUnmounted(() => {
    window.removeEventListener('resize', checkHeight)
    window.removeEventListener('scroll', checkHeight)
})
</script>

<template>
    <div class="flex min-h-screen flex-col gap-4 overflow-hidden md:flex-row">
        <!-- Left side with Swiper -->
        <div class="relative h-[40vh] md:h-screen md:w-2/3">
            <!-- Utility navigation -->
            <div
                class="absolute right-4 top-4 z-10 flex items-center gap-2 pt-safe"
            >
                <button
                    v-for="link in utilityLinks"
                    :key="link.command"
                    class="flex items-center justify-center rounded-full bg-white/10 p-3 text-white backdrop-blur-md transition-all hover:bg-white/20 md:h-12 md:w-12"
                    @click="handleLinkClick(link.command, $event)"
                >
                    <iconify-icon :icon="link.icon" />
                    <span class="sr-only">{{ link.label }}</span>
                </button>
            </div>

            <!-- Scroll Indicator -->
            <Transition
                enter-active-class="transition-opacity duration-300 ease-out"
                leave-active-class="transition-opacity duration-200 ease-in"
                enter-from-class="opacity-0"
                leave-to-class="opacity-0"
            >
                <div
                    v-if="showScrollIndicator"
                    class="fixed bottom-36 left-0 right-0 z-40 transform animate-bounce md:bottom-16"
                >
                    <div class="flex flex-col items-center gap-2 text-center">
                        <Button
                            variant="text"
                            rounded
                            icon="pi pi-angle-double-down"
                            @click="scrollToBottom"
                        />
                    </div>
                </div>
            </Transition>

            <Swiper
                :modules="[Autoplay, Pagination]"
                :slides-per-view="1"
                :loop="true"
                :autoplay="{
                    delay: 3000,
                    disableOnInteraction: false,
                }"
                :pagination="{
                    clickable: true,
                }"
                class="h-full w-full"
            >
                <SwiperSlide
                    v-for="(slide, index) in swiperSlides"
                    :key="index"
                    class="h-full"
                >
                    <div class="relative h-full w-full">
                        <NuxtImg
                            :src="slide.image"
                            :alt="slide.title"
                            loading="lazy"
                            format="webp"
                            quality="80"
                            class="absolute inset-0 h-full w-full object-cover"
                            sizes="sm:100vw md:100vw lg:100vw"
                            placeholder
                        />
                        <!-- Gradient overlay -->
                        <div
                            class="absolute inset-0 bg-gradient-to-b from-black/10 via-black/30 to-black/80"
                        ></div>
                        <!-- Content -->
                        <div
                            class="absolute bottom-0 left-0 right-0 px-4 py-8 text-white md:px-8"
                        >
                            <h2 class="mb-2 text-3xl font-bold">
                                {{ slide.title }}
                            </h2>
                            <p class="text-xl">{{ slide.description }}</p>
                        </div>
                    </div>
                </SwiperSlide>
            </Swiper>
        </div>

        <!-- Right side with content -->
        <div class="relative flex flex-1 flex-col justify-between px-4">
            <!-- Top decorative element -->
            <div class="relative hidden h-[25%] py-4 md:block"></div>

            <!-- Main content -->
            <div class="flex-auto py-4">
                <div class="mx-auto max-w-lg">
                    <div class="space-y-5 text-center md:text-left">
                        <!-- Logo and Title -->
                        <div
                            class="flex items-center justify-center gap-2 md:justify-start"
                        >
                            <div class="size-10 md:size-16">
                                <img
                                    src="/images/logo.svg"
                                    alt="Wellcare"
                                    class="h-full w-full object-contain drop-shadow-md"
                                />
                            </div>
                            <h1>
                                <span> Wellcare </span>
                            </h1>
                        </div>

                        <!-- Subtitle -->
                        <div>
                            <p
                                class="text-lg leading-relaxed text-surface-600 dark:text-surface-100"
                            >
                                {{ t('starter.hero.subtitle') }}
                            </p>
                        </div>

                        <!-- Features grid -->
                        <div
                            class="grid grid-cols-1 gap-4 pb-16 text-sm md:grid-cols-2 md:pb-0"
                        >
                            <div
                                v-for="i in 5"
                                :key="i"
                                class="flex items-center space-x-2"
                            >
                                <i class="pi pi-check text-primary"></i>
                                <span>{{
                                    t(`starter.hero.features-${i}`)
                                }}</span>
                            </div>
                        </div>
                    </div>

                    <!-- Button section -->
                    <div
                        :class="[
                            'mt-8',
                            {
                                'fixed bottom-0 left-0 right-0 z-50 bg-white p-4 shadow-[0_-4px_16px_rgba(0,0,0,0.1)]':
                                    isMobile,
                            },
                        ]"
                    >
                        <div :class="[{ 'mx-auto max-w-lg': isMobile }]">
                            <Button
                                class="w-full"
                                :loading="isLoading"
                                @click="$emit('on:starter')"
                            >
                                {{ t('starter.hero.cta.button') }}
                            </Button>

                            <!-- Mobile footer -->
                            <div
                                class="flex w-full flex-col items-center gap-2 border-t py-2 md:hidden"
                            >
                                <div class="text-sm text-surface-500">
                                    © 2015 - 2024 • Wellcare • All Rights
                                    Reserved
                                </div>
                                <div
                                    class="flex flex-wrap items-center justify-center gap-4"
                                >
                                    <a
                                        v-for="link in footerLinks"
                                        :key="link.command"
                                        :href="link.href"
                                        class="text-sm text-surface-600 transition-colors hover:text-primary hover:underline"
                                        @click="
                                            handleLinkClick(
                                                link.command,
                                                $event,
                                            )
                                        "
                                    >
                                        {{ link.label }}
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Desktop footer -->
            <div
                class="hidden w-full flex-col items-center gap-2 border-t py-6 md:flex"
            >
                <div class="text-sm text-surface-500">
                    © 2015 - 2024 • Wellcare • All Rights Reserved
                </div>
                <div class="flex flex-wrap items-center gap-4 md:gap-8">
                    <a
                        v-for="link in footerLinks"
                        :key="link.command"
                        :href="link.href"
                        class="text-sm text-surface-600 transition-colors hover:text-primary hover:underline"
                        @click="handleLinkClick(link.command, $event)"
                    >
                        {{ link.label }}
                    </a>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
/* Mobile padding adjustments */
@media (max-width: 768px) {
    .min-h-screen {
        padding-bottom: 180px;
    }
}

/* Swiper styles */
:deep(.swiper) {
    height: 100%;
}

:deep(.swiper-slide) {
    height: 100%;
}

:deep(.swiper-pagination-bullet) {
    background: white;
    opacity: 0.5;
}

:deep(.swiper-pagination-bullet-active) {
    opacity: 1;
}

/* Animation keyframes */
@keyframes bounce {
    0%,
    100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.animate-bounce {
    animation: bounce 1s infinite;
}
</style>
