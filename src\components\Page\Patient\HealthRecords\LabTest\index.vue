<script setup lang="ts">
import { useFileSearch } from '~/composables'
import { computed, ref } from 'vue'

const { memberActivatedId } = defineProps({
    title: {
        type: String,
        required: true,
    },
    memberActivatedId: {
        type: String,
        default: '',
    },
})

const userId = computed(() => memberActivatedId as string)

const projectId = ref('emr')
const tags = ['lab']
const initialQuery = {
    skip: 0,
    limit: 4,
    count: true,
    fields: 'tags, description, url, labels, folder, state, progress, name, description, sprite, thumbnail, mimetype, blurhash, updatedAt, createdAt, capturedAt, duration, size',
    filter: {
        tags: tags,
    },
    sort: '-updatedAt',
}

const {
    data: labFiles,
    refresh,
    status,
} = useFileSearch('lab-files', userId, projectId, initialQuery)

const viewToAllAction = computed(() => {
    return sortedLabs.value?.length > 0
        ? `/patient/health-records/${userId.value}/lab`
        : false
})

const sortedLabs = computed(() => {
    return [...(labFiles.value || [])].sort(
        (a, b) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    )
})
const isLoading = computed(() => status.value === 'pending')

const handleSuccess = () => {
    refresh()
}

watch(
    userId,
    (newVal) => {
        if (newVal) {
            refresh()
        }
    },
    { immediate: true },
)

const handleError = (error: any) => {
    console.error('Upload failed:', error)
}
</script>

<template>
    <SharedPageSection
        :title="title"
        :pt="{ content: 'flex flex-col gap-3 px-6' }"
        :view-all-to="viewToAllAction"
    >
        <template v-if="isLoading">
            <div v-for="index in 4" :key="index">
                <Skeleton height="48px" class="mt-2 p-4" />
            </div>
        </template>
        <template v-else-if="sortedLabs?.length > 0">
            <template v-for="lab in sortedLabs" :key="lab?.url">
                <SharedFileCard :file="lab" />
            </template>
        </template>
        <PagePatientHealthRecordsAddFileCard
            v-else
            project="emr"
            :user="userId"
            :tags="tags"
            :as-button="true"
            @upload-success="handleSuccess"
            @upload-error="handleError"
        />
    </SharedPageSection>
</template>
