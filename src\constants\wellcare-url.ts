export const wellcareUrl = {
    registerPhoneOnboarding: () => '/identity/public/register/idp/phone',
    registerIdpOnboarding: () => '/identity/public/register/idp',
    registerUserPublic: () => '/identity/public/register',
    checkPhone: (phone: string, countryCode: string) =>
        `/identity/public/username/${phone}?countryCode=${countryCode}`,
    updatePhone: (userId: string) => `/identity/account/${userId}/phone`,
    mergeUser: (uuid: string) => `/identity/account/merge/uuid/${uuid}/phone`,
    sendOtp: () => `/identity/public/otp/signup`,
    sendOtpForgot: () => '/identity/public/otp/forgot-password',
}
