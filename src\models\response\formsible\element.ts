export interface IChoice {
    src?: string
    label: string
    value: string
}

export interface IValidation {
    rule:
        | 'required'
        | 'alpha'
        | 'alphaNum'
        | 'numeric'
        | 'between'
        | 'maxLength'
        | 'minLength'
        | 'maxValue'
        | 'minValue'
        | 'email'
        | 'ipAddress'
        | 'macAddress'
        | 'requiredIf'
        | 'requiredUnless'
        | 'sameAs'
        | 'url'
    params?: string[]
}

export interface IFetchElement {
    url?: string
}

export enum EComponentType {
    Address = 'address',
    AudioRecorder = 'audio_recorder',
    Captcha = 'captcha',
    Checkboxes = 'checkboxes',
    CodeJavaScript = 'code_javascript',
    CodeJson = 'code_json',
    DatePicker = 'date_picker',
    Dropdown = 'dropdown',
    Email = 'email',
    FileUpload = 'file_upload',
    ImageChoice = 'image_choice',
    LikertScale = 'likert_scale',
    List = 'list',
    LongText = 'long_text',
    Matrix = 'matrix',
    MultipleChoice = 'multiple_choice',
    NetPromoterScore = 'net_promoter_score',
    Number = 'number',
    Password = 'password',
    PhoneNumber = 'phone_number',
    Ranking = 'ranking',
    RatingScale = 'rating_scale',
    ShortText = 'short_text',
    Signature = 'signature',
    Slider = 'slider',
    DataTable = 'data_table',
    TimePicker = 'time_picker',
    Website = 'website',
}

export interface InputProperties {
    label?: string
    description?: string
    key: string
    component: EComponentType
    defaultValue?: string
    choices?: IChoice[]
    props?: Record<string, any>
    placeholder?: string
    hint?: string
    validations?: IValidation[]
    fetch?: IFetchElement
}

export interface IDisplayProperties {
    tag?:
        | 'h1'
        | 'h2'
        | 'h3'
        | 'h4'
        | 'h5'
        | 'h6'
        | 'p'
        | 'blockquote'
        | 'code'
        | 'pre'
        | 'ul'
        | 'ol'
        | 'img'
        | 'hr'
    content?: string
    icon?: string
    src?: string
    alt?: string
    width?: string
    height?: string
    size?: string
    color?: string
    items?: string[]
    component:
        | 'html_block_content'
        | 'html_block_list'
        | 'html_block_media'
        | 'icon_display'
        | 'embed_video'
        | 'embed_slide'
        | 'embed_document'
        | 'locale_selector'
        | 'action_redirect'
}

export interface IFormProperties {
    input?: InputProperties
    display?: IDisplayProperties
}

export interface IFormElement {
    idexport: string
    type?: 'input' | 'display'
    properties: IFormProperties
}
