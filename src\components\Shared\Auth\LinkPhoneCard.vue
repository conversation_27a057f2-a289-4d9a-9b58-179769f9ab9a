<script setup lang="ts">
import { appRoutes } from '~/constants'

const { t } = useI18n()
const { refreshTokenOidc } = useAppAuth()
const healthRecordStore = useHealthRecordStore()
const { user, refresh } = useUserInfo({ scope: ['_id'] })
const showPhoneVerification = ref(false)

const handleVerificationComplete = async () => {
    showPhoneVerification.value = false
    healthRecordStore.$reset()
    await refreshTokenOidc()
    await refresh()
    navigateTo(appRoutes.auth.callback)
}
</script>

<template>
    <div>
        <div
            class="overflow-hidden rounded-xl bg-gradient-to-br from-primary-50/50 to-primary-100/50 dark:from-primary-900/20 dark:to-primary-800/20"
        >
            <div class="relative">
                <!-- Decorative Elements -->
                <div class="absolute inset-0 overflow-hidden">
                    <!-- Gradient Orbs -->
                    <div
                        class="absolute -left-8 -top-8 size-32 rounded-full bg-primary-300/20 blur-2xl"
                    ></div>
                    <div
                        class="absolute -bottom-8 -right-8 size-32 rounded-full bg-primary-200/20 blur-2xl"
                    ></div>
                </div>

                <!-- Content -->
                <div class="relative flex items-start gap-4 p-4">
                    <div class="shrink-0">
                        <div
                            class="flex size-12 items-center justify-center rounded-full bg-primary-100 dark:bg-primary-900"
                        >
                            <iconify-icon
                                icon="fluent:person-link-20-regular"
                                class="text-2xl text-primary-600 dark:text-primary-400"
                            />
                        </div>
                    </div>
                    <div class="flex-1">
                        <h3
                            class="mb-1.5 text-lg font-semibold text-surface-900 dark:text-surface-50"
                        >
                            {{ t('link-phone-title') }}
                        </h3>
                        <p
                            class="mb-3 text-sm leading-relaxed text-surface-700 dark:text-surface-300"
                        >
                            {{ t('link-phone-description') }}
                        </p>
                        <Button
                            :label="t('link-phone-action')"
                            severity="primary"
                            size="small"
                            class="font-medium"
                            @click="showPhoneVerification = true"
                        />
                    </div>
                </div>
            </div>
        </div>

        <SharedDialogPhoneVerification
            v-model:visible="showPhoneVerification"
            :user-id="user._id"
            @verification-complete="handleVerificationComplete"
        />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "link-phone-title": "Link to your medical record",
        "link-phone-description": "Link your phone number to retrieve your previous medical history, consultation records, and health data from your existing account",
        "link-phone-action": "Link account"
    },
    "vi": {
        "link-phone-title": "Liên kết với hồ sơ sức khỏe",
        "link-phone-description": "Liên kết số điện thoại để lấy lại lịch sử khám bệnh, hồ sơ tư vấn và dữ liệu sức khỏe từ tài khoản cũ của bạn",
        "link-phone-action": "Liên kết"
    }
}
</i18n>
