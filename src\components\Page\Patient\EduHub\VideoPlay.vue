<script lang="ts" setup>
import type { IItemPage } from '~/models'

const { media } = defineProps({
    media: {
        type: Object as PropType<IItemPage>,
        required: true,
    },
})
const mediaVideo = computed(() => media.url)
</script>

<template>
    <div class="xl mx-auto w-full max-w-4xl">
        <div class="aspect-[16/9]">
            <WVideo
                :key="media._id"
                :sources="[
                    {
                        src: replaceCDNHost(mediaVideo),
                        type: media.mimeType,
                    },
                ]"
                :poster="media.thumbnail"
                control-bar="true"
                native-audio-tracks
                native-text-track
                playsinline
                class="video-js vjs-default-skin vjs-big-play-centered !h-full !w-full transition-all"
            />
        </div>
        <div class="p-4">
            <h3 class="text-base font-semibold">
                {{ media.name }}
            </h3>
            <p v-dompurify-html="media.content" class="text-sm" />
        </div>
    </div>
</template>
<style>
.video-js .vjs-big-play-button {
    border-radius: 1000px !important;
    width: 4rem !important;
    height: 4rem !important;
}

.video-js .vjs-big-play-button .vjs-icon-placeholder:before {
    top: 13% !important;
}
</style>
