<script setup lang="ts">
import { convertToBase64 } from '~/utils'

const { t } = useI18n()
const onboardingStore = useOnboardingStore()
const { key, rerenderSrc } = useRerender()

const fileUpload = ref<File | null>(null)
const avatarPreviewRef = ref<any>(null)
const initialSrc = ref<string | null>(null)
const showRequired = ref(false)

onMounted(async () => {
    if (onboardingStore.profile.file) {
        fileUpload.value = onboardingStore.profile.file
        initialSrc.value = await convertToBase64(onboardingStore.profile.file)
    }
})

const upload = () => {
    if (avatarPreviewRef.value) {
        avatarPreviewRef.value?.triggerFileInput()
    }
}

const onFileSelected = (file: File) => {
    fileUpload.value = file
    showRequired.value = false
}

const submit = async () => {
    if (!fileUpload.value) {
        showRequired.value = true
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            file: null,
        })
        return false
    }

    onboardingStore.setProfile({
        ...onboardingStore.profile,
        file: fileUpload.value,
    })
    return true
}

rerenderSrc({
    source: initialSrc,
})

defineExpose({ submit })
</script>

<template>
    <div class="flex min-h-[80vh] px-4 pt-8 md:items-center md:justify-center">
        <div class="w-full max-w-xl">
            <div class="mb-6 w-full text-center">
                <div class="space-y-8">
                    <h1 class="text-2xl font-semibold md:text-3xl">
                        {{ t('onboarding.update-your-profile-picture') }}
                    </h1>

                    <div
                        class="flex flex-col items-center justify-center gap-4"
                    >
                        <div class="group relative">
                            <WAvatarPreview
                                :key="key"
                                ref="avatarPreviewRef"
                                :initial-src="initialSrc ? initialSrc : ''"
                                class="transition-transform duration-200 hover:scale-105"
                                @on:file-selected="onFileSelected"
                            />

                            <small
                                v-if="!fileUpload && showRequired"
                                class="absolute -bottom-6 left-0 right-0 text-center text-red-500"
                            >
                                {{ t('required') }}
                            </small>
                        </div>

                        <Button
                            severity="secondary"
                            :label="t('upload')"
                            class="px-6 py-2 text-base font-medium shadow-sm hover:opacity-90"
                            @click="upload"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
