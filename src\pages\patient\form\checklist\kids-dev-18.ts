import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'TnZ190qmxQ',
    pages: [
        {
            elements: [
                {
                    id: 'm2jviwtr0r',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON><PERSON> mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 18 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. Bác sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2jvj7gwxt8',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_media',
                            tag: 'img',
                            src: 'https://images.pexels.com/photos/406014/pexels-photo-406014.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1',
                            alt: 'Nice picture',
                        },
                    },
                },
            ],
            id: '18 tháng',
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2juxsbvj8b',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự tách khỏi mẹ để chơi, nhưng vẫn dòm chừng để chắc chắn rằng mẹ đang ở gần đó',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2juxsbvj8b',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2juxtuxsir',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Dùng tay chỉ cho cha mẹ thấy điều gì đó thú vị',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2juxtuxsir',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2juxv0ukb',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Xòe tay ra cho cha mẹ rửa',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2juxv0ukb',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2juxw8u38g',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cùng cha mẹ xem một vài trang trong cuốn sách',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2juxw8u38g',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2juxxg2zkt',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết cùng cha mẹ mặc quần áo cho mình bằng cách đưa cánh tay qua ống tay áo hoặc nhấc chân đưa vào ống quần.',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2juxxg2zkt',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jv2dck4sg',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cố gắng nói ba từ trở lên ngoài “ba ba” hoặc “mẹ mẹ”',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jv2dck4sg',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jv2elh7kr',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Thực hiện được các chỉ dẫn đơn lẻ, mà không cần kèm thêm bất kỳ cử chỉ hướng dẫn nào, chẳng hạn như đưa cho cha mẹ món đồ chơi khi cha mẹ nói "Đưa nó cho mẹ nào"',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jv2elh7kr',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jv4sqmprp',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Bắt chước cha mẹ làm việc nhà, chẳng hạn như quét nhà bằng chổi',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jv4sqmprp',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jv4vjiiq9',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chơi đồ chơi một cách đơn giản, như đẩy cho xe hơi đi',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jv4vjiiq9',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jv6yafp6i',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Bước đi mà không cần bám vào bất cứ ai hoặc bất cứ vật gì',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                            ],
                            key: 'MultipleChoicem2jv6yafp6i',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jv6zcx.bou',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Vẽ nét chữ nguệch ngoạc',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                            ],
                            key: 'MultipleChoicem2jv6zcxbou',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jv71g9.v0a',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Uống bằng cốc không có nắp và thỉnh thoảng có thể bị đổ ra ngoài',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                            ],
                            key: 'MultipleChoicem2jv71g9v0a',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jv70dy.h9',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự bốc ăn bằng ngón tay',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                            ],
                            key: 'MultipleChoicem2jv70dyh9',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jv72jk.yb',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cố gắng dùng thìa, muỗng',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                            ],
                            key: 'MultipleChoicem2jv72jkyb',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jv754s.v4e',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự leo lên và leo xuống ghế mà không cần sự giúp đỡ',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                            ],
                            key: 'MultipleChoicem2jv754sv4e',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    createdBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    updatedBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    title: '18 tháng',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
})
