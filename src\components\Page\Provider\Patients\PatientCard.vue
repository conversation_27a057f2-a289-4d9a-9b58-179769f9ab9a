<template>
    <div class="card flex flex-row items-center">
        <Avatar
            :image="patient.avatar"
            size="xlarge"
            shape="circle"
            class="mr-2 flex-none"
        />
        <div class="flex-auto">
            <div class="flex flex-row items-center justify-between">
                <NuxtLink
                    to="/provider/patients/[id]/consultation/[id]"
                    class="link text-xl font-semibold"
                >
                    {{ patient.name }}
                </NuxtLink>
                <p class="m-0 text-[var(--p-surface-500)]">
                    {{ patient.lastActivity.time }}
                </p>
            </div>

            <p class="mb-0 line-clamp-1">
                I have a diagram headache pls help but i delt with it
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
defineProps<{ patient: any }>()
</script>
