<script setup lang="ts">
import { type PropType, computed, useI18n } from '#imports'
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
const { article } = defineProps({
    article: {
        type: Object,
        required: true,
    },
    slug: {
        type: String,
        default: '',
    },
    blocks: {
        type: Array as PropType<BlockType[]>,
        default: () => ['title', 'sapo'],
    },
})
const placeholderSource = computed<string>(() => {
    const name: string = article.item.page.properties.Name
    return (
        'https://placehold.co/1920x1080?text=' +
        name.split(' ').slice(0, 3).join(' ') +
        ' ...'
    )
})
const publishDate = computed<string>(
    () => article?.item?.page?.properties?.PublishedAt?.start ?? '',
)
const { locale } = useI18n()
</script>
<template>
    <div class="my-2">
        <div
            v-if="publishDate !== ''"
            class="/[0.7] absolute -top-2 mb-2 text-lg capitalize text-black/[0.7]"
        >
            {{ $dayjs(publishDate).locale(locale).format('MMMM, YYYY') }}
        </div>
        <NuxtImg
            v-if="article.item.page.cover"
            :src="article.item.page.cover.url"
            sizes="600px sm:620px md:750px lg:1000px"
            :placeholder="placeholderSource"
            class="aspect-[16/9] rounded-lg object-cover hover:cursor-pointer"
            loading="lazy"
        />
        <div class="mt-2 text-lg">
            <div
                class="font-bold hover:cursor-pointer"
                @click="
                    navigateTo(`/${slug}/${article.item.page.properties.Slug}`)
                "
            >
                {{ article.item.page.properties.Name }}
            </div>
            <p class="text-base">
                {{ article.item.page.properties.Sapo }}
            </p>
        </div>
        <Divider />
    </div>
</template>
