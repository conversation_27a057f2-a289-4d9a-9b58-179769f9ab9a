<script lang="ts" setup>
import { definePageMeta } from '#imports'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const { t } = useI18n()
const route = useRoute()
const callHandle = useCallHandle()

const layoutStore = useLayoutStore()
layoutStore.setTitle(t('All Consultations'))

const patientId = route.params.id as string

const { user } = useUserInfo({ scope: ['_id', 'isProvider', 'phone'] })
const userId = computed(() => user.value?._id ?? '')
// const userPhone = computed(() => user.value?.phone ?? '')

const callVideoHandler = (data: any) => {
    callHandle.open('video', {
        _id: data._id,
        conversation: data.conversationId,
        providerUser: data.provider?.user,
        user: data.user._id,
    })
}
const callVoiceHandler = (data: any) => {
    callHandle.open('voice', {
        _id: data._id,
        conversation: data.conversationId,
        providerUser: data.provider?.user,
        user: data.user._id,
    })
}

const handleOpenMr = (consultation: any) => {
    navigateTo(`/provider/consultation/${consultation._id}`)
}

const handleOpenRating = (consultation: any) => {
    console.log('Opening rating for consultation:', consultation)
}
</script>

<template>
    <div>
        <WConsultationHistory
            :user-id="patientId"
            :is-provider="true"
            :showing-quantity="10"
            :showing-load-btn="true"
            :filter="{
                providerUser: userId,
                patient: patientId,
                type: {
                    $in: ['indepth', 'question'],
                },
            }"
            @click:call="callVoiceHandler"
            @click:open-mr="handleOpenMr"
            @click:call-video="callVideoHandler"
            @click:open-rating="handleOpenRating"
        />
    </div>
</template>
<i18n lang="json">
{
    "en": {
        "All Consultations": "All Consultations"
    },
    "vn": {
        "All Consultations": "Tất cả các buổi tư vấn"
    }
}
</i18n>
