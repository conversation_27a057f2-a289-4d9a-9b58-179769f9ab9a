<script setup lang="ts">
import { onMounted, ref, useAppAuth } from '#imports'
import { App } from '@capacitor/app'

const { checkAuthStatus, signInRefreshToken, handleAppAuthUrlOpen, signOut } =
    useAppAuth()
const refreshToken = ref<string>('')

const isLogged = computedAsync(async () => {
    return await checkAuthStatus()
}, false)

onMounted(() => {
    App.addListener('appUrlOpen', handleAppAuthUrlOpen)
})
</script>

<template>
    <div
        class="flex min-h-screen items-center justify-center bg-surface-100 p-4 dark:bg-surface-900"
    >
        <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
            <button @click="navigateTo('/debug')">
                <span class="underline">Back to home</span>
            </button>
        </div>
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
            <!-- Login Card -->
            <div
                class="rounded-xl bg-surface-50 p-6 shadow-lg dark:bg-surface-700"
            >
                <h2
                    class="mb-4 text-center text-2xl font-bold text-surface-900"
                >
                    Login
                </h2>
                <div class="space-y-4">
                    <p class="mb-2 text-surface-600 dark:text-surface-400">
                        checkAuthStatus: {{ isLogged }}
                    </p>
                    <InputGroup>
                        <InputText
                            v-model="refreshToken"
                            placeholder="Enter refresh token"
                        />
                    </InputGroup>

                    <button
                        v-if="isLogged"
                        class="w-full rounded-lg bg-red-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-red-600"
                        @click="signOut"
                    >
                        sign out
                    </button>
                    <template v-else>
                        <button
                            class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                            @click="signInRefreshToken(refreshToken)"
                        >
                            Sign in by refresh token
                        </button>
                    </template>
                </div>
            </div>
        </div>
    </div>
</template>
