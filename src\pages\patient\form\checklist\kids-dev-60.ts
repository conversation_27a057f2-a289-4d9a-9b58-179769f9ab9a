import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'DayG8gEnSy',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2ismyhv.yd',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON>c mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 5 tuổi. Đ<PERSON>ng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. Bác sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2isn0ui.xrn',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'img',
                            src: 'https://bikestead.com/wp-content/uploads/2022/12/What-Is-A-Balance-Bike-Or-Strider-Bike.jpg',
                            alt: '',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2isx1uh.kkp',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết tuân theo luật chơi hoặc biết đổi lượt, khi chơi trò chơi với những đứa trẻ khác',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isx1uhkkp',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2isztkw.k5',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Hát, nhảy hoặc biểu diễn cho cha mẹ xem',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isztkwk5',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2it2hgk.yqr',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Làm những công việc đơn giản ở nhà, chẳng hạn như xếp tất thành từng đôi hoặc dọn bàn sau khi ăn',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2it2hgkyqr',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jr39my.d0m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Kể một câu chuyện đã được nghe hoặc tự tưởng tượng ra có ít nhất hai sự kiện. Ví dụ, một con mèo bị mắc kẹt trên cây và chú cảnh sát đã cứu nó.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jr39myd0m',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jr5bf4.og',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Trả lời các câu hỏi đơn giản về một cuốn sách hoặc một câu chuyện sau khi được đọc hoặc kể cho nghe',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jr5bf4og',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jr6vlo.fb6',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Có thể tiếp tục trò chuyện với nhiều hơn ba lượt hỏi - đáp  qua lại',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jr6vlofb6',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jr7wmp.hsq',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Sử dụng hoặc nhận biết các vần điệu đơn giản (chi chi chành chành, bống bống bang bang)',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jr7wmphsq',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jra3xc.h5m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Đếm từ 1 đến 10',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jra3xch5m',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrbx30.tc6',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhận biết được mặt số từ 1 đến 5 khi cha mẹ chỉ vào',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrbx30tc6',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrd1wq.bgl',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Sử dụng các từ chỉ thời gian, như “hôm qua”, “ngày mai”, “sáng” hoặc “đêm”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrd1wqbgl',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrdsyq.8jk',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Giữ được sự tập trung trong 5 đến 10 phút trong các hoạt động.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrdsyq8jk',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jregdj.5a',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Viết được một số chữ cái trong tên của mình',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jregdj5a',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrf58r.yna',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Đọc được một số chữ cái khi cha mẹ chỉ vào chữ',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrf58ryna',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jrgcbw.yw',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chơi các đồ chơi nhấn nút',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrgcbwyw',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jrgdxt.r7l',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhảy lò cò trên 1 chân',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jrgdxtr7l',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    createdBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: 'ou3LIcv6ES',
    },
    updatedBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    title: '5 tuổi',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
    createdAt: '2024-10-21T09:07:37.311Z',
    updatedAt: '2024-10-22T01:55:14.320Z',
})
