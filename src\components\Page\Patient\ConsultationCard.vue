<template>
    <div class="card flex flex-col gap-2">
        <div class="flex flex-row gap-2">
            <Tag :value="consultation.type" severity="info" />
            <Tag
                :value="t(consultationState.value)"
                :severity="consultationState.severity"
            />
            <Button
                class="ml-auto font-bold"
                text
                @click="navigateTo('/patient/consultation/' + consultation._id)"
            >
                EMR
                <i class="pi pi-chevron-right"></i>
            </Button>
        </div>
        <!-- Header -->
        <div v-if="calendar">
            <span class="font-bold"
                >{{ dayjs(calendar.startTime).format('HH:mm') }}
                {{ timezoneFormat }}</span
            >,
            <span>{{ dayjs(calendar.startTime).format('DD-MM-YYYY') }}</span>
        </div>

        <!-- Body -->
        <div>
            <div class="flex flex-row items-center gap-2">
                <NuxtImg :src="providerAvatar" class="size-10 rounded-full" />
                <div>
                    <p class="font-bold">
                        {{ consultation.provider.title }}
                        {{ consultation.provider.name }}
                    </p>
                    <p class="line-clamp-1">
                        {{ consultation.reason }}
                    </p>
                </div>
            </div>
        </div>
        <div class="flex w-full flex-row justify-end gap-2">
            <!-- <NuxtLink v-for="action in consultationTypeActions[consultationType][
                consultation.state
            ]" :key="action.name" :to="'/patient/consultation/' + consultation._id">
                <Button :label="action.name" :severity="action.severity" :icon="icons ? action.icon : ''" />
            </NuxtLink> -->
            <Button v-for="(action, i) in consultationActions" :key="i">{{
                t(action.label)
            }}</Button>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { IConsultationDetail } from '~/models/page/consultation/detail-data'

const dayjs = useDayjs()
const { t } = useI18n()

const props = defineProps({
    icons: {
        type: Boolean,
        default: false,
    },
    consultation: {
        type: Object as PropType<IConsultationDetail>,
        required: true,
    },
    calendar: {
        type: Object,
        default: null,
    },
})

const timezoneFormat = computed(() => {
    const timezoneStr = dayjs(props.calendar.startTime).format('[GMT]Z')
    const GMT = timezoneStr.slice(0, 4)
    let isZero = timezoneStr.slice(4, 5)
    if (isZero === '0') {
        isZero = ''
    }
    const time = timezoneStr.slice(5, 6)
    return GMT + isZero + time
})

const consultationState = computed(() => {
    switch (props.consultation.state) {
        case 'COMPLETED':
        case 'FREE':
            return {
                value: 'state:' + props.consultation.state,
                severity: 'success',
            }
        case 'REJECTED':
        case 'CANCELLED':
            return {
                value: 'state:' + props.consultation.state,
                severity: 'warn',
            }
        default:
            return {
                value: props.consultation.state,
                severity: 'info',
            }
    }
})

const consultationActions = computed(() => {
    const callActions = [
        {
            label: 'btn:voice-call',
        },
    ]
    switch (props.consultation.state) {
        case 'COMPLETED':
        case 'FREE':
            return [
                {
                    label: 'btn:prescription-and-note',
                },
            ]
        case 'WAITING':
        case 'INCONSULTATION':
            if (props.consultation.medium === 'video')
                callActions.push({
                    label: 'btn:video-call',
                })
            return callActions
        default:
            return []
    }
})

const providerAvatar = computed(
    () => props.consultation.provider?.avatar?.url ?? '',
)
</script>
<i18n lang="yaml">
en:
    state:COMPLETED: completed
    state:FREE: waived
    state:CANCELLED: cancelled
    state:REJECTED: rejected
    btn:prescription-and-note: Prescription and note
    btn:video-call: Video call
    btn:voice-call: Voice call
vi:
    state:COMPLETED: đã xong
    state:FREE: chuyển phí
    state:CANCELLED: đã hủy
    state:REJECTED: đã từ chối
    btn:prescription-and-note: Thuốc và dặn dò
    btn:video-call: Gọi video
    btn:voice-call: Gọi thoại
</i18n>
