export function useFamilyHealthHistory() {
  const familyHealthHistory = ref([
    {
      person: {
        avatar: 'https://randomuser.me/api/portraits/women/2.jpg',
        name: '<PERSON>',
      },
      relation: 'Father',
      description: 'Diagnosed with high blood pressure and Type 2 diabetes.',
    },
    {
      person: {
        avatar: 'https://randomuser.me/api/portraits/men/4.jpg',
        name: '<PERSON><PERSON>',
      },
      relation: 'Mother',
      description: 'Suffers from arthritis and occasional migraines.',
    },
    {
      person: {
        avatar: 'https://randomuser.me/api/portraits/men/5.jpg',
        name: '<PERSON><PERSON>',
      },
      relation: 'Spouse',
      description: 'Has asthma and a history of back trouble.',
    },
    {
      relation: 'Brother(s)',
      person: {
        avatar: 'https://randomuser.me/api/portraits/women/6.jpg',
        name: '<PERSON>',
      },
      description:
        'One brother diagnosed with heart disease, another with chronic headaches.',
    },
    {
      relation: 'Sister(s)',
      person: {
        avatar: 'https://randomuser.me/api/portraits/women/10.jpg',
        name: 'Cinderella',
      },
      description: 'One sister has hypothyroidism, another has scoliosis.',
    },
  ])
  return { familyHealthHistory }
}
