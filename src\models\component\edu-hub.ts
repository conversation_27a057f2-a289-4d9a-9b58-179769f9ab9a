export interface ICategoryCard {
    _id: string
    cover: string
    name: string
    slug: string
    description: string
}

export interface ITopicCard {
    _id: string
    slug: string
    name: string
    description: string
}

export interface IArticleFirstAidCard {
    _id: string
    name: string
    slug: string
    cover: string
    content: string
    updatedAt: string
}

export interface IItemCard {
    _id: string
    name: string
    cover: string
    delivery: string
    duration: string
    publishedAt: string
    provider: {
        name: string
        title: string
        avatar: string
    }
    patient: {
        name: string
        avatar: string
    }
}

export interface IItemPage {
    _id: string
    url: string
    name: string
    content: string
    delivery: string
    mimeType: string
    thumbnail: string
    provider?: string
}
