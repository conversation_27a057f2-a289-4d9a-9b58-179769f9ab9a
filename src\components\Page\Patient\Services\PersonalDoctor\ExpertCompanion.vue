<script setup lang="ts">
import { appRoutes } from '~/constants'
import {
    ref,
    useI18n,
    useMembership,
    useRouter,
    useGetAvailableDoctors,
} from '#imports'

const { t } = useI18n()
const router = useRouter()

const doctorListRef = ref<HTMLElement | null>(null)
const doctorList: any = ref(null)
const isLoading = ref(false)

const { transformedHits: availableDoctors, execute: executeGetAvailable } =
    useGetAvailableDoctors()

const fetchDoctors = async () => {
    isLoading.value = true
    await executeGetAvailable()
    isLoading.value = false
    doctorList.value = availableDoctors.value || []
}
fetchDoctors()

const { isMembership } = useMembership()

const handleClick = (provider: any) => {
    if (isMembership.value && provider.conversation !== undefined) {
        router.push({
            path: appRoutes.patient.diary(provider.conversation),
            query: {
                provider: provider?._id,
                providerUser: provider?.user,
                patient: provider?.patient,
            },
        })
    } else {
        router.push({
            path: `/patient/checkout/${provider.slug}-membership-personal-doctor`,
        })
    }
}
</script>

<template>
    <div ref="doctorListRef" class="flex flex-col gap-5 p-2">
        <template v-if="isLoading">
            <div v-for="i in 6" :key="i" class="mx-2 flex">
                <div class="shrink-0">
                    <span class="block size-28 rounded-md bg-zinc-200"></span>
                </div>

                <div class="ms-4 mt-2 w-full">
                    <p
                        class="h-4 rounded-full bg-zinc-200"
                        style="width: 40%"
                    ></p>

                    <ul class="mt-5 space-y-3">
                        <li class="h-3 w-full rounded-full bg-zinc-200"></li>
                        <li class="h-3 w-full rounded-full bg-zinc-200"></li>
                        <li class="h-3 w-full rounded-full bg-zinc-200"></li>
                    </ul>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="relative">
                <TransitionGroup
                    tag="div"
                    class="flex flex-col gap-5"
                    enter-active-class="transition ease-out duration-500"
                    enter-from-class="opacity-0 translate-y-5"
                    enter-to-class="opacity-100 translate-y-0"
                    leave-active-class="transition ease-in duration-500"
                    leave-from-class="opacity-100 translate-y-0"
                    leave-to-class="opacity-0 translate-y-5"
                >
                    <div
                        v-for="doctor in doctorList"
                        :key="doctor._id"
                        class="flex w-full max-w-md rounded-lg transition-all"
                    >
                        <div class="relative h-28 w-28 flex-shrink-0">
                            <img
                                :src="doctor?.avatar?.url || ''"
                                :alt="doctor?.name"
                                loading="lazy"
                                decoding="async"
                                width="200"
                                height="200"
                                :srcset="`${doctor?.avatar?.url || ''} 200w, ${doctor?.avatar?.url || ''} 300w`"
                                sizes="(min-width: 768px) 300px, 200px"
                                class="h-full w-full rounded-lg border object-cover"
                            />
                            <div
                                v-if="doctor.isEminent"
                                class="absolute bottom-0 left-0 right-0 flex items-center justify-center bg-yellow-50 px-2 py-1 text-center text-xs font-medium text-yellow-700 opacity-80"
                            >
                                <span>{{ t('eminent-provider') }}</span>
                                <iconify-icon
                                    icon="mage:star-fill"
                                    class="ml-1 text-xs text-yellow-400"
                                />
                            </div>
                        </div>
                        <div class="ml-3 flex flex-col justify-between">
                            <div>
                                <p
                                    class="line-clamp-2 whitespace-break-spaces break-words text-base font-semibold"
                                >
                                    {{ doctor?.title }}. {{ doctor?.name }}
                                </p>
                                <p
                                    v-if="doctor?.specialties"
                                    class="text-sm text-zinc-600"
                                >
                                    {{ doctor?.specialties.join(', ') }}
                                </p>
                            </div>
                            <div class="mt-4">
                                <Button @click="handleClick(doctor)">
                                    {{ t('btn:buy-now') }}
                                </Button>
                            </div>
                        </div>
                    </div>
                </TransitionGroup>
            </div>
        </template>
    </div>
</template>
