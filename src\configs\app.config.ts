import type { AppConfig } from '@nuxt/schema'

export const app: Partial<AppConfig> | any = {
    head: {
        charset: 'utf-16',
        viewport: 'width=device-width,initial-scale=1',
        meta: [{ name: 'description', content: 'wellcare' }],
        script: [
            {
                src: 'https://code.iconify.design/iconify-icon/2.1.0/iconify-icon.min.js',
                defer: true,
            },
            {
                src: 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/gsap.min.js',
                defer: true,
            },
            {
                src: 'https://cdnjs.cloudflare.com/ajax/libs/gsap/3.12.2/ScrollTrigger.min.js',
                defer: true,
            },
        ],
    },
    pageTransition: { name: 'page-right', mode: 'out-in' },
    layoutTransition: { name: 'layout', mode: 'out-in' },
}
