{"objectId": "YeMXuAaRXC", "pages": [{"id": "main", "type": "input", "elements": [{"id": "title", "type": "display", "properties": {"display": {"component": "html_block_content", "tag": "h1", "content": "Follow Up"}}}, {"id": "1", "type": "input", "properties": {"input": {"key": "recovery", "component": "multiple_choice", "label": "How is {name} recovering since the last consultation?", "choices": [{"value": "well", "label": "Recovered"}, {"value": "better", "label": "Getting better, but not yet fully recovered."}, {"value": "worse", "label": "Condition is worsening"}]}}}]}, {"id": "end1", "type": "end", "elements": [{"id": "4", "type": "display", "properties": {"display": {"component": "html_block_content", "tag": "p", "content": "Excellent! Your steady recovery is the outcome we are eagerly looking forward to."}}}]}, {"id": "end2", "type": "end", "elements": [{"id": "4", "type": "display", "properties": {"display": {"component": "html_block_content", "tag": "p", "content": "Please carefully read and follow the doctor's note or treatment plan, and follow-up as needed."}}}]}], "computes": [{"key": "name", "component": "hidden_field"}, {"key": "consultationId", "component": "hidden_field"}], "machine": {"id": "followUpStateMachine", "initial": "main", "states": {"main": {"on": {"ANSWER": [{"target": "end1", "actions": [], "guard": "isRecovered"}, {"target": "end2", "actions": [], "guard": "isNotFullyRecovered"}]}}, "end1": {"type": "final"}, "end2": {"type": "final"}}, "actions": {}, "guards": {"isRecovered": "return true", "isNotFullyRecovered": "return true"}}, "title": "Follow Up", "status": "draft", "submission": "remote", "space": {"__type": "Pointer", "className": "Space", "objectId": "ZhKJvCjuhu"}}