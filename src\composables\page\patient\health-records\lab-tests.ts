import type { IFile } from '~/models/component/file'
import type { IFolder } from '~/models/component/folder.interface'
import type { ILabTest } from '~/models/page/health-records'
export function useLabTests() {
    const labTests: Ref<ILabTest[]> = ref([
        {
            files: [
                'https://images.drlogy.com/assets/uploads/lab/image/cbc-absolute-count-test-report-format-example-sample-template-drlogy-lab-report.webp',
            ],
            date: 'Last week',
        },
        {
            files: [
                'https://5.imimg.com/data5/SELLER/Default/2022/7/SR/QN/OT/48922289/vein-pathology-lab-software-pdf-export-reports-mis-reports-500x500.jpg',
            ],
            date: 'Sat 7 Jan',
        },
        {
            files: [
                'https://s3.qima.com/images/responsive/SampleReports/report_LT_REACH.jpg',
            ],
            date: 'Last year',
        },
    ])

    const labTestToFolder = (labTest: ILabTest): IFolder => {
        return {
            date: labTest.date,
            files: labTest.files,
            tag: { label: 'Lab Test', color: 'dodgerblue' },
            title: labTest.date,
        }
    }
    const labTestToFile = (labTest: ILabTest): IFile => {
        return {
            date: labTest.date,
            label: labTest.date,
            tag: { label: 'Lab Test', color: 'dodgerblue' },
            url: labTest.files[0],
        }
    }

    return { labTests, labTestToFolder, labTestToFile }
}
