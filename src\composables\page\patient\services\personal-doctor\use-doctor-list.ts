// composables/use-doctor-list.ts
import { ref, watch } from '#imports'
import type { IProvider } from '~/models'
import type { Ref, ComputedRef } from '#imports'
import { useGetAssignedDoctor, useGetAvailableDoctors } from '.'

export interface DoctorWithConversation extends IProvider {
    conversation?: string
    provider?: string
    providerUser?: string
    patient?: string
}

export function useDoctorList(userId: Ref<string> | ComputedRef<string>) {
    const isLoading = ref(false)
    const doctors = ref<DoctorWithConversation[]>([])
    const personalDoctorFlag = ref(false)
    const error = ref<any>(null)

    const {
        transformedProviders: assignedDoctors,
        additionalInfo: assignedInfo,
        execute: executeGetAssigned,
    } = useGetAssignedDoctor(userId)

    const { transformedHits: availableDoctors, execute: executeGetAvailable } =
        useGetAvailableDoctors()

    const fetchDoctors = async () => {
        if (!userId.value) {
            doctors.value = []
            return
        }

        isLoading.value = true
        error.value = null

        try {
            await executeGetAssigned()

            if (assignedDoctors.value && assignedDoctors.value.length > 0) {
                doctors.value = assignedDoctors.value.map((doctor, index) => ({
                    ...doctor,
                    conversation: assignedInfo.value[index]?.conversation,
                    patient: assignedInfo.value[index]?.related?._id,
                }))
                personalDoctorFlag.value = true
            } else {
                await executeGetAvailable()
                doctors.value = availableDoctors.value || []
            }
        } catch (err) {
            error.value = err
            console.error('Error fetching doctors:', err)
        } finally {
            isLoading.value = false
        }
    }

    watch(
        () => userId.value,
        (newValue) => {
            if (newValue) {
                fetchDoctors()
            }
        },
        { immediate: true },
    )

    return {
        isLoading,
        doctors,
        error,
        assignedInfo,
        refresh: fetchDoctors,
        personalDoctorFlag,
    }
}
