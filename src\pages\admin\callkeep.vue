<script setup lang="ts">
import { navigateTo, reactive, ref } from '#imports'
import { Capacitor } from '@capacitor/core'
import { CapacitorCallkeep } from '@wellcare/capacitor-callkeep'

const { user } = useUserInfo({ scope: ['_id'] })
const { logout, getIdentifier, init } = useCallkeep()
const { getPlayerId } = useOnesignal()

const errorMessage = ref<string>('')
const calling = ref<boolean>(false)
const identifier = ref<string>()
const playerId = ref<string>()
const callData = reactive({
    uuid: '20c69e94-8adc-4dff-9b35-5408f35d6cb8',
    conversation: '66b587e5fcf263f4d70af3fa',
    callTitle: 'User test',
    callSubtitle: 'calling ... ',
})

const uuid = ref('')

const onDisplay = async () => {
    calling.value = true
    await CapacitorCallkeep.displayIncomingCall({
        conversation: callData.conversation,
        callTitle: callData.callTitle,
        callSubtitle: callData.callSubtitle,
        uuid: callData.uuid,
    })
}

const onCancel = async () => {
    calling.value = false
    const platform = Capacitor.getPlatform()
    let id = ''
    if (platform === 'ios') id = callData.uuid
    else if (platform === 'android') id = callData.conversation
    await CapacitorCallkeep.endCall({
        callId: id,
    })
}

const onEndcall = async () => {
    await CapacitorCallkeep.endCall({
        callId: uuid.value,
    })
}

const onLogout = async () => {
    logout()
    if (import.meta.client) {
        window.location.reload()
    }
}

onMounted(async () => {
    identifier.value = await getIdentifier()
    playerId.value = await getPlayerId()
    await init()
})
</script>

<template>
    <div
        class="flex min-h-screen items-center justify-center bg-gray-100 p-4 dark:bg-gray-900"
    >
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <!-- BackToHome -->
            <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
                <button @click="navigateTo('/debug')">
                    <span class="underline">Back to home</span>
                </button>
            </div>

            <!-- Main Info Card -->
            <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
                <span class="mb-2 block text-gray-600 dark:text-gray-400">
                    User Id {{ user._id }}
                </span>
                <span class="mb-2 block text-gray-600 dark:text-gray-400">
                    identifier: {{ identifier }}
                </span>
                <span class="mb-2 block text-gray-600 dark:text-gray-400">
                    player: {{ playerId }}
                </span>
                <span class="mb-2 block text-red-600 dark:text-gray-400">
                    {{ errorMessage }}
                </span>
                <div class="mb-2 flex flex-col">
                    <span>UUID</span>
                    <InputText v-model="uuid" />
                </div>

                <div>
                    <Button
                        v-if="!calling"
                        class="mx-2"
                        severity="info"
                        @click="onDisplay"
                        >Display</Button
                    >
                    <Button
                        v-else
                        class="mx-2"
                        severity="danger"
                        @click="onCancel"
                        >Cancel</Button
                    >
                    <Button class="mx-2" severity="danger" @click="onLogout"
                        >Logout</Button
                    >
                </div>
                <div>
                    <Button class="mx-2" severity="danger" @click="onEndcall"
                        >End</Button
                    >
                </div>
            </div>
        </div>
    </div>
</template>
