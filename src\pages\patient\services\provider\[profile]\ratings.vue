<script setup lang="ts">
import { ElasticIndex } from '~/models'
import type { HitNotionRatings } from '~/models'

definePageMeta({
    layout: 'nested-page',
})

const COMMENT_LENGTH = 200

const layoutStore = useLayoutStore()

const { t } = useI18n()
const { params } = useRoute()

const ratingSize = ref(10)
const { hits, total } = useFetchElasticWithDefault<HitNotionRatings>(
    ElasticIndex.NOTION_RATINGS,
    {
        size: ratingSize,
        filters: [
            { term: { 'page.properties.IsFeature': true } },
            {
                term: {
                    'page.properties.ProviderSlug.keyword': params.profile,
                },
            },
            {
                term: {
                    'page.properties.Sentiment.keyword': 'positive',
                },
            },
        ],
    },
)

const ratings = computed(() =>
    hits.value?.map((hit) => ({
        _id: hit._id || '',
        authorGender: hit.page?.properties?.RatingByGender || '',
        authorName: hit.page?.properties?.RatingByName || '',
        avatar: '',

        dateCreated: hit.page?.properties?.CreatedAt?.start || '',
        fullComment: hit.page?.properties?.Comment || '',

        halfComment:
            hit.page?.properties?.Comment?.substring(0, COMMENT_LENGTH) +
            (hit.page?.properties?.Comment?.length || 0 > COMMENT_LENGTH
                ? '...'
                : ''),
        isOverlap:
            (hit.page?.properties?.Comment?.length || 0) > COMMENT_LENGTH,
        notionLink: hit.page?.url || '',
        stars: hit.page?.properties.Rating || 0,
    })),
)

const onLoadMoreRating = () => {
    if (total.value && ratings.value.length < total.value) {
        ratingSize.value += 10
    }
}

onMounted(() => {
    layoutStore.setTitle(t('page:title'))
})
</script>

<template>
    <SharedSmoothScroll @arrived-bottom="onLoadMoreRating">
        <template #main>
            <div
                v-for="rating in ratings"
                :key="rating._id"
                class="app-margin-x mb-3"
            >
                <WTestimonialFeedbackCard :rating="rating" />
            </div>
        </template>
    </SharedSmoothScroll>
</template>

<i18n lang="json">
{
    "en": {
        "page:title": "Rating"
    },
    "vi": {
        "page:title": "Nhận xét"
    }
}
</i18n>
