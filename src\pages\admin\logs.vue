<template>
    <div class="log-viewer mt-10">
        <Button class="mb-4" text @click="() => navigateTo('/')"
            >Go back</Button
        >
        <h2 class="mb-4 text-2xl font-bold">Logging List</h2>

        <!-- Log Input Form -->
        <div class="log-input-form mb-4 rounded-md border p-4">
            <p class="mb-4 text-xl font-semibold">Add New Log</p>

            <div class="mb-4 grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                    <label for="level" class="block font-medium">Level</label>
                    <Dropdown
                        v-model="log.level"
                        :options="[
                            { label: 'Verbose', value: 'verbose' },
                            { label: 'Warn', value: 'warn' },
                            { label: 'Debug', value: 'debug' },
                            { label: 'Error', value: 'error' },
                        ]"
                        option-label="label"
                        option-value="value"
                        placeholder="Select Level"
                        class="w-full"
                    />
                </div>

                <div>
                    <label for="message" class="block font-medium"
                        >Message</label
                    >
                    <InputText
                        v-model="log.message"
                        placeholder="Enter message"
                        class="w-full"
                    />
                </div>

                <div>
                    <label for="context" class="block font-medium"
                        >Context</label
                    >
                    <InputText
                        id="context"
                        v-model="log.context"
                        placeholder="Enter context"
                        class="w-full"
                    />
                </div>

                <div class="col-span-full">
                    <label for="data" class="block font-medium"
                        >Data (Optional)</label
                    >
                    <Textarea
                        v-model="log.data"
                        placeholder="Enter JSON data"
                        class="w-full"
                        rows="4"
                    />
                </div>
            </div>
            <Button
                label="Copy"
                class="p-button-info mx-3"
                :disabled="!selectedLog"
                @click="onCopy"
            />
            <Button
                label="Add Log"
                icon="pi pi-plus"
                class="p-button-success"
                @click="addNewLog"
            />
            <Button
                label="Clear All Logs"
                icon="pi pi-times"
                class="p-button-danger mx-3"
                severity="danger"
                @click="clearLog"
            />
        </div>
        <DataTable
            v-model:expanded-rows="expandedRows"
            v-model:selection="selectedLog"
            :value="logs"
            responsive-layout="scroll"
            :paginator="true"
            :rows="5"
            data-key="message"
        >
            <Column
                selection-mode="single"
                style="width: 3rem"
                :exportable="false"
            ></Column>
            <Column expander style="width: 3rem" />
            <Column field="level" header="Level" />
            <Column field="message" header="Message" />
            <Column field="context" header="Context" />
            <Column field="time" header="Time" />

            <template #expansion="slotProps">
                <div class="p-3">
                    <pre class="rounded p-2">{{
                        JSON.stringify(slotProps.data, null, 2)
                    }}</pre>
                </div>
            </template>
        </DataTable>
    </div>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref, toRaw } from '#imports'
import { Clipboard } from '@capacitor/clipboard'
import { useLogs } from '~/composables/use-logs'

const { addNativeLogs, getNativeLogs, clearNativeLogs } = useLogs()

const logs = ref([])

const log = reactive({
    level: 'debug',
    message: '',
    context: '',
    data: '',
    time: '',
})

const selectedLog = ref(null)

const expandedRows = ref<any[]>([])

const addNewLog = async () => {
    log.time = new Date().toLocaleString()
    const data = { ...toRaw(log) }
    await addNativeLogs(data)
    logs.value.unshift(data)
    log.message = ''
    log.context = ''
    log.data = ''
    log.time = ''
}

const clearLog = async () => {
    await clearNativeLogs()
    logs.value = []
    log.message = ''
    log.context = ''
    log.data = ''
    log.time = ''
}

const onCopy = async () => {
    let data = selectedLog.value?.data
    try {
        data = JSON.stringify(data)
    } catch (e) {
        console.error(e)
    }
    await Clipboard.write({
        string: data,
    })
}

onMounted(async () => {
    logs.value = await getNativeLogs()
})
</script>
