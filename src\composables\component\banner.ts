import {
    computed,
    ref,
    useFetchElastic,
    useGtm,
    useRoute,
    useRouter,
} from '#imports'

import { ElasticIndex } from '~/models'
import type { ComponentBanner, HitNotionBanners } from '~/models'

import type { BannerPosition } from '~/types'

export function useBanner(query: { position: BannerPosition; size: number }) {
    const { path } = useRoute()
    const { push } = useRouter()

    const gtm = useGtm()

    const dataLayer = ref({})

    const { hits } = useFetchElastic<HitNotionBanners>(
        ElasticIndex.NOTION_BANNERS,
        {
            filters: [
                {
                    term: {
                        'page.properties.App.keyword':
                            'app.wellcare.vn',
                    },
                },
                {
                    term: {
                        'page.properties.Condition.keyword': '.*'
                    }
                },
                {
                    term: {
                        'page.properties.Status.keyword': 'Done',
                    }
                }
            ],
        },
    )
    const bannerDirective = computed(() => {
        return hits.value.map((hit) => ({
            file: hit.page?.properties?.Files?.[0]?.url,
            target: hit.page?.properties?.Target,
        }))
    })

    const banners = computed(() => {
        const filtered =
            hits?.value.filter((hit) => {
                try {
                    const regex = new RegExp(
                        hit.page?.properties?.Condition || '',
                        'i',
                    )
                    // console.log('[banner] regex: ', regex, ' path: ', path)
                    return regex.test(path)
                } catch {
                    console.error(
                        `Invalid regex in condition: ${hit.page?.properties?.Condition}`,
                    )
                    return false
                }
            })
                .map((hit) => ({
                    weight: hit.page?.properties?.Weight,
                    source: hit.page?.properties?.Files?.[0]?.url,
                    target: hit.page?.properties?.Target,
                    position: hit.page?.properties?.Position,
                })) || []
        return filtered
    })

    const banner: ComputedRef<ComponentBanner> = computed(() => {
        // track banner viewed
        return banners.value.find((banner) => {
            let isShown = false
            if (banner.position === query.position) isShown = true
            if (banner.target === path) isShown = false
            return isShown
        })
    })

    const impress = () => {
        console.info('[track-banner] impress', banner.value)
    }

    const click = () => {
        // track banner clicked
        console.info('[track-banner] clicked', banner.value)
        push({
            path: banner.value.target,
        })
        gtm.push({
            ...dataLayer.value,
            event: 'select_content',
        })
    }

    return { banner, click, banners, impress, bannerDirective }
}
