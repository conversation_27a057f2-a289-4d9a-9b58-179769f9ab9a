export interface IAbility {
    [state: string]: {
        [role: string]: {
            sections: {
                [secion<PERSON>ey in ISectionKey]?: Array<ISectionAction>
            }
            actions: Array<IConsultationAction>
        },
    }
}

export enum Role {
    ADMIN = "admin",
    USER = "user",
    PROVIDER = "provider"
}

export type ISectionAction = "view" | "create" | "update" | "delete"

export type ISectionKey = "check-in-message" | "follow-up-message" | "expectation" | "description" | "questions" | "form-request" | "observation" | "vaccination" | "lab-files" | "medical-files" | "symptom-files" | "prev-consultation" | "symptom" | "diagnosis" | "prescription" | "note" | "doctor-files" | "presonal-note" | "doctor-preview" | "doctor-info" | "additional-request"

export type IConsultationAction = "cancel" | "reschedule" | "complete" | "call-audio" | "call-video"