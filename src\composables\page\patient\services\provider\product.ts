import { BUNDLE_ID } from '~/constants'

export function usePagePatientServicesProviderProduct() {
    const { locale } = useI18n()
    const { $fetchWellcare } = useNuxtApp()

    const { isWeb } = useDevice()

    const provider = ref<string>('')

    const tags = computed(() => {
        if (isWeb.value) {
            return [window.location.host]
        } else {
            return [BUNDLE_ID]
        }
    })

    const { data, execute } = useAsyncData(
        'get-all-products',
        () => {
            if (provider.value) {
                return $fetchWellcare('/api/ecommerce/product/search-all', {
                    method: 'GET',
                    params: {
                        filters: JSON.stringify({
                            sellable: true,
                            state: 'published',
                            provider: provider.value,
                            tags: {
                                $in: tags.value,
                            },
                        }),
                        fields: 'sku',
                        sort: 'order',
                        pageSize: 999,
                        locale: locale.value,
                    },
                })
            } else {
                return new Promise((resolve) => resolve(undefined))
            }
        },
        {
            immediate: false,
        },
    )

    const allProducts = computed(
        () =>
            data.value?.results?.data?.map((product: any) => ({
                _id: product._id,
                sku: product.sku,
                slug: product.slug,
            })) ?? [],
    )

    const getAllProducts = async (providerId: string) => {
        provider.value = providerId
        await execute()
    }

    return { allProducts, getAllProducts }
}
