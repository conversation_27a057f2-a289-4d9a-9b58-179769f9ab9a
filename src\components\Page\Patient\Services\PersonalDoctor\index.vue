<script setup>
// const { userId } = defineProps({
//     userId: {
//         type: String,
//         default: '',
//     },
// })

// const { assignedInfo } = useDoctorList(computed(() => userId))

// const { isMembership } = useMembership()

const { t } = useI18n()
const router = useRouter()

const handleDirectTo = () => {
    router.push({
        path: '/patient/personal-doctor',
    })
}
</script>

<template>
    <SharedPageSection
        :title="t('personal:doctor')"
        :pt="{
            content: 'mx-6',
        }"
    >
        <div class="relative flex w-full flex-col">
            <!-- Container -->
            <div class="relative h-48 w-full overflow-hidden rounded-xl">
                <img
                    class="h-full w-full object-cover object-top"
                    src="/images/personal-doctor.png"
                />
                <!-- Overlay gradient -->
                <div
                    class="absolute inset-0 bg-gradient-to-t from-black/60 via-black/40 to-transparent"
                ></div>
                <!-- Text overlay -->
                <div class="absolute inset-0 flex items-end p-4 text-white">
                    <div class="flex flex-col">
                        <p class="text-sm font-medium">
                            Có được một bác sĩ hiểu mình và chăm sóc sức khỏe
                            thường xuyên cho mình, là điều vô giá!
                        </p>
                        <!-- CTA -->
                        <Button class="w-fit self-end" @click="handleDirectTo">
                            <span>{{ t('btn:buy-now') }}</span>
                        </Button>
                    </div>
                </div>
            </div>
        </div>
    </SharedPageSection>
</template>
