import { useAsyncData, useDayjs, useNuxtApp } from '#imports'

export function useConsultationSearch() {
    const dayjs = useDayjs()
    const { $fetchWellcare } = useNuxtApp()

    const user = ref<string>('')

    const { data: searchResults, execute: executeSearchConsultation } =
        useAsyncData(
            'no-rating-consultations',
            () =>
                $fetchWellcare('/consultation-server/v2/consultation/search', {
                    method: 'GET',
                    query: {
                        fields: 'user,provider,state,rating,type',
                        populate: JSON.stringify([
                            { path: 'user' },
                            { path: 'provider' },
                        ]),
                        filter: {
                            closedAt: {
                                $gte: dayjs().subtract(3, 'day').toISOString(),
                                $lte: dayjs().toISOString(),
                            },
                            user: user.value,
                            state: {
                                $in: ['COMPLETED', 'FREE'],
                            },
                            type: 'indepth',
                        },
                    },
                }),
            {
                immediate: false,
            },
        )

    const consultations = computed(() =>
        searchResults.value?.results.map((result: any) => ({
            ...result?.consultation,
            rating: result?.rating,
        })),
    )

    const searchConsultation = async (userId: string) => {
        user.value = userId
        await executeSearchConsultation()
    }

    return { consultations, searchConsultation }
}
