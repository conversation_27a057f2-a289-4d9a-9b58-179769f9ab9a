<template>
    <div class="flex flex-col flex-wrap gap-2">
        <div
            v-for="patient in recentPatients"
            :key="patient.name"
            class="card flex flex-row items-center"
        >
            <Avatar
                :image="patient.avatar"
                shape="circle"
                class="mr-2 object-cover"
            />
            <NuxtLink to="patients/[id]" class="link">
                <p class="m-0 font-medium">{{ patient.name }}</p>
            </NuxtLink>

            <i class="pi pi-circle-fill mx-2 text-[4px] text-surface-400" />
            <small> {{ patient.lastActivity.time }}</small>
            <Tag :value="patient.lastActivity.status" class="ml-auto" />
        </div>
    </div>
</template>

<script setup lang="ts">
const recentPatients = [
    {
        avatar: 'https://www.europarl.europa.eu/mepphoto/251859.jpg',
        name: '<PERSON> bob',
        lastActivity: {
            time: 'Monday, 2 Jul',
            status: 'Check-in',
        },
    },
    {
        avatar: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSxwMbFLye8AzAYen2JjlxqImoZT9wM-Lo-sw&s',
        name: 'Jimmy doo',
        lastActivity: {
            time: 'Friday, 6 Nov',
            status: 'Followup',
        },
    },
    {
        avatar: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSxwMbFLye8AzAYen2JjlxqImoZT9wM-Lo-sw&s',
        name: 'Jimmy doo',
        lastActivity: {
            time: 'Friday, 6 Nov',
            status: 'Sent',
        },
    },
]
</script>
