FROM mhealthvn/node-builder:20.14 as builder
RUN apk --no-cache add zip
WORKDIR /usr/src/app

ARG GIT_TOKEN
ENV GIT_TOKEN=$GIT_TOKEN
COPY package.json ./
RUN yarn install --silent

COPY . .
COPY .env.production.android .env
RUN yarn generate

FROM alvrme/alpine-android:android-33-jdk17 as publisher
RUN apk update \ 
    && apk upgrade --available \
    && apk --no-cache --virtual add git curl python3 py3-pip make g++ gcc nodejs npm gradle
WORKDIR /usr/src/app
ARG GIT_TOKEN
ENV GIT_TOKEN=$GIT_TOKEN
ARG VERSION_CODE
ARG VERSION_NAME
ARG TELEGRAM_TOKEN
ARG TELEGRAM_CHAT
ENV TELEGRAM_TOKEN=$TELEGRAM_TOKEN
ENV TELEGRAM_CHAT=$TELEGRAM_CHAT
COPY . .
COPY --from=builder /usr/src/app/.output .output
COPY --from=builder /usr/src/app/node_modules ./node_modules
RUN npx cap sync android
RUN (echo VERSION_CODE=$VERSION_CODE; echo VERSION_NAME=$VERSION_NAME) > ./android/version.properties
RUN cd ./android && ./gradlew :app:assembleRelease
RUN curl -L -X POST "https://api.telegram.org/bot$TELEGRAM_TOKEN/sendMessage?chat_id=$TELEGRAM_CHAT&text=vn.wellcare@${VERSION_NAME}-${VERSION_CODE}+https://play.google.com/apps/internaltest/4700349553138876148"
RUN cd ./android && ./gradlew :app:publishReleaseBundle --scan