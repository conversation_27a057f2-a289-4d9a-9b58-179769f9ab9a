import { ElasticIndex } from '~/models'
import type { HitNotionWebsite } from '~/models'

import _ from 'lodash'

export function usePageSettingsSupportFAQ() {
    const runtimeConfig = useRuntimeConfig()
    const { page }: any = runtimeConfig.public
    const sites = page.site.split(',')
    const status = page.status.split(',')

    const { hits: questionHits } = useFetchElastic<HitNotionWebsite>(
        ElasticIndex.NOTION_WEBSITE,
        {
            filters: [
                {
                    terms: {
                        'page.properties.Site.keyword': sites,
                    },
                },
                {
                    terms: {
                        'page.properties.Status.keyword': status,
                    },
                },
                {
                    term: {
                        'page.properties.Type.keyword': 'FAQ',
                    },
                },
            ],
            size: 100,
            _source: {
                includes: ['page', 'blockstring'],
            },
        },
    )

    const faqs = computed(() => {
        const categories = _.groupBy(questionHits.value, (hit) => {
            const parent =
                hit?.page?.properties?.['Parent item']?.[0]?.properties
            return (parent?.Slug || '') + (parent?.Name || '')
        })

        return Object.values(categories)
            .map((hits) => {
                const category =
                    hits[0]?.page?.properties?.['Parent item']?.[0]?.properties

                const questions = hits.map(
                    ({ page: { properties, blocks } }: HitNotionWebsite) => ({
                        slug: properties.Slug || '',
                        content: properties?.Name,
                        answer: blocks as any,
                    }),
                )

                return {
                    category,
                    questions,
                }
            })
            .filter(({ questions }) => questions.length > 0)
    })

    return { faqs }
}
