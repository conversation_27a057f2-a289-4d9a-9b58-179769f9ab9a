import { type Ref, ref } from 'vue'
import type { IFile } from '~/models/component/file'
import type { IFolder } from '~/models/component/folder.interface'
import type { IMedication } from '~/models/page/health-records'

export function useMedications() {
    const medications: Ref<IMedication[]> = ref([
        {
            date: 'Mon 5 2025',
            files: [
                {
                    url: 'https://ohiostate.pressbooks.pub/app/uploads/sites/160/h5p/content/5/images/image-5bd08790e1864.png',
                },
                {
                    url: 'https://www.slideteam.net/media/catalog/product/cache/1280x720/o/n/one_page_health_fact_sheet_for_high_school_example_report_ppt_pdf_document_slide01.jpg',
                },
            ],
        },
        {
            date: 'Mon 5 2025',
            files: [
                {
                    url: 'https://www.safetyandquality.gov.au/sites/default/files/styles/resource_410x594px/public/2021-12/nmmp_2021_thumbnail.png?itok=LkdFBJBV',
                },
            ],
        },
        {
            date: 'Mon 5 2025',
            files: [
                {
                    url: 'https://images.sampleforms.com/wp-content/uploads/2016/10/Health-Care-Insurance-Form.jpg',
                },
                {
                    url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS7NpsERvjbMEIKzB0pcbSLq9tDnZVH88FY5g&s',
                },
            ],
        },
        {
            date: 'Mon 5 2025',
            files: [
                {
                    url: 'https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRroospqyfLP23dJ8fHt_yn1Cn18nIGM-dd9A&s',
                },
            ],
        },
    ])

    const mapMedicationToFolder = (medication: IMedication): IFolder => {
        return {
            files: medication.files?.map((file) => file.url) || [],
            tag: { label: 'Medication', color: 'darkviolet' },
            title: medication.date,
            date: medication.date,
        }
    }

    const mapMedicationToFile = (medication: IMedication): IFile => {
        return {
            date: medication.date,
            label: medication.date,
            tag: { label: 'Medication', color: 'darkviolet' },
            url: medication.files[0].url,
        }
    }

    return { medications, mapMedicationToFolder, mapMedicationToFile }
}
