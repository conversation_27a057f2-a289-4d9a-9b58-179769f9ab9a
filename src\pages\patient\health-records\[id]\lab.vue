<script setup lang="ts">
import { useFileSearch } from '~/composables'
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

definePageMeta({
    middleware: 'auth-capacitor',
    layout: 'nested-page',
})

const { t } = useI18n()
const layoutStore = useLayoutStore()

const route = useRoute()

const userId = computed(() => route?.params?.id as string)

const projectId = ref('emr')
const tags = ['lab']
const initialQuery = {
    skip: 0,
    limit: 50,
    count: true,
    fields: 'tags, description, url, labels, folder, state, progress, name, description, sprite, thumbnail, mimetype, blurhash, updatedAt, createdAt, capturedAt, duration, size',
    filter: {
        tags: tags,
    },
}

const { data: labFiles, refresh } = useFileSearch(
    'lab-files',
    userId,
    projectId,
    initialQuery,
)

const sortedLabs = computed(() => {
    return [...(labFiles.value || [])].sort(
        (a, b) =>
            new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime(),
    )
})

const handleSuccess = () => {
    refresh()
}

nextTick(() => {
    layoutStore.setTitle(t('Lab and Radiology Results'))
})

onUnmounted(() => {
    layoutStore.$reset()
})
</script>

<template>
    <div class="mt-8 flex flex-col px-6 pb-10">
        <template v-if="sortedLabs?.length > 0">
            <PagePatientHealthRecordsAddFileCard
                project="emr"
                :user="userId"
                :tags="tags"
                :as-button="true"
                @upload-success="handleSuccess"
            >
                <template #default="{ handleClick, loading }">
                    <div class="flex w-full">
                        <Button
                            :disabled="loading"
                            severity="secondary"
                            class="ml-auto"
                            @click="handleClick"
                        >
                            <iconify-icon
                                icon="mynaui:plus-solid"
                                class="text-xl"
                            />
                            {{ t('upload') }}
                        </Button>
                    </div>
                </template>
            </PagePatientHealthRecordsAddFileCard>
            <div class="mt-5 flex flex-row flex-wrap gap-3">
                <template v-for="lab in sortedLabs" :key="lab?.url">
                    <SharedFileCard :file="lab" layout="Grid" />
                </template>
            </div>
        </template>
        <PagePatientHealthRecordsAddFileCard
            v-else-if="!loading && !sortedLabs?.length"
        />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "Lab and Radiology Results": "Lab and Radiology Results",
        "Add medication": "Add medication"
    },
    "vi": {
        "Lab and Radiology Results": "Xét nghiệm và chẩn đoán hình ảnh",
        "Add medication": "Thêm"
    }
}
</i18n>
