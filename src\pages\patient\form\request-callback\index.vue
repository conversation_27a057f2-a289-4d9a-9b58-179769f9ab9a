<script setup lang="ts">
import {
    reactive,
    onMounted,
    useDayjs,
    useRouter,
    useI18n,
    definePageMeta,
    useRoute,
    useRuntimeConfig,
    useLayoutStore,
    ref,
    computed,
    useConsultationSession,
} from '#imports'
import type { ManipulateType } from 'dayjs'
import { appRoutes } from '~/constants'
import { form } from './form'

definePageMeta({
    layout: 'nested-page',
})

const dayjs = useDayjs()
const router = useRouter()
const { t } = useI18n()
const { query }: any = useRoute()
const layoutStore = useLayoutStore()
const runtimeConfig: any = useRuntimeConfig()

const BUFFER_TIME = {
    value:
        runtimeConfig.public?.app?.['personal-doctor']?.['remind-delay'] || 30,
    unit: 'minutes' as ManipulateType,
}

const state = reactive({
    user: query?.user,
    sessionId: query?.sessionId,
    conversation: query?.conversation,
})

const session = ref<any>(null)
const conversation = computed(() => query?.conversation)

const {
    updateConsultationSession,
    getLatestConsultationSession,
    updateSessionWellchat,
} = useConsultationSession()

const handleFormSubmit = async () => {
    if (query?.consultation) {
        await updateConsultationSession(query?.consultation, {
            chiefComplaint: (state as any)?.chiefComplaint,
            time: dayjs().add(BUFFER_TIME.value, BUFFER_TIME.unit).format(),
        })
    }

    session.value = await getLatestConsultationSession(
        query?.user,
        conversation.value,
    )

    if (session.value?.title === 'New session') {
        await updateSessionWellchat(session.value?._id, {
            user: query?.user,
            payload: {
                title: (state as any)?.chiefComplaint,
            },
        })
    }

    router.push({
        path: appRoutes.patient.diary(query.conversation),
        query: {
            provider: query?.provider,
            providerUser: query?.providerUser,
            patient: query?.patient,
        },
    })
}

const hidden_fields = computed(() => {
    return [
        {
            key: 'user',
            value: query?.user,
        },
        {
            key: 'sessionId',
            value: query?.sessionId,
        },
        {
            key: 'conversation',
            value: query?.conversation,
        },
    ]
})

onMounted(async () => {
    window.addEventListener('message', async (event) => {
        if (event.data.event === 'submit') {
            console.log('Form submitted:', event.data.state)
        } else if (event.data.event === 'state') {
            const newState = event.data.state
            Object.assign(state, newState)
            console.log('State updated:', state)
        }
    })

    layoutStore.setTitle(t('request-callback'))
})
</script>

<template>
    <div class="form mx-auto w-full max-w-7xl p-4 md:p-8">
        <F
            :form="form(hidden_fields)"
            :state="state"
            @submit="handleFormSubmit"
        />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "request-callback": "Request a Callback"
    },
    "vi": {
        "request-callback": "Gửi yêu cầu gọi"
    }
}
</i18n>
