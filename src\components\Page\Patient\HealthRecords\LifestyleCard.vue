<script setup lang="ts">
import { useLifestyle } from '~/composables/page/patient/health-records/lifestyle'
import type { IHabitsData } from '~/models/page/health-records'

interface Props {
  lifeStyle: IHabitsData
}

const { mapLifeStyle } = useLifestyle()
defineProps<Props>()
</script>

<template>
  <div>
    <template v-for="habit in mapLifeStyle()" :key="habit.label">
      <div class="flex flex-row justify-between">
        <p class="mb-0">{{ habit.label }}</p>
        <p class="mb-0 font-semibold">{{ habit.value }}</p>
      </div>
      <Divider class="!my-2" />
    </template>
  </div>
</template>
