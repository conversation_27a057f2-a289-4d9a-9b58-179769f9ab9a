<script setup lang="ts">
import type { PropType } from '#imports'
import type { ComponentBanner } from '~/models'

const { banner } = defineProps({
  banner: {
    type: Object as PropType<ComponentBanner>,
    default: () => ({}),
  },
})

defineEmits(['click'])
</script>
<template>
  <div>
    <NuxtImg
      :src="banner.source"
      class="hidden w-full md:block"
      sizes="sm:468 md:596px xl:800"
      loading="lazy"
    />
  </div>
</template>
