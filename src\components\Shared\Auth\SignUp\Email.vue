<script setup lang="ts">
import * as yup from 'yup'
const { t } = useI18n()
const onboardingStore = useOnboardingStore()

// const email = ref('')
const { errors, defineField, validate } = useForm({
    validationSchema: yup.object({
        email: yup
            .string()
            .required(t('required'))
            .email(t('error.please-enter-valid-email')),
        // .test(
        //     'valid',
        //     t('error.please-enter-valid-email'),
        //     (value) => {
        //         const emailRegex = /^[\w-.]+@([\w-]+\.)+[\w-]{2,4}$/g
        //         const isEmailValid = emailRegex.test(value ?? "")
        //         return Boolean(isEmailValid)
        //     },
        // ),
    }),
})
const [emailField, emailAttrs] = defineField('email')

async function handleSave() {
    const result = await validate()
    if (result.valid) {
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            email: emailField.value,
        })
    }
}

const submit = async () => {
    onboardingStore.setEmail(emailField.value || '')

    const result = await validate()
    if (result.valid) {
        return true
    }
    return false
}

// Watch for nameField changes and update store only when valid
watch(emailField, async (newValue: string) => {
    const result = await validate()
    if (result.valid) {
        onboardingStore.setProfile({
            ...onboardingStore.profile,
            email: newValue,
        })
    }
})

defineExpose({
    submit,
})
</script>
<template>
    <div class="flex min-h-[80vh] pt-8 md:items-center md:justify-center">
        <div class="w-full max-w-2xl rounded-lg">
            <div class="mb-6 w-full text-center">
                <div class="space-y-4 text-center">
                    <h1>{{ t('enter-email') }}</h1>

                    <div>
                        <InputText
                            ref="inputRef"
                            v-model="emailField"
                            class="w-full"
                            :class="{
                                'border-red-400': errors.email,
                            }"
                            placeholder="<EMAIL>"
                            v-bind="emailAttrs"
                            @keyup.enter="handleSave"
                            @blur="handleSave"
                        />
                        <small v-if="errors.email" class="text-red-500">{{
                            errors.email
                        }}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<i18n lang="json">
{
    "vi": {
        "enter-email": "Nhập email",
        "error": {
            "please-enter-valid-email": "Email không hợp lệ"
        }
    },
    "en": {
        "enter-email": "Enter your email",
        "error": {
            "please-enter-valid-email": "Email is invalid"
        }
    }
}
</i18n>
