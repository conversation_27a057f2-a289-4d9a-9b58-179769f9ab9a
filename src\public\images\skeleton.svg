<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100">
  <defs>
    <linearGradient id="shimmer" x1="0" x2="1" y1="0" y2="0">
      <stop offset="0%" stop-color="#e8e8e8">
        <animate attributeName="offset" values="-2; 1" dur="1.5s"
                 repeatCount="indefinite"/>
      </stop>
      <stop offset="50%" stop-color="#d8d8d8">
        <animate attributeName="offset" values="-1.5; 1.5" dur="1.5s"
                 repeatCount="indefinite"/>
      </stop>
      <stop offset="100%" stop-color="#e8e8e8">
        <animate attributeName="offset" values="-1; 2" dur="1.5s"
                 repeatCount="indefinite"/>
      </stop>
    </linearGradient>
  </defs>

  <rect width="100" height="100" rx="4" fill="url(#shimmer)"/>
</svg>
