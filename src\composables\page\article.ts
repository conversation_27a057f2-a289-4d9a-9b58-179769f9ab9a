import {
  type Ref,
  showError,
  useGtm,
  useHead,
  useI18n,
  useJsonld,
  useRuntimeConfig,
  useSeoMeta,
  watch,
} from '#imports'
import type { AsyncDataRequestStatus } from 'nuxt/app'
import type { HitNotionWebsite, IJsonLD } from '~/models'

export function usePageArticle(options: {
  hit: Ref<Pick<HitNotionWebsite, 'page' | '_id'>>
  status: Ref<AsyncDataRequestStatus>
  jsonld?: IJsonLD
}) {
  const { jsonld, status, hit } = options || {}
  const config = useRuntimeConfig()

  const { t } = useI18n()

  const checkError = () => {
    if (
      status.value === 'error' ||
      (status.value === 'success' && hit.value && !hit.value._id)
    ) {
      showError({
        statusCode: 404,
        statusMessage: t('error:page-not-found:message'),
        cause: t('error:page-not-found:cause'),
        fatal: true,
      })
    }
  }
  const datalayerpush = () => {
    if (hit.value?.page.properties['Parent item'][0]) {
      const gtm = useGtm()
      gtm.push({
        content_group:
          hit.value?.page.properties['Parent item'][0].properties.Name,
      })
    }
  }
  watch(status, () => {
    checkError()
    if (status.value == 'success') {
      useSeoMeta({
        title: hit.value?.page.properties.Name,
        ogTitle: hit.value?.page.properties.Name,
        description:
          hit.value?.page.properties['Meta Description'] ||
          hit.value?.page.properties.Sapo,
        ogDescription:
          hit.value?.page.properties['Meta Description'] ||
          hit.value?.page.properties.Sapo,
        ogLocale: hit.value?.page.properties.Locale,
        ogImage: hit.value?.page.cover?.url,
      })
      if (hit.value?.page.properties['Parent item']) {
        datalayerpush()
        useHead({
          link: [
            {
              rel: 'canonical',
              href: `${config.public.page.site}/${hit.value?.page.properties['Parent item'][0].properties.Slug}/${hit.value?.page.properties.Slug}`,
            },
          ],
        })
      }
    }
  })
  checkError()
  datalayerpush()

  // JSONLD
  if (jsonld?.enabled) {
    useJsonld(() => ({
      '@context': 'https://schema.org',
      '@graph': [
        {
          '@type': 'NewsArticle',
          headline: hit.value?.page.properties.Name,
          image: [hit.value?.page.cover?.url],
          datePublished:
            hit.value?.page.properties.PublishedAt?.start ||
            hit.value?.page.last_edited_time,
          dateModified: hit.value?.page.last_edited_time,
        },
      ],
    }))
  }
}
