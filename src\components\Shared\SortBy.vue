<template>
    <div class="flex flex-row items-center justify-end py-3">
        <label for="sort-by" class="mr-2 text-surface-700">Sort by:</label>
        <Dropdown
            id="sort-by"
            v-model="sortBy"
            :options="sortOptions"
            option-label="label"
            option-value="value"
            class="w-48 rounded border border-gray-300 bg-white px-2 py-1"
        />
    </div>
</template>

<script setup lang="ts">
import { ref } from '#imports'
const sortOptions = [
    { label: 'Date', value: 'date' },
    { label: 'Provider', value: 'provider' },
    { label: 'Status', value: 'status' },
]

const sortBy = ref('date')
</script>
