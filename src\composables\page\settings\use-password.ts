import { useOidc, useAppAuth } from '#imports'
import { <PERSON><PERSON><PERSON> } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'
import { deepLink } from '~/constants'

export function usePassword() {
    const route = useRoute()
    const { getOidcClient } = useOidc()
    const { getUserManager } = useAppAuth()

    const resetPassword = async () => {
        if (Capacitor.isNativePlatform()) {
            const request = await getOidcClient.createSigninRequest({
                request_type: 'si:r',
                redirect_uri: deepLink.resetPassword,
                extraQueryParams: {
                    kc_action: 'UPDATE_PASSWORD',
                },
            })
            await Browser.open({
                url: request.url,
                windowName: '_self',
                presentationStyle: 'popover',
            })
        } else {
            await getUserManager.signinRedirect({
                redirect_uri: route.fullPath,
                extraQueryParams: {
                    kc_action: 'UPDATE_PASSWORD',
                },
            })
        }
    }

    return {
        resetPassword,
    }
}
