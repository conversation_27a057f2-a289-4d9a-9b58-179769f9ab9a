// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN
include ':capacitor-android'
project(':capacitor-android').projectDir = new File('../node_modules/.pnpm/@capacitor+android@6.2.0_@capacitor+core@6.2.0/node_modules/@capacitor/android/capacitor')

include ':capacitor-app'
project(':capacitor-app').projectDir = new File('../node_modules/.pnpm/@capacitor+app@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/app/android')

include ':capacitor-browser'
project(':capacitor-browser').projectDir = new File('../node_modules/.pnpm/@capacitor+browser@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/browser/android')

include ':capacitor-clipboard'
project(':capacitor-clipboard').projectDir = new File('../node_modules/.pnpm/@capacitor+clipboard@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/clipboard/android')

include ':capacitor-device'
project(':capacitor-device').projectDir = new File('../node_modules/.pnpm/@capacitor+device@6.0.2_@capacitor+core@6.2.0/node_modules/@capacitor/device/android')

include ':capacitor-preferences'
project(':capacitor-preferences').projectDir = new File('../node_modules/.pnpm/@capacitor+preferences@6.0.3_@capacitor+core@6.2.0/node_modules/@capacitor/preferences/android')

include ':capacitor-splash-screen'
project(':capacitor-splash-screen').projectDir = new File('../node_modules/.pnpm/@capacitor+splash-screen@6.0.3_@capacitor+core@6.2.0/node_modules/@capacitor/splash-screen/android')

include ':capawesome-capacitor-app-update'
project(':capawesome-capacitor-app-update').projectDir = new File('../node_modules/.pnpm/@capawesome+capacitor-app-update@6.1.0_@capacitor+core@6.2.0/node_modules/@capawesome/capacitor-app-update/android')

include ':capgo-inappbrowser'
project(':capgo-inappbrowser').projectDir = new File('../node_modules/.pnpm/@capgo+inappbrowser@6.15.0_@capacitor+core@6.2.0/node_modules/@capgo/inappbrowser/android')

include ':revenuecat-purchases-capacitor'
project(':revenuecat-purchases-capacitor').projectDir = new File('../node_modules/.pnpm/@revenuecat+purchases-capacitor@10.3.8_@capacitor+core@6.2.0/node_modules/@revenuecat/purchases-capacitor/android')

include ':wellcare-capacitor-app-auth'
project(':wellcare-capacitor-app-auth').projectDir = new File('../node_modules/.pnpm/@wellcare+capacitor-app-auth@0.0.9_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-app-auth/android')

include ':wellcare-capacitor-callkeep'
project(':wellcare-capacitor-callkeep').projectDir = new File('../node_modules/.pnpm/@wellcare+capacitor-callkeep@6.0.6_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-callkeep/android')

include ':wellcare-capacitor-editor'
project(':wellcare-capacitor-editor').projectDir = new File('../node_modules/.pnpm/@wellcare+capacitor-editor@6.0.0_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-editor/android')

include ':wellcare-capacitor-permissions'
project(':wellcare-capacitor-permissions').projectDir = new File('../node_modules/.pnpm/@wellcare+capacitor-permissions@6.0.0-permission.3_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-permissions/android')

include ':wellcare-capacitor-plugin-agora'
project(':wellcare-capacitor-plugin-agora').projectDir = new File('../node_modules/.pnpm/@wellcare+capacitor-plugin-agora@6.0.6-socket_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-plugin-agora/android')

include ':wellcare-capacitor-stringee'
project(':wellcare-capacitor-stringee').projectDir = new File('../node_modules/.pnpm/@wellcare+capacitor-stringee@6.0.1-hangup.2_@capacitor+core@6.2.0/node_modules/@wellcare/capacitor-stringee/android')

include ':capacitor-native-settings'
project(':capacitor-native-settings').projectDir = new File('../node_modules/.pnpm/capacitor-native-settings@6.0.6_@capacitor+core@6.2.0/node_modules/capacitor-native-settings/android')

include ':capacitor-plugin-safe-area'
project(':capacitor-plugin-safe-area').projectDir = new File('../node_modules/.pnpm/capacitor-plugin-safe-area@3.0.4_@capacitor+core@6.2.0/node_modules/capacitor-plugin-safe-area/android')

include ':capacitor-screen-orientation'
project(':capacitor-screen-orientation').projectDir = new File('../node_modules/.pnpm/@capacitor+screen-orientation@6.0.3_@capacitor+core@6.2.0/node_modules/@capacitor/screen-orientation/android')
