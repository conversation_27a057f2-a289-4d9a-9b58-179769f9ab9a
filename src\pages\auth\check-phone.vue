<script lang="ts" setup>
import { SharedAuthOnboardingPhone as CheckPhone } from '#components'
import { Capacitor } from '@capacitor/core'
import type { Country } from '~/composables/common/phone-validation'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'
import { appRoutes } from '~/constants'

definePageMeta({
    pageTransition: {
        name: 'page',
        mode: 'out-in',
    },
})

const { checkPhone, isLoading } = useOnboarding()
const router = useRouter()
const { t, locale } = useI18n()
const { signIn } = useAppAuth()
const { config } = useJsonConfigApp()

const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)

const showAlert = ref(false)
const phoneData = ref({
    countryCode: '',
    phone: '',
})
const checkPhoneRef = ref<InstanceType<typeof CheckPhone> | null>()
const popoverLangRef = ref<any>(null)
const support = ref(false)

const utilityLinks = computed(() => [
    {
        label: t('auth.support'),
        href: '#',
        command: handleSupport,
        icon: 'material-symbols:support-agent',
    },
    {
        label: t('auth.language'),
        href: '#',
        command: togglePopoverLang,
        icon: 'material-symbols:translate',
    },
])

const isNative = computed(() => Capacitor.isNativePlatform())

const togglePopoverLang = (event: Event) => {
    if (popoverLangRef.value) {
        popoverLangRef.value.toggle(event)
    }
}

const handleSupport = () => {
    support.value = true
}

const onPhoneSubmit = (data: { countryCode: string; phoneNumber: string }) => {
    showAlert.value = false
    phoneData.value = {
        countryCode: data.countryCode,
        phone: data.phoneNumber,
    }
}

const handleSubmit = async () => {
    const isValid = checkPhoneRef.value?.submit()
    if (!isValid) return

    const { data, success } = await checkPhone({
        t: t,
        phone: phoneData.value.phone,
        countryCode: phoneData.value.countryCode,
        disabledNotification: true,
    })

    const code = data?.value?.code

    if (success || data?.value) {
        switch (code) {
            case 200:
                await signIn({
                    login_hint: phoneData.value.phone,
                    extraQueryParams: {
                        ui_locales: locale.value,
                        kc_country_code: phoneData.value.countryCode,
                    },
                })
                break
            case 400:
                if (phoneData.value.countryCode !== '84') {
                    showAlert.value = true
                }
                break
            case 404:
                if (phoneData.value.countryCode !== '84') {
                    showAlert.value = true
                } else {
                    navigateTo({
                        path: appRoutes.auth.signUp,
                        query: {
                            phone: phoneData.value.phone,
                            countryCode: phoneData.value.countryCode,
                        },
                    })
                }
                break
            default:
                break
        }
    }
}

const goBack = () => {
    router.back()
}

const onChangeCountryCode = (country: Country) => {
    if (country.dialCode === '84') {
        showAlert.value = false
    }
}
</script>

<template>
    <div>
        <div
            :class="`flex h-screen flex-col justify-between px-4 ${isNative ? 'py-safe' : 'py-4'}`"
        >
            <div class="flex w-full items-center justify-between">
                <Button
                    icon="pi pi-angle-left"
                    text
                    :disabled="isLoading"
                    rounded
                    @click="goBack"
                />

                <div class="flex items-center gap-3">
                    <Button
                        v-for="(link, index) in utilityLinks"
                        :key="index"
                        class="flex h-10 w-10 items-center justify-center rounded-full bg-white/80 text-gray-700 shadow-sm backdrop-blur-md transition-all hover:bg-gray-100 hover:shadow-md"
                        text
                        rounded
                        @click="link.command"
                    >
                        <iconify-icon :icon="link.icon" class="text-xl" />
                        <span class="sr-only">{{ link.label }}</span>
                    </Button>
                </div>
            </div>
            <div class="flex-1 space-y-4 text-center">
                <Avatar
                    shape="circle"
                    image="/logo.svg"
                    alt="Logo"
                    class="h-auto w-14 drop-shadow-lg"
                />

                <CheckPhone
                    ref="checkPhoneRef"
                    :title="t('check-phone.title')"
                    :description="t('check-phone.description')"
                    :use-store="false"
                    @change:country-code="onChangeCountryCode"
                    @phone-submit="onPhoneSubmit"
                />

                <!-- Alert component with split message -->
                <div v-if="showAlert">
                    <div
                        class="w-full rounded-md border-2 border-primary/50 bg-primary/10 p-4 text-left text-primary"
                    >
                        <span
                            >{{ t('check-phone.contact-admin-start') }}
                            <a
                                class="underline"
                                href="https://zalo.me/0366905905"
                                >+84366905905</a
                            >
                            {{ t('check-phone.contact-admin-end') }}</span
                        >
                    </div>
                </div>
            </div>

            <Button @click="handleSubmit"> {{ t('continue') }} </Button>
        </div>

        <!-- Reusable components -->
        <SharedPopoverLang ref="popoverLangRef" />
        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="support"
        />
    </div>
</template>

<style scoped>
/* Native-like slide transitions */
.slide-right-enter-active,
.slide-right-leave-active {
    position: fixed;
    width: 100%;
    transition: all 0.25s cubic-bezier(0.33, 1, 0.68, 1);
}

.slide-right-enter-from {
    opacity: 0;
    transform: translateX(100%);
}

.slide-right-leave-to {
    opacity: 0;
    transform: translateX(-30%);
}

/* Left slide transition for back navigation */
.slide-left-enter-active,
.slide-left-leave-active {
    position: fixed;
    width: 100%;
    transition: all 0.25s cubic-bezier(0.33, 1, 0.68, 1);
}

.slide-left-enter-from {
    opacity: 0;
    transform: translateX(-100%);
}

.slide-left-leave-to {
    opacity: 0;
    transform: translateX(30%);
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}
</style>

<i18n lang="json">
{
    "en": {
        "check-phone": {
            "title": "Enter your phone number",
            "description": "We will check if you are a new or an old friend and prompt you to login or register new account",
            "contact-admin-start": "For an international phone number, please text our care team via Zalo at",
            "contact-admin-end": "to verify phone number and create an account."
        },
        "continue": "continue"
    },
    "vi": {
        "check-phone": {
            "title": "Nhập số điện thoại",
            "description": "Hệ thống sẽ xác định nếu bạn là người dùng quen, để tiếp tục đăng nhập hoặc đăng ký",
            "contact-admin-start": "Nếu bạn ở nước ngoài, vui lòng báo Zalo cho care team",
            "contact-admin-end": "để xác thực và mở tài khoản."
        },
        "continue": "tiếp tục"
    }
}
</i18n>
