// composables/useAudioPlayer.ts
import { ref, onMounted, onBeforeUnmount } from '#imports'
import WaveSurfer from 'wavesurfer.js'
import type { IItemPage } from '~/models'

export const useAudioPlayer = (media: IItemPage) => {
    const wavesurfer = ref<WaveSurfer | null>(null)
    const isPlaying = ref(false)
    const isLoading = ref(true)
    const currentTime = ref('0:00')
    const duration = ref('0:00')
    const waveformRef = ref<HTMLDivElement | null>(null)

    const formatTime = (seconds: number) => {
        const minutes = Math.floor(seconds / 60)
        const remainingSeconds = Math.floor(seconds % 60)
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`
    }

    const cleanupWaveSurfer = () => {
        if (wavesurfer.value) {
            wavesurfer.value.pause() // Ensure audio is paused
            wavesurfer.value.destroy() // Destroy instance
            wavesurfer.value = null
        }
        isPlaying.value = false
        currentTime.value = '0:00'
        duration.value = '0:00'
    }

    const initializeWaveSurfer = () => {
        if (!waveformRef.value) return

        // Cleanup any existing instance first
        cleanupWaveSurfer()

        wavesurfer.value = WaveSurfer.create({
            container: waveformRef.value,
            waveColor: '#E0E0E0',
            progressColor: '#04c8b1',
            cursorColor: 'transparent',
            barWidth: 3,
            barRadius: 20,
            barGap: 3,
            height: 50,
            normalize: true,
        })

        wavesurfer.value.load(replaceCDNHost(media?.url))

        wavesurfer.value.on('ready', () => {
            isLoading.value = false
            if (!wavesurfer.value) return
            duration.value = formatTime(wavesurfer.value.getDuration())

            if (isPlaying.value) {
                wavesurfer.value.play()
            }
        })

        wavesurfer.value.on('audioprocess', () => {
            if (!wavesurfer.value) return
            currentTime.value = formatTime(wavesurfer.value.getCurrentTime())
        })

        wavesurfer.value.on('play', () => (isPlaying.value = true))
        wavesurfer.value.on('pause', () => (isPlaying.value = false))

        // Add finish event handler
        wavesurfer.value.on('finish', () => {
            isPlaying.value = false
            currentTime.value = duration.value
        })

        // Error handling
        wavesurfer.value.on('error', (err) => {
            console.error('WaveSurfer error:', err)
            cleanupWaveSurfer()
        })
    }

    const togglePlay = () => {
        wavesurfer.value?.playPause()
    }

    const skipBackward = () => {
        if (!wavesurfer.value) return
        const currentTimeValue = wavesurfer.value.getCurrentTime()
        const newTime = Math.max(0, currentTimeValue - 10)
        wavesurfer.value.setTime(newTime)
        currentTime.value = formatTime(newTime)
    }

    const skipForward = () => {
        if (!wavesurfer.value) return
        const currentTimeValue = wavesurfer.value.getCurrentTime()
        const durationValue = wavesurfer.value.getDuration()
        const newTime = Math.min(durationValue, currentTimeValue + 10)
        wavesurfer.value.setTime(newTime)
        currentTime.value = formatTime(newTime)
    }

    const refreshAudio = () => {
        if (wavesurfer.value) {
            const wasPlaying = isPlaying.value
            wavesurfer.value.load(replaceCDNHost(media?.url))
            currentTime.value = '0:00'
            wavesurfer.value.seekTo(0)

            wavesurfer.value.once('ready', () => {
                duration.value = formatTime(wavesurfer.value!.getDuration())
                if (wasPlaying) {
                    wavesurfer.value?.play()
                }
            })
        }
    }

    const calculateIconPosition = () => {
        if (!wavesurfer.value) return 0
        const currentTimeValue = wavesurfer.value.getCurrentTime()
        const durationValue = wavesurfer.value.getDuration()
        return (currentTimeValue / durationValue) * 100
    }

    onMounted(() => {
        initializeWaveSurfer()
    })

    onBeforeUnmount(() => {
        cleanupWaveSurfer()
    })

    return {
        waveformRef,
        isPlaying,
        isLoading,
        currentTime,
        duration,
        togglePlay,
        skipBackward,
        skipForward,
        refreshAudio,
        calculateIconPosition,
        cleanupWaveSurfer, // Export for manual cleanup if needed
    }
}
