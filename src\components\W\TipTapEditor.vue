<template>
    <div>
        <div
            v-if="editor"
            class="grid grid-cols-8 place-items-center gap-2 bg-surface-300 py-1 dark:bg-surface-300"
        >
            <!-- <button @click="editor.chain().focus().toggleBold().run()"
                :disabled="!editor.can().chain().focus().toggleBold().run()"
                :class="{ 'is-active': editor.isActive('bold') }">
                bold
            </button> -->
            <div>
                <Button
                    class="font-bold"
                    :disabled="!editor.can().chain().focus().toggleBold().run()"
                    :text="!editor.isActive('bold')"
                    @click="editor.chain().focus().toggleBold().run()"
                >
                    B
                </Button>
            </div>
            <div>
                <Button
                    class="font-bold italic"
                    :disabled="
                        !editor.can().chain().focus().toggleItalic().run()
                    "
                    :text="!editor.isActive('italic')"
                    @click="editor.chain().focus().toggleItalic().run()"
                >
                    I
                </Button>
            </div>
            <div>
                <Button
                    class="font-bold line-through"
                    :disabled="
                        !editor.can().chain().focus().toggleStrike().run()
                    "
                    :text="!editor.isActive('strike')"
                    @click="editor.chain().focus().toggleStrike().run()"
                >
                    U
                </Button>
            </div>
            <div>
                <Button
                    class="font-bold underline"
                    :disabled="
                        !editor.can().chain().focus().toggleUnderline().run()
                    "
                    :text="!editor.isActive('underline')"
                    @click="editor.chain().focus().toggleUnderline().run()"
                >
                    U
                </Button>
            </div>
            <!-- <button @click="editor.chain().focus().toggleItalic().run()"
                :disabled="!editor.can().chain().focus().toggleItalic().run()"
                :class="{ 'is-active': editor.isActive('italic') }">
                italic
            </button>
            <button :disabled="!editor.can().chain().focus().toggleStrike().run()"
                :class="{ 'is-active': editor.isActive('strike') }"
                @click="editor.chain().focus().toggleStrike().run()">
                strike
            </button>
            <button :disabled="!editor.can().chain().focus().toggleCode().run()"
                :class="{ 'is-active': editor.isActive('code') }" @click="editor.chain().focus().toggleCode().run()">
                code
            </button>
            <button @click="editor.chain().focus().unsetAllMarks().run()">
                clear marks
            </button>
            <button @click="editor.chain().focus().clearNodes().run()">
                clear nodes
            </button>
            <button :class="{ 'is-active': editor.isActive('paragraph') }"
                @click="editor.chain().focus().setParagraph().run()">
                paragraph
            </button>
            <button :class="{ 'is-active': editor.isActive('heading', { level: 1 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 1 }).run()">
                h1
            </button>
            <button :class="{ 'is-active': editor.isActive('heading', { level: 2 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 2 }).run()">
                h2
            </button>
            <button :class="{ 'is-active': editor.isActive('heading', { level: 3 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 3 }).run()">
                h3
            </button>
            <button :class="{ 'is-active': editor.isActive('heading', { level: 4 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 4 }).run()">
                h4
            </button>
            <button :class="{ 'is-active': editor.isActive('heading', { level: 5 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 5 }).run()">
                h5
            </button>
            <button :class="{ 'is-active': editor.isActive('heading', { level: 6 }) }"
                @click="editor.chain().focus().toggleHeading({ level: 6 }).run()">
                h6
            </button>
            <button :class="{ 'is-active': editor.isActive('bulletList') }"
                @click="editor.chain().focus().toggleBulletList().run()">
                bullet list
            </button>
            <button :class="{ 'is-active': editor.isActive('orderedList') }"
                @click="editor.chain().focus().toggleOrderedList().run()">
                ordered list
            </button>
            <button :class="{ 'is-active': editor.isActive('codeBlock') }"
                @click="editor.chain().focus().toggleCodeBlock().run()">
                code block
            </button>
            <button :class="{ 'is-active': editor.isActive('blockquote') }"
                @click="editor.chain().focus().toggleBlockquote().run()">
                blockquote
            </button>
            <button @click="editor.chain().focus().setHorizontalRule().run()">
                horizontal rule
            </button>
            <button @click="editor.chain().focus().setHardBreak().run()">
                hard break
            </button>
            <button :disabled="!editor.can().chain().focus().undo().run()" @click="editor.chain().focus().undo().run()">
                undo
            </button>
            <button :disabled="!editor.can().chain().focus().redo().run()" @click="editor.chain().focus().redo().run()">
                redo
            </button> -->
            <div>
                <Button
                    class="font-bold"
                    text
                    rounded
                    :disabled="!editor.can().chain().focus().undo().run()"
                    icon="pi pi-replay"
                    @click="editor.chain().focus().undo().run()"
                ></Button>
            </div>
            <div>
                <Button
                    class="font-bold"
                    text
                    rounded
                    :disabled="!editor.can().chain().focus().redo().run()"
                    icon="pi pi-refresh"
                    @click="editor.chain().focus().redo().run()"
                >
                </Button>
            </div>
        </div>
        <EditorContent
            :editor="editor"
            class="h-[350px] rounded-b-md border bg-surface-100 text-black outline-none"
        />
    </div>
</template>

<script setup>
import StarterKit from '@tiptap/starter-kit'
import { Editor, EditorContent } from '@tiptap/vue-3'
import Underline from '@tiptap/extension-underline'
const emit = defineEmits(['update:modelValue'])

const props = defineProps({
    modelValue: {
        type: String,
        default: '',
    },
})
const editor = new Editor({
    content: props.modelValue,
    extensions: [StarterKit, Underline],
    onUpdate: () => {
        console.log('update ...')
        emit('update:modelValue', editor.getHTML())
    },
})

watch(
    () => props.modelValue,
    (val) => {
        if (editor.getHTML() === val) return
        editor.commands.setContent(val, false)
    },
)

onBeforeUnmount(() => {
    unref(editor).destroy()
})
</script>
