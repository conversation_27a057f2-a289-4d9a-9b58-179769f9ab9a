<script setup lang="ts">
import { type PropType, defineAsyncComponent } from '#imports'
import type { HitNotionWebsite } from '~/models'
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
type LayoutType = 'list' | 'grid' | 'timeline' | 'gallery'

const { blocks, slug, loading, articles, layout } = defineProps({
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  articles: {
    type: Array as PropType<HitNotionWebsite[]>,
    required: true,
  },
  slug: {
    type: String,
    required: true,
  },
  container: {
    type: String as PropType<'timeline' | 'iterator'>,
    default: 'iterator',
  },
  layout: {
    type: String as PropType<LayoutType>,
    default: () => 'list',
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const asyncComponents = {
  timeline: defineAsyncComponent(() => import('./timeline.vue')),
  iterator: defineAsyncComponent(() => import('./iterator.vue')),
}
</script>
<template>
  <component
    :is="asyncComponents[container]"
    :layout="layout"
    :articles="articles"
    :slug="slug"
    :blocks="blocks"
    :loading="loading"
  />
</template>
