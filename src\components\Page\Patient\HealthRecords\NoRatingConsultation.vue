<script setup lang="ts">
import { useConsultationSearch } from '~/composables'
import { appRoutes } from '~/constants'

const router = useRouter()
const { t } = useI18n()

const { consultations, searchConsultation } = useConsultationSearch()

const { user } = useUserInfo({ scope: ['_id'] })
const userId = computed(() => user.value?._id)

const visible = ref<boolean>(false)
const providerId = ref<string>('')
const providerAvatar = ref<string>('')
const consultationId = ref<string>('')
const isOpenRating = ref<boolean>(false)

const handleOpenMR = (selectedConsultation: any) => {
    consultationId.value = selectedConsultation._id
    router.push({
        path: appRoutes.patient.consultation.detail(consultationId.value),
    })
}

const handleOpenRating = (selectedConsultation: any) => {
    isOpenRating.value = true
    consultationId.value = selectedConsultation._id
    providerId.value = selectedConsultation?.provider?._id
    providerAvatar.value = selectedConsultation?.provider?.avatar?.url
}

const onSubmitted = async () => {
    await searchConsultation(userId.value)
    isOpenRating.value = false
}

watch(
    () => userId,
    async (newVal) => {
        if (newVal.value) {
            await searchConsultation(newVal.value)
        }
    },
    {
        immediate: true,
        deep: true,
    },
)

watch(consultations, () => {
    visible.value = consultations.value?.length
})
</script>

<template>
    <Drawer
        v-model:visible="visible"
        :header="t('header')"
        position="bottom"
        :dismissable="false"
        class="!h-fit !max-h-screen"
    >
        <p>{{ t('rating remind') }}</p>

        <WConsultationCard
            v-for="consultation in consultations"
            :key="consultation._id"
            class="mt-4"
            :consultation="consultation"
            @click:open-mr="handleOpenMR"
            @click:open-rating="handleOpenRating"
        />

        <WConsultationRating
            :visible="isOpenRating"
            :provider-id="providerId"
            :consultation-id="consultationId"
            :provider-avatar="providerAvatar"
            @submitted="onSubmitted"
            @skip-rating="isOpenRating = false"
            @update:visible="isOpenRating = false"
        />
    </Drawer>
</template>

<style scoped></style>

<i18n lang="yaml">
en:
    'header': 'Complete'
    'rating remind': "Please see the Doctor's Note and Suggested Prescription (if any) and rate the consultation herein"
vi:
    'header': 'Hoàn tất'
    'rating remind': 'Mời bạn đọc Dặn dò và Thuốc gợi ý (nếu có) của bác sĩ và chấm điểm cuộc tư vấn'
</i18n>
