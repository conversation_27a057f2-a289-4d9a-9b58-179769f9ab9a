<script setup lang="ts">
import { definePageMeta, onMounted, useI18n, useLayoutStore } from '#imports'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const { t } = useI18n()
const layoutStore = useLayoutStore()

// const { user } = useUserInfo({
//     scope: ['meta', '_id'],
// })

// const patientId = computed(() => {
//     if (user.value?.meta?.baby_id) return user.value?.meta?.baby_id
//     return user.value?._id
// })

onMounted(() => {
    layoutStore.setTitle(t('personal-doctor'))
})
</script>

<template>
    <div class="mb-8 mt-4 px-2">
        <PagePatientServicesPersonalDoctorExpertCompanion />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "personal-doctor": "Personal Doctor"
    },
    "vi": {
        "personal-doctor": "<PERSON><PERSON><PERSON> sĩ riêng"
    }
}
</i18n>
