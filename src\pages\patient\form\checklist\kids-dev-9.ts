import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'mV9GFeb7ld',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2isfowdfg9',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON>c mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 9 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. <PERSON><PERSON><PERSON> sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2isfzkdhme',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_media',
                            tag: 'img',
                            src: 'https://assets.babycenter.com/ims/2016/04/iStock_56387970_4x3.jpg',
                            alt: 'Nice picture',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2ishizyj8m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhút nhát, đeo mẹ, hoặc sợ hãi khi ở gần người lạ',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2ishizyj8m',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2ishlutysc',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nét mặt thể hiện được một số cảm xúc như vui, buồn, tức giận và ngạc nhiên',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2ishlutysc',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2ishoidf8',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhìn theo khi nghe gọi tên mình',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2ishoidf8',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2ishqa5yx',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Phản ứng khi cha mẹ rời đi (nhìn, với tay về phía cha mẹ, hoặc khóc)',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2ishqa5yx',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2ishs1xgam',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Mỉm cười hoặc cười to khi chơi trò ú òa',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2ishs1xgam',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2isjuzxwie',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Bập bẹ “mamamama” và “babababa”',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isjuzxwie',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2isjwr9na6',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Giơ tay lên để được bế',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isjwr9na6',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2isp1hxjxm',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tìm kiếm đồ vật bị rơi (như cái muỗng hoặc đồ chơi)',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isp1hxjxm',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2isp4j92jg',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Đập hai món đồ vào nhau',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2isp4j92jg',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2issalx5i',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự ngồi dậy không cần kéo, nâng lên',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2issalx5i',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2issdeel1h',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chuyển đồ vật từ tay này sang tay kia',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2issdeel1h',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2issgxaya',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Dùng ngón tay “cào” thức ăn về phía mình',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2issgxaya',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2issku6rjb',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự ngồi vững không cần đỡ',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'option2',
                                },
                                {
                                    value: 'option3',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2issku6rjb',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    title: '9 tháng',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
})
