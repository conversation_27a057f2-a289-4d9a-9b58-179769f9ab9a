<script lang="ts" setup>
import {
    computed,
    defineI18nRoute,
    ref,
    useFetchElastic,
    useI18n,
    useRouter,
} from '#imports'
import { ElasticIndex } from '~/models'

defineI18nRoute({
    paths: {
        en: '/cau-hoi-thuong-gap',
    },
})

const router = useRouter()
const { t } = useI18n({ useScope: 'local' })

const props = defineProps({
    category: {
        type: Object,
        default: () => ({}),
    },
    title: {
        type: String,
        default: '',
    },
    query: {
        type: String,
        default: '',
    },
})

const search = ref('')
const computedQuery = computed(() => props.query)

const onFocus = () => {
    setTimeout(() => {
        router.push({
            query: {
                action: 'search',
                target: '/cau-hoi-thuong-gap',
            },
        })
    }, 100)
}

const { data: posts }: any = useFetchElastic(ElasticIndex.NOTION_WEBSITE, {
    filters: [
        {
            term: {
                'page.properties.Type.keyword': 'FAQ',
            },
        },
    ],
    must: [
        {
            multi_match: {
                query: computedQuery.value,
                fields: ['page.properties.Name^3', 'page.properties.Sapo^2'],
            },
        },
    ],
    highlight: {
        fields: {
            'page.properties.Name': {
                pre_tags: ['<mark>'],
                post_tags: ['</mark>'],
            },
            'page.properties.Sapo': {
                pre_tags: ['<mark>'],
                post_tags: ['</mark>'],
            },
        },
    },
})
const indexes = computed(() => {
    const indices = []
    for (let i = 0; i < (posts?._rawValue?.hits?.length ?? 0); i++) {
        indices.push(i)
    }
    return indices
})
</script>
<template>
    <div class="container mb-4 flex flex-col">
        <WHeroSection
            class="h-100 lg:h-80"
            :blocks="['title', 'sub-title', 'under']"
            :sub-title="category?.page?.properties?.Sapo"
        >
            <template #title>
                <WSheet
                    tag="h1"
                    :class="`mb-4 text-5xl font-semibold leading-none tracking-tight text-slate-900 sm:text-6xl md:text-7xl lg:text-8xl`"
                >
                    {{ title }}
                </WSheet>
            </template>
            <template #under>
                <IconField icon-position="right" class="mx-[10vw]">
                    <InputIcon class="pi pi-search" />
                    <InputText
                        v-model="search"
                        placeholder="Search"
                        @focus="onFocus"
                    />
                </IconField>
            </template>
        </WHeroSection>

        <p class="ml-4 text-2xl font-semibold">{{ t('title:result') }}</p>
        <Accordion
            :multiple="true"
            :active-index="indexes"
            expand-icon="pi pi-angle-down"
            collapse-icon="pi pi-angle-up"
        >
            <AccordionTab
                v-for="(post, index) in posts?.hits"
                :key="index"
                :pt="{
                    root: {
                        class: '!mb-0 dark:border-slate-800 md:hover:bg-slate-100 md:dark:hover:bg-slate-900 last:border-b-0',
                    },
                    headerAction: {
                        class: 'px-0 py-0 !text-lg',
                    },
                    headerTitle: {
                        class: '!leading-6 sm:leading-none',
                    },
                }"
            >
                <template #header>
                    <p
                        v-dompurify-html="
                            post?._highlight?.page?.properties?.Name?.[0]
                        "
                    />
                </template>
                <p
                    v-dompurify-html="
                        post?._highlight?.page?.properties?.Sapo?.[0]
                    "
                />
            </AccordionTab>
        </Accordion>
    </div>
</template>

<i18n lang="yaml">
en:
    'title:result': 'Results'
vi:
    'title:result': 'Kết quả'
</i18n>
