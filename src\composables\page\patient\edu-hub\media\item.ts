import { ElasticIndex } from '~/models'
import type { HitNotionAskDoctor, HitNotionEduHub, IItemPage } from '~/models'

export function usePagePatientEduHubMediaItem() {
    const { params }: any = useRoute()

    const { hit, loading } = useFetchElastic<HitNotionEduHub>(
        ElasticIndex.NOTION_EDU_HUB,
        {
            size: 1,
            filters: [
                {
                    term: {
                        'page.id.keyword': params.item,
                    },
                },
            ],
        },
    )

    const { hits: questions } = useFetchElasticWithDefault<HitNotionAskDoctor>(
        ElasticIndex.NOTION_ASK_DOCTOR,
        {
            filters: [
                {
                    term: {
                        'page.properties.EduHub.id.keyword': params.item,
                    },
                },
            ],
        },
    )

    const item: ComputedRef<IItemPage> = computed(() => {
        return {
            _id: hit.value?.page?.properties.Files?.[0]?.id || '',
            url: hit.value?.page?.properties.Files?.[0]?.properties?.Url || '',
            name: hit.value?.page?.properties.Name || '',
            delivery: hit.value?.page?.properties?.Delivery || '',
            content: hit.value?.html || '',
            mimeType:
                hit.value?.page?.properties.Files?.[0]?.properties?.Mimetype ||
                '',
            thumbnail:
                hit.value?.page?.properties.Files?.[0]?.properties?.Thumbnail ||
                '',
            provider: {
                avatar:
                    hit.value?.page?.properties.Providers?.[0]?.properties
                        ?.Avatar || '',
                name:
                    hit.value?.page?.properties.Providers?.[0]?.properties
                        ?.Name || '',
                title:
                    hit.value?.page?.properties.Providers?.[0]?.properties
                        ?.Title || '',
            },
        }
    })

    return { item, loading, questions }
}
