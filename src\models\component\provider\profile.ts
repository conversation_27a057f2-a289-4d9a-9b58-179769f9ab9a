import type { INotionBlock } from '~/models/transform'

export type ContentType = 'Text' | 'Chips' | 'Metrics'

export type TProviderTitle = 'Bs' | 'Tlg'

export interface IDataSection {
    title: string
    contentType: ContentType
    content: any
}

export interface IActionFooter {
    label: string
    icon?: string
    severity?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger'
    command: (args?: any) => void
}

export interface IProviderTag {
    key: string
    name?: string
}

export interface IProviderMetric {
    name: string
    icon: string
    label: string | number
    unit: 'years' | 'mins' | '%'
}

export interface IProviderClinic {
    Address?: string
    Email?: string
    Name?: string
    Phone?: string
}

export interface IProviderFilter {
    title?: 'Tlg' | 'Bs'
    specialties?: string[]
    group?: string
    keyword: string
    gender?: 'Female' | 'Male'
}

export interface IProviderProfile {
    _id: string
    avatar: string
    blocks?: INotionBlock[]
    conditions?: IProviderTag[]
    cover?: string
    highlight?: string
    metrics?: IProviderMetric[]
    name: string
    slug: string
    specialties?: IProviderTag[]
    title?: string
    clinics?: string[]
}
