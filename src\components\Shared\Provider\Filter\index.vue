<script setup lang="ts">
import type { IProviderFilter, IProviderTag } from '~/models'

defineProps({
    specialties: {
        type: Array as PropType<IProviderTag[]>,
        required: true,
    },
})

const model = defineModel<IProviderFilter>({
    required: true,
})

const { query }: any = useRoute()

const { t, locale } = useI18n({ useScope: 'local' })

const dataLists = {
    titles: [
        { name: t('doctor'), key: 'Bs' },
        { name: t('psychologist-psychiatrist'), key: 'Tlg' },
    ],
    genders: [
        { name: t('is-male'), key: 'Male' },
        { name: t('is-female'), key: 'Female' },
    ],
    groups: [
        {
            name: t('adult'),
            key: locale.value === 'en' ? 'adult' : 'nguoi-lon',
        },
        { name: t('kid'), key: locale.value === 'en' ? 'child' : 'nhi' },
    ],
}

const collapsed = reactive({
    title: false,
    gender: false,
    group: false,
    specialties: false,
})

const toggleCollapse = (panel: keyof typeof collapsed) => {
    collapsed[panel] = !collapsed[panel]
}

watch(
    () => model.value.title,
    () => {
        const { title } = model.value
        const group = query?._group
        const isEnglish = locale.value === 'en'

        model.value.specialties = []
        model.value.gender = undefined

        if (title === 'Bs' && group) {
            if (['adult', 'nguoi-lon'].includes(group)) {
                model.value.group = isEnglish ? 'adult' : 'nguoi-lon'
            } else if (['child', 'nhi'].includes(group)) {
                model.value.group = isEnglish ? 'child' : 'nhi'
            } else {
                model.value.group = undefined
            }
        } else if (title === 'Tlg') {
            model.value.group = isEnglish
                ? 'mental-health'
                : 'suc-khoe-tinh-than'
        }
    },
)

watch(
    query,
    () => {
        if (query.specialties) {
            model.value.specialties = [query.specialties]
        }
    },
    { immediate: true },
)
</script>
<template>
    <div>
        <SharedProviderFilterCustomPanel
            v-model="model.title"
            type="radio-button"
            :collapsed="collapsed.title"
            :items="dataLists.titles"
            :header="t('title')"
            @toggle="toggleCollapse('title')"
        />

        <SharedProviderFilterCustomPanel
            v-if="model.title === 'Tlg'"
            v-model="model.gender"
            type="radio-button"
            :collapsed="collapsed.gender"
            :items="dataLists.genders"
            :header="t('gender-orientation')"
            @toggle="toggleCollapse('gender')"
        />

        <SharedProviderFilterCustomPanel
            v-if="model.title === 'Bs'"
            v-model="model.group"
            type="radio-button"
            :collapsed="collapsed.group"
            :items="dataLists.groups"
            :header="t('group')"
            @toggle="toggleCollapse('group')"
        />

        <SharedProviderFilterCustomPanel
            v-model="model.specialties"
            type="checkbox"
            :collapsed="collapsed.specialties"
            :items="specialties"
            :header="t('specialties')"
            @toggle="toggleCollapse('specialties')"
        />
    </div>
</template>

<i18n lang="yaml">
en:
    'doctor': 'Doctor'
    'psychologist-psychiatrist': 'Psychologist & Psychiatrist'
    'is-male': 'Is male'
    'is-female': 'Is female'
    'adult': 'Adult'
    'kid': 'Child'
    'title': 'Title'
    'group': 'Group'
    'gender-orientation': 'Gender orientation'
vi:
    'doctor': 'Bác sĩ'
    'psychologist-psychiatrist': 'Tâm lý gia và Bác sĩ tâm thần kinh'
    'is-male': 'Nam'
    'is-female': 'Nữ'
    'adult': 'Người lớn'
    'kid': 'Trẻ em'
    'title': 'Chức danh'
    'group': 'Tham vấn'
    'gender-orientation': 'Xu hướng giới tính'
</i18n>
