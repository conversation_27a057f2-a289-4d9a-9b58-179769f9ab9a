import type { INotionPage, NotionFiles } from '../notion'
import type { IHitNotion } from './notion'
import type { NotionProviders } from './notion-providers'
import type { NotionWebsite } from './notion-website'

export interface NotionSpecialties {
    _id?: string
    Contents?: INotionPage<NotionWebsite>[]
    Files?: INotionPage<NotionFiles>[]
    Name?: string
    Providers?: INotionPage<NotionProviders>[]
    Slug?: string
    Status?: string
}

export interface HitNotionSpecialties extends IHitNotion {
    page?: INotionPage<NotionSpecialties>
}
