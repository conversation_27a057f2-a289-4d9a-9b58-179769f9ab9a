import { <PERSON>rows<PERSON> } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'

export function useExternalLink() {
    const isLoading = ref(false)
    const error = ref<string | null>(null)

    const openExternalLink = async (url: string) => {
        // Reset states
        error.value = null
        isLoading.value = true

        try {
            // Validate URL
            if (!url) {
                throw new Error('URL is required')
            }

            // Check if URL is valid
            try {
                new URL(url)
            } catch {
                throw new Error('Invalid URL format')
            }

            // Check platform and open link accordingly
            if (Capacitor.isNativePlatform()) {
                // Open in Capacitor Browser for mobile
                await Browser.open({
                    url,
                    presentationStyle: 'fullscreen',
                    toolbarColor: '#ffffff', // Customize as needed
                })
            } else {
                // Open in browser for web platform
                window.open(url, '_blank', 'noopener,noreferrer')
            }
        } catch (e) {
            error.value = e instanceof Error ? e.message : 'Failed to open link'
            throw e
        } finally {
            isLoading.value = false
        }
    }

    return {
        openExternalLink,
        isLoading,
        error,
    }
}
