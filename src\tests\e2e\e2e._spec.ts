import { setup } from '@nuxt/test-utils/e2e'
import { fileURLToPath } from 'node:url'
// import { DescribeNavSearch } from './components'
// import { describe } from 'vitest'
// import DescribeDesktopNavigation from './navigation/desktop'

await setup({
  rootDir: fileURLToPath(new URL('../', import.meta.url)),
  nuxtConfig: {
    ssr: false,
  },
  browser: true,
  browserOptions: {
    launch: {
      // headless: true, // in ci
      headless: false, // to debug
    },
    type: 'chromium',
  },
})

// describe('nav/search', DescribeNavSearch)
// describe('navigation/desktop', DescribeDesktopNavigation)
