import { useEventBus } from '#imports'
export interface IData {
    _id?: string
    conversation: string
    user: string
    providerUser: string
    [key: string]: any
}

export const useCallHandle = createGlobalState(() => {
    const event = useEventBus<string>('call-handle')
    const { $fetchWellcare } = useNuxtApp()
    const { user } = useUserInfo({ scope: ['_id', 'isProvider', 'phone'] })

    const open = (key: 'voice' | 'video' | 'internet', data: IData) => {
        event.emit(key, data)
    }

    const close = () => {
        event.emit('close', {})
    }

    async function setPhone(data: {
        phoneNumber: string
        countryCode: string
    }) {
        if (!user.value?._id) throw new Error('missing user')
        if (user.value?.phone) throw new Error('phone existed')
        await $fetchWellcare(
            '/user-management/generic/user/' + user.value?._id,
            {
                method: 'POST',
                body: {
                    update: {
                        phone: data.phoneNumber,
                        countryCode: data.countryCode,
                    },
                },
            },
        )
    }

    return { event, open, close, setPhone }
})
