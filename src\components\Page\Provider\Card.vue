<script setup lang="ts">
import type { IProviderCard } from '~/models'

defineProps({
    provider: {
        type: Object as PropType<IProviderCard>,
        required: true,
    },
})

defineEmits(['on-click-card'])

const { t } = useI18n({ useScope: 'local' })
</script>

<template>
    <div class="flex flex-col gap-2 shadow-lg border rounded-lg p-4">
        <div
            class="flex items-stretch gap-3"
            @click="$emit('on-click-card')"
        >
            <NuxtImg
                class="h-20 w-20 rounded-full object-cover"
                height="80"
                width="80"
                :src="provider.avatar"
                :alt="provider.name"
                loading="lazy"
                placeholder="/images/skeleton.svg"
            />
            <div class="flex flex-col">
                <div class="text-lg font-semibold text-primary">
                    <span v-if="provider?.title">{{ provider?.title }}. </span>
                    <span v-dompurify-html="provider.name" />
                </div>
                <p
                    v-dompurify-html="provider.specialties"
                    class="line-clamp-2 text-gray-600 text-sm"
                />
            </div>
        </div>

        <div class="flex items-center justify-around">
            <div
                v-for="metric in provider.metrics"
                :key="metric.label"
                class="flex flex-col text-center"
            >
                <b>{{ `${metric.label} ${t(metric.unit)}` }}</b>
                <p class="text-xs">{{ t(metric.name) }}</p>
            </div>
        </div>

        <div>
            <Button
                :label="t('btn:see-profile')"
                pt:root:class="w-full h-10 border-none text-white"
                rounded
                @click="$emit('on-click-card')"
            />
        </div>
    </div>
</template>

<i18n lang="yaml">
en:
    'btn:see-profile': 'See profile'
    'years': 'years'
    'mins': 'mins'
    'experience': 'Experience'
    'average length': 'Average length'
    'satisfaction': 'Satisfaction'
vi:
    'btn:see-profile': 'Xem thông tin'
    'years': 'năm'
    'mins': 'phút'
    'experience': 'Kinh nghiệm'
    'average length': 'Tư vấn trung bình'
    'satisfaction': 'Hài lòng'
</i18n>
