<script setup lang="ts">
import { useI18n } from '#imports'

definePageMeta({
    layout: 'nested-page',
    validate: async (route) => {
        const validIds = [2, 4, 6, 9, 12, 15, 18, 24, 36, 48, 60]
        const id = parseInt(route.params.id as string)
        return validIds.includes(id)
    },
})

const route = useRoute()
const { query }: any = useRoute()
const { t, locale } = useI18n()
const state = reactive({
    userId: query?.userId,
})

const activeTab = computed(() => {
    const [unit, value] = (route.query.activeTab as string)?.split(':') || []
    const amount = parseInt(value, 10)

    if (unit === 'month') {
        return locale.value === 'vi'
            ? `${amount} tháng tuổi`
            : `${amount} month${amount > 1 ? 's' : ''} old`
    } else if (unit === 'year') {
        return locale.value === 'vi'
            ? `${amount} tuổi`
            : `${amount} year${amount > 1 ? 's' : ''} old`
    }
    return ''
})

const id = computed(() => route.params.id)
const layoutStore = useLayoutStore()
let timeoutId: ReturnType<typeof setTimeout> | null = null

const hidden_fields = computed(() => {
    return [
        {
            key: 'userId',
            value: query?.userId,
        },
    ]
})

const form: any = ref(null)
const router = useRouter()

const isSubmitted = ref(false)

const handleFormSubmit = () => {
    isSubmitted.value = true
    timeoutId = setTimeout(() => {
        router.back()
    }, 800)
}

onMounted(async () => {
    if (timeoutId !== null) {
        clearTimeout(timeoutId)
    }

    const formModule = await import(`./kids-dev-${id.value}.ts`)
    form.value = formModule.form

    timeoutId = setTimeout(() => {
        layoutStore.setTitle(
            t('development milestones', { date: activeTab.value }),
        )
    }, 300)
})

onBeforeUnmount(() => {
    if (timeoutId !== null) {
        clearTimeout(timeoutId)
    }
})
</script>

<template>
    <div class="p-4 py-6">
        <template v-if="!isSubmitted">
            <F
                v-if="form"
                :form="form(hidden_fields)"
                :state="state"
                @submit="handleFormSubmit"
            />
            <div v-else class="mx-auto flex w-full max-w-xl items-center">
                <ProgressSpinner
                    style="width: 50px; height: 50px"
                    stroke-width="8"
                    fill="transparent"
                    animation-duration=".5s"
                    aria-label="Custom ProgressSpinner"
                />
            </div>
        </template>
        <template v-else>
            <div
                class="flex h-[70vh] flex-col items-center justify-center text-center"
            >
                <iconify-icon
                    icon="lets-icons:check-ring-duotone"
                    class="text-[130px] text-green-500"
                />
                <p class="mt-2 font-semibold">
                    {{ t('completed') }}
                </p>
            </div>
        </template>
    </div>
</template>

<i18n lang="json">
{
    "vi": {
        "development milestones": "Mốc phát triển {date}",
        "btn:previous": "Quay lại",
        "btn:next": "Tiếp theo",
        "btn:submit": "Gửi",
        "completed": "Hoàn tất"
    },
    "en": {
        "development milestones": "Developmental Milestones {date}",
        "btn:previous": "Previous",
        "btn:next": "Next",
        "btn:submit": "Submit",
        "completed": "Completed"
    }
}
</i18n>
