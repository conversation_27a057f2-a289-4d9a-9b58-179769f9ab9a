<script setup lang="ts">
import { useNotivueNotifications } from '~/composables/component/use-notivue-notifications'
const notifications = useNotivueNotifications()

const showSuccess = () => {
    notifications.success({
        title: 'Thành công!',
        message: '<PERSON><PERSON> tác thành công.',
        duration: 3000,
    })
}

const showError = () => {
    notifications.error({
        title: 'Lỗi!',
        message: 'Đ<PERSON> xảy ra lỗi trong quá trình xử lý.',
        duration: 3000,
    })
}

const showWarning = () => {
    notifications.warning({
        title: 'Cảnh báo!',
        message: '<PERSON>ui lòng kiểm tra lại thông tin.',
        duration: 3000,
    })
}

const showInfo = () => {
    notifications.info({
        title: 'NHẮC gọi lại Bn. <PERSON><PERSON><PERSON><PERSON> - "Bác sĩ riêng"',
        message:
            'Bn. <PERSON><PERSON><PERSON><PERSON> <PERSON> đ<PERSON> gửi yêu cầu gọi lại lúc 10:39 ngày 07.01.2025. Hãy nhấp vào thông báo này để kết nối!',
        duration: 3000,
        props: {
            path: '/',
        },
    })
}
</script>

<template>
    <div class="container">
        <h1>Notification Demo</h1>

        <div class="button-group flex flex-col gap-4">
            <button
                class="success bg-green-500 px-4 py-3 text-white"
                @click="showSuccess"
            >
                Success Notification
            </button>

            <button class="bg-red-500 px-4 py-3 text-white" @click="showError">
                Error Notification
            </button>

            <button
                class="warning bg-orange-300 px-4 py-3 text-white"
                @click="showWarning"
            >
                Warning Notification
            </button>

            <button
                class="info bg-sky-600 px-4 py-3 text-white"
                @click="showInfo"
            >
                Info Notification
            </button>
        </div>
    </div>
</template>
