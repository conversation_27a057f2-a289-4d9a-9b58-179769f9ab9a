import type { IHitNotion } from '../hit/notion'
import type { INotionPage } from '.'

export interface NotionHealthProgram {
  'Parent item'?: INotionPage<NotionHealthProgram>[]
  'Sub-item'?: INotionPage<NotionHealthProgram>[]
  Description?: string
  Duration?: number
  Key?: string
  Labels?: string
  Locale?: string
  Name?: string
  Status?: string
  Tags?: string[]
  Unit?: string
}

export interface HitNotionHealthProgram extends IHitNotion {
  page?: INotionPage<NotionHealthProgram>
}
