<script setup lang="ts">
const { account } = useAppAuth()
const onboardingStore = useOnboardingStore()
const { key, rerenderSrc, refresh } = useRerender()

const wUserProfileFormViewRef = ref<any>(null)
const defaultData = reactive({
    avatar: { url: '' },
    name: '',
    dob: '',
    gender: '',
})

// Cập nhật defaultData từ onboardingStore nếu có dữ liệu
onMounted(async () => {
    if (onboardingStore.profile.name) {
        defaultData.name = onboardingStore.profile.name
        defaultData.dob = onboardingStore.profile.dob
        defaultData.gender = onboardingStore.profile.gender

        // Chuyển đổi file thành base64 nếu có
        if (onboardingStore.profile.file) {
            defaultData.avatar.url = await convertToBase64(
                onboardingStore.profile.file,
            )
            defaultData.file = onboardingStore.profile.file
        }

        refresh()
    }
})

const handleData = (data: any) => {
    defaultData.name = data.name
    defaultData.dob = data.dob
    defaultData.gender = data.gender

    // Lưu file vào defaultData
    if (data.file) {
        defaultData.file = data.file // Giữ lại file gốc để lưu vào store
    }
}

const convertToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result as string)
        reader.onerror = (error) => reject(error)
    })
}

const submit = async (): Promise<boolean> => {
    const isValid = await wUserProfileFormViewRef.value.submit()

    if (isValid) {
        onboardingStore.setProfile({
            name: defaultData.name,
            dob: defaultData.dob,
            gender: defaultData.gender,
            file: defaultData.file, // Lưu file gốc vào store
        })
        return true
    }

    return false
}

// Cập nhật defaultData từ account nếu không có dữ liệu trong store
rerenderSrc({
    source: account,
    onChanged: (newVal) => {
        if (!onboardingStore.profile.name) {
            defaultData.name = newVal.profile?.fullName || ''
        }
    },
})

defineExpose({
    submit,
})
</script>

<template>
    <div class="flex flex-col items-center justify-between gap-2 md:flex-row">
        <div class="w-full md:w-[60%]">
            <div class="text-left">
                <h4>Step 1/4</h4>
                <h1>Complete Your Profile</h1>
                <p class="mt-2 text-sm text-gray-600">
                    Please provide your personal information
                </p>
            </div>
        </div>

        <WUserProfileFormView
            :key="key"
            ref="wUserProfileFormViewRef"
            class="w-full rounded-xl md:w-[40%] md:border md:border-gray-200 md:p-4"
            is-hidden-submit
            is-require-avatar
            :default-data="defaultData"
            @on:submit="handleData"
        />
    </div>
</template>
