<script setup lang="ts">
import type { IProviderTag, ISpecialtyCard } from '~/models'

const props = defineProps({
    specialties: {
        type: Array as PropType<IProviderTag[]>,
        required: true,
    },
})

const router = useRouter()

const { user }: any = useUserInfo({
    scope: ['provider'],
})

const providerSlug = computed<string>(() => user.value?.provider?.slug)

const specialtySlugs = props.specialties.map(
    (specialty: IProviderTag) => specialty.key,
)

const { specialties: specialtyCards, loading } = usePageProviderHomeSpecialty(
    specialtySlugs,
    providerSlug,
)

const onClickCard = (specialtyCard: ISpecialtyCard) => {
    router.push(`/provider/home/<USER>
}
</script>

<template>
    <div v-if="!loading" class="grid grid-cols-1 gap-5 lg:grid-cols-3">
        <div
            v-for="specialtyCard in specialtyCards"
            :key="specialtyCard.slug"
            class="relative h-44 overflow-hidden rounded-md"
            @click="onClickCard(specialtyCard)"
        >
            <NuxtImg
                :src="specialtyCard.cover"
                sizes="364px md:400px"
                alt="Specialty photo"
                class="h-full w-full object-cover"
                loading="lazy"
            />
            <div
                class="absolute left-0 top-0 flex h-full w-full flex-col justify-between rounded-md bg-black bg-opacity-40 p-4"
            >
                <h4 class="text-xl text-white">{{ specialtyCard.name }}</h4>

                <AvatarGroup v-if="specialtyCard?.providers">
                    <Avatar
                        v-for="(
                            provider, index
                        ) in specialtyCard.providers.slice(0, 4)"
                        :key="index"
                        :image="provider.avatar"
                        shape="circle"
                        size="large"
                    />

                    <Avatar
                        v-if="specialtyCard.providers.length === 5"
                        :image="specialtyCard.providers[4].avatar"
                        shape="circle"
                        size="large"
                    />

                    <Avatar
                        v-if="specialtyCard.providers.length > 5"
                        shape="circle"
                        size="large"
                    >
                        <template #default>
                            <div class="relative h-full w-full">
                                <img
                                    :src="specialtyCard.providers[4].avatar"
                                    class="h-full w-full rounded-full"
                                    alt="provider"
                                />
                                <div class="overlay-text text-sm">
                                    +{{ specialtyCard.providers.length - 5 }}
                                </div>
                            </div>
                        </template>
                    </Avatar>
                </AvatarGroup>
            </div>
        </div>
    </div>
</template>

<style scoped>
.overlay-text {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    border-radius: 50%;
}
</style>
