<template>
    <div class="p-8">
        <!-- Input Block -->
        <div class="mb-4 mt-10 space-y-4">
            <div class="flex items-center gap-4">
                <label>Raw</label>
                <ToggleSwitch v-model="isRaw" />
            </div>
            <div v-if="isRaw">
                <Textarea
                    v-model="rawConfig"
                    class="w-full"
                    cols="10"
                    auto-resize
                />
            </div>
            <template v-else>
                <div>
                    <label class="block text-sm font-medium text-surface-700"
                        >App Id</label
                    >
                    <InputText
                        v-model="config.appid"
                        class="mt-1 w-full"
                        readonly
                    />
                </div>
                <div class="flex space-x-4">
                    <div class="w-full">
                        <label
                            class="block text-sm font-medium text-surface-700"
                            >Channel</label
                        >
                        <InputText
                            v-model="config.channel"
                            class="mt-1 w-full"
                        />
                    </div>
                    <!-- <div class="w-1/2">
          <label class="block text-sm font-medium text-surface-700">UID</label>
          <InputText v-model="config.uid" class="w-full mt-1" />
        </div> -->
                </div>
                <!-- Token Textarea -->
                <div>
                    <label class="block text-sm font-medium text-surface-700"
                        >Token</label
                    >
                    <Textarea
                        v-model="config.token"
                        rows="4"
                        class="mt-1 w-full"
                    />
                </div>
                <div class="border-[1px] border-gray-300" />
                <div class="flex space-x-4">
                    <div class="w-1/2">
                        <label
                            class="block text-sm font-medium text-surface-700"
                            >From Name</label
                        >
                        <InputText
                            v-model="config.from.name"
                            class="mt-1 w-full"
                        />
                    </div>
                    <div class="w-1/2">
                        <label
                            class="block text-sm font-medium text-surface-700"
                            >From id</label
                        >
                        <InputText
                            v-model="config.from._id"
                            class="mt-1 w-full"
                        />
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="grow">
                        <label
                            class="block text-sm font-medium text-surface-700"
                            >From avatar</label
                        >
                        <InputText
                            v-model="config.from.avatar"
                            class="mt-1 w-full"
                        />
                    </div>
                    <Avatar
                        :image="config.from.avatar"
                        size="large"
                        class="mt-2"
                    />
                </div>
                <div class="border-[1px] border-gray-300" />
                <div class="flex space-x-4">
                    <div class="w-1/2">
                        <label
                            class="block text-sm font-medium text-surface-700"
                            >From Name</label
                        >
                        <InputText
                            v-model="config.to.name"
                            class="mt-1 w-full"
                        />
                    </div>
                    <div class="w-1/2">
                        <label
                            class="block text-sm font-medium text-surface-700"
                            >To id</label
                        >
                        <InputText
                            v-model="config.to._id"
                            class="mt-1 w-full"
                        />
                    </div>
                </div>

                <div class="flex items-center space-x-4">
                    <div class="grow">
                        <label
                            class="block text-sm font-medium text-surface-700"
                            >To avatar</label
                        >
                        <InputText
                            v-model="config.to.avatar"
                            class="mt-1 w-full"
                        />
                    </div>
                    <Avatar
                        :image="config.to.avatar"
                        size="large"
                        class="mt-2"
                    />
                </div>
                <div class="border-[1px] border-gray-300" />
                <div>
                    <label class="block text-sm font-medium text-surface-700"
                        >Duration</label
                    >
                    <InputText
                        v-model="config.duration"
                        type="number"
                        class="mt-1 w-full"
                    />
                </div>
                <div>
                    <label class="block text-sm font-medium text-surface-700"
                        >Locale</label
                    >
                    <Dropdown
                        v-model="config.locale"
                        :options="['vi', 'en']"
                        class="mt-1 w-full"
                    />
                </div>
                <!-- Mode Dropdown -->
                <div>
                    <label class="block text-sm font-medium text-surface-700"
                        >Mode</label
                    >
                    <Dropdown
                        v-model="config.mode"
                        :options="['voice', 'video']"
                        class="mt-1 w-full"
                    />
                </div>
            </template>
        </div>

        <!-- Action Block -->
        <div class="flex space-x-4">
            <Button
                label="Join Channel"
                class="p-button-success"
                @click="joinChannel"
            />
            <Button label="End Call" severity="danger" @click="endCall" />
            <Button
                label="Back"
                severity="secondary"
                @click="navigateTo('/debug')"
            />
        </div>
    </div>
</template>

<script setup lang="ts">
import {
    CapacitorPluginAgora,
    type IStartCallOption,
} from '@wellcare/capacitor-plugin-agora'
import { reactive, navigateTo, toRaw } from '#imports'

const isRaw = ref<boolean>(false)
const config = reactive<IStartCallOption>({
    channel: 'test',
    token: '007eJxTYCgX+fjy57a0OaK9Oz+u4I2p/hnDw82q5/p6wU7Vq8vVL01VYEhKNDEzMU4zSjJONDQxTTOyNDU3TbIwtzBOMU40TU023HcyI70hkJHh661iBkYoBPFZGEpSi0sYGACh1SEa',
    appid: 'ba4643f2b3a145f29575b8783d3a5ec1',
    from: {
        name: 'Local user',
        // avatar: 'https://i.pravatar.cc/150?u=local',
        _id: 'u1',
    },
    to: {
        name: 'Remote user',
        // avatar: 'https://i.pravatar.cc/150?u=remote',
        _id: 'u2',
    },
    duration: 60,
    locale: 'en',
    mode: 'voice',
})
const rawConfig = ref<string>(JSON.stringify(config))

const joinChannel = () => {
    if (typeof config.duration === 'string')
        config.duration = parseInt(config.duration)
    CapacitorPluginAgora.startCall(toRaw(config))
}

const endCall = () => {
    CapacitorPluginAgora.endCall()
}
</script>

<style scoped>
/* Add any scoped styles if needed */
</style>
