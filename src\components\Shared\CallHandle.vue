<template>
    <WVideoConnecting :enabled="callType === 'video-call'" />

    <WVoiceConnecting :enabled="callType === 'voice-call'" />

    <WInternetConnecting :enabled="callType === 'internet-call'" />

    <OpenAppDialog ref="open-app-ref" />

    <WPhoneInputDialog ref="input-phone-ref" @on:save="onSavePhone" />

    <WCallOptions ref="call-option-ref" @on:select="onSelectCall" />
</template>
<script lang="ts" setup>
import { Capacitor } from '@capacitor/core'
import { CapacitorPluginAgora } from '@wellcare/capacitor-plugin-agora'
import { push } from 'notivue'
import OpenAppDialog from '~/components/Shared/OpenAppDialog.vue'
import type { IData } from '~/composables/component/use-call-handle'

const notifications = useNotivueNotifications()
const { t } = useI18n()
const dayjs = useDayjs()
const logger = useLogger('CallHandle')
const { user } = useUserInfo({ scope: ['_id', 'isProvider', 'phone'] })
const { getCallConnectStatus, executeSwitchPhoneConsultation } =
    useCallConnect()
const alertNotifications = useHeaderAlert()
const config: any = useRuntimeConfig()
const env = String(config.public.app.env) as 'production' | 'sandbox'
// const { options: phoneOptions } = useUpdatePhone()
const callHandle = useCallHandle()
const internetCall = useInternetCall()
const voiceCall = useVoiceCall()
const videoCall = useVideoCall()

const openAppRef = useTemplateRef('open-app-ref')
const inputPhoneRef = useTemplateRef('input-phone-ref')
const callOption = useTemplateRef('call-option-ref')

const conversation = ref<string>('')
const consultation = ref<string | undefined>('')
const toId = ref<string>('')
const callType = ref<string | null>(null)
const isMobile = Capacitor.isNativePlatform()

const onVideo = async (data: IData) => {
    logger.log('openVideo', JSON.stringify(data))
    if (!user.value?._id) {
        notifications.error({
            message: 'Error: Missing user data',
            duration: 5000,
        })
        throw new Error('missing user')
    }

    if (Capacitor.isNativePlatform()) {
        callType.value = 'video-call'
        conversation.value = data.conversation
        consultation.value = data._id
        toId.value =
            user.value?._id === data.user ? data.providerUser : data.user
        let res = null
        const pushPromise = push.promise(t('processing'))
        try {
            res = await getCallConnectStatus({
                from: user.value?._id,
                to: toId.value,
                conversation: conversation.value,
                consultation: consultation.value,
            })
            if (!res) throw new Error('no-connect')
            pushPromise.clear()
            await videoCall.startCall(res)
        } catch (e: any) {
            logger.error('openVideo', e.message)
            if (res) {
                pushPromise.clear()
                notifications.warning({
                    message: t(e.message, {
                        recallAt: dayjs(res?.stoppedAt).format('HH:mm'),
                    }),
                    duration: 5000,
                })
            } else {
                // notifications.error({
                //     message: e.message ?? 'Unexpected error. Please try again.',
                // })
                pushPromise.reject(
                    e.message ?? 'Unexpected error. Please try again.',
                )
            }
        }
    } else {
        openAppRef.value?.open()
    }
}
const onVoice = async (data: IData) => {
    logger.log('openVoice', JSON.stringify(data))
    if (!user.value?._id) {
        notifications.error({
            message: 'Error: Missing user data',
            duration: 5000,
        })
        throw new Error('missing user')
    }

    // if (Capacitor.isNativePlatform()) {
    conversation.value = data.conversation
    consultation.value = data._id
    toId.value = user.value?._id === data.user ? data.providerUser : data.user
    let res = null
    const pushPromise = push.promise(t('processing'))
    try {
        res = await getCallConnectStatus({
            from: user.value?._id,
            to: toId.value,
            conversation: conversation.value,
            consultation: consultation.value,
        })
        if (!res) throw new Error('no-connect')
        callOption.value?.open(res)
        pushPromise.clear()
    } catch (e: any) {
        if (res) {
            pushPromise.clear()
            notifications.warning({
                message: t(e.message, {
                    recallAt: dayjs(res?.stoppedAt).format('HH:mm'),
                }),
                duration: 5000,
            })
        } else {
            // notifications.error({
            //     message: e.message ?? 'Unexpected error. Please try again.',
            // })
            pushPromise.reject(
                e.message ?? 'Unexpected error. Please try again.',
            )
        }
    }
    // } else {
    //     openAppRef.value?.open()
    // }
}
callHandle.event.on((key: string, data: IData) => {
    if (key === 'video') onVideo(data)
    else if (key === 'voice') onVoice(data)
    else if (key === 'close') callOption.value?.close()
})

const onSelectCall = async (key: string, connectStatus: any) => {
    console.log('onSelectCall', JSON.stringify({ key, connectStatus }))
    if (['voice-call', 'internet-call'].includes(key) && !isMobile) {
        callOption.value?.close()
        openAppRef.value?.open()
        return
    }
    try {
        switch (key) {
            case 'cellular-call':
                callType.value = 'cellular-call'
                window.open('tel:+' + connectStatus.phone, '_self')
                break
            case 'voice-call':
                callType.value = 'voice-call'
                await voiceCall.startCall(connectStatus.connect)
                break
            case 'video-call':
                callType.value = 'video-call'
                await videoCall.startCall(connectStatus.connect)
                break
            case 'internet-call':
                callType.value = 'internet-call'
                await internetCall.startCall(connectStatus.connect)
                break
            case 'change-phone':
                inputPhoneRef.value?.open()
                break
        }
        callOption.value?.close()
    } catch (e: any) {
        notifications.warning({
            message: t(e.message, {
                recallAt: dayjs(connectStatus.connect?.stoppedAt).format(
                    'HH:mm',
                ),
            }),
            duration: 5000,
        })
    }
}

async function onSavePhone(data: any) {
    if (!user.value?.phone) {
        await callHandle.setPhone(data)
    } else {
        await executeSwitchPhoneConsultation({
            user: user.value._id!,
            consultation: consultation.value ?? '',
            countryCode: data.countryCode,
            phone: data.phoneNumber,
        })
    }
    inputPhoneRef.value?.close()
    // callOption.value?.open()
}

function configEnv() {
    CapacitorPluginAgora.setSocketEnv({
        env,
    })
}

watch(internetCall.isConnected, (val) => {
    const id = 'internetCall.isConnected'
    if (!val) {
        alertNotifications.add({
            id,
            content: t('call not available'),
            severity: 'warn',
        })
    } else {
        alertNotifications.add({
            id,
            content: t('call connection successful'),
            severity: 'success',
        })
        setTimeout(() => alertNotifications.remove(id), 3000)
    }
})
watch(voiceCall.isConnected, (val) => {
    const id = 'call.isConnected'
    if (!val) {
        alertNotifications.add({
            id,
            content: t('call not available'),
            severity: 'warn',
        })
    } else {
        alertNotifications.add({
            id,
            content: t('call connection successful'),
            severity: 'success',
        })
        setTimeout(() => alertNotifications.remove(id), 3000)
    }
})
watch(videoCall.isConnected, (val) => {
    const id = 'call.isConnected'
    if (!val) {
        alertNotifications.add({
            id,
            content: t('call not available'),
            severity: 'warn',
        })
    } else {
        alertNotifications.add({
            id,
            content: t('call connection successful'),
            severity: 'success',
        })
        setTimeout(() => alertNotifications.remove(id), 3000)
    }
})
onMounted(() => {
    configEnv()
})
</script>
<i18n lang="json">
{
    "vi": {
        "appointment-time-passed": "Đã qua giờ hẹn bác sĩ",
        "callee-is-busy": "Bác sĩ chưa khám xong ca trước do bệnh nhân gọi trễ ít phút so với giờ hẹn. Thời lượng tư vấn chỉ tính từ lúc bạn gặp bác sĩ. Vui lòng gọi lại lúc {recallAt}.",
        "server-busy": "Server đang bận, hãy thử lại sau.",
        "no-phone-connect": "Không tìm thấy kết nối. vui lòng thử lại.",
        "please-call-on-time": "Chưa đến giờ hẹn. Vui lòng gọi đúng giờ",
        "quota-has-been-exceeded": "Thời lượng tư vấn đã hết",
        "time-passed": "Đã qua giờ hẹn bác sĩ",
        "quota-exceeded": "Thời lượng tư vấn đã hết",
        "call-ontime": "Chưa đến giờ hẹn. Vui lòng gọi đúng giờ",
        "call-busy": "Bác sĩ chưa khám xong ca trước do bệnh nhân gọi trễ ít phút so với giờ hẹn. Thời lượng tư vấn chỉ tính từ lúc bạn gặp bác sĩ. Vui lòng gọi lại lúc {recallAt}.",
        "no-connect": "Không tìm thấy kết nối. vui lòng thử lại.",
        "on-call": "Đang trong cuộc gọi. Vui lòng kết thúc cuộc gọi để tiếp tục",
        "call not available": "Không có kết nối",
        "call connection successful": "Đã kết nối",
        "processing": "đang xử lý"
    },
    "en": {
        "appointment-time-passed": "Appointment time passed",
        "callee-is-busy": "The doctor is still with the previous patient because he/she was late. The time duration is only counted from the time the doctor answers your call. Please call again at {recallAt}.",
        "server-busy": "The server is busy, please try again later.",
        "no-phone-connect": "No connect found. Please try again.",
        "please-call-on-time": "Please call on time",
        "quota-has-been-exceeded": "Quota has been exceeded",
        "time-passed": "Appointment time passed",
        "quota-exceeded": "Quota has been exceeded",
        "call-ontime": "Please call on time",
        "call-busy": "The doctor is still with the previous patient because he/she was late. The time duration is only counted from the time the doctor answers your call. Please call again at {recallAt}.",
        "no-connect": "No connect found. Please try again.",
        "on-call": "In a call. Please end the call before you start a new one",
        "call not available": "No call connection",
        "call connection successful": "Call connected",
        "processing": "processing"
    }
}
</i18n>
