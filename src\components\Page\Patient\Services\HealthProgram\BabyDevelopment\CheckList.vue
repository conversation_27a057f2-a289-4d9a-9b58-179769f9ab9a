<!-- ~/components/MilestoneProgress.vue -->
<script setup lang="ts">
import { ref } from 'vue'
import { DevelopmentArea } from '~/models'
import { useMilestoneForm } from '~/composables'

const { userId, activeTab } = defineProps({
    userId: {
        type: String,
        default: '',
    },
    activeTab: {
        type: String,
        default: '',
    },
})

const {
    currentMonth,
    currentForm,
    nearestMilestone,
    data,
    getAreaProgress,
    getAnswerColor,
    totalProgress,
} = useMilestoneForm(
    computed(() => userId),
    computed(() => activeTab),
)

const selectedArea = ref(DevelopmentArea.SOCIAL)

const filteredItems = computed(() => {
    if (!data.value?.results[0]?.response || !currentForm.value) return 0

    const questions = currentForm.value?.pages
        .filter((page) => page.id === selectedArea.value)
        .flatMap((page) => page.elements)
        .filter((el) => el.properties?.input?.label)
        .map((el) => el.properties.input.label)

    return data.value?.results[0].response.filter((q) =>
        questions.includes(q.question),
    )
})

const router = useRouter()

const navigateToMilestone = () => {
    if (currentMonth) {
        router
            .push({
                path: `/patient/form/checklist/${nearestMilestone.value?.month}`,
                query: {
                    userId,
                    activeTab,
                },
            })
            .catch((err) => {
                console.error('Router push error:', err)
            })
    } else {
        console.error('Computed milestone is null or invalid.')
    }
}
</script>

<template>
    <div class="mx-auto max-w-3xl">
        <!-- Progress Overview -->
        <div class="mb-6 rounded-lg bg-white p-2 py-3 shadow-sm">
            <div class="mb-6 flex items-center justify-between">
                <div>
                    <h4 class="text-base font-semibold text-gray-800">
                        Mốc phát triển {{ nearestMilestone?.label }}
                    </h4>

                    <p class="mt-1 text-xs text-gray-600">
                        Tổng tiến độ hoàn thành: {{ totalProgress }}%
                    </p>
                </div>
                <Button @click="navigateToMilestone"> Cập nhật </Button>
            </div>

            <!-- Progress Circles -->
            <div class="grid grid-cols-4 gap-3">
                <div
                    v-for="area in Object.values(DevelopmentArea)"
                    :key="area"
                    class="text-center"
                >
                    <div class="relative inline-block">
                        <svg class="h-20 w-20 -rotate-90 transform">
                            <circle
                                class="text-gray-200"
                                stroke-width="5"
                                stroke="currentColor"
                                fill="transparent"
                                r="30"
                                cx="40"
                                cy="40"
                            />
                            <circle
                                class="text-primary"
                                stroke-width="5"
                                :stroke-dasharray="2 * Math.PI * 30"
                                :stroke-dashoffset="
                                    2 *
                                    Math.PI *
                                    30 *
                                    (1 - getAreaProgress(area) / 100)
                                "
                                stroke="currentColor"
                                fill="transparent"
                                r="30"
                                cx="40"
                                cy="40"
                            />
                        </svg>
                        <span
                            class="absolute inset-0 flex items-center justify-center text-sm font-medium"
                        >
                            {{ getAreaProgress(area) }}%
                        </span>
                    </div>
                    <p class="mt-2 text-xs font-medium text-gray-600">
                        {{ area }}
                    </p>
                </div>
            </div>
        </div>

        <!-- Development Areas Tabs -->
        <div v-if="currentForm" class="rounded-lg bg-white shadow-sm">
            <div class="border-b">
                <nav class="flex">
                    <button
                        v-for="area in Object.values(DevelopmentArea)"
                        :key="area"
                        :class="[
                            'border-b-2 px-3 py-2 text-xs font-medium transition-colors',
                            selectedArea === area
                                ? 'border-primary-600 text-primary-600'
                                : 'border-transparent text-gray-500 hover:text-gray-700',
                        ]"
                        @click="selectedArea = area"
                    >
                        {{ area }}
                    </button>
                </nav>
            </div>

            <!-- Questions for Selected Area -->
            <div class="p-4">
                <div class="space-y-3">
                    <div
                        v-for="item in filteredItems"
                        :key="item.question"
                        :class="[
                            'rounded-lg border-l-4 p-4 transition-all',
                            getAnswerColor(item.answer),
                        ]"
                    >
                        <div class="flex items-center justify-between">
                            <p class="text-sm text-gray-800">
                                {{ item.question }}
                            </p>
                            <span
                                :class="[
                                    'rounded-full px-3 py-1 text-center text-xs font-medium',
                                    item.answer === 'Có'
                                        ? 'bg-green-200 text-green-800'
                                        : item.answer === 'Không chắc'
                                          ? 'bg-yellow-100 text-yellow-800'
                                          : 'bg-red-100 text-red-800',
                                ]"
                            >
                                {{ item.answer }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
circle {
    transition: stroke-dashoffset 0.35s;
    transform-origin: 50% 50%;
}
</style>
