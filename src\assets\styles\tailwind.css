@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import './primevue/tailwind.css';
@import 'tailwindcss/utilities';

@layer components {
    .link {
        @apply cursor-pointer underline decoration-transparent underline-offset-2 transition hover:text-primary hover:decoration-[var(--p-primary-500)];
    }
    .card {
        @apply rounded bg-surface-50 p-2 sm:p-3 dark:bg-surface-900;
    }
    .bg-highlight {
        @apply !text-primary;
    }
}

@layer base {
    ::selection {
        @apply bg-primary-100 dark:bg-primary-950;
    }

    html {
        font-family: var(--font-style);
        height: -webkit-fill-available;
    }

    body {
        @apply bg-surface-0 text-surface-950 dark:bg-surface-950 dark:text-surface-100;
    }

    h1 {
        @apply text-3xl font-bold tracking-tight md:text-5xl;
    }

    h2 {
        @apply text-2xl font-bold tracking-tight md:text-4xl;
    }

    h3 {
        @apply text-xl font-semibold leading-tight md:text-3xl;
    }
    h4 {
        @apply text-lg font-medium leading-tight md:text-3xl;
    }
}
