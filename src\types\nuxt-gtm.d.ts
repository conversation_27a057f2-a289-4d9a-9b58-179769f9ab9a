import type { Context as NuxtContext } from '@nuxt/types'

declare global {
  interface Window {
    readonly dataLayer: {
      push?: (data: { event: string; [k: string]: any }) => void
    }
  }
}

interface GtmClient {
  init(id): void
  push(obj): void
}

declare module '@nuxt/types' {
  interface Context extends NuxtContext {
    $gtm: GtmClient
  }
  interface NuxtAppOptions extends NuxtContext {
    $gtm: GtmClient
  }
}
