import type {
    IChoice,
    IValidation,
    IFetchElement,
    EComponentType,
} from '../element'

export interface IPersonalDoctorList {
    id: number
    label: string // Personal Doctor List
    component: EComponentType // 'multiple_choice'
    description: string // Select one option
    choices: IChoice[]
    fetch?: IFetchElement
    validations?: IValidation[]
}

// Example:
// {
//   "id": "m32hzkx1.u5",
//   "type": "input",
//   "properties": {
//     "input": {
//       "label": "Checkboxes",
//       "component": "checkboxes",
//       "description": "Select one or more options.",
//       "placeholder": "Choose options",
//       "choices": [
//         {
//           "label": "Membership",
//           "value": "option1"
//         },
//         {
//           "label": "Option 2",
//           "value": "option2"
//         }
//       ],
//       "key": "Checkboxesm32hzkx1.u5"
//     }
//   }
// }
