<script setup lang="ts">
import type { IFolder } from '~/models/component/folder.interface'

interface Props {
    // eslint-disable-next-line vue/require-default-prop
    folder?: IFolder
    layout?: 'Grid' | 'List'
}

withDefaults(defineProps<Props>(), {
    layout: 'List',
})
</script>

<template>
    <div>
        <div
            v-if="layout == 'List'"
            class="card flex flex-row items-center gap-2"
        >
            <iconify-icon
                icon="material-symbols:folder"
                class="text-5xl text-amber-300"
            />
            <div>
                <div class="mb-1 flex flex-row items-center gap-2">
                    <p class="text-lg font-semibold">{{ folder?.title }}</p>
                    <Tag :value="folder?.tag?.label" />
                </div>
                <p class="leading-none text-slate-400">
                    {{ folder?.files?.length }} Files, {{ folder?.date }}
                </p>
            </div>
        </div>
        <div v-else class="card">
            <div class="flex flex-row items-center gap-1 pb-1">
                <Icon
                    name="material-symbols:folder"
                    class="text-amber-500 dark:text-amber-500"
                />
                <p class="m-0 font-semibold leading-none">
                    {{ folder?.title }}
                </p>
            </div>

            <div class="flex flex-row flex-wrap gap-3">
                <Image v-for="file in folder?.files" :key="file" preview image>
                    <template #image>
                        <NuxtImg
                            :src="file"
                            alt="image"
                            class="h-32 w-24 rounded object-cover"
                        />
                    </template>
                    <template #preview="slotProps">
                        <NuxtImg
                            :src="file"
                            alt="preview"
                            :style="slotProps.style"
                            @click="slotProps.onClick"
                        />
                    </template>
                </Image>
            </div>
        </div>
    </div>
</template>
