<script lang="ts" setup>
import type { ICategoryCard } from '~/models'

defineProps({
    category: {
        type: Object as PropType<ICategoryCard>,
        required: true,
    },
    numberOfTopics: {
        type: Number,
        required: true,
    },
})

const { t } = useI18n()

const handleSelectCategory = async (category: any) => {
    // Navigate to the selected category page
    await navigateTo({ path: `/patient/edu-hub/media/${category.slug}` })
}
</script>

<template>
    <li
        class="card flex cursor-pointer items-stretch gap-3 overflow-hidden rounded-md border hover:bg-zinc-100 dark:border-gray-700 dark:hover:bg-black/20"
        @click="handleSelectCategory(category)"
    >
        <img
            :src="category.cover"
            :alt="category.name"
            class="h-32 w-32"
            loading="lazy"
        />
        <div class="mt-1 flex flex-1 flex-col pr-1">
            <div class="flex items-stretch justify-between font-semibold">
                <span>{{ category.name }}</span>
                <i class="pi pi-angle-right mt-1" aria-hidden="true" />
            </div>
            <div class="flex-1 text-sm">
                {{ category.description }}
            </div>
            <div class="flex items-center gap-1 pb-1 text-sm text-primary-400">
                <span>{{ numberOfTopics }}</span>
                <span>{{ t('topics') }}</span>
            </div>
        </div>
    </li>
</template>

<i18n lang="yaml">
en:
    topics: 'topics'
vi:
    topics: 'chuyên đề'
</i18n>
