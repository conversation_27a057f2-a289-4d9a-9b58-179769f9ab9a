// stores/app.ts
import { defineStore } from 'pinia'
import type { DeviceInfo } from '@capacitor/device'
import type { ISafeArea } from '~/models/common/device-info.interface'

interface State {
    device: DeviceInfo | null
    safeArea: ISafeArea
    signature: string
    callkeepInitialize: boolean
    version: string | null
}

export const useAppStore = defineStore('app', {
    state: (): State => ({
        device: null,
        safeArea: {
            top: 0,
            right: 0,
            bottom: 0,
            left: 6,
            statusBarHeight: 0,
        },
        signature: '',
        callkeepInitialize: false,
        version: null,
    }),
    actions: {
        setSafeArea(safeArea: ISafeArea) {
            this.safeArea = safeArea
        },
        setDevice(device: DeviceInfo) {
            this.device = device
        },
    },
    getters: {
        deviceName: (state) => state.device?.name || '',
    },
})
