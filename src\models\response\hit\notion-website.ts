import type { IDate } from '../../transform'
import type { NotionContributors } from '../notion'
import type { INotionPage } from '../notion/page'
import type { IHitNotion } from './notion'

export interface NotionWebsite {
    'Last edited time'?: string
    'Meta Description'?: string
    'Parent item'?: INotionPage<NotionWebsite>[]
    Contributors?: INotionPage<NotionContributors>[]
    Keywords?: string[]
    Labels?: string[]
    Layout?: string
    Locale?: string
    ModifiedAt?: IDate
    Name?: string
    Order?: number
    PublishedAt?: IDate
    Sapo?: string
    Site?: string
    Slug?: string
    Status?: string
    Translation?: INotionPage<NotionWebsite>[]
    Type?: string
    URL?: string
}

export interface HitNotionWebsite extends IHitNotion {
    html?: string
    page: INotionPage<NotionWebsite>
}
