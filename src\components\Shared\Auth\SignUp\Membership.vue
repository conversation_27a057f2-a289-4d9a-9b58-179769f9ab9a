<script setup lang="ts">
import { ref } from 'vue'

const wantMembership = ref(false)
const submit = () => {
    console.log('submit')
    return true
}

defineExpose({
    submit,
})
</script>

<template>
    <div>
        <!-- Highlighted Offer Section -->
        <div
            class="mb-4 flex items-center rounded-lg border border-blue-500 bg-blue-50 p-4 shadow-md"
            role="alert"
        >
            <svg
                class="h-6 w-6 text-blue-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M13 16h-1v-4h-1m4 4h-1v-4h-1m-1 4h-1v-4h-1m0 4H8v-4H7m5-6v4pV8pm0 0V4m0 0H8m-4 0p"
                />
            </svg>
            <div class="ml-3">
                <p class="text-lg font-semibold text-blue-800">
                    Special Offer: Membership for only 250K!
                </p>
                <p class="text-blue-700">
                    Enjoy exclusive benefits, discounts, and more.
                </p>
            </div>
        </div>

        <!-- Membership Option -->
        <div class="mb-4">
            <Checkbox id="membership" v-model="wantMembership" :binary="true" />
            <label for="membership" class="ml-2 font-medium text-surface-800"
                >Yes, I want to become a member!</label
            >
        </div>

        <!-- Additional Info -->
        <div v-if="wantMembership" class="mt-4 text-green-700">
            <p class="font-bold">Great choice!</p>
            <p>You’re about to unlock exclusive content and offers.</p>
        </div>
    </div>
</template>
