import { type Ref, ref } from '#imports'
import type { IMedicalHistory } from '~/models/page/health-records'

export function useMedicalHistory() {
  const medicalHistory: Ref<IMedicalHistory[]> = ref([
    {
      condition: 'Hypertension',
      diagnosisDate: '2021-05-15',
      treatment: 'ACE Inhibitors',
      doctor: 'Dr. <PERSON>',
      clinic: 'HealthCare Clinic',
      notes: 'Blood pressure well controlled with medication',
    },
    {
      condition: 'Type 2 Diabetes',
      diagnosisDate: '2019-08-23',
      treatment: 'Metformin',
      doctor: 'Dr. <PERSON>',
      clinic: 'Wellness Medical Center',
      notes:
        'Patient following a low-carb diet and monitoring blood sugar levels regularly',
    },
    {
      condition: 'Asthma',
      diagnosisDate: '2015-02-10',
      treatment: 'Inhaled Corticosteroids',
      doctor: 'Dr. <PERSON>',
      clinic: 'Pulmonary Care Clinic',
      notes:
        'Condition stable with current treatment, occasional flare-ups during cold weather',
    },
    {
      condition: 'Hyperlipidemia',
      diagnosisDate: '2020-11-01',
      treatment: 'Statins',
      doctor: 'Dr. <PERSON>',
      clinic: 'Heart & Vascular Institute',
      notes:
        'Cholesterol levels improving, advised to continue lifestyle changes',
    },
    {
      condition: 'Allergic Rhinitis',
      diagnosisDate: '2018-04-12',
      treatment: 'Antihistamines',
      doctor: 'Dr. Sarah <PERSON>',
      clinic: 'Allergy & Immunology Center',
      notes: 'Patient managing well with seasonal allergy medications',
    },
  ])

  return { medicalHistory }
}
