<script lang="ts" setup>
import { WPhoneInput } from '#components'

const props = withDefaults(
    defineProps<{
        title?: string
        description?: string
        placeholder?: string
        useStore?: boolean
        initialPhone?: string
    }>(),
    {
        useStore: true,
    },
)

const emit = defineEmits<{
    'phone-submit': [data: { countryCode: string; phoneNumber: string }]
}>()

const { t } = useI18n()
const onboardingStore = useOnboardingStore()

const phoneNumber = ref('')
const p = reactive({
    phoneNumber: '',
    countryCode: '',
})
const phoneNumberRef = ref<InstanceType<typeof WPhoneInput> | null>(null)

const handlePhoneChange = (value: {
    countryCode: string
    phoneNumber: string
}) => {
    p.phoneNumber = value.phoneNumber
    p.countryCode = value.countryCode
}

const submit = () => {
    const isValid = phoneNumberRef.value?.isValid()
    if (isValid) {
        if (props.useStore) {
            onboardingStore.setPhoneData(p.phoneNumber, p.countryCode)
            onboardingStore.setUsername(p.phoneNumber)
        }

        emit('phone-submit', p)
    }
    return isValid
}

onMounted(() => {
    if (props.initialPhone) {
        phoneNumber.value = props.initialPhone
    } else if (props.useStore && onboardingStore.profile.phone) {
        phoneNumber.value = onboardingStore.profile.phone
        p.phoneNumber = onboardingStore.profile.phone
        p.countryCode = onboardingStore.profile.countryCode
    }
})

defineExpose({ submit })
</script>

<template>
    <div class="space-y-2 text-center">
        <h3 v-if="title">
            {{ title }}
        </h3>
        <h3 v-else>
            {{ t('phone-title') }}
        </h3>

        <p
            v-if="description"
            class="text-base text-gray-600 dark:text-gray-400"
        >
            {{ description }}
        </p>
        <p v-else class="text-base text-gray-600 dark:text-gray-400">
            {{ t('phone-description') }}
        </p>

        <div>
            <WPhoneInput
                ref="phoneNumberRef"
                v-model="phoneNumber"
                :placeholder="placeholder || t('phone-placeholder')"
                class="w-full"
                @change="handlePhoneChange"
            />
        </div>
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "phone-title": "Link with your phone account",
        "phone-description": "Enter your phone number to verify",
        "phone-placeholder": "Enter phone number",
        "phone-privacy-notice": "We'll send you a verification code via SMS. Message and data rates may apply."
    },
    "vi": {
        "phone-title": "Đồng bộ với tài khoản của số điện thoại",
        "phone-description": "Nhập số điện thoại để xác thực tài khoản",
        "phone-placeholder": "Nhập số điện thoại",
        "phone-privacy-notice": "Chúng tôi sẽ gửi mã xác thực qua SMS. Có thể áp dụng phí tin nhắn và dữ liệu."
    }
}
</i18n>
