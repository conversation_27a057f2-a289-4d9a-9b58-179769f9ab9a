import { useRuntimeConfig } from '#app'

interface IHiddenField {
    key: string
    value: any
    component?: string
}

export const form = (hidden_fields: IHiddenField[]) => {
    const runtimeConfig: any = useRuntimeConfig()
    const appEnv = runtimeConfig.public.app.env

    return {
        objectId: appEnv === 'production' ? 'VMbxnFrIXJ' : '2EqXkGNnho',
        pages: [
            {
                id: 'main',
                type: 'input',
                elements: [
                    {
                        id: 'chiefComplaint',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Lý do yêu cầu',
                                description:
                                    'Vui lòng nhập lý do cho yêu cầu của bạn',
                                placeholder: 'Lý do tối đa 50 ký tự',
                                key: 'chiefComplaint',
                                component: 'short_text',
                                validations: [
                                    {
                                        rule: 'required',
                                    },
                                    {
                                        rule: 'maxLength',
                                        params: ['50'],
                                    },
                                ],
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'expectedTime',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Thời gian dự kiến',
                                description: 'Vui lòng nhập thời gian dự kiến',
                                key: 'expectedTime',
                                placeholder: 'VD: 20h tối nay ...',
                                component: 'short_text',
                                validations: [
                                    {
                                        rule: 'required',
                                    },
                                    {
                                        rule: 'maxLength',
                                        params: ['40'],
                                    },
                                ],
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'files',
                        type: 'input',

                        properties: {
                            input: {
                                label: 'Tải tệp lên',
                                component: 'file_upload',
                                description: 'Tải lên',
                                placeholder: 'Tải tệp lên tại đây',
                                key: 'files',
                                props: {
                                    accept: 'image/*,.doc,.docx,.pdf,video/*',
                                },
                                validations: [
                                    {
                                        rule: 'maxFileSize',
                                        params: [40],
                                        constraintMessage:
                                            'Tối đa 10 hình hay 3 video clip ngắn dưới 60 giây, 10 images or 3 video clip less than 60 seconds',
                                        exceedMessage:
                                            'Kích thước tệp tải lên quá lớn',
                                    },
                                    {
                                        rule: 'maxLength',
                                        params: ['13'],
                                    },
                                ],
                            },
                        },
                    },
                ],
            },
        ],
        title: 'Request Callback',
        submission: 'remote',
        space: {
            __type: 'Pointer',
            className: 'Space',
            objectId: appEnv === 'production' ? 'opVNsUfKoM' : 'ZhKJvCjuhu',
        },
        computes: hidden_fields.map((hidden_field: IHiddenField) => {
            return {
                key: hidden_field.key,
                value: hidden_field.value,
                component: 'hidden_field',
            }
        }),
    }
}
