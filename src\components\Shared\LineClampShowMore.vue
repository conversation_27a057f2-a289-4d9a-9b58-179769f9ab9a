<script setup lang="ts">
// import type { ShallowRef } from 'vue'
interface Props {
    show: boolean
    class: string
}

const props = defineProps<Props>()

const _Ref = useTemplateRef('_Ref')
const isExpanded = computed(() => props.show)
const isTruncated = ref(false)

const checkTruncation = () => {
    const element = _Ref.value
    if (element) {
        isTruncated.value = element.scrollHeight > element.clientHeight
    }
}

// Theo dõi resize window
let resizeObserver: ResizeObserver

onMounted(() => {
    checkTruncation()

    // Khởi tạo ResizeObserver để theo dõi thay đổi kích thước
    resizeObserver = new ResizeObserver(checkTruncation)
    if (_Ref.value) {
        resizeObserver.observe(_Ref.value)
    }

    // Thêm listener cho window resize
    window.addEventListener('resize', checkTruncation)
})

onBeforeUnmount(() => {
    // Cleanup
    if (_Ref) {
        resizeObserver.disconnect()
    }
    window.removeEventListener('resize', checkTruncation)
})
</script>

<template>
    <div ref="_Ref" :class="[props.class, { 'line-clamp-none': isExpanded }]">
        <slot />
    </div>

    <slot name="show-more" />
</template>
