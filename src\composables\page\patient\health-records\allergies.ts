import type { IAllergy } from '~/models/page/health-records'

export function useAllergies() {
  const allergies: IAllergy[] = [
    {
      name: 'Pollen',
      severity: 'Moderate',
      lastReaction: '2 months ago',
      medications: ['Antihistamines'],
      hasEpiPen: false,
    },
    {
      name: 'Nuts',
      severity: 'Severe',
      lastReaction: '1 year ago',
      medications: ['Epinephrine'],
      hasEpiPen: true,
    },
  ]

  return { allergies }
}
