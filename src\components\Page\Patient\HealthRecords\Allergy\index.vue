<script setup lang="ts">
import { useAllergies } from '~/composables/page/patient/health-records/allergies'
const { allergies } = useAllergies()
interface Props {
    title: string
    to: string
}

defineProps<Props>()
</script>

<template>
    <div>
        <SharedPageSection
            v-if="allergies.length > 0"
            :title="title"
            :pt="{ content: 'flex flex-row flex-nowrap gap-3 px-6' }"
            :view-all-to="to"
        >
            <PagePatientHealthRecordsAllergyCard
                v-for="allergy in allergies"
                :key="allergy.name"
                :allergy="allergy"
            />
        </SharedPageSection>
        <PagePatientHealthRecordsAddInfo
            v-else
            title="Add Allergies"
            class="mx-6 mt-8"
            icon="material-symbols:medical-services"
            description="List any allergies you have to ensure proper care."
        />
    </div>
</template>
