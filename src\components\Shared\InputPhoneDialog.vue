<template>
    <!-- Dialog Component -->
    <Dialog
        v-model:visible="showDialog"
        header="Enter Phone Details"
        :modal="true"
        :closable="true"
        :draggable="false"
        position="bottom"
    >
        <div>
            <!-- Grouped Inputs in Grid -->
            <div class="mb-4 mt-2 grid grid-cols-12 gap-4">
                <!-- Country Code Input -->
                <div class="col-span-4">
                    <InputText
                        id="countryCode"
                        v-model="countryCode"
                        class="p-inputtext p-component w-full rounded-md border border-gray-300 focus:border-blue-300 focus:ring"
                        placeholder="e.g., +1"
                    />
                </div>

                <!-- Phone Number Input -->
                <div class="col-span-8">
                    <InputText
                        id="phoneNumber"
                        v-model="phoneNumber"
                        class="p-inputtext p-component w-full rounded-md border border-gray-300 focus:border-blue-300 focus:ring"
                        placeholder="e.g., ************"
                    />
                </div>
            </div>

            <!-- Dialog Actions -->
            <div class="flex justify-end">
                <Button
                    label="Cancel"
                    class="p-button-secondary mr-2"
                    @click="showDialog = false"
                />
                <Button
                    label="Save"
                    class="p-button-primary"
                    @click="submitPhoneDetails"
                />
            </div>
        </div>
    </Dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// PrimeVue Components
import Button from 'primevue/button'
import Dialog from 'primevue/dialog'
import InputText from 'primevue/inputtext'

const emit = defineEmits(['on:save'])

const showDialog = ref(false)
const countryCode = ref<string>('')
const phoneNumber = ref<string>('')

// Methods
const submitPhoneDetails = () => {
    console.log('Country Code:', countryCode.value)
    console.log('Phone Number:', phoneNumber.value)
    emit('on:save', {
        countryCode: countryCode.value,
        phoneNumber: phoneNumber.value,
    })
}

function open() {
    showDialog.value = true
}
function close() {
    showDialog.value = false
}
defineExpose({
    open,
    close,
})
</script>
