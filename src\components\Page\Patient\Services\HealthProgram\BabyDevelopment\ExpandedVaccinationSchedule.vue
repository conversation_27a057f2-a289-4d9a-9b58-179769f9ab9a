<script lang="ts" setup>
interface ICell {
    content: string
    colspan: number
    background?: string
    title?: string
    subtitle?: string
    describe?: string
}

const headers = [
    'Vaccine',
    'Tên thuốc',
    '<PERSON><PERSON> sinh',
    '1 tháng',
    '2 tháng',
    '4 tháng',
    '6 tháng',
    '9 tháng',
    '12 tháng',
    '15 tháng',
    '18 tháng',
    '19-23 tháng',
    '2-3 tuổi',
    '4-6 tuổi',
    '7-10 tuổi',
    '9 tuổi',
    '11-12 tuổi',
    '13-15 tuổi',
    '16 tuổi',
    '17-18 tuổi',
]

const rows: ICell[][] = [
    [
        {
            content: 'Lao (BCG)',
            colspan: 1,
        },
        {
            content: 'BCG',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 1,
            background: 'bg-[#00c853]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Viêm gan B (Hepatitis B)',
            colspan: 1,
        },
        {
            content: 'Engerix-B',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 1,
            background: 'bg-[#e8f5e9]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Vaccine 6 trong 1 (6in1)',
            colspan: 1,
            title: 'Vaccine 6 trong 1',
            subtitle: '(6in1)',
            describe:
                'Phòng ngừa các bệnh: Bạch hầu; Bại liệt; Uốn ván; Viêm gan B; Ho gà; Hib',
        },
        {
            content: 'Hexaxim hoặc Infanrix Hexa',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '3 mũi',
            colspan: 3,
            background: 'bg-[#c8e6c9]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 1,
            background: 'bg-[#c8e6c9]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Tiêu chảy do virus Rota (Rota virus)',
            colspan: 1,
        },
        {
            content: 'Rotarix hoặc Rotateq',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '2 mũi Rotarix hoặc 3 mũi Rotateq',
            colspan: 3,
            background: 'bg-[#a5d6a7]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Phế cầu (pneumococcal)',
            colspan: 1,
            title: 'Phế cầu',
            subtitle: '(pneumococcal)',
            describe:
                'Phòng ngừa các bệnh: Viêm phổi; Viêm màng não; Nhiễm trùng huyết...',
        },
        {
            content: 'Prevenar 13 hoặc Synflorix',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '3 mũi',
            colspan: 3,
            background: 'bg-[#81c784]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 2,
            background: 'bg-[#81c784]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Cúm (Influenza)',
            colspan: 1,
        },
        {
            content: 'Influvac hoặc Vaxigrip',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: 'Từ 6 tháng và nhắc lại hàng năm',
            colspan: 14,
            background: 'bg-[#66bb6a]',
        },
    ],
    [
        {
            content: 'Sởi (Measles)',
            colspan: 1,
        },
        {
            content: 'MVVAC',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 2,
            background: 'bg-[#4caf50]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 2,
            background: 'bg-[#4caf50]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Sởi, Quai bị, Rubella (Measles Mumps Rubella)',
            colspan: 1,
        },
        {
            content: 'M-M-R II',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 2,
            background: 'bg-[#00c853]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 1,
            background: 'bg-[#00c853]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Thủy đậu (Varicella)',
            colspan: 1,
        },
        {
            content: 'Varivax',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 1,
            background: 'bg-[#c8e6c9]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '1 mũi',
            colspan: 1,
            background: 'bg-[#c8e6c9]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Viêm gan A (Hepatitis A)',
            colspan: 1,
        },
        {
            content: 'Havrix hoặc Vaqta',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: 'mũi 1: 1-15 tuổi \n mũi 2 cách mũi 1 từ 6-18 tháng',
            colspan: 10,
            background: 'bg-[#a5d6a7]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Viêm màng não do não mô cầu (Meningococcal)',
            colspan: 1,
        },
        {
            content: 'MenACWY',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content:
                'mũi 1: 9-24 tháng \n mũi 2 cách mũi 1 ít nhất 3 tháng \n (hoặc 1 mũi từ 24 tháng-55 tuổi)',
            colspan: 5,
            background: 'bg-[#81c784]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'Viêm não Nhật Bản (Japanese encephalitis)',
            colspan: 1,
        },
        {
            content: 'Imojev',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content:
                'mũi 1: 9-12 tháng \n mũi 2: 12 - 24 tháng \n mũi 2 cách mũi 1 ít nhất 1 tháng',
            colspan: 5,
            background: 'bg-[#66bb6a]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
    [
        {
            content: 'HPV (HPV)',
            colspan: 1,
        },
        {
            content: 'Gardasil 9 hoặc Cervarix',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content:
                'mũi 1: 9-14 tuổi mũi 2: cách mũi 1 từ 5-13 tháng (hoặc 15 - 26 tuổi: mũi 2 cách mũi 1 từ 1-2 tháng, mũi 3 cách mũi 1 từ 5-13 tháng)',
            colspan: 3,
            background: 'bg-[#4caf50]',
        },
        {
            content: '',
            colspan: 1,
        },
        {
            content: '',
            colspan: 1,
        },
    ],
]

const validateRows = () => {
    rows.forEach((row, index) => {
        const totalColspan = row.reduce((sum, cell) => sum + cell.colspan, 0)

        if (totalColspan !== headers.length) {
            throw new Error(
                `Row ${index} has incorrect colspan total. Total: ${totalColspan}, Row content: ${JSON.stringify(row)}`,
            )
        }
    })
    return true
}

onMounted(() => {
    try {
        validateRows()
    } catch (error) {
        console.error(error)
    }
})
</script>

<template>
    <div class="overflow-x-auto">
        <table class="w-full border-collapse">
            <!-- Header -->
            <thead>
                <tr>
                    <th
                        v-for="(header, index) in headers"
                        :key="`th-${index}`"
                        v-dompurify-html="header"
                    />
                </tr>
            </thead>

            <!-- Body -->
            <tbody>
                <tr v-for="(row, i) of rows" :key="`tr-${i}`">
                    <td
                        v-for="(cell, j) in row"
                        :key="`td-${j}`"
                        :class="cell.background"
                        :colspan="cell.colspan"
                    >
                        <template v-if="cell.describe">
                            <span>{{ cell.title }}</span>
                            <Button
                                v-tooltip.focus="cell.describe"
                                icon="pi pi-info-circle"
                                text
                                rounded
                                aria-label="More detail"
                                class="mx-1 size-4"
                            />
                            <span>
                                {{ cell.subtitle }}
                            </span>
                        </template>
                        <template v-else-if="cell.content">
                            {{ cell.content }}
                        </template>
                    </td>
                </tr>
            </tbody>

            <!-- Footer -->
            <thead>
                <tr>
                    <th
                        v-for="(header, index) in headers"
                        :key="`th-${index}`"
                        v-dompurify-html="header"
                    />
                </tr>
            </thead>
        </table>
    </div>
</template>

<style scoped>
table th,
table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: center;
    min-width: 50px;
    width: max-content;
}

table th {
    background-color: #f4f4f4;
}

table thead th:first-child,
tbody td:first-child {
    position: sticky;
    left: 0;
    background-color: #f4f4f4;
    z-index: 1;
    border: 1px solid #ddd;
}

@media screen and (max-width: 768px) {
    table th,
    table td {
        font-size: 12px;
        padding: 6px;
    }
}
</style>
