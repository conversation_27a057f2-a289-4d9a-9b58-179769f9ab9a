<template>
    <div class="grid grid-cols-2 gap-3 md:grid-cols-3 md:gap-6">
        <div
            v-for="stat in metrics"
            :key="stat.name"
            class="flex flex-col bg-slate-50 dark:bg-slate-900 rounded-xl p-4 md:px-6 md:py-8 col-end-auto  sm:col-span-1"
        >
            <div
                class="mt-1 flex h-10 w-10 shrink-0 items-center justify-center rounded-lg border border-primary bg-primary/10 p-2 transition-colors"
            >
                <iconify-icon
                    :icon="stat.icon"
                    :class="['text-xl text-primary']"
                />
            </div>
            <p class="mt-3 text-xl">
                <b>{{ stat.label }}</b> {{ t(stat.unit) }}
            </p>
            <p class="text-surface-600 dark:text-surface-400">
                {{ t(stat.name) }}
            </p>
        </div>
    </div>
</template>

<script setup lang="ts">
import type { IDoctorMetric } from '~/models'

const { t } = useI18n({ useScope: 'local' })

defineProps<{
    metrics: IDoctorMetric[]
}>()
</script>

<i18n lang="yaml">
en:
    mins: minutes
    years: 'years'
    average-length: Average consultation duration
    satisfaction: Patient satisfaction
    experience: Specialty experience
vi:
    mins: phút
    years: năm
    average-length: Thời gian tư vấn trung bình
    satisfaction: Bệnh nhân hài lòng
    experience: kinh nghiệm
</i18n>
