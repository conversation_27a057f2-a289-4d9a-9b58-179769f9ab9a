<template>
    <div class="grid grid-cols-12 gap-3 overflow-hidden">
        <div class="col-span-3 flex flex-col">
            <div
                v-for="settingGroup in allSettings"
                :key="settingGroup.name"
                class="flex flex-col"
            >
                <h6 class="mb-0 mt-2 font-bold text-surface-400">
                    {{ settingGroup.name }}
                </h6>
                <NuxtLink
                    v-for="setting in settingGroup.settings"
                    :key="setting.name"
                    :to="setting.to ? setting.to : ''"
                    class="cursor-pointer rounded-md px-2 py-1 transition hover:bg-surface-100 dark:hover:bg-surface-900"
                    target="_blank"
                    @click="setting.component ? openSetting(setting) : () => {}"
                >
                    {{ setting.name }}
                </NuxtLink>
            </div>
        </div>
        <div class="relative col-span-9 h-full overflow-y-auto">
            <Transition
                enter-active-class="transition"
                leave-active-class="transition"
                enter-from-class="opacity-0"
                leave-to-class="opacity-0"
            >
                <component
                    :is="currentSetting.component"
                    v-if="currentSetting"
                    class="absolute left-0 right-0 top-0"
                />
            </Transition>
        </div>
    </div>
</template>

<script setup lang="ts">
import { type Ref, ref } from '#imports'
import PageSettingAccount from '~/components/Page/Setting/Account.vue'
import PageSettingFAQ from '~/components/Page/Setting/FAQ.vue'
import PageSettingLanguage from '~/components/Page/Setting/Language.vue'
import PageSettingPrivacyPolicy from '~/components/Page/Setting/PrivacyPolicy.vue'
import PageSettingTermsAndConditions from '~/components/Page/Setting/TermsAndConditions.vue'
import PageSettingWallet from '~/components/Page/Setting/Wallet.vue'
import type { ISetting } from '~/models/component/setting'

const allSettings: { name: string; settings: ISetting[] }[] = [
    {
        name: 'General',
        settings: [
            {
                name: 'Account',
                component: PageSettingAccount,
            },
            {
                name: 'Wallet',
                component: PageSettingWallet,
            },
            {
                name: 'Language',
                component: PageSettingLanguage,
            },
            {
                name: 'App rating',
                to: 'https://play.google.com/store/apps/details?id=vn.wellcare',
            },
        ],
    },
    {
        name: 'Support',
        settings: [
            {
                name: 'FAQ',
                component: PageSettingFAQ,
            },
            {
                name: 'Terms and conditions',
                component: PageSettingTermsAndConditions,
            },
            {
                name: 'Privacy policy',
                component: PageSettingPrivacyPolicy,
            },
            {
                name: 'Debug',
                to: '/debug',
            },
        ],
    },
]

const openSetting = (setting: ISetting) => {
    currentSetting.value = setting
}

const currentSetting: Ref<ISetting | null> = ref(null)
</script>
