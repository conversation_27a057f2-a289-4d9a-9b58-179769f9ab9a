<script setup lang="ts">
import { Capacitor } from '@capacitor/core'

const router = useRouter()

const layoutStore = useLayoutStore()
const { title, subTitle, target } = storeToRefs(layoutStore)

const isNative = computed(() => Capacitor.isNativePlatform())

const titleSize = computed<string>(() => {
    const titleValue = title?.value ?? ''
    // const wordCount = titleValue.split(' ').length
    const wordCount = titleValue.length

    if (wordCount >= 35) return 'text-xs truncate'
    else if (wordCount >= 28) return 'text-sm truncate'

    return 'text-lg'
})

const onBack = () => {
    if (target?.value) {
        router.push(target.value)
    } else {
        router.back()
    }
}
</script>

<template>
    <div id="nested-page" class="flex h-full flex-col">
        <!-- Top bar -->
        <header
            :class="[
                'relative flex items-center justify-center p-4 shadow-[0_8px_24px_rgba(149,157,165,0.2)]',
                {
                    'pt-safe': isNative,
                },
            ]"
        >
            <Button
                plain
                text
                icon="pi pi-angle-left"
                class="!absolute left-2"
                aria-label="Return"
                @click="onBack"
            />
            <div
                class="flex flex-col items-center text-center transition-all"
                style="width: calc(100% - 60px)"
            >
                <p v-if="title" :class="['font-bold', titleSize]">
                    {{ title }}
                </p>
                <Skeleton v-else width="80%" height="18px" />
                <p class="text-xs">{{ subTitle }}</p>
            </div>
            <div class="absolute right-4">
                <slot name="right" />
            </div>
        </header>

        <!-- Main content -->
        <main class="flex-1 overflow-y-scroll">
            <slot />
        </main>
    </div>
</template>
