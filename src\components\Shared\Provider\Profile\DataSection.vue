<script setup lang="ts">
import { useDisplay, useI18n } from '#imports'
import type { IDataSection, IProviderMetric } from '~/models'

defineProps<IDataSection>()

const { t } = useI18n({
    useScope: 'local',
})

const { display } = useDisplay()
const isMobile = computed(() => display?.breakpoint?.isMobile)

const expanded = ref(false)
const shouldShowExpandButton = ref(false)
const chipContainer = ref<HTMLElement | null>(null)

const toggleExpand = async (title: string) => {
    // Toggle expanded state
    expanded.value = !expanded.value

    const toggleButton = document.getElementById(`toggleButton-${title}`)

    // Scroll into view if collapsing
    if (!expanded.value && toggleButton) {
        await nextTick() // Ensure DOM is updated

        toggleButton.scrollIntoView({
            behavior: 'smooth', // Smooth scrolling
            block: 'center', // Scroll so that the button is centered vertically
        })
    }
}

onMounted(async () => {
    await nextTick()

    // Calculate the total width of the Chips and compare it with the width of the container.
    if (chipContainer.value) {
        const chips = chipContainer.value.querySelectorAll('.chip')
        let totalWidth = 0
        let rowCount = 1
        const containerWidth = chipContainer.value.offsetWidth

        chips.forEach((chip) => {
            const chipWidth = (chip as HTMLElement).offsetWidth
            totalWidth += chipWidth

            // When the total width exceeds the container, increase the number of items and reset the total width.
            if (totalWidth > containerWidth) {
                rowCount++
                totalWidth = chipWidth
            }
        })

        // Only display the "See more" button if there are more than 2 rows.
        shouldShowExpandButton.value = rowCount > 2
    }
})

const hasContent = computed(() => {
    if (!content) return false

    switch (contentType) {
        case 'Text':
            return typeof content === 'string' && content.trim().length > 0
        case 'Chips':
            return Array.isArray(content) && content.length > 0
        case 'Metrics':
            return Array.isArray(content) && content.length > 0
        default:
            return false
    }
})
</script>

<template>
    <section 
        v-if="hasContent" 
        class="app-padding-x mt-4 sm:mx-auto sm:w-full sm:max-w-[80rem]"
    >
        <h3 class="mb-1">{{ title }}</h3>

        <template v-if="contentType === 'Text'">
            <p class="rounded-md bg-primary/30 px-4 py-2 dark:bg-primary/70">
                {{ content }}
            </p>
        </template>

        <div v-else-if="contentType === 'Chips'" class="relative">
            <div
                ref="chipContainer"
                :class="[
                    'flex flex-row flex-wrap gap-2',
                    isMobile && !expanded
                        ? 'max-h-[88px] overflow-hidden'
                        : 'max-h-auto',
                ]"
            >
                <Chip
                    v-for="item in content"
                    :key="item?.key"
                    :label="item?.name"
                    class="chip"
                />
            </div>

            <Button
                v-if="isMobile && shouldShowExpandButton"
                :id="`toggleButton-${title}`"
                text
                @click="toggleExpand(title)"
            >
                {{ expanded ? t('btn:show-less') : t('btn:show-more') }}
            </Button>
        </div>

        <div v-else-if="contentType === 'Metrics'" class="grid gap-4 sm:flex">
            <div
                v-for="(metric, mIndex) in content as IProviderMetric[]"
                :key="metric.label"
                :class="[
                    'col-end-auto flex flex-col rounded-xl bg-zinc-50 p-4 sm:col-span-1 sm:flex-1 md:px-6 md:py-8 dark:bg-zinc-900',
                    mIndex == 2 ? 'col-span-2' : '',
                ]"
            >
                <iconify-icon
                    :icon="metric.icon"
                    class="mb-1 w-fit rounded-md border border-primary bg-primary/10 p-2 text-2xl text-primary"
                />
                <b class="text-lg sm:text-xl">
                    {{ metric.label + ' ' + t(metric.unit) }}
                </b>
                <h6>{{ t(metric.unit + '-sub') }}</h6>
            </div>
        </div>
    </section>
</template>

<i18n lang="yaml">
en:
    '%-sub': 'Satisfaction Level'
    '%': '%'
    'btn:show-less': 'Show less'
    'btn:show-more': 'See more'
    'mins-sub': 'Avg. Consultation Length'
    'mins': 'Minutes'
    'years-sub': 'Years in Practice'
    'years': 'Years'
vi:
    '%-sub': 'Bệnh nhân hài lòng'
    '%': '%'
    'btn:show-less': 'Thu gọn'
    'btn:show-more': 'Xem thêm'
    'mins-sub': 'Thời gian tư vấn trung bình'
    'mins': 'Phút'
    'years-sub': 'Kinh nghiệm'
    'years': 'Năm'
</i18n>
