import dayjs from 'dayjs'
import 'dayjs/locale/vi'
import relativeTime from 'dayjs/plugin/relativeTime'
import { useI18n } from 'vue-i18n' // Assuming you're using vue-i18n for internationalization

export function useDateDiffWithLocale(dateString: string): string {
  const { locale } = useI18n()

  // Extend dayjs with the relativeTime plugin
  dayjs.extend(relativeTime)

  // Set the locale
  dayjs.locale(locale.value)

  const date = dayjs(dateString)

  // Use fromNow() to get the relative time difference
  return date.fromNow()
}
