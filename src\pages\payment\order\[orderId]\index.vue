<script lang="ts" setup>
import { useRouter, definePageMeta, ref } from '#imports'
import { EffectCards, Pagination } from 'swiper/modules'
const checked = ref(false)
const router = useRouter()
definePageMeta({
    layout: 'payment',
})

const modules = [EffectCards, Pagination]
const onSwiper = (swiper) => {
    console.log(swiper)
}
</script>
<template>
    <section class="bg-surface-100 py-4 sm:py-8 lg:py-12">
        <div class="mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-center">
                <!-- <h1 class="text-2xl font-semibold text-surface-900"></h1> -->
            </div>

            <div class="mx-auto mt-4 max-w-2xl md:mt-8">
                <div class="rounded-xl bg-white shadow">
                    <div class="px-4 py-6 sm:px-8 sm:py-10">
                        <div class="flow-root">
                            <ul class="-my-8">
                                <li class="flex flex-col">
                                    <div
                                        class="flex flex-row space-x-2 py-6 pb-4 text-left sm:space-x-5"
                                    >
                                        <div class="shrink-0">
                                            <img
                                                class="h-16 w-16 max-w-full rounded-lg object-cover"
                                                src="https://cdn.wellcare.vn/imaginary/resize?width=300&url=https://storage.googleapis.com/wellcare-user-profile/614a8075e265b92202c48e37/885a82a7-8c4b-52f8-b8bb-8928906fd83e.png"
                                                alt=""
                                            />
                                        </div>
                                        <div
                                            class="relative flex flex-1 flex-col justify-between"
                                        >
                                            <div
                                                class="sm:col-gap-5 sm:grid sm:grid-cols-2"
                                            >
                                                <div class="pr-8 sm:pr-5">
                                                    <p
                                                        class="my-0 text-base font-semibold text-surface-900"
                                                    >
                                                        Nguyễn Trí Đoàn
                                                    </p>
                                                    <p
                                                        class="mx-0 mb-0 mt-1 text-sm text-surface-400"
                                                    >
                                                        20:00 16-07-2024
                                                    </p>
                                                    <p
                                                        class="mx-0 mb-0 mt-1 text-sm text-surface-400"
                                                    >
                                                        Phone - 15 minutes
                                                    </p>
                                                </div>
                                            </div>
                                            <div
                                                class="absolute right-0 top-0 flex sm:bottom-0 sm:top-auto"
                                            >
                                                <button
                                                    type="button"
                                                    class="flex rounded p-2 text-center text-surface-500 transition-all duration-200 ease-in-out hover:text-surface-900 focus:shadow"
                                                >
                                                    <svg
                                                        class="h-5 w-5"
                                                        xmlns="http://www.w3.org/2000/svg"
                                                        fill="none"
                                                        viewBox="0 0 24 24"
                                                        stroke="currentColor"
                                                    >
                                                        <path
                                                            stroke-linecap="round"
                                                            stroke-linejoin="round"
                                                            stroke-width="2"
                                                            d="M6 18L18 6M6 6l12 12"
                                                            class=""
                                                        />
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                    <p
                                        class="mx-0 mt-1 text-sm text-surface-400"
                                    >
                                        Lorem ipsum, dolor sit amet consectetur
                                        adipisicing elit. Expedita voluptatem
                                        voluptates nulla ipsa eum voluptatibus.
                                        Explicabo, laboriosam veritatis? Odit
                                        eius nobis, animi deleniti blanditiis
                                        quas voluptates quis optio? Ullam, vel.
                                    </p>
                                    <div
                                        class="mt-4 flex items-center justify-between sm:mt-0 sm:items-start sm:justify-end"
                                    >
                                        <div class="sm:order-1">
                                            <div
                                                class="mx-auto flex h-8 items-stretch text-surface-600"
                                            >
                                                <button
                                                    class="flex items-center justify-center rounded-l-md bg-surface-200 px-4 transition hover:bg-primary hover:text-white"
                                                >
                                                    -
                                                </button>
                                                <div
                                                    class="flex w-full items-center justify-center bg-surface-100 px-4 text-xs uppercase transition"
                                                >
                                                    1
                                                </div>
                                                <button
                                                    class="flex items-center justify-center rounded-r-md bg-surface-200 px-4 transition hover:bg-primary hover:text-white"
                                                >
                                                    +
                                                </button>
                                            </div>
                                        </div>
                                        <p
                                            class="w-20 shrink-0 text-right text-base font-semibold text-surface-900 sm:order-2 sm:ml-8 sm:text-right"
                                        >
                                            $399.00
                                        </p>
                                    </div>
                                </li>
                            </ul>
                        </div>

                        <div class="mt-10 border-b border-t py-2">
                            <div class="flex items-center justify-between">
                                <p class="text-sm text-surface-400">Subtotal</p>
                                <p
                                    class="text-lg font-semibold text-surface-900"
                                >
                                    $399.00
                                </p>
                            </div>
                            <div class="flex items-center justify-between">
                                <p class="text-sm text-surface-400">
                                    Service fee
                                </p>
                                <p
                                    class="text-lg font-semibold text-surface-900"
                                >
                                    $50.00
                                </p>
                            </div>
                        </div>
                        <div class="mt-8 flex items-center justify-between">
                            <p class="text-sm font-medium text-surface-900">
                                Total
                            </p>
                            <p class="text-2xl font-semibold text-surface-900">
                                <span
                                    class="text-xs font-normal text-surface-400"
                                    >USD</span
                                >
                                408.00
                            </p>
                        </div>

                        <div class="mt-8 text-center">
                            <Button class="w-full max-w-xl">
                                Checkout
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    class="ml-4 h-6 w-6 transition-all group-hover:ml-8"
                                    fill="none"
                                    viewBox="0 0 24 24"
                                    stroke="currentColor"
                                    stroke-width="2"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        d="M13 7l5 5m0 0l-5 5m5-5H6"
                                    />
                                </svg>
                            </Button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-4">
            <div class="mx-auto mt-2 rounded-lg bg-white p-4 shadow-sm">
                <div class="text-xl font-bold">Other</div>
                <div>
                    <div class="mt-2 flex items-start">
                        <Checkbox v-model="checked" :binary="true" />

                        <label
                            for="input-103"
                            class="ml-2 font-bold text-surface-800"
                            >Do you have a voucher or access code?</label
                        >
                    </div>
                    <div class="mx-auto my-6 flex items-center">
                        <div class="relative w-full">
                            <input
                                class="p-component focus:shadow-outline border-1 m-0 w-full appearance-none rounded-md border border-surface-200 bg-surface-0 p-2 px-4 py-3 font-sans leading-none text-surface-600 transition-colors duration-200 placeholder:text-surface-400 hover:bg-surface-50 focus:z-10 focus:border-primary focus:outline-none focus:outline-offset-0 focus:ring focus:ring-primary/50 dark:border-surface-600 dark:bg-surface-900 dark:text-surface-200 dark:placeholder:text-surface-500 dark:hover:bg-surface-800 dark:focus:ring-primary/50"
                                type="text"
                                placeholder="Enter gift code"
                                data-pc-name="inputtext"
                                data-pc-section="root"
                                value=""
                            />
                            <button
                                class="absolute right-0 top-0 mr-1 mt-1 cursor-not-allowed rounded-lg bg-surface-300 px-3 py-2 text-surface-500"
                                disabled
                            >
                                Apply
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="p-4">
            <div class="card mx-auto rounded-lg bg-white p-6 shadow-md">
                <div class="mb-4 text-xl font-bold">Topup method</div>
                <ul class="m-0 list-none p-0">
                    <li
                        class="flex cursor-pointer items-center border-b border-gray-200 p-4"
                    >
                        <img
                            src="https://khamtuxa.wellcare.vn/checkout/payment/methods/bank-transfer.svg"
                            class="mr-4 h-10 w-10 object-cover"
                            alt="Bank Transfer"
                        />
                        <div class="flex-1">
                            <div class="text-lg font-bold">Bank transfer</div>
                            <div class="mt-1 text-sm text-surface-600">
                                Transfer from a bank
                            </div>
                        </div>
                        <i class="pi pi-chevron-right text-surface-500" />
                    </li>
                    <li
                        class="flex cursor-pointer items-center border-b border-gray-200 p-4"
                    >
                        <img
                            src="https://khamtuxa.wellcare.vn/checkout/payment/methods/credit-card.svg"
                            class="mr-4 h-10 w-10 object-cover"
                            alt="Bank Card"
                        />
                        <div class="flex-1">
                            <div class="text-lg font-bold">Bank card</div>
                            <div class="mt-1 text-sm text-surface-600">
                                Credit card or Debit card
                            </div>
                        </div>
                        <i class="pi pi-chevron-right text-surface-500" />
                    </li>
                    <li
                        class="flex cursor-pointer items-center border-b border-gray-200 p-4"
                    >
                        <img
                            src="https://khamtuxa.wellcare.vn/checkout/payment/methods/momo.svg"
                            class="mr-4 h-10 w-10 object-cover"
                            alt="Momo"
                        />
                        <div class="flex-1">
                            <div class="text-lg font-bold">Momo</div>
                            <div class="mt-1 text-sm text-surface-600">
                                Through momo e-wallet
                            </div>
                        </div>
                        <i class="pi pi-chevron-right text-surface-500" />
                    </li>
                    <li
                        class="flex cursor-pointer items-center border-b border-gray-200 p-4"
                    >
                        <img
                            src="https://khamtuxa.wellcare.vn/checkout/payment/methods/zalo-pay-2.png"
                            class="mr-4 h-10 w-10 object-cover"
                            alt="ZaloPay"
                        />
                        <div class="flex-1">
                            <div class="text-lg font-bold">ZaloPay</div>
                            <div class="mt-1 text-sm text-surface-600">
                                Through zalopay e-wallet
                            </div>
                        </div>
                        <i class="pi pi-chevron-right text-surface-500" />
                    </li>
                </ul>
            </div>
        </div>

        <div class="mx-auto my-8 mb-20 w-full p-4">
            <div class="mb-4 text-xl font-bold">Choose card</div>
            <swiper
                class="max-w-[340px]"
                :grab-cursor="true"
                :modules="modules"
                :effect="'cards'"
                :pagination="{
                    dynamicBullets: true,
                }"
                @swiper="onSwiper"
            >
                <swiper-slide class="relative">
                    <img
                        src="/images/wellcare-service-card.png"
                        class="h-full w-full object-cover"
                    />
                    <!-- Thông tin trên ảnh -->
                    <div
                        class="absolute inset-0 flex flex-col items-center justify-center text-white"
                    >
                        <div class="text-lg font-bold">Long Vo</div>
                        <!-- Tên user -->
                        <div class="text-lg">Balance: $1,200</div>
                        <!-- Số tiền -->
                    </div>
                </swiper-slide>
                <swiper-slide class="relative">
                    <img
                        src="/images/wellcare-member-card.png"
                        class="h-full w-full object-cover"
                    />
                    <div
                        class="absolute inset-0 flex flex-col items-center justify-center text-white"
                    >
                        <div class="text-lg font-bold">Long Vo</div>
                        <div class="text-lg">Balance: $1,200</div>
                    </div>
                </swiper-slide>
            </swiper>
        </div>

        <div class="fixed bottom-0 left-0 right-0 z-50 bg-white p-4 shadow-md">
            <Button
                class="h-12 w-full"
                @click="router.push('/payment/success')"
            >
                Payment
            </Button>
        </div>
    </section>
</template>
<style scoped>
.swiper {
    width: 100%;
    height: 210px;
}

.swiper-slide {
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 18px;
    font-size: 22px;
    font-weight: bold;
    color: #fff;
}

.swiper-pagination-bullet-active {
    background: #009688 !important;
}
.swiper-horizontal > .swiper-pagination-bullets-dynamic {
    bottom: -20px;
}
</style>
