import type { HitNotionEduHub, ITopicCard } from '~/models'
import { ElasticIndex } from '~/models'
import { transform } from '../../../../../utils'
import type { ComputedRef, Ref } from '#imports'
import { useFetchElasticWithDefault } from '#imports'

export function usePagePatientEduHubMediaCategorySlug(
    from: Ref<number> | ComputedRef<number>,
    size: Ref<number> | ComputedRef<number>,
) {
    const { params }: any = useRoute()

    const totalAudio = ref<number>(0)
    const totalVideo = ref<number>(0)

    const { hit: category, loading: loadingCategory } =
        useFetchElastic<HitNotionEduHub>(ElasticIndex.NOTION_EDU_HUB, {
            size: 1,
            filters: [
                {
                    term: {
                        'page.properties.Slug.keyword': params.category,
                    },
                },
            ],
            _source: {
                includes: ['blockstring', 'page.properties.Name'],
            },
        })

    const { hits: topicHits, total: totalTopics } =
        useFetchElasticWithDefault<HitNotionEduHub>(
            ElasticIndex.NOTION_EDU_HUB,
            {
                from,
                size,
                filters: [
                    {
                        term: {
                            'page.properties.Parent item.properties.Slug.keyword':
                                params.category,
                        },
                    },
                    { term: { 'page.properties.Type.keyword': 'Topic' } },
                ],
                sort: [{ 'page.properties.Slug.keyword': 'asc' }],
                _source: {
                    includes: [
                        'page.cover.url',
                        'page.properties.Name',
                        'page.properties.Slug',
                        'page.properties.Description',
                    ],
                },
            },
        )

    const topics: ComputedRef<ITopicCard[]> = computed(() => {
        return topicHits.value.map((hit: HitNotionEduHub) => {
            return {
                _id: hit?._id || '',
                slug: hit?.page?.properties.Slug || '',
                name: hit?.page?.properties.Name || '',
                description: hit?.page?.properties.Description || '',
            }
        })
    })

    const { hits: topicIds, loading: loadingTopicIds } =
        useFetchElasticWithDefault<HitNotionEduHub>(
            ElasticIndex.NOTION_EDU_HUB,
            {
                size: 1000,
                filters: [
                    {
                        term: {
                            'page.properties.Parent item.properties.Slug.keyword':
                                params.category,
                        },
                    },
                    { term: { 'page.properties.Type.keyword': 'Topic' } },
                ],
                _source: {
                    includes: ['_id'],
                },
            },
        )

    const { aggregations, loading: loadingAggregations } =
        useFetchElasticWithDefault(
            ElasticIndex.NOTION_EDU_HUB,
            {
                size: 0,
                aggs: {
                    ids: {
                        terms: {
                            field: 'page.properties.Parent item.id.keyword',
                            size: 1000,
                        },
                        aggs: {
                            deliveries: {
                                terms: {
                                    field: 'page.properties.Delivery.keyword',
                                },
                            },
                        },
                    },
                },
            },
            {
                transform: transform.aggregations
            },
        )

    const countDelivery = (id: string, deliveryKey: string) => {
        const bucket = (aggregations.value as any)?.ids?.buckets?.find(
            (bucket: any) => bucket?.key === id,
        )

        if (bucket) {
            const delivery = bucket?.deliveries?.buckets?.find(
                (delivery: any) => delivery?.key === deliveryKey,
            )

            return parseInt(delivery?.doc_count || '0')
        }

        return 0
    }

    const countAudio = (id: string) => countDelivery(id, 'Podcasts')
    const countVideo = (id: string) => {
        const relevantVideos = [
            'Short Videos',
            'Offline Events',
            'Live Streams',
        ]
        return relevantVideos.reduce(
            (total, key) => total + countDelivery(id, key),
            0,
        )
    }

    watch(
        () => [aggregations.value, topicIds.value],
        () => {
            const ids = topicIds.value.map((topic: any) => topic?._id)

            totalAudio.value = 0
            totalVideo.value = 0
            ;(aggregations.value as any)?.ids?.buckets.forEach(
                (bucket: any) => {
                    if (ids.includes(bucket?.key)) {
                        totalAudio.value += countAudio(bucket.key)
                        totalVideo.value += countVideo(bucket.key)
                    }
                },
            )
        },
        {
            deep: true,
        },
    )

    const loading = computed(
        () =>
            loadingCategory.value ||
            loadingTopicIds.value ||
            loadingAggregations.value,
    )

    return {
        category,
        loading,
        totalTopics,
        topics,
        totalAudio,
        totalVideo,
        countAudio,
        countVideo,
    }
}
