<script setup lang="ts">
import { computed, definePageMeta, ref } from '#imports'
import { appRoutes } from '~/constants'
import { ElasticIndex } from '~/models'
import type { IRating } from '~/models'

definePageMeta({ layout: 'provider', middleware: 'auth-capacitor' })

const { t } = useI18n({ useScope: 'local' })
const { user } = useUserInfo({
    scope: ['_id', 'name', 'phone', 'isProvider', 'provider', 'avatar'],
})

const openAccount = () => {
    navigateTo(appRoutes.provider.settings.profile)
}
const providerSlug = computed<string>(() => user.value?.provider?.slug)

const { loading, profile } = usePageProviderHomeProfile(providerSlug)

const providerAvatar = computed<string>(
    () =>
        user.value?.provider?.avatar?.url ||
        user.value?.avatar?.url ||
        `https://ui-avatars.com/api/?name=${user.value?.provider?.name}`,
)

const ratingFilters = computed(() =>
    profile.value?.name
        ? [
              { term: { 'page.properties.IsFeature': true } },
              {
                  term: {
                      'page.properties.Provider.keyword': profile.value?.name,
                  },
              },
              { term: { 'page.properties.Sentiment.keyword': 'positive' } },
          ]
        : [],
)

const isShowRatingSection = ref<boolean>(true)
const onRatingFetched = (ratings: IRating[]) => {
    isShowRatingSection.value = Boolean(ratings.length)
}
</script>

<template>
    <div>
        <div
            v-if="!loading"
            class="mx-auto grid max-w-6xl grid-flow-row grid-cols-1 gap-4"
        >
            <SharedDynamicHeader class="col-span-full">
                <template #detailed>
                    <div class="flex flex-row justify-between px-4 pt-6">
                        <div class="flex flex-row gap-3">
                            <Avatar
                                :image="providerAvatar"
                                shape="circle"
                                class="size-12 shrink-0 sm:!hidden"
                                @click="openAccount"
                            />
                            <div>
                                <p>{{ t('Hello') }},</p>
                                <h4>
                                    {{ user.provider?.title }}
                                    {{
                                        user.provider?.name ||
                                        user?.name ||
                                        'guest'
                                    }}
                                </h4>
                            </div>
                        </div>
                        <!-- <Button
                            icon="pi pi-bell"
                            severity="secondary"
                            rounded
                            class="!h-10 !w-10"
                        /> -->
                    </div>
                </template>
                <template #header>
                    <div
                        class="flex flex-row items-center gap-4"
                        @click="openAccount"
                    >
                        <div class="flex flex-col items-center">
                            <Avatar
                                :image="providerAvatar"
                                shape="circle"
                                size="large"
                                class="shrink-0 bg-zinc-100 sm:!hidden dark:bg-zinc-700"
                            />
                        </div>

                        <div class="flex flex-col">
                            <h4
                                class="line-clamp-1 overflow-hidden overflow-ellipsis whitespace-normal break-words break-all"
                            >
                                {{ user.provider?.title }}
                                {{
                                    user.provider?.name || user?.name || 'guest'
                                }}
                            </h4>
                        </div>
                    </div>
                </template>
            </SharedDynamicHeader>

            <!-- <SharedPageSection class="col-span-full" title="Quick Actions">
                <SharedNewQuickAccess
                    :quick-actions="quickActions"
                    class="px-6"
                />
            </SharedPageSection>
            <PageProviderDashboardUpcoming /> -->

            <SharedPageSection
                v-if="!user.phone"
                :pt="{
                    content: 'mx-4 mb-4',
                }"
            >
                <SharedAuthLinkPhoneCard />
            </SharedPageSection>

            <PageProviderDashboardUpcoming
                v-if="user._id"
                :user-id="user._id"
                :phone="user.phone"
                :is-provider="user.isProvider"
            />

            <PageProviderDashboardAvailableSlots />

            <SharedPageSection
                v-if="profile.specialties"
                :title="t('my-colleagues-specialties')"
                view-all-to="/provider/home/<USER>"
                :pt="{ content: 'mx-4' }"
            >
                <PageProviderDashboardSpecialtiesList
                    :specialties="profile.specialties"
                />
            </SharedPageSection>

            <SharedPageSection
                :title="t('my-profile')"
                :pt="{ content: 'mx-4' }"
            >
                <PageProviderDashboardMyProfile :profile="profile" />
            </SharedPageSection>

            <SharedPageSection
                v-if="isShowRatingSection"
                :title="t('feedback')"
                :pt="{ content: 'mx-4 max-w-full overflow-hidden sm:m-0' }"
            >
                <WTestimonial
                    v-if="ratingFilters?.length > 0"
                    :elastic-index="ElasticIndex.NOTION_RATINGS"
                    :filters="ratingFilters"
                    @on-fetched="onRatingFetched"
                />
            </SharedPageSection>

            <PageProviderHomeInviteActions
                class="mt-6"
                :provider-slug="profile.slug"
            />
        </div>
    </div>
</template>

<i18n lang="yaml">
en:
    my-colleagues-specialties: "My colleagues' specialties"
    my-profile: 'My profile'
    feedback: 'Feedback'
vi:
    my-colleagues-specialties: 'Đồng nghiệp của tôi'
    my-profile: 'Hồ sơ của tôi'
    feedback: 'Nhận xét'
</i18n>
