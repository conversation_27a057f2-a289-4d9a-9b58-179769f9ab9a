import { ref, watch } from '#imports'
import type { Ref, ComputedRef } from '#imports'

type Watchable =
    | Ref<any>
    | ComputedRef<any>
    | (() => Ref<any> | ComputedRef<any> | any)
    | Array<
          | Ref<any>
          | ComputedRef<any>
          | (() => Ref<any> | ComputedRef<any> | any)
      >
    | null

interface IWatchSrc {
    source: Watchable
    immediate?: boolean
    deep?: boolean
    onChanged?: (_newValue: any, _prevValue: any) => void
    once?: boolean
}

interface IRerenderReturn {
    key: Ref<number>
    refresh: () => void
    rerenderSrc: (_options: IWatchSrc) => void
}

interface IRerenderOptions {
    debug?: boolean
}

export const useRerender: ({ debug }?: IRerenderOptions) => IRerenderReturn = (
    options: IRerenderOptions = { debug: false },
) => {
    const key = ref<number>(0)
    let stopWatch: (() => void) | null = null

    const refresh = () => {
        key.value = Date.now()

        if (options.debug) {
            console.info('[Refresh-key]: ', key.value)
        }
    }

    const applyWatch = ({
        source,
        immediate,
        deep,
        onChanged,
        once,
    }: IWatchSrc) => {
        if (stopWatch) {
            stopWatch()
        }

        if (!source) {
            return
        }

        const watchCallback = (newValue: any, oldValue: any) => {
            refresh()
            if (onChanged) {
                onChanged(newValue, oldValue)
            }
            if (once && stopWatch) {
                stopWatch() // Stop watching after the first change
            }
        }

        stopWatch = watch(source, watchCallback, { immediate, deep })
    }

    const rerenderSrc = ({
        source = null,
        immediate = false,
        deep = false,
        onChanged,
        once = false,
    }: IWatchSrc) => {
        applyWatch({ source, immediate, deep, onChanged, once })
    }

    return {
        key,
        refresh,
        rerenderSrc,
    }
}
