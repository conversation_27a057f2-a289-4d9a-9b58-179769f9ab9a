export interface IHiddenField {
    key: string
    value: any
    component?: string
}

export interface MilestoneQuestionChoice {
    label: string
    value: string
}

export interface MilestoneQuestion {
    id: string
    label: string
    component: string
    choices: MilestoneQuestionChoice[]
    area: DevelopmentArea
}

export enum DevelopmentArea {
    SOCIAL = '<PERSON>iao tiếp xã hội và tình cảm',
    LANGUAGE = 'Ngôn ngữ và Giao tiếp',
    COGNITIVE = '<PERSON>h<PERSON>n thức (<PERSON><PERSON><PERSON> tập tư duy giải quyết vấn đề)',
    PHYSICAL = 'Vận động/Phát triển thể chất',
}

export interface MilestoneForm {
    objectId: string
    questions: MilestoneQuestion[]
    title: string
    pages: any[]
}

export interface MilestoneSummary {
    month: number
    objectId: string
}
