<script setup lang="ts">
import { appRoutes } from '~/constants'
import { push } from 'notivue'
import { useActionToolkit, useI18n } from '#imports'

const props = defineProps({
    providerSlug: {
        type: String,
        required: true,
    },
})

const { t } = useI18n({ useScope: 'local' })
const { copySource } = useActionToolkit()

const profileUrl = appRoutes.external.web.khamtuxa.providerProfile(
    props.providerSlug,
)

const onCopySuccess = () => {
    push.success({
        message: t('toast:success:summary'),
    })
}

const onCopyFailure = () => {
    push.error({
        message: t('toast:error:summary'),
    })
}

const oncopySource = () => {
    copySource(profileUrl, onCopySuccess, onCopyFailure)
}
</script>

<template>
    <div class="app-padding-x mt-6 flex flex-col gap-3">
        <p class="text-lg font-semibold">{{ t('title') }}</p>
        <div class="mt-2 flex items-stretch gap-3">
            <div
                class="flex h-10 w-10 flex-none items-center justify-center rounded-full border-2 border-primary"
            >
                <p class="text-sm">1</p>
            </div>
            <div class="flex flex-col gap-1">
                <p class="font-semibold">{{ t('method:1') }}</p>
                <div class="flex items-center gap-2 flex-wrap">
                    <div
                        class="flex-1 rounded-md bg-zinc-100 px-3 py-1 text-sm text-zinc-600"
                    >
                        {{ profileUrl }}
                    </div>
                    <Button
                        :label="t('copy')"
                        class="flex-none"
                        @click="oncopySource"
                    />
                </div>
            </div>
        </div>

        <div class="mt-4 flex items-stretch gap-3">
            <div
                class="flex h-10 w-10 flex-none items-center justify-center rounded-full border-2 border-primary"
            >
                <p class="text-sm">2</p>
            </div>
            <div class="flex gap-1 flex-wrap">
                <p class="font-semibold">{{ t('method:2') }}</p>
                <NuxtLinkLocale
                    to="https://zalo.me/2727084330920973261"
                    class="flex flex-row-reverse items-center flex-wrap"
                >
                    <Button label="careteam" />
                </NuxtLinkLocale>
            </div>
        </div>
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "title": "Get More Patients: 2 Quick Tips",
        "method:1": "Include your profile link in your posts",
        "method:2": "Share your e-card (Free printing available upon request)",
        "copy": "Copy",
        "toast:success:summary": "Copied!",
        "toast:success:detail": "",
        "toast:error:summary": "Copy not supported",
        "toast:error:detail": ""
    },
    "vi": {
        "title": "2 Cách đơn giản để mở rộng bệnh nhân quen trên Wellcare",
        "method:1": "Đặt link profile dưới mỗi bài viết",
        "method:2": "Gửi danh thiếp điện tử (có thể in miễn phí)",
        "copy": "Sao chép",
        "toast:success:summary": "Đã sao chép!",
        "toast:success:detail": "",
        "toast:error:summary": "Không hỗ trợ sao chép",
        "toast:error:detail": ""
    }
}
</i18n>
