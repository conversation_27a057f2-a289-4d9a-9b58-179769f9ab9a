import type { INotionFileObject } from './notion-file-object'
import type { IRichText } from './rich-text'

export interface INotionFile {
  caption?: IRichText[]
  type: 'external' | 'file'
  file: INotionFileObject // A file object that details information about the file contained in the block.
  name?: string // The name of the file block, as shown in the Notion UI. Note that the UI may auto-append .pdf or other extensions.
}
