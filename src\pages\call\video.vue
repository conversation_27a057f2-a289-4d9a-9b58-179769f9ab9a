<template>
    <NuxtLoadingIndicator />
</template>

<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import {
    CapacitorPluginAgora,
    type IStartCallOption,
} from '@wellcare/capacitor-plugin-agora'

const config = useRuntimeConfig()
const router = useRouter()
const log = useLogs()
const { get, remove } = usePreferences()
const platform = Capacitor.getPlatform()
const { locale } = useI18n()

const goBack = (back?: string) => {
    if (back) router.push(back)
    else router.push('/')
}

const startVideoCall = async (data: any) => {
    const uuid = await get({ key: 'call_uuid' })
    const fromAgent =
        data.from?.role === 'doctor' || data.from?.role === 'provider'
            ? data.provider
            : data.patient
    const toAgent =
        data.to?.role === 'doctor' || data.to?.role === 'provider'
            ? data.provider
            : data.patient
    const validateData =
        data.conversation &&
        data.token &&
        data.appid &&
        uuid &&
        platform === 'ios'
    if (validateData) {
        const option: IStartCallOption = {
            token: data.token,
            appid: data.appid,
            mode: 'video',
            channel: data.conversation,
            locale: locale.value as 'vi' | 'en',
            duration: Number.parseInt(data.remaining),
            from: {
                name: `${toAgent?.title ? toAgent?.title + ' ' : ''}${toAgent?.name}`,
                _id: data.to.user,
                avatar: toAgent?.avatar?.url ?? '',
            },
            to: {
                name: `${fromAgent?.title ? fromAgent?.title + ' ' : ''}${fromAgent?.name}`,
                _id: data.from.user,
                avatar: fromAgent?.avatar?.url ?? '',
            },
            callback: {
                url:
                    data?.callback ??
                    config.public['nuxt3-module-data-layer'].baseUrl +
                        '/callcenter/video/event',
                data,
            },
        }
        log.addNativeLogs({
            context: 'conference',
            message: 'joinChannel agora video',
            data: option,
        })
        setTimeout(async () => {
            // CapacitorPluginAgora.joinChannel(option)
            CapacitorPluginAgora.startCall(option)
            if (data.path) router.push(data.path)
            else router.push('/')
        }, 1000)
    } else {
        if (data.path) router.push(data.path)
        else router.push('/')
    }
}

onMounted(async () => {
    const localData = await get({ key: 'callkeep:data' })
    log.addNativeLogs({
        context: 'conference',
        message: 'get data from preference',
        data: {
            data: localData,
        },
    })
    const data = JSON.parse(localData ?? '{}')
    await remove({ key: 'callkeep:data' })
    log.addNativeLogs({
        context: 'conference',
        message: 'receive data',
        data,
    })
    router.replace({ path: '', query: {} })
    try {
        if (!data?.conversation) {
            goBack(data.back)
        } else {
            startVideoCall(data)
        }
    } catch (e: any) {
        console.error('video-call', e.message)
        goBack(data.back)
    }
})
</script>
