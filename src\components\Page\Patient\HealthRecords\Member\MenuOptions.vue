<script lang="ts" setup>
import type { Relationship } from '~/models'

interface Props {
    visible: boolean
    isShowDelete?: boolean
    member: Relationship
}

const props = withDefaults(defineProps<Props>(), {
    isShowDelete: true,
})

const emit = defineEmits(['update:visible', 'share', 'edit', 'delete'])
const { t, locale } = useI18n()
const dayjs = useDayjs()

const formattedDob = computed(() => {
    if (!props.member.related?.dob) return ''

    const dateLocale = locale.value === 'vi' ? 'vi' : 'en'
    dayjs.locale(dateLocale)

    return dayjs(props.member.related.dob).format('DD MMMM, YYYY')
})

const formattedGender = computed(() => {
    const gender = props.member.related?.gender?.toLowerCase()
    if (gender === 'm') return t('gender.male')
    if (gender === 'f') return t('gender.female')
    return gender
})

const avatarUrl = computed(
    () =>
        props.member.related?.avatar?.url ||
        `https://ui-avatars.com/api/?name=${props.member.related?.name}`,
)

const items = computed(() => {
    const menuItems = [
        {
            label: t('menu.share.title'),
            icon: 'pi pi-share-alt',
            class: 'text-gray-700 dark:text-gray-200',
            // command: () => emit('share'),
            isComingSoon: true,
        },
        {
            label: t('menu.edit.title'),
            icon: 'pi pi-pen-to-square',
            class: 'text-gray-700 dark:text-gray-200',
            command: () => emit('edit'),
        },
    ]

    if (props.isShowDelete) {
        menuItems.push({
            label: t('menu.delete.title'),
            icon: 'pi pi-trash',
            class: 'text-red-500 dark:text-red-400',
            command: () => emit('delete'),
        })
    }

    return menuItems
})
</script>

<template>
    <Dialog
        :visible="visible"
        modal
        :closable="false"
        dismissable-mask
        position="bottom"
        :draggable="false"
        class="!m-0 !w-full !max-w-full"
        :pt="{
            content: 'pb-safe pt-6 !px-0',
            header: 'hidden',
            footer: 'hidden',
        }"
        @update:visible="(val: boolean) => emit('update:visible', val)"
    >
        <div class="flex items-center gap-4 px-3">
            <!-- Profile Section -->
            <Avatar
                :image="avatarUrl"
                shape="circle"
                size="large"
                class="border border-primary"
            />

            <!-- Info Section -->
            <div class="flex-1">
                <h2
                    class="mb-1 text-lg font-semibold text-gray-800 dark:text-gray-100"
                >
                    {{ member.related?.name || 'Unknown User' }}
                </h2>
                <div
                    v-if="formattedDob"
                    class="flex items-center space-x-1 text-sm text-gray-600 dark:text-gray-400"
                >
                    <span>{{ formattedDob }}</span>
                    <span v-if="formattedGender" class="flex items-center">
                        <span class="mx-1 opacity-50">•</span>
                        {{ formattedGender }}
                    </span>
                    <!-- <span v-if="member?.relationship" class="flex items-center">
                        <span class="mx-1 opacity-50">•</span>
                        <span class="text-primary">
                            {{ t(member?.relationship) }}
                        </span>
                    </span> -->
                </div>
            </div>
        </div>

        <Divider class="mb-[-4px]" />

        <!-- Action Menu -->
        <Menu
            pt:root:class="border-none rounded-bl-none rounded-br-none"
            pt:list:class="!px-0"
            pt:submenu-label:class="border-t"
            :model="items"
        >
            <template #item="{ item, props: _props }">
                <a
                    v-ripple
                    :class="[
                        'flex h-12 items-center justify-between',
                        {
                            'bg-gray-200/55': item?.isComingSoon,
                        },
                    ]"
                    v-bind="_props.action"
                >
                    <span
                        :class="[
                            item.icon,
                            {
                                'text-gray-300': item?.isComingSoon,
                            },
                        ]"
                    />
                    <span
                        :class="[
                            'flex-1',
                            {
                                'text-gray-300': item?.isComingSoon,
                            },
                        ]"
                    >
                        {{ item.label }}
                    </span>
                    <span
                        v-if="item?.isComingSoon"
                        class="rounded-2xl border border-primary-400 bg-teal-200/25 px-3 py-1 text-xs text-gray-500"
                    >
                        {{ t('coming-soon') }}
                    </span>
                </a>
            </template>
        </Menu>
    </Dialog>
</template>

<i18n lang="json">
{
    "en": {
        "menu": {
            "delete": {
                "title": "Delete relationship"
            },
            "edit": {
                "title": "Update profile"
            },
            "share": {
                "title": "Share health record"
            }
        },
        "gender": {
            "male": "Male",
            "female": "Female"
        },
        "self": "Self"
    },
    "vi": {
        "menu": {
            "delete": {
                "title": "Xoá mối quan hệ"
            },
            "edit": {
                "title": "Cập nhật hồ sơ"
            },
            "share": {
                "title": "Chia sẻ hồ sơ sức khoẻ"
            }
        },
        "gender": {
            "male": "Nam",
            "female": "Nữ"
        },
        "self": "Chính tôi"
    }
}
</i18n>
