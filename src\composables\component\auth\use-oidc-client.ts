import { useRuntimeConfig } from '#imports'
import type { OidcClientSettings } from 'oidc-client-ts'
import { OidcClient } from 'oidc-client-ts'

export const useOidcClient = (
    settings: Partial<OidcClientSettings> & { client_id: string },
) => {
    const runtimeConfig: any = useRuntimeConfig()
    const baseUrl = window?.location?.origin
    const redirectUri = baseUrl + runtimeConfig.public?.auth?.redirectUri
    // const store = new OidcStorage();
    const client = new OidcClient({
        response_type: 'code',
        scope: 'openid profile email offline_access',
        loadUserInfo: true,
        ...settings,
        authority: runtimeConfig.public?.auth?.keycloakIssuer,
        redirect_uri: redirectUri,
        post_logout_redirect_uri: baseUrl,
    })

    return { client }
}
