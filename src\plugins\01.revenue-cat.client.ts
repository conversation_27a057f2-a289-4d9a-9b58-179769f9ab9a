// plugins/revenueCat.ts
import { Capacitor } from '@capacitor/core'
import {
    ENTITLEMENT_VERIFICATION_MODE,
    Purchases,
} from '@revenuecat/purchases-capacitor'

export default defineNuxtPlugin(async () => {
    const runtimeConfig = useRuntimeConfig()

    const isIos = computed(() => Capacitor.getPlatform() === 'ios')

    // Configure RevenueCat chỉ một lần khi khởi động app
    const configurePurchases = async () => {
        const isConfigured = (await Purchases.isConfigured()).isConfigured
        if (isConfigured) return

        try {
            await Purchases.configure({
                apiKey: runtimeConfig.public.revenueCat.apiKey,
                entitlementVerificationMode:
                    ENTITLEMENT_VERIFICATION_MODE.INFORMATIONAL,
                diagnosticsEnabled: true,
            })
            console.log('RevenueCat SDK configured successfully!')
        } catch (error) {
            console.error('Failed to configure RevenueCat:', error)
            throw error
        }
    }
    if (isIos.value) {
        await configurePurchases()
    }

    return {
        provide: {
            purchases: Purchases,
        },
    }
})
