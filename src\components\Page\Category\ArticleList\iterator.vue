<script setup lang="ts">
import { defineAsyncComponent, type PropType } from '#imports'
import type { HitNotionWebsite } from '~/models'
type LayoutType = 'list' | 'grid' | 'timeline' | 'gallery'
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
const skeleton = {
  gallery: defineAsyncComponent(() => import('./Item/Sketeton/gallery.vue')),
  list: defineAsyncComponent(() => import('./Item/Sketeton/list.vue')),
}
const { articles, slug, blocks, loading } = defineProps({
  articles: {
    type: Array as PropType<HitNotionWebsite[]>,
    required: true,
  },
  slug: {
    type: String,
    required: true,
  },
  blocks: {
    type: Array as PropType<BlockType[]>,
    default: () => ['title', 'sapo'],
  },
  layout: {
    type: String as PropType<LayoutType>,
    default: () => 'list',
  },
  loading: {
    type: Boolean,
    default: false,
  },
})
</script>
<template>
  <component :is="skeleton[layout]" v-if="loading" />
  <div v-for="(article, index) in articles" v-else :key="index" class="w-full">
    <PageCategoryArticleListItem
      :article="article"
      :layout="layout"
      :slug="slug"
      :blocks="blocks"
    />
  </div>
</template>
