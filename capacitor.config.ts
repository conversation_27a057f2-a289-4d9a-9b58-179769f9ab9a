import type { CapacitorConfig } from '@capacitor/cli'

const config: CapacitorConfig = {
    appId: 'vn.wellcare',
    appName: 'Wellcare',
    webDir: '.output/public',
    plugins: {
        // CapacitorHttp: {
        //     enabled: true,
        // },
        SplashScreen: {
            launchShowDuration: 2000,
            launchAutoHide: true,
            launchFadeOutDuration: 1000,
            backgroundColor: '#009688',
            androidSplashResourceName: 'splash',
            androidScaleType: 'CENTER_CROP',
            showSpinner: false,
            androidSpinnerStyle: 'large',
            iosSpinnerStyle: 'small',
            splashFullScreen: true,
            splashImmersive: true,
            layoutName: 'activity_main',
            useDialog: false,
        },

        FirebaseAuthentication: {
            skipNativeAuth: false,
            providers: ['google.com'],
        },
    },
    server: {
        iosScheme: 'nuxtmobile',
        androidScheme: 'http',
        allowNavigation: ['storage.googleapis.com', 'cdn.wellcare.vn'],
    },
    ios: {},
}

export default config
