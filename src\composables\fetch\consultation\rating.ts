interface IRating {
    type: string
    consultation: string
    rating: number
    comment: string
    ratingBy: string
    provider: string
}

export function useConsultationRating() {
    const { $fetchWellcare } = useNuxtApp()

    const consultationId = ref<string>('')
    const payload = ref({})

    const { execute: executeSubmitRating } = useAsyncData(
        'consultation-rating',
        () =>
            $fetchWellcare(
                `/consultation-server/consultation/${consultationId.value}/rating`,
                {
                    method: 'POST',
                    body: payload.value,
                },
            ),
        {
            immediate: false,
        },
    )

    const submitRating = async (
        _consultationId: string,
        _payload: Partial<IRating>,
    ) => {
        consultationId.value = _consultationId
        payload.value = _payload

        await executeSubmitRating()
    }

    return { submitRating }
}
