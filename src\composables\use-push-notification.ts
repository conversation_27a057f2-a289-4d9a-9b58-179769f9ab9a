import { navigateTo, useRuntimeConfig } from '#imports'
import { Capacitor } from '@capacitor/core'
import OneSignal from 'onesignal-cordova-plugin'
import { useLogs } from './use-logs'

const notificationsQueue: Array<string> = []

export function usePushNotification() {
    const config = useRuntimeConfig()
    const { addNativeLogs } = useLogs()
    const { user } = useUserInfo({ scope: ['avatar', 'name', '_id'] })
    const notifications = useNotivueNotifications()

    const app_id = config.public.onesignal.app_id

    const login = async () => {
        if (!Capacitor.isNativePlatform())
            throw new Error('not implement in web')

        OneSignal.Debug.setLogLevel(6)

        OneSignal.initialize(app_id)

        const externalId = await getExternalId()
        const onesignalId = await getOnesignalId()
        const subcriptionId = await getSubscriptionId()

        console.log('usePushNotification.externalId', externalId)
        console.log('usePushNotification.onesignalId', onesignalId)
        console.log('usePushNotification.userId', user.value?._id)
        console.log('usePushNotification.subscriptionId', subcriptionId)

        // if (!externalId && !user.value?._id) return

        // if (!externalId && user.value?._id) {
        //     console.log('login with: ' + user.value._id)
        //     OneSignal.login(String(user.value._id))
        // }

        // if (externalId && !user.value?._id) {
        //     console.log('logout')
        //     OneSignal.logout()
        // }

        // if (user.value?._id && externalId !== user.value._id) {
        //     console.log('login with: ' + user.value._id)
        //     OneSignal.login(String(user.value._id))
        // }
        // if (user.value.id) {
        console.log('login with: ' + user.value._id)
        addNativeLogs({
            context: 'usePushNotification',
            message: 'login push onesignal',
            data: {
                userId: user.value._id,
            },
            time: new Date().toLocaleString(),
        })
        OneSignal.login(String(user.value._id))
        // }
        const token = await getPushToken()
        console.log('[wellcare - debug] push token: ' + token)
        addNativeLogs({
            context: 'usePush',
            message: 'get push token',
            data: {
                token,
            },
            time: new Date().toLocaleString(),
        })

        OneSignal.Notifications.addEventListener('click', (data) => {
            const dataReceive: any = data.notification.additionalData
            console.log(
                '🚀 ~ OneSignal.Notifications.addEventListener ~ dataReceive:',
                dataReceive,
            )
            if (dataReceive?.path) {
                navigateTo(dataReceive.path)
            }
            // if (dataReceive.event === 'kick-out') signOut()
            console.log(
                'OneSignal.Notifications.click',
                JSON.stringify({ data }),
            )
        })

        OneSignal.Notifications.addEventListener(
            'foregroundWillDisplay',
            (data) => {
                const dataReceive: any = data.getNotification().additionalData
                const notification = data.getNotification()
                console.log(
                    'OneSignal.Notifications.foregroundWillDisplay',
                    JSON.stringify({ dataReceive }),
                )
                // console.log('[wellcare debug] Foreground will display: ')
                // console.log(JSON.stringify(data.getNotification().additionalData))
                // if ((data.getNotification().additionalData as any).event === 'kick-out')
                //     signOut()
                if (Capacitor.getPlatform() === 'ios') {
                    const event = (notification.additionalData as any)?.event
                    if (
                        typeof event === 'string' &&
                        event.includes('invite-call')
                    ) {
                        return
                    }
                    const title = notification?.title ?? ''
                    const subtitle = notification?.subtitle ?? ''
                    if (
                        !subtitle ||
                        notificationsQueue.includes(notification.notificationId)
                    )
                        return
                    const props: Record<string, any> = {}
                    if (dataReceive?.path) {
                        props.path = dataReceive?.path
                    }
                    notifications.info({
                        title,
                        message: subtitle,
                        duration: 10000,
                        props,
                    })
                    notificationsQueue.push(notification.notificationId)
                }
            },
        )

        // OneSignal.Notifications.requestPermission(true).then((accept) => {
        //     console.log(
        //         'OneSignal.Notifications.requestPermission',
        //         JSON.stringify({ accept }),
        //     )
        // })
    }

    const logout = () => {
        OneSignal.logout()
    }

    const getOnesignalId = () => {
        return OneSignal.User.getOnesignalId()
    }

    const getExternalId = () => {
        return OneSignal.User.getExternalId()
    }

    const getSubscriptionId = async () => {
        return await OneSignal.User.pushSubscription.getIdAsync()
    }

    const getPushToken = async () => {
        return await OneSignal.User.pushSubscription.getTokenAsync()
    }

    return {
        login,
        logout,
        getOnesignalId,
        getExternalId,
    }
}
