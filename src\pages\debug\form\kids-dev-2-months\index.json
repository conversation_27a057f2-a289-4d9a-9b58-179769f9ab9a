{"objectId": "7h6gS7vvF1", "pages": [{"id": "Starting Page", "elements": [{"id": "m2jwptahb3", "type": "display", "properties": {"display": {"component": "html_block_content", "tag": "p", "content": "<PERSON><PERSON><PERSON> mốc phát triển rất quan trọng! Cách em bé chơi, h<PERSON><PERSON>, n<PERSON><PERSON>, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 2 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. <PERSON><PERSON><PERSON> sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.\n"}}}, {"id": "m2jwq2chnyj", "type": "display", "properties": {"display": {"component": "html_block_media", "tag": "img", "src": "https://images.ctfassets.net/4h8s6y60f7sb/41VKJs6hwNqtEzDdCXYAGY/1458a54798476c2edf45a7b722395d91/NEW_Blog_Headers__28_.jpg?w=1440&fm=jpg&q=90", "alt": "Nice picture"}}}], "type": "start"}, {"elements": [{"id": "m2jwrs2hy8", "type": "input", "properties": {"input": {"label": "<PERSON><PERSON>t nhìn vào khuôn mặt của cha mẹ", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwrs2hy8", "fetch": {}}}}, {"id": "m2jwsd5gb3b", "type": "input", "properties": {"input": {"label": "<PERSON><PERSON> vẻ rất vui khi được gặp, khi cha mẹ đang bước đến gần bé", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwsd5gb3b", "fetch": {}}}}], "id": "<PERSON><PERSON><PERSON> ti<PERSON>p xã hội và tình cảm", "type": "input"}, {"elements": [{"id": "m2jwtzret0s", "type": "input", "properties": {"input": {"label": "<PERSON>ệng phát ra âm <PERSON> kh<PERSON>, ngo<PERSON>i tiếng khóc", "component": "multiple_choice", "description": "Select one option from he list.", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwtzret0s", "fetch": {}}}}, {"id": "m2jwue2ujnt", "type": "input", "properties": {"input": {"label": "<PERSON><PERSON><PERSON>ng với âm thanh lớn", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwue2ujnt", "fetch": {}}}}], "id": "<PERSON><PERSON><PERSON> ngữ và Giao tiếp", "type": "input"}, {"elements": [{"id": "m2jwvdkt0f", "type": "input", "properties": {"input": {"label": "<PERSON>uan sát cha mẹ đang di chuyển gần đó", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwvdkt0f", "fetch": {}}}}, {"id": "m2jwvrpk2t", "type": "input", "properties": {"input": {"label": "Mắt nhìn vào một món đồ chơi trong vài giây", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwvrpk2t", "fetch": {}}}}], "id": "<PERSON><PERSON><PERSON><PERSON> thức (<PERSON><PERSON><PERSON> tập tư duy gi<PERSON>i quyết vấn đề)", "type": "input"}, {"elements": [{"id": "m2jwwq91l16", "type": "input", "properties": {"input": {"label": "<PERSON><PERSON><PERSON> đầu lên khi nằm sấp", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwwq91l16", "fetch": {}}}}, {"id": "m2jwx3gysz", "type": "input", "properties": {"input": {"label": "<PERSON> chuyển cả hai tay và cả hai chân", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwx3gysz", "fetch": {}}}}, {"id": "m2jwxh1nb8", "type": "input", "properties": {"input": {"label": "<PERSON><PERSON> thể mở lòng bàn tay nhanh chóng", "component": "multiple_choice", "description": "", "placeholder": "Choose an option", "choices": [{"label": "<PERSON><PERSON>", "value": "yes"}, {"label": "<PERSON><PERSON><PERSON><PERSON>c", "value": "not_sure"}, {"label": "Chưa", "value": "not_yet"}], "key": "MultipleChoicem2jwxh1nb8", "fetch": {}}}}], "id": "<PERSON><PERSON><PERSON> động/<PERSON><PERSON><PERSON> triển thể chất", "type": "input"}], "computes": [{"key": "user", "value": "659bc0335028762cf46a2555", "component": "hidden_field"}, {"key": "conversation", "value": "66b20045d28f5574b24e4181", "component": "hidden_field"}], "title": "2 tháng ", "createdAt": "2024-10-22T03:49:16.172Z", "updatedAt": "2024-11-20T12:34:05.579Z", "submission": "remote", "status": "draft"}