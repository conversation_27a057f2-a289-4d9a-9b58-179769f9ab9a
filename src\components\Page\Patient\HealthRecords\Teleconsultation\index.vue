<script setup lang="ts">
import { ref, watch, computed } from '#imports'
import { WUpcomingConsultations } from '#components'
import { io } from 'socket.io-client'

defineProps({
    title: {
        type: String,
        required: true,
    },
    memberActivatedId: {
        type: String,
        required: true,
    },
})

const router = useRouter()
const providerId = ref<string>('')
const consultationId = ref<string>('')
const providerAvatar = ref<string>('')
const isOpenRating = ref<boolean>(false)
const upConsultationRef = ref<InstanceType<
    typeof WUpcomingConsultations
> | null>(null)
const isShow = ref(false)

const { user } = useUserInfo({ scope: ['_id'] })
const config = useRuntimeConfig()

const sharedCount = computed(
    () => upConsultationRef.value?.consultationSectionsCount,
)
const userId = computed(() => user.value?._id ?? '')

const callHandle = useCallHandle()

const openEMRHandler = (consultation: any) => {
    navigateTo(`/patient/consultation/${consultation._id}`)
}

const callVideoHandler = async (data: any) => {
    callHandle.open('video', data)
}

const callVoiceHandler = async (data: any) => {
    callHandle.open('voice', data)
}

const onOpenRating = (selectedConsultation: any) => {
    isOpenRating.value = true
    consultationId.value = selectedConsultation._id
    providerId.value = selectedConsultation?.provider?._id
    providerAvatar.value = selectedConsultation?.provider?.avatar?.url
}

const handleRedicrect = () => {
    router.push('/patient/services/provider')
}

const refreshConsultations = useDebounceFn(() => {
    if (upConsultationRef.value) {
        upConsultationRef.value?.refreshConsultations()
        console.info('[socket] refreshSearch')
    }
}, 500)

// Socket setup
const socketEndpoint = config.public.socketEndPoint as string
const socket = io(socketEndpoint + '/Consultation', {
    autoConnect: true,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    reconnectionAttempts: 5,
    transports: ['websocket'],
})

// Socket event handlers
socket.on('created', refreshConsultations)
socket.on('updated', refreshConsultations)
socket.on('removed', refreshConsultations)

// Watch for user ID changes and join room
watch(
    userId,
    (val) => {
        if (val) {
            socket.emit('join', {
                user: val,
                room: val,
            })
        }
    },
    { immediate: true },
)

watch(sharedCount, () => {
    isShow.value = Boolean(sharedCount.value)
})

// Cleanup on component unmount
onUnmounted(() => {
    socket.disconnect()
})
</script>

<template>
    <div v-show="isShow">
        <SharedPageSection
            :title="title"
            :pt="{
                content:
                    'flex snap-x flex-row flex-nowrap gap-4 overflow-x-auto px-4 sm:flex flex-col',
            }"
            :view-all-to="`/patient/health-records/${memberActivatedId}/consultation`"
        >
            <WUpcomingConsultations
                ref="upConsultationRef"
                :user-id="userId"
                @click:call="callVoiceHandler"
                @click:open-mr="openEMRHandler"
                @click:call-video="callVideoHandler"
                @click:open-rating="onOpenRating"
                @click:redirect-choose-doctor="handleRedicrect"
            />
            <WConsultationRating
                :visible="isOpenRating"
                :provider-id="providerId"
                :consultation-id="consultationId"
                :provider-avatar="providerAvatar"
                @submitted="isOpenRating = false"
                @skip-rating="isOpenRating = false"
                @update:visible="isOpenRating = false"
            />
        </SharedPageSection>
    </div>
</template>
