<script setup lang="ts">
import { Pagination } from 'swiper/modules'
import MemberAvatar from './AvatarProfile.vue'
import MemberMenu from './MenuOptions.vue'
import DialogUserProfile from './DialogUserProfile.vue'
import type { Relationship } from '~/models'
import type { User } from '@wellcare/nuxt3-module-account'
import { push } from 'notivue'

interface Props {
    members: Relationship[]
    memberActivated: Relationship
}

interface Form extends User {
    relationship: Relationship['relationship']
}

const props = defineProps<Props>()
const emit = defineEmits(['on:change', 'on:add', 'on:edit', 'on:refresh'])

// Constants
const SWIPER_MODULES = [Pagination]
const SWIPER_BREAKPOINTS = {
    '0': { slidesPerView: 5, slidesPerGroup: 3 },
    '640': { slidesPerView: 8, slidesPerGroup: 8 },
    '768': { slidesPerView: 12, slidesPerGroup: 8 },
    '1024': { slidesPerView: 16, slidesPerGroup: 16 },
    '1440': { slidesPerView: 20, slidesPerGroup: 20 },
}

// Composables
const { t } = useI18n()
const { deleteRelationship } = useFetchUserInfo()
const confirm = useConfirm()

// Refs
const visibleForm = ref(false)
const visibleMenu = ref(false)
const defaultDataForm = ref<Partial<Form>>({})

// Computed
const isCreate = computed(() => !defaultDataForm.value?._id)
const isMainUser = computed(
    () => defaultDataForm.value?._id === props.members[0].related?._id,
)

// Methods
const handleOpenForm = () => {
    visibleForm.value = true
    defaultDataForm.value = {}
}

const handleEditForm = () => {
    visibleMenu.value = false
    visibleForm.value = true
}

const handleShare = () => {
    visibleMenu.value = false
    push.info({
        message: t('coming-soon'),
    })
}

const handleDelete = async () => {
    const _id = defaultDataForm.value?._id

    visibleMenu.value = false
    confirm.require({
        message: t('delete.message'),
        header: t('delete.title'),
        position: 'bottom',
        acceptLabel: 'ok',
        rejectLabel: t('cancel'),
        acceptClass: 'w-1/8 px-4 py-2',
        rejectClass: 'p-button-secondary',
        accept: async () => {
            try {
                if (_id) {
                    await deleteRelationship(_id)
                    emit('on:refresh')

                    push.success({
                        message: t('notification.delete.success'),
                    })
                }
            } catch (error) {
                push.error({
                    message: t('notification.delete.error'),
                })
                console.error('Failed to delete relationship:', error)
            }
        },
    })
}

const chooseMember = (member: Relationship, event: MouseEvent) => {
    emit('on:change', member, event)

    defaultDataForm.value = {
        ...member.related,
        relationship: member.relationship,
    }
}
</script>

<template>
    <div class="family-members-container pl-6 pr-2">
        <ClientOnly>
            <swiper
                :grab-cursor="true"
                :modules="SWIPER_MODULES"
                :initial-slide="0"
                :breakpoints="SWIPER_BREAKPOINTS"
                class="!pb-2"
            >
                <!-- Add Member Button -->
                <swiper-slide>
                    <div
                        class="mb-3 mt-2 flex h-16 w-16 items-center justify-center"
                    >
                        <button
                            class="add-member-btn size-12 rounded-full border border-primary-50 bg-primary-50/10 text-primary-100 transition-all duration-300"
                            @click="handleOpenForm"
                        >
                            <i class="pi pi-plus" />
                        </button>
                    </div>
                </swiper-slide>
                <!-- Member Cards -->
                <swiper-slide
                    v-for="(member, index) in members"
                    :id="index"
                    :key="member._id"
                    class="member-slide"
                >
                    <MemberAvatar
                        :member="member"
                        :is-active="member._id === memberActivated._id"
                        :is-show-delete="member._id !== members[0]._id"
                        :class="[
                            {
                                'cursor-pointer':
                                    member._id !== memberActivated._id,
                            },
                        ]"
                        @open:menu="visibleMenu = true"
                        @click="chooseMember(member, $event)"
                    />
                </swiper-slide>
            </swiper>
        </ClientOnly>

        <MemberMenu
            v-model:visible="visibleMenu"
            :member="memberActivated"
            :is-show-delete="!isMainUser"
            @edit="handleEditForm"
            @delete="handleDelete"
            @share="handleShare"
        />

        <!-- Add/Edit Member Dialog -->
        <DialogUserProfile
            v-model:visible="visibleForm"
            :is-main-user="isMainUser"
            :is-create="isCreate"
            :id-relationship="memberActivated._id"
            :default-data-form="defaultDataForm"
            @update:visible="($data: boolean) => (visibleForm = $data)"
            @on:refresh="emit('on:refresh')"
        />
    </div>
</template>

<style scoped>
.family-members-container {
    --swiper-scrollbar-opacity: 0;
}

.member-slide {
    transition: transform 0.3s ease;
}
</style>

<i18n lang="json">
{
    "en": {
        "coming-soon": "Coming Soon",
        "delete": {
            "title": "Delete Confirmation",
            "message": "Permanently delete this data"
        },
        "notification": {
            "delete": {
                "success": "Deleted successfully",
                "error": "Deleted failed"
            }
        }
    },
    "vi": {
        "coming-soon": "Sắp ra mắt",
        "delete": {
            "title": "Xác nhận xóa",
            "message": "Bạn có chắc chắn muốn xóa số liệu này?"
        },
        "notification": {
            "delete": {
                "success": "Xoá thành công",
                "error": "Xoá thất bại"
            }
        }
    }
}
</i18n>
