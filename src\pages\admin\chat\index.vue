<script setup lang="ts">
definePageMeta({
    layout: 'nested-page',
    middleware: ['auth'],
})

const { t } = useI18n()
const route = useRoute()
const router = useRouter()

const layoutStore = useLayoutStore()

const { searchAgent } = useChat()
const { createToken, createAgentToken } = useAuthWellchat()

const { user } = useUserInfo({ scope: ['_id', 'name', 'avatar'] })

const agents = ref<any[]>()
const selectedAgent = ref<any>()

const firebaseToken = ref<string>('')

const onChooseContact = (contact: any) => {
    let _view

    switch (contact.type) {
        case 'chat-admin':
            _view = 'admin-conversation'
            break

        case 'chat-gpt':
            _view = 'gpt-conversation'
            break

        case 'general-chat':
            _view = 'timelines'
            break

        default:
            _view = 'default-conversation'
            break
    }

    router.push({
        path: `/admin/chat/${contact._id}`,
        query: {
            _view,
            agent: selectedAgent.value?._id,
        },
    })
}

watch(selectedAgent, async () => {
    let customToken
    switch (selectedAgent.value?.type) {
        case 'user':
            customToken = await createToken(user.value?._id)
            break

        case 'agent':
            customToken = await createAgentToken({
                userId: user.value?._id,
                agent: selectedAgent.value?._id,
            })

            break

        default:
            break
    }

    if (customToken) {
        firebaseToken.value = customToken?.token
    }
})

onMounted(async () => {
    layoutStore.setTitle(t('contacts'))

    const _agents = (await searchAgent(user.value?._id)) as any[]
    agents.value = _agents.map((agent: any) => {
        return {
            _id: agent?._id,
            name: agent?.organization?.name,
            avatar: agent?.organization?.logo?.url,
            type: 'agent',
        }
    })

    agents.value.push({
        _id: user.value?._id,
        name: user.value.name,
        avatar: user.value.avatar.url,
        type: 'user',
    })

    if (route.query?.agent) {
        selectedAgent.value = agents.value.find(
            (agent: any) => agent?._id === route.query.agent,
        )
    } else {
        selectedAgent.value = agents.value.find(
            (agent: any) => agent?._id === user.value?._id,
        )
    }
})
</script>

<template>
    <div class="flex flex-col">
        <div class="m-4 flex justify-center gap-4">
            <Select
                v-model="selectedAgent"
                :options="agents"
                option-label="name"
                placeholder="Select a agent"
            >
                <template #value="slotProps">
                    <div v-if="slotProps.value" class="flex items-center gap-2">
                        <img
                            :alt="slotProps.value.name"
                            :src="slotProps.value.avatar"
                            class="size-5 rounded-full"
                        />
                        <div>{{ slotProps.value.name }}</div>
                    </div>
                    <span v-else>
                        {{ slotProps.placeholder }}
                    </span>
                </template>
                <template #option="slotProps">
                    <div class="flex items-center gap-2">
                        <img
                            :alt="slotProps.option.name"
                            :src="slotProps.option.avatar"
                            class="size-5 rounded-full"
                        />
                        <div>{{ slotProps.option.name }}</div>
                    </div>
                </template>
            </Select>
        </div>

        <w-chat-contact
            style="height: calc(100vh - 136px)"
            :user-id="selectedAgent?._id"
            :firebase-token="firebaseToken"
            @choose-contact="onChooseContact"
        />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "contacts": "Contacts"
    },
    "vi": {
        "contacts": "Liên hệ"
    }
}
</i18n>
