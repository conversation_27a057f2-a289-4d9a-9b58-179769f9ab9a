import type { INotionPage } from '../notion'
import type { IHitNotion } from './notion'

export interface NotionRatings {
  Comment?: string
  CreatedAt: { start: string }
  IsFeature?: boolean
  Provider?: string
  ProviderSlug?: string
  Rating?: number
  RatingByAvatar?: { url?: string }
  RatingByGender?: string
  RatingByName?: string
  Sentiment?: string
  ShowOnHomepage?: boolean
  Source?: string
  Status?: string
}

export interface HitNotionRatings extends IHitNotion {
  page?: INotionPage<NotionRatings>
}
