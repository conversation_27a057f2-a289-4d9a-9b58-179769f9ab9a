<script lang="ts" setup>
import { useRoute, useRouter, useUserInfo } from '#imports'
import { Capacitor } from '@capacitor/core'
import { push } from 'notivue'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'
import type { Relationship } from '~/models'

definePageMeta({
    layout: 'blank',
})

const { t } = useI18n()
const dayjs = useDayjs()
const router = useRouter()
const { params }: any = useRoute()
const { share, isSupported: shareSupport } = useShare()
const { copy, isSupported: clipboardSupport } = useClipboard()

const chatStore = useChatStore()
const { user } = useUserInfo({
    scope: ['_id'],
})

const healthGptContext = useLocalStorage('health-gpt-context', {})

const { members, refreshMembers } = useMembers()
const updateMember = ref()
const selectedMember = ref()
const defaultDataForm = ref<any>({})
const visibleFormProfile = ref(false)
const visibleMemberProfile = ref(false)
const isAddedContextMember = ref(false)
const visibleContext = ref(!Object.keys(healthGptContext.value).length)
const isCreate = computed(() => !defaultDataForm.value?._id)
const isMainUser = computed(
    () => defaultDataForm.value?._id === members.value?.[0].related?._id,
)
const onSelectContextMember = (member: Relationship) => {
    selectedMember.value = member
    healthGptContext.value = member
    visibleContext.value = false
}
const onAddContextMember = () => {
    isAddedContextMember.value = false
    visibleFormProfile.value = true
    defaultDataForm.value = {}
}
const onUpdateContextMember = (member: Relationship) => {
    updateMember.value = member
    visibleMemberProfile.value = true
}
const onUpdateMemberProfile = (member: Relationship) => {
    visibleFormProfile.value = true
    defaultDataForm.value = {
        ...member.related,
        relationship: member.relationship,
    }
}
watch(members, () => {
    if (isAddedContextMember.value) {
        healthGptContext.value = members.value
            .filter((member) => member.related?._id !== user.value._id)
            .sort(
                (a, b) =>
                    dayjs(b.createdAt).valueOf() - dayjs(a.createdAt).valueOf(),
            )
            .at(0)
        selectedMember.value = healthGptContext.value
    } else {
        healthGptContext.value = Object.keys(healthGptContext.value).length
            ? healthGptContext.value
            : members.value.find(
                  (member: Relationship) =>
                      member.related?._id === user.value._id,
              )

        selectedMember.value = healthGptContext.value
    }
})
const handleRefreshMember = async () => {
    healthGptContext.value = {}
    isAddedContextMember.value = true
    await refreshMembers()
}

const { config } = useJsonConfigApp()
const shouldCheckMembership = computed<boolean>(() =>
    Boolean(config.value.pages['health-gpt']?.checkMembership),
)
const { loading: isCheckingMembership, isMembership: checkMembership } =
    useMembership()
const isMembership = computed(() => {
    if (config.value?.pages['health-gpt']?.checkMembership === false)
        return true
    else return checkMembership.value
})

const permissions = ref<any>()
const visibleProgram = ref(false)
const isShowExpireTime = ref(false)
const visibleMembership = ref(false)
const isExpired = ref<boolean>(false)
const isConnectedRoom = ref<boolean>(false)

const isNative = computed(() => Capacitor.isNativePlatform())

const packages = computed(() => {
    const primaryColor = 'text-primary'
    const primaryBorderColor = 'border-primary'
    const expiredTextColor = 'text-[#ff9980]'
    const activeTextColor = 'text-[#ff5722]'
    const expiredBorderColor = 'border-[#ff9980]'
    const activeBorderColor = 'border-[#ff5722]'

    return [
        {
            icon: 'pi pi-crown',
            name: 'program:name',
            description: 'program:description',
            'text-color': primaryColor,
            'border-color': primaryBorderColor,
            onClick: () => (visibleMembership.value = true),
        },
        {
            icon: 'pi pi-bolt',
            name: 'program:name',
            description: 'program:description',
            'text-color': isExpired.value ? expiredTextColor : activeTextColor,
            'border-color': isExpired.value
                ? expiredBorderColor
                : activeBorderColor,
            onClick: () => {
                if (!isExpired.value) {
                    onActiveTrial()
                }
            },
        },
    ]
})

const { statusCode, activateGptTrial } = useWellchatPermission()

const onBack = () => {
    router.push('/patient/health-gpt')
}

const onConnected = () => {
    console.log('User connected to the chat room!')
    isConnectedRoom.value = true
}

const onDisconnected = () => {
    console.log('User disconnected from the chat room!')
}

const onPermissionUpdated = (_permissions: any) => {
    console.log('User permissions updated:', _permissions)
    permissions.value = _permissions
}

const onShareProfile = () => {
    const url = document.URL
    const shareData = {
        title: 'HealthGPT',
        text: 'Check out this bot on Wellcare',
        url,
    }

    if (shareSupport.value) {
        return share(shareData)
    }

    if (clipboardSupport.value) {
        copy(url)
        push.info({
            message: t('toast:copied to clipboard'),
        })
    } else {
        push.error({
            message: t('toast:copy not supported'),
        })
    }
}

const onActiveTrial = () => {
    activateGptTrial({ member: user.value._id })
}

const onLeaveRoom = () => {
    router.push('/patient/health-gpt')
}

watch(statusCode, () => {
    if (statusCode.value === 200) {
        push.success({
            title: t('a 7-day trial starts successfully!'),
            message: t('you can now start chatting with our HealthGPT.'),
            duration: 3000,
        })
        chatStore.setIsReadonly(false)
    }
})

watch(
    [isConnectedRoom, permissions],
    () => {
        const now = dayjs().valueOf()
        const gptTrial = permissions.value?.['gpt-chat']?.gptTrial
        const expiredAt = gptTrial?.expiredAt

        // Set prevent action based on membership status
        chatStore.setIsReadonly(!isMembership.value)

        // If the user is non-member
        if (isConnectedRoom.value && !isMembership.value) {
            isShowExpireTime.value = !!expiredAt
            visibleProgram.value = expiredAt ? now >= expiredAt : true

            if (expiredAt && now >= expiredAt) {
                push.info({
                    title: t('a 7-day trial ends today'),
                    message: t(
                        'you can continue chatting with our HealthGPT if you become a member.',
                    ),
                    duration: 3000,
                })
                isExpired.value = true
            } else if (expiredAt) {
                chatStore.setIsReadonly(false)
            }
        } else {
            visibleProgram.value = false
        }
    },
    {
        deep: true,
        immediate: true,
    },
)
</script>

<template>
    <PagePatientHealthGPTSkeletonProfile v-if="isCheckingMembership" />
    <div v-else class="h-screen w-full">
        <w-chat-bot
            v-if="user._id"
            :user-id="user._id"
            :bot-key="params.key"
            :owner="{
                name: 'Wellcare',
                website: 'https://wellcare.vn',
            }"
            base-path="/patient/health-gpt/room"
            class="h-full w-full"
            @connected="onConnected"
            @disconnected="onDisconnected"
            @share-profile="onShareProfile"
            @permission-updated="onPermissionUpdated"
        >
            <template #header-top>
                <div
                    :class="[
                        {
                            'pt-safe': isNative,
                        },
                    ]"
                />
            </template>

            <template #header-left>
                <Button text severity="secondary" @click="onBack">
                    <template #icon>
                        <iconify-icon
                            icon="mingcute:left-line"
                            class="text-2xl"
                        />
                    </template>
                </Button>
            </template>

            <template #header-bottom>
                <div>
                    <div
                        v-if="isShowExpireTime"
                        class="flex h-9 items-center justify-center gap-2 text-sm text-primary"
                        :style="{
                            backgroundImage:
                                'linear-gradient(45deg, #d6f4df, #fce9d0)',
                        }"
                    >
                        <span>
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                fill="currentColor"
                                class="bi bi-stars"
                                viewBox="0 0 16 16"
                            >
                                <path
                                    d="M7.657 6.247c.11-.33.576-.33.686 0l.645 1.937a2.89 2.89 0 0 0 1.829 1.828l1.936.645c.33.11.33.576 0 .686l-1.937.645a2.89 2.89 0 0 0-1.828 1.829l-.645 1.936a.361.361 0 0 1-.686 0l-.645-1.937a2.89 2.89 0 0 0-1.828-1.828l-1.937-.645a.361.361 0 0 1 0-.686l1.937-.645a2.89 2.89 0 0 0 1.828-1.828zM3.794 1.148a.217.217 0 0 1 .412 0l.387 1.162c.173.518.579.924 1.097 1.097l1.162.387a.217.217 0 0 1 0 .412l-1.162.387A1.73 1.73 0 0 0 4.593 5.69l-.387 1.162a.217.217 0 0 1-.412 0L3.407 5.69A1.73 1.73 0 0 0 2.31 4.593l-1.162-.387a.217.217 0 0 1 0-.412l1.162-.387A1.73 1.73 0 0 0 3.407 2.31zM10.863.099a.145.145 0 0 1 .274 0l.258.774c.115.346.386.617.732.732l.774.258a.145.145 0 0 1 0 .274l-.774.258a1.16 1.16 0 0 0-.732.732l-.258.774a.145.145 0 0 1-.274 0l-.258-.774a1.16 1.16 0 0 0-.732-.732L9.1 2.137a.145.145 0 0 1 0-.274l.774-.258c.346-.115.617-.386.732-.732z"
                                />
                            </svg>
                        </span>
                        <p>
                            {{
                                t('unlimited chat', {
                                    expireTime: dayjs(
                                        permissions?.['gpt-chat']?.gptTrial
                                            ?.expiredAt,
                                    ).format('DD/MM/YYYY'),
                                })
                            }}
                        </p>
                    </div>

                    <div
                        v-if="selectedMember"
                        class="flex items-center gap-2 bg-gray-100 px-2 py-1"
                    >
                        <div class="w-8">
                            <Avatar
                                :image="selectedMember.related?.avatar?.url"
                                shape="circle"
                            />
                        </div>
                        <p class="line-clamp-2 flex-grow text-sm">
                            {{
                                t('persona', {
                                    name: selectedMember.related?.name,
                                })
                            }}
                        </p>
                        <Button
                            text
                            :label="t('button:change')"
                            pt:label:class="text-xs"
                            @click="visibleContext = true"
                        />
                    </div>
                </div>
            </template>
        </w-chat-bot>

        <PagePatientHealthGPTProgram
            v-if="shouldCheckMembership"
            :visible="visibleProgram"
            :packages="packages"
            @update:visible="onLeaveRoom"
        />

        <PagePatientHealthGPTContext
            :visible="!visibleProgram && visibleContext"
            :members="members"
            :active-member="selectedMember"
            :is-show="visibleContext"
            @select-member="onSelectContextMember"
            @add-member="onAddContextMember"
            @update-member="onUpdateContextMember"
            @update:visible="visibleContext = false"
        />

        <PagePatientHealthGPTMember
            :visible="visibleMemberProfile"
            :member="updateMember"
            @update-member="onUpdateMemberProfile"
            @update:visible="visibleMemberProfile = false"
        />

        <PagePatientHealthRecordsMemberDialogUserProfile
            v-model:visible="visibleFormProfile"
            :is-main-user="isMainUser"
            :is-create="isCreate"
            :id-relationship="selectedMember?._id"
            :default-data-form="defaultDataForm"
            @update:visible="($data: boolean) => (visibleFormProfile = $data)"
            @on:refresh="handleRefreshMember"
        />

        <SharedMembershipPopup
            v-model:visible="visibleMembership"
            @skip="visibleMembership = false"
            @update:visible="(val: boolean) => (visibleMembership = val)"
        />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "toast:copied to clipboard": "Profile copied to clipboard",
        "toast:copy not supported": "Clipboard functionality is not supported in your browser",
        "unlimited chat": "Unlimited chat until {expireTime}",
        "a 7-day trial starts successfully!": "A 7-day trial starts successfully!",
        "you can now start chatting with our HealthGPT.": "You can now start chatting with our HealthGPT.",
        "a 7-day trial ends today": "A 7-day trial ends today",
        "you can continue chatting with our HealthGPT if you become a member.": "You can continue chatting with our HealthGPT if you become a member.",
        "persona": "Personalizing responses for {name}",
        "button:change": "Change"
    },
    "vi": {
        "toast:copied to clipboard": "Thông tin của bot đã được sao chép",
        "toast:copy not supported": "Chức năng sao chép không được hỗ trợ trong trình duyệt của bạn",
        "unlimited chat": "Chat không giới hạn đến ngày {expireTime}",
        "a 7-day trial starts successfully!": "Đăng ký thành công 7 ngày dùng thử miễn phí!",
        "you can now start chatting with our HealthGPT.": "Bạn đã có thể chat với HealthGPT ngay bây giờ.",
        "a 7-day trial ends today": "Hết hạn 7 ngày dùng thử miễn phí",
        "you can continue chatting with our HealthGPT if you become a member.": "Bạn có thể tiếp tục chat với HealthGPT nếu trở thành thành viên.",
        "persona": "Personalizing responses for {name}",
        "button:change": "Đổi"
    }
}
</i18n>
