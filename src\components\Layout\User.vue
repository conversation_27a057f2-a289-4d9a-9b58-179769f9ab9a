<script setup lang="ts">
import type { NavItem } from '~/models/component/navigation-bar'
defineProps<{ navItems: NavItem[] }>()
</script>

<template>
    <div class="flex min-h-screen flex-col sm:flex-row">
        <!-- Sticky Sidebar (hidden on small screens, visible on sm and up) -->
        <LayoutSideNavigation :nav-items="navItems">
            <template #start="{ isCollapsed }">
                <slot name="sideNavStart" :is-collapsed="isCollapsed" />
            </template>
            <template #end="{ isCollapsed }">
                <slot name="sideNavEnd" :is-collapsed="isCollapsed" />
            </template>
        </LayoutSideNavigation>

        <!-- Main Content -->
        <main
            class="no-scrollbar flex-1 overflow-y-auto overflow-x-hidden pb-20 sm:p-4 lg:p-8"
        >
            <slot name="main" />
        </main>

        <!-- Bottom Navigation (visible on small screens, hidden on sm and up) -->
        <LayoutNewBottomBar :nav-items="navItems" />
    </div>
</template>
