import type { ICalendarItem } from '~/models'
import { GenericEndpoint } from '~/models'

export async function useProviderCalendar() {
    const { $fetchWellcare } = useNuxtApp()
    // Fetch user info
    const { user } = useUserInfo({
        scope: ['_id', 'avatar', 'name', 'dob', 'gender'],
    })

    // Fetch
    return useAsyncData<{
        results: ICalendarItem[]
    }>('provider-calendar', () =>
        $fetchWellcare(
            `${GenericEndpoint.CALENDAR_ITEM}/user/${user.value._id}`,
            {
                method: 'GET',
                query: {
                    filter: {
                        type: {
                            $in: [
                                'booking',
                                'outofoffice',
                                'onduty',
                                'bookingoncall',
                            ],
                        },
                        active: true,
                        $or: [
                            {
                                'booking.to.date': { $gte: new Date() },
                                'booking.to.never': { $ne: true },
                                'type': 'booking'
                            },
                            {
                                'booking.to.never': true,
                                'type': 'booking'
                            },
                            {
                                'booking.to.date': { $gte: new Date() },
                                'type': 'outofoffice'
                            }
                        ],
                    },
                    limit: 100,
                    fields: '_id,calendar,type,title,allDay,startTime,endTime,booking,state',
                },
            },
        ),
    )
}
