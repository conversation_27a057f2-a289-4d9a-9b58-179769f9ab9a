<script setup lang="ts">
// import PagePatientMembershipQA from '~/components/Page/Patient/Membership/QA.vue'
import {
    DOMAIN_CHECKOUT_PRODUCTION,
    DOMAIN_CHECKOUT_SANDBOX,
} from '~/constants'

// Define the page meta data for the nested page
definePageMeta({
    layout: 'nested-page',
})

const runtimeConfig: any = useRuntimeConfig()

const { t } = useI18n()
const dayjs = useDayjs()

const layoutStore = useLayoutStore()
layoutStore.setTitle(t('membership-details'))
const { isMembership, subscriptions } = useMembership()

const endAt = computed(() => {
    if (subscriptions && subscriptions.value?.length > 0)
        return subscriptions.value[0]?.endAt
    return ''
})

const { user } = useUserInfo({ scope: ['avatar', 'name'] })

// const isAvatar = computed<boolean>(() => !!user.value?.avatar?.url)
const avatarUrl = computed<string>(
    () =>
        user.value?.avatar?.url ||
        `https://ui-avatars.com/api/?name=${user.value?.name}`,
)

// const tabs = ref([
//     { title: 'Tab 1', content: PagePatientMembershipQA, value: '0' },
//     { title: 'Tab 2', content: undefined, value: '1' },
//     { title: 'Tab 3', content: undefined, value: '2' },
// ])
const handleJoinNow = async () => {
    const appEnv = runtimeConfig.public?.app?.env || 'sandbox'
    const domain =
        appEnv === 'production'
            ? DOMAIN_CHECKOUT_PRODUCTION
            : DOMAIN_CHECKOUT_SANDBOX

    await navigateTo(`${domain}/checkout/membership`, { external: true })
}
</script>

<template>
    <div>
        <div class="mx-auto max-w-xl space-y-6 p-4">
            <div
                v-if="isMembership"
                class="relative overflow-hidden rounded-lg bg-gradient-to-b from-teal-400 to-primary-400/50 p-4 text-white"
            >
                <div class="relative z-10 mt-3">
                    <div class="mb-3 flex items-center space-x-3">
                        <div
                            class="relative flex flex-col items-center rounded-2xl"
                        >
                            <NuxtImg
                                class="absolute -top-6 z-10 size-6"
                                src="/images/crown.png"
                            />
                            <Avatar
                                class="size-12 rounded-full border-2 border-yellow-300 object-cover p-[1px]"
                                :image="avatarUrl"
                                :alt="user?.name"
                                shape="circle"
                            />
                        </div>
                        <div>
                            <Tag
                                class="bg-yellow-50 text-xs font-medium text-yellow-500"
                            >
                                {{ t('active-member') }}
                            </Tag>
                            <h3 class="mt-1">Wellcare Membership</h3>
                        </div>
                    </div>
                    <div class="mb-2 text-sm font-bold text-white/90">
                        <span>{{ t('Valid to') }}: </span>
                        <span>
                            {{ dayjs(endAt).format('DD/MM/YYYY') }}
                        </span>
                    </div>
                    <Button class="border-none bg-primary-400 outline-none">{{
                        t('renew')
                    }}</Button>
                </div>
            </div>

            <div>
                <div class="pb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        {{ t('membership-benefits') }}
                    </h3>
                </div>

                <Button
                    v-if="!isMembership"
                    :label="t('join-now')"
                    class="mt-3 hidden border-none bg-primary-400 outline-none"
                    @click="handleJoinNow"
                />
            </div>
        </div>
        <!--      <Tabs value="0" class="mt-8 px-4">
            <TabList>
                <Tab v-for="tab in tabs" :key="tab.title" :value="tab.value">{{
                    tab.title
                }}</Tab>
            </TabList>
            <TabPanels
                :pt="{
                    root: 'pt-4 pl-1 pr-1',
                }"
            >
                <TabPanel
                    v-for="tab in tabs"
                    :key="tab.value"
                    :value="tab.value"
                >
                    <component :is="tab.content" v-if="tab.content"></component>
                </TabPanel>
            </TabPanels>
        </Tabs> -->
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "membership-details": "Healthcare benefits",
        "membership-benefits": "Membership Benefits",
        "Valid to": "VALID TO",
        "active-member": "Active Member"
    },
    "vi": {
        "membership-details": "Quyền lợi chăm sóc sức khỏe",
        "membership-benefits": "Quyền lợi thành viên",
        "Valid to": "HIỆU LỰC ĐẾN",
        "renew": "Gia hạn",
        "active-member": "Đã kích hoạt"
    }
}
</i18n>
