<script lang="ts" setup>
import { definePageMeta } from '#imports'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const { t } = useI18n()
const callHandle = useCallHandle()
const layoutStore = useLayoutStore()
const { user } = useUserInfo({ scope: ['_id', 'isProvider', 'phone'] })
layoutStore.setTitle(t('title'))

const userId = computed(() => user.value?._id ?? '')
// const userPhone = computed(() => user.value?.phone ?? '')
const providerId = ref<string>('')
const consultationId = ref<string>('')
const providerAvatar = ref<string>('')
const isOpenRating = ref<boolean>(false)

const handleOpenMr = (consultation: any) => {
    navigateTo(`/patient/consultation/${consultation._id}`)
}

const callVideoHandler = (data: any) => {
    callHandle.open('video', {
        _id: data._id,
        conversation: data.conversationId,
        providerUser: data.provider?.user,
        user: data.user._id,
    })
}
const callVoiceHandler = (data: any) => {
    callHandle.open('voice', {
        _id: data._id,
        conversation: data.conversationId,
        providerUser: data.provider?.user,
        user: data.user._id,
    })
}

const handleOpenRating = (selectedConsultation: any) => {
    isOpenRating.value = true
    consultationId.value = selectedConsultation._id
    providerId.value = selectedConsultation?.provider?._id
    providerAvatar.value = selectedConsultation?.provider?.avatar?.url
}
</script>

<template>
    <div class="mx-4 mt-6">
        <WConsultationHistory
            v-if="userId"
            :user-id="userId"
            :showing-quantity="10"
            :showing-load-btn="true"
            :filter="{
                user: userId,
                type: {
                    $in: ['indepth', 'question'],
                },
            }"
            @click:call="callVoiceHandler"
            @click:open-mr="handleOpenMr"
            @click:call-video="callVideoHandler"
            @click:open-rating="handleOpenRating"
        />

        <WConsultationRating
            :visible="isOpenRating"
            :provider-id="providerId"
            :consultation-id="consultationId"
            :provider-avatar="providerAvatar"
            @submitted="isOpenRating = false"
            @skip-rating="isOpenRating = false"
            @update:visible="isOpenRating = false"
        />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "title": "Consultations"
    },
    "vi": {
        "title": "Các cuộc tư vấn"
    }
}
</i18n>
