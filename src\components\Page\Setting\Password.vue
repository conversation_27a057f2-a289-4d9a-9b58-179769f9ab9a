<script setup lang="ts">
import { useOidc, useAppAuth } from '#imports'
import { <PERSON>rowser } from '@capacitor/browser'
import { Capacitor } from '@capacitor/core'
import { deepLink } from '~/constants';

const { getOidcClient } = useOidc()
const { getUserManager } = useAppAuth()

defineEmits([''])

const resetPassword = async () => {
    if (Capacitor.isNativePlatform()) {
        const request = await getOidcClient.createSigninRequest({
            request_type: 'si:r',
            redirect_uri: deepLink.resetPassword,
            extraQueryParams: {
                kc_action: 'UPDATE_PASSWORD',
            },
        })
        await Browser.open({
            url: request.url,
            windowName: '_self',
            presentationStyle: 'popover',
        })
    } else {
        await getUserManager.signinRedirect({
            extraQueryParams: {
                kc_action: 'UPDATE_PASSWORD',
            },
        })
    }
}

onMounted(() => {
    resetPassword()
})
</script>

<template>
    <div class="text-center">
        <h2>Change my password</h2>
        <!-- <div class="space-y-4">
            <Password
                v-model="value1"
                :feedback="false"
                toggle-mask
                size="large"
            />
            <Password v-model="value2" />
            <Password v-model="value3" />
        </div> -->
    </div>
</template>
