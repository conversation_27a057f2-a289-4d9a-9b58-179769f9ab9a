<script setup lang="ts">
import { definePageMeta } from '#imports'

definePageMeta({
    middleware: 'auth-capacitor',
    layout: 'nested-page',
})

const { t } = useI18n()
const route = useRoute()
const layoutStore = useLayoutStore()

const userId = computed<string>(() => (route.params?.id as string) || '')

onMounted(() => {
    layoutStore.setTitle(t('vaccination-detail'))
})
onUnmounted(() => {
    layoutStore.setTitle('')
})
</script>
<template>
    <div class="flex flex-col px-4 py-6">
        <WVaccinationDetail v-if="userId" :user-id="userId" />
    </div>
</template>
