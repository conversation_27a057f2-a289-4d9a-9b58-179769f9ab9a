import { useLazyAsyncData } from '#imports'
export function useGetDoctorCalendar(userId: string) {
    // const fetchUrl: string = `https://api.mhealthvn.com/calendar/calendar-item/user/${userId}?limit=10&skip=0&locale=vi&fields=allDay startTime endTime booking title&sort=_id&populate=[]&filter={"active":true,"type":{"$in":["onduty","outofoffice","booking","bookingoncall"]},"state":"scheduled"}`
    const { data, execute } = useLazyAsyncData(() =>
        $fetch(`/api/calendar/calendar-item/user/${userId}`, {
            query: {
                limit: 10,
                skip: 0,
                locale: 'vi',
                fields: 'allDay startTime endTime booking title',
                sort: '_id',
                populate: [],
                filter: {
                    active: true,
                    type: {
                        $in: [
                            'onduty',
                            'outofoffice',
                            'booking',
                            'bookingoncall',
                        ],
                    },
                    state: 'scheduled',
                },
            },
        }),
    )
    return { data, execute }
}
