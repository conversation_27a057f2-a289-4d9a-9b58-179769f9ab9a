import type { ICalendarItem } from '~/models'
import { GenericEndpoint } from '~/models'

export async function useCalender() {
    const { $fetchWellcare } = useNuxtApp()
    // Fetch user info
    const { user } = useUserInfo({
        scope: ['_id', 'avatar', 'name', 'dob', 'gender'],
    })

    // Fetch
    return useAsyncData<{
        results: ICalendarItem[]
    }>('provider-calendar', () =>
        $fetchWellcare(
            `${GenericEndpoint.CALENDAR_ITEM}/user/${user.value._id}`,
            {
                method: 'GET',
                query: {
                    filter: {
                        type: {
                            $in: [
                                'booking',
                                'outofoffice',
                                'onduty',
                                'bookingoncall',
                            ],
                        },
                        active: true,
                    },
                    size: 100,
                    fields: '_id,calendar,type,title,allDay,startTime,endTime,booking',
                },
            },
        ),
    )
}
