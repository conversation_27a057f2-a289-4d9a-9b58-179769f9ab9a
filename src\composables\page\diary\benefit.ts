export const useBenefitPersonalDoctor = () => {
    const { $fetchWellcare } = useNuxtApp()
    const loading = ref(false)
    const error = ref<Error | null>(null)
    const data = ref<null>(null)

    const fetchBenefit = async (userId: string, patientId: string) => {
        loading.value = true
        error.value = null

        try {
            const populate = encodeURIComponent(
                JSON.stringify([{ path: 'related' }, { path: 'provider' }]),
            )

            const response = await $fetchWellcare(
                `/user-management/personal-doctor/doctor/user/${userId}/patient/${patientId}`,
                {
                    method: 'GET',
                    query: {
                        populate,
                    },
                },
            )

            data.value = response
            return response
        } catch (err) {
            error.value = err as Error
            throw err
        } finally {
            loading.value = false
        }
    }

    return {
        data,
        loading,
        error,
        fetchBenefit,
    }
}
