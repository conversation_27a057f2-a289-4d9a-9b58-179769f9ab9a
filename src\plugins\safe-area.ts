import { defineNuxtPlugin } from '#app'
import { SafeArea } from 'capacitor-plugin-safe-area'

export default defineNuxtPlugin(() => {
    const appStore = useAppStore()

    async function updateSafeArea() {
        const safeArea = await SafeArea.getSafeAreaInsets().then(
            ({ insets }) => insets,
        )
        const statusBarHeight = await SafeArea.getStatusBarHeight().then(
            ({ statusBarHeight }) => statusBarHeight,
        )
        appStore.setSafeArea({ ...safeArea, statusBarHeight })
    }

    if (import.meta.client) {
        updateSafeArea() // Initial update
        window.addEventListener('resize', updateSafeArea)
    }
})
