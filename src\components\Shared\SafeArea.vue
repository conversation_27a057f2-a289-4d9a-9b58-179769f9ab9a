<template>
  <div class="min-h-screen w-full" :style="safeAreaStyles">
    <slot />
  </div>
</template>

<script setup lang="ts">
import { computed } from '#imports'
import { Capacitor } from '@capacitor/core'
import { onMounted, ref } from 'vue'
import { useAppStore } from '~/stores/app'

const store = useAppStore()
const safeArea = computed(() => store.safeArea)

const safeAreaStyles = ref({})

onMounted(async () => {
  if (Capacitor.isNativePlatform()) {
    try {
      safeAreaStyles.value = {
        paddingTop: `${safeArea.value.top}px`,
        paddingBottom: `${safeArea.value.bottom}px`,
        paddingLeft: `${safeArea.value.left}px`,
        paddingRight: `${safeArea.value.right}px`,
      }
    } catch (error) {
      console.error('Error getting status bar insets:', error)
    }
  }
})
</script>
