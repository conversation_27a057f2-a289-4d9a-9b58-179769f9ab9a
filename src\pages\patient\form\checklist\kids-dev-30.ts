import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'YLs14guIfS',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2jt4p8nbep',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON>c mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 30 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. <PERSON><PERSON><PERSON> sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2jt4ypt8k',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_media',
                            tag: 'img',
                            src: 'https://todaysparent.mblycdn.com/uploads/tp/2020/03/GettyImages-1322307899.jpg',
                            alt: 'Nice picture',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2jt6zibs38',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Chơi cùng những đứa trẻ khác và đôi khi đùa với chúng',
                            component: 'multiple_choice',
                            description: 'Select one option from the list.',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jt6zibs38',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jt7z6oj2m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cho cha mẹ thấy mình có thể làm được gì bằng cách nói, “Mẹ mẹ xem này!”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jt7z6oj2m',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jt8viq0yt',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Làm được những việc đơn giản khi được yêu cầu, như cùng nhặt và dọn dẹp đồ chơi khi cha mẹ nói, “Đến giờ dọn dẹp rồi bé.”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jt8viq0yt',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jta6zx9q9',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói được khoảng 50 từ vựng',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jta6zx9q9',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtaqo5z9',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói được cùng lúc hai từ trở lên bằng một hành động  từ như “Con chó chạy”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtaqo5z9',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtcj5moh',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Kể tên các đồ vật trong sách khi cha mẹ chỉ và hỏi bé, "Đây là cái gì?"',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtcj5moh',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtdfd0a1c',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nói và dùng được những từ như “con” hoặc “chúng mình”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtdfd0a1c',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jtesx6d2',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Dùng đồ vật để chơi giả vờ, như cho búp bê ăn một khối gỗ, như thể đó là đồ ăn vậy.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtesx6d2',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtfc3tv0m',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Thực hiện được các kỹ năng giải quyết vấn đề đơn giản, như đứng lên một chiếc ghế đẩu nhỏ để với lấy thứ gì đó.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtfc3tv0m',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtfvclhp',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Làm được theo các hướng dẫn gồm hai bước như “Đặt đồ chơi xuống và đóng cửa lại.”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtfvclhp',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtgdml0v',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Cho thấy bé đã biết ít nhất một màu sắc, như chỉ vào một bút chì màu đỏ khi được hỏi, “Cái nào màu đỏ?”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtgdml0v',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jth7lwhlr',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Dùng tay để vặn đồ vật như xoay tay nắm cửa hoặc tháo nắp chai.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: '',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jth7lwhlr',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jthm7plik',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tự cởi quần áo được, như một chiếc quần rộng hoặc một chiếc áo khoác đã mở nút.',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jthm7plik',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jwacl5tnk',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhảy lên khỏi mặt đất bằng cả hai chân',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'option1',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jwacl5tnk',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jwcsnhzwj',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Lật giở từng trang sách khi được cha mẹ đọc cho nghe',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'V',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jwcsnhzwj',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    createdBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    updatedBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    title: '30 tháng',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
})
