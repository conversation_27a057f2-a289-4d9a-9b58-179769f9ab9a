<script setup lang="ts">
import type { IProviderProfile } from '~/models'
import { push } from 'notivue'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

defineProps({
    profile: {
        type: Object as PropType<IProviderProfile>,
        required: true,
    },
})

const { t } = useI18n({ useScope: 'local' })

const { user } = useUserInfo({
    scope: ['provider.title', 'provider.slug', 'provider.name'],
})

const transformSpecialties = (profile: IProviderProfile) => {
    const firstThreeSpecialties = profile.specialties?.slice(0, 3)
    return firstThreeSpecialties
        ?.map((specialty: any) => specialty?.name || specialty)
        .join(', ')
}

const { isClipboardSupported, isCopied, shareSource } = useActionToolkit()

const onShareProfile = () => {
    shareSource(
        `${(user.value?.provider as any)?.title} ${(user.value?.provider as any)?.name}`,
        '',
        'https://khamtuxa.wellcare.vn/bac-si/' + (user.value?.provider as any)?.slug,
        () =>
            push.success({
                message: t('profile.copied-to-clipboard'),
            }),
        () =>
            push.error({
                message: t('profile.copy-error'),
            }),
    )
}

watch(isClipboardSupported, () => {
    if (!isClipboardSupported.value) {
        push.error({
            message: t('profile.clipboard-not-supported'),
        })
    }
})

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)

const supportUrl = 'https://zalo.me/**********'
const requestEdit = () => {
    navigateTo(supportUrl, {
        external: true,
        open: {
            target: '-blank',
        },
    })
}
</script>

<template>
    <div class="flex flex-1 flex-col gap-3">
        <div class="card p-3">
            <div class="flex gap-3">
                <NuxtImg
                    v-if="profile?.avatar"
                    :src="profile?.avatar"
                    class="mb-2 h-20 w-20 rounded-lg object-cover"
                    sizes="300px"
                />
                <div>
                    <h4
                        v-if="profile?.title && profile?.name"
                        class="font-semibold"
                    >
                        {{ profile?.title }}. {{ profile?.name }}
                    </h4>
                    <p class="mt-1 text-sm">
                        {{ transformSpecialties(profile) }}
                    </p>
                </div>
            </div>

            <!-- Highlight -->
            <div v-if="profile.highlight">
                <p
                    class="mt-4 flex items-center text-lg font-medium text-gray-700"
                >
                    <iconify-icon
                        class="mr-1 text-zinc-600"
                        icon="uim:user-md"
                    />
                    <span>{{ t('profile.about') }}</span>
                </p>
                <p class="px-4">{{ profile.highlight }}</p>
            </div>

            <!-- specialties -->
            <div v-if="profile.specialties && profile.specialties.length > 0">
                <div
                    class="mt-4 flex items-center text-lg font-medium text-gray-700"
                >
                    <iconify-icon
                        class="mr-2 text-zinc-600"
                        icon="uim:briefcase"
                    />
                    <p>
                        {{ t('profile.specialties') }}
                    </p>
                </div>

                <SharedProviderProfileDataSection
                    title=""
                    content-type="Chips"
                    :content="profile.specialties"
                    class="mt-0 !p-0"
                />
            </div>

            <!-- conditions -->
            <div v-if="profile?.conditions && profile?.conditions?.length > 0">
                <div
                    class="mt-4 flex items-center text-lg font-medium text-gray-700"
                >
                    <iconify-icon class="mr-1" icon="uim:user-md" />
                    <p>{{ t('profile.conditions') }}</p>
                </div>

                <SharedProviderProfileDataSection
                    title=""
                    content-type="Chips"
                    :content="profile.conditions"
                    class="mt-0 !p-0"
                />
            </div>

            <!-- office -->
            <template v-if="profile?.clinics && profile?.clinics?.length > 0">
                <p
                    class="mt-4 flex items-center text-lg font-medium text-gray-700"
                >
                    <iconify-icon
                        class="mr-2 inline-block text-lg font-medium"
                        icon="uim:clinic-medical"
                    />
                    <span>{{ t('profile.clinic') }}</span>
                </p>
                <ul>
                    <li
                        v-for="(clinic, index) in profile?.clinics"
                        :key="index"
                    >
                        {{ clinic }}
                    </li>
                </ul>
            </template>
        </div>

        <SharedProviderProfileDataSection
            title=""
            content-type="Metrics"
            :content="profile.metrics"
            class="mt-0 !p-0"
        />

        <!-- Request update -->
        <div class="flex flex-row items-center justify-end gap-3">
            <Button
                v-if="!hideSupportContact"
                :label="t('profile.request-update')"
                severity="secondary"
                @click="requestEdit"
            />

            <Button
                :label="isCopied ? t('profile.copied') : t('profile.share')"
                @click="onShareProfile"
            />
        </div>
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "profile": {
            "about": "About",
            "clinic": "Office",
            "specialties": "Specialties",
            "conditions": "Conditions and diseases",
            "organization": "Organization",
            "request-update": "edit",
            "share": "Share",
            "copied": "Copied!",
            "copied-to-clipboard": "Profile URL copied to clipboard",
            "copy-error": "Error encountered while trying to copy URL",
            "clipboard-not-supported": "Clipboard functionality is not supported on this device or browser",
            "no-data": "No infomation"
        }
    },
    "vi": {
        "profile": {
            "about": "Giới thiệu",
            "clinic": "Nơi công tác",
            "specialties": "Chuyên khoa",
            "conditions": "Các điều kiện và bệnh",
            "organization": "Tổ chức",
            "request-update": "sửa",
            "share": "Chia sẻ",
            "copied": "Đã sao chép!",
            "copied-to-clipboard": "URL hồ sơ đã được sao chép vào clipboard",
            "copy-error": "Đã gặp lỗi khi cố gắng sao chép URL",
            "clipboard-not-supported": "Chức năng clipboard không được hỗ trợ trên thiết bị hoặc trình duyệt này",
            "no-data": "Chưa có thông tin"
        }
    }
}
</i18n>
