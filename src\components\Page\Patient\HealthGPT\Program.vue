<script lang="ts" setup>
interface IPackage {
    icon: string
    name: string
    description: string
    'text-color': string
    'border-color': string
    onClick: () => void
}

defineProps({
    packages: {
        type: Array as PropType<IPackage[]>,
        default: () => [],
    },
})

const visible = defineModel<boolean>()

const { t } = useI18n()
</script>

<template>
    <Dialog
        :v-model:visible="visible"
        modal
        :header="t('title')"
        pt:root:class="mx-4 border-none"
        pt:header:class="flex items-center justify-between shrink-0 px-6 py-4 rounded-tl-lg rounded-tr-lg bg-primary text-white"
        pt:title:class="font-semibold text-2xl"
        pt:mask:class="backdrop-blur-sm"
        pt:pcclosebutton:root:class="inline-flex items-center justify-center w-10 h-10 rounded-full !bg-transparent !text-white focus:outline-none focus:ring-0"
        :close-on-escape="false"
    >
        <p class="mt-4">
            {{ t('overview') }}
        </p>

        <div v-for="i in 3" :key="i" class="flex items-center gap-2 pt-2">
            <i class="pi pi-check-circle text-primary" />
            <p>{{ t(`benefit:${i}`) }}</p>
        </div>

        <Card
            v-for="(item, index) in packages"
            :key="index"
            pt:root:class="py-0 rounded shadow-none hover:cursor-pointer hover:bg-slate-50 dark:hover:bg-slate-700/50"
            pt:body:class="mt-4 p-0"
            @click="item.onClick"
        >
            <template #content>
                <div
                    :class="`flex items-center justify-between gap-2 rounded border p-4 ${item['text-color']} ${item['border-color']} `"
                >
                    <div>
                        <div class="flex items-center gap-2">
                            <i :class="item.icon" />
                            <p class="font-semibold">
                                {{ t(`${item.name}:${index + 1}`) }}
                            </p>
                        </div>
                        <p class="text-sm">
                            {{ t(`${item.description}:${index + 1}`) }}
                        </p>
                    </div>
                    <i class="pi pi-chevron-right" />
                </div>
            </template>
        </Card>
    </Dialog>
</template>

<i18n lang="yaml">
en:
    'title': 'HealthGPT Chat'
    'benefit:1': 'Medical, Lifestyle and Mental Health.'
    'benefit:2': 'AI technology, expanded from GPT-4.0.'
    'benefit:3': 'Conversations can be submitted to doctors for verification, avoiding issues like bias and hallucination.'
    'overview': 'HealthGPT Chatbots are AI-powered virtual assistants that provide answers based on carefully selected resources.'
    'program:name:1': 'Membership (1 year)'
    'program:name:2': '7-day Free Trial'
    'program:description:1': 'Apply code of your organization, or Purchase on our website'
    'program:description:2': 'Unlimited Q&A sessions with all HealthGPT chatbots.'
vi:
    'title': 'Chat với HealthGPT'
    'overview': 'Hỏi đáp 24/7 với trợ lý AI dựa trên các nguồn kiến thức đã chọn lọc của Wellcare.'
    'benefit:1': 'Chủ đề Sức khỏe, Lối sống và Tâm lý.'
    'benefit:2': 'Công nghệ AI, mở rộng từ GPT-4.0'
    'benefit:3': 'Có thể gửi đoạn hội thoại và nhận ý kiến đánh giá từ bác sĩ và chuyên gia.'
    'program:name:1': 'Gói Thành viên (1 năm)'
    'program:name:2': 'Miễn phí Dùng thử 7 ngày'
    'program:description:1': 'Nhập code từ doanh nghiệp, hoặc Mua trên website'
    'program:description:2': 'Không giới hạn số lượt Hỏi - Đáp với tất cả các HealthGPT chatbot.'
</i18n>
