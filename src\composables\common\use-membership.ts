import { computed, ref, useAsyncData, useNuxtApp, watch } from '#imports'

/**
 * Composable to check and manage membership status.
 * Optionally, it can periodically refresh membership data based on a provided timeout.
 *
 * @param {number} [timeout] - Optional interval (in milliseconds) for auto-refreshing membership status.
 * @returns {object} - Contains:
 *   - `loading`: Computed<boolean> indicating the loading state of the request.
 *   - `isMembership`: Ref<boolean> indicating if the user has an active membership.
 *   - `subscriptions`: Ref<any> holding subscription details if membership is active.
 *   - `checkMembership`: Function to manually trigger membership check.
 *   - `clearIntervalFunction`: Function to clear the periodic refresh interval if set.
 *
 * @example
 * // Usage example in a Vue component
 *
 * import { onBeforeUnmount, watch } from 'vue'
 * import { useMembership } from '@/composables/useMembership'
 *
 * // Manual membership check
 * const { isMembership, checkMembership } = useMembership()
 *
 * const onManualCheckMembership = () => {
 *    checkMembership() // Manually trigger membership check
 * }
 *
 * // Set up periodic check with a 60-second interval
 * const { isMembership, clearIntervalFunction } = useMembership(60000)
 *
 * // Watch for membership status changes
 * watch(isMembership, (newStatus) => {
 *    console.log("Membership status updated:", newStatus ? "Active" : "Inactive")
 * })
 *
 * // Clear the periodic check interval when the component is destroyed
 * onBeforeUnmount(() => {
 *    clearIntervalFunction()
 * })
 */
export function useMembership(timeout?: number) {
    const { $fetchWellcare } = useNuxtApp()
    const isMembership = ref<boolean>(false)
    const subscriptions = ref<any>(null)
    let intervalId: NodeJS.Timeout | null = null

    // Lazy fetch membership data
    const {
        execute: checkMembership,
        status,
        data,
    } = useAsyncData('subscription', () =>
        $fetchWellcare('/membership/member/subscriptions', {
            method: 'GET',
        }),
    )

    // Watch data for changes to update membership status
    watch(data, () => {
        const code = data.value?.code
        const _subscriptions = data.value?.results

        switch (code) {
            case 200:
                isMembership.value = true
                subscriptions.value = _subscriptions
                break
            default:
                isMembership.value = false
                subscriptions.value = null
                break
        }
    })

    // Set up periodic refresh if timeout is provided
    if (timeout) {
        intervalId = setInterval(checkMembership, timeout)
    }

    // Clear the interval if needed
    const clearIntervalFunction = () => {
        if (intervalId) {
            clearInterval(intervalId)
            intervalId = null
        }
    }

    const loading = computed(() => status.value === 'pending')

    return {
        loading,
        isMembership,
        subscriptions,
        checkMembership,
        clearIntervalFunction,
    }
}
