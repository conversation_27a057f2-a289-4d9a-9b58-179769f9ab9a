<script lang="ts" setup>
import type { IHealthProgramCalendar } from '~/models'

interface ISubtitle {
    content: string
    position: 'top' | 'bottom'
}

const props = defineProps({
    activeTab: {
        type: String,
        required: true,
    },
    times: {
        type: Array as PropType<IHealthProgramCalendar[]>,
        required: true,
    },
    subtitle: {
        type: Object as PropType<ISubtitle>,
        required: true,
    },
})

const emit = defineEmits(['on-change-tab'])

let timer: number | null = null

const onChangeTab = (payload: any) => {
    emit('on-change-tab', payload)
}

onMounted(() => {
    timer = window.setTimeout(() => {
        const tab = document.getElementById(`tab-${props.activeTab}`)
        if (tab) {
            tab.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'center',
            })
        }
    }, 800)
})

onUnmounted(() => {
    if (timer !== null) {
        clearTimeout(timer)
        timer = null
    }
})
</script>

<template>
    <Tabs
        id="tabs"
        :value="activeTab"
        scrollable
        :show-navigators="false"
        @update:value="onChangeTab"
    >
        <TabList
            :pt="{
                root: 'bg-white',
            }"
        >
            <Tab
                v-for="(time, index) in times"
                :id="`tab-${time?.label}`"
                :key="index"
                :value="time.label"
                :disabled="!time.isActive"
            >
                <span
                    v-if="
                        activeTab === time.label && subtitle.position === 'top'
                    "
                    class="absolute left-1 top-1 text-[10px]"
                >
                    {{ subtitle.content }}
                </span>
                <span>{{ time.value }}</span>
                <span
                    v-if="
                        activeTab === time.label &&
                        subtitle.position === 'bottom'
                    "
                    class="absolute bottom-1 right-1 text-[10px]"
                >
                    {{ subtitle.content }}
                </span>
                <div
                    v-if="time.isToday"
                    class="absolute -right-1 top-0 h-2.5 w-2.5 rounded-full bg-red-500"
                />
            </Tab>
        </TabList>
        <div class="h-[calc(100vh-150px)] overflow-y-auto">
            <slot name="content" />
        </div>
    </Tabs>
</template>

<style scoped>
#tabs button[id^='tab'] {
    border-width: 1px 1px 4px 1px;
    border-color: transparent;
}

#tabs button[id^='tab'][aria-selected='true'] {
    border-color: #009688;
    color: #009688;
}

#tabs button[id^='tab'][aria-selected='true']::after {
    content: 'week';
    position: absolute;
    top: -20px;
    left: 0;
    opacity: 0;
    transform: translateY(-10px);
    transition:
        opacity 0.5s ease,
        transform 0.5s ease;
    color: #009688;
    font-size: 14px;
}
</style>
