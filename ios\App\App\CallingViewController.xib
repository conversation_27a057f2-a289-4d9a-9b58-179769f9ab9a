<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="21507" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21505"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner" customClass="CallingViewController" customModule="WellcareCapacitorStringee">
            <connections>
                <outlet property="backButton" destination="t5N-Zh-Z9K" id="qCV-RQ-85f"/>
                <outlet property="blurView" destination="YI4-he-cWp" id="rex-iX-oKX"/>
                <outlet property="btAnswer" destination="SJo-W2-ceo" id="nF6-FV-rtK"/>
                <outlet property="btEnd" destination="nIR-xU-jiG" id="rAb-KE-dPl"/>
                <outlet property="btMute" destination="eyq-tC-duC" id="92Z-bK-JBF"/>
                <outlet property="btReject" destination="Yim-Ux-Vyh" id="x4M-uR-ckN"/>
                <outlet property="btSpeaker" destination="hEh-W0-vkz" id="QZT-j1-4ci"/>
                <outlet property="containerView" destination="PDi-UU-Wb6" id="uHn-Ec-AR5"/>
                <outlet property="ivDisplayImage" destination="ReB-XB-Y8C" id="Rwu-Ds-Lco"/>
                <outlet property="lbName" destination="oYs-Pt-AXb" id="FcW-uL-Oel"/>
                <outlet property="lbStatus" destination="XSJ-gX-fez" id="HP3-zH-DC9"/>
                <outlet property="localView" destination="kNv-x5-ttk" id="gLk-BE-oqj"/>
                <outlet property="remoteView" destination="7dt-Pn-wXm" id="pIX-eT-OnJ"/>
                <outlet property="view" destination="i5M-Pr-FkT" id="sfx-zR-JGt"/>
            </connections>
        </placeholder>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view clearsContextBeforeDrawing="NO" contentMode="scaleToFill" id="i5M-Pr-FkT">
            <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7dt-Pn-wXm">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="kNv-x5-ttk">
                    <rect key="frame" x="10" y="50" width="124" height="186.5"/>
                    <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="width" secondItem="kNv-x5-ttk" secondAttribute="height" multiplier="720:1080" id="mVz-xJ-v23"/>
                    </constraints>
                </view>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="PDi-UU-Wb6" userLabel="ContentView">
                    <rect key="frame" x="0.0" y="48" width="414" height="794"/>
                    <subviews>
                        <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="ReB-XB-Y8C">
                            <rect key="frame" x="147" y="80" width="120" height="120"/>
                            <color key="backgroundColor" red="0.57647058819999997" green="0.37647058820000001" blue="1" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                            <accessibility key="accessibilityConfiguration">
                                <bool key="isElement" value="YES"/>
                            </accessibility>
                            <constraints>
                                <constraint firstAttribute="height" constant="120" id="UDE-b5-9Wc"/>
                                <constraint firstAttribute="width" constant="120" id="WAS-A0-XyO"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="layer.cornerRadius">
                                    <integer key="value" value="60"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </imageView>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="IVv-Dn-v5x">
                            <rect key="frame" x="97" y="387" width="220" height="120"/>
                            <subviews>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="eyq-tC-duC">
                                    <rect key="frame" x="10" y="10" width="70" height="70"/>
                                    <constraints>
                                        <constraint firstAttribute="height" constant="70" id="0Z9-Sv-M1B"/>
                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="70" id="I4A-Va-akg"/>
                                        <constraint firstAttribute="width" constant="70" id="m4t-l6-iyw"/>
                                        <constraint firstAttribute="width" secondItem="eyq-tC-duC" secondAttribute="height" multiplier="1:1" id="tWP-Ur-GAW"/>
                                    </constraints>
                                    <state key="normal" backgroundImage="icon_mute"/>
                                    <connections>
                                        <action selector="muteTapped:" destination="-1" eventType="touchUpInside" id="yUa-4k-gv7"/>
                                    </connections>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mute" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9UL-PL-ukM">
                                    <rect key="frame" x="27" y="90" width="36" height="18"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="hEh-W0-vkz">
                                    <rect key="frame" x="140" y="10" width="70" height="70"/>
                                    <constraints>
                                        <constraint firstAttribute="width" relation="greaterThanOrEqual" constant="70" id="8cu-Jc-dh9"/>
                                        <constraint firstAttribute="width" secondItem="hEh-W0-vkz" secondAttribute="height" multiplier="1:1" id="gAn-km-CSW"/>
                                        <constraint firstAttribute="width" constant="70" id="wqM-J2-ffL"/>
                                        <constraint firstAttribute="height" constant="70" id="zYI-hv-fBX"/>
                                    </constraints>
                                    <state key="normal" backgroundImage="icon_speaker"/>
                                    <connections>
                                        <action selector="speakerTapped:" destination="-1" eventType="touchUpInside" id="dsq-Ze-5WB"/>
                                    </connections>
                                </button>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Speaker" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WvZ-YD-MZ0">
                                    <rect key="frame" x="146" y="90" width="58" height="18"/>
                                    <fontDescription key="fontDescription" type="system" weight="medium" pointSize="15"/>
                                    <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                                    <nil key="highlightedColor"/>
                                </label>
                            </subviews>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="calibratedWhite"/>
                            <constraints>
                                <constraint firstItem="9UL-PL-ukM" firstAttribute="centerX" secondItem="eyq-tC-duC" secondAttribute="centerX" id="A5x-hg-Kh0"/>
                                <constraint firstAttribute="trailing" secondItem="hEh-W0-vkz" secondAttribute="trailing" constant="10" id="DQP-bV-DxW"/>
                                <constraint firstItem="hEh-W0-vkz" firstAttribute="centerY" secondItem="eyq-tC-duC" secondAttribute="centerY" id="EEv-iJ-G0S"/>
                                <constraint firstAttribute="height" constant="120" id="GYY-Ij-oGI"/>
                                <constraint firstItem="eyq-tC-duC" firstAttribute="leading" secondItem="IVv-Dn-v5x" secondAttribute="leading" constant="10" id="Grz-cc-h2x"/>
                                <constraint firstItem="9UL-PL-ukM" firstAttribute="top" secondItem="eyq-tC-duC" secondAttribute="bottom" constant="10" id="VOq-m8-hoU"/>
                                <constraint firstAttribute="width" constant="220" id="ePQ-fb-kiS"/>
                                <constraint firstItem="eyq-tC-duC" firstAttribute="top" secondItem="IVv-Dn-v5x" secondAttribute="top" constant="10" id="fht-Mu-i0K"/>
                                <constraint firstItem="WvZ-YD-MZ0" firstAttribute="centerX" secondItem="hEh-W0-vkz" secondAttribute="centerX" id="m6G-rP-4nw"/>
                                <constraint firstItem="WvZ-YD-MZ0" firstAttribute="top" secondItem="hEh-W0-vkz" secondAttribute="bottom" constant="10" id="mYF-Mp-51c"/>
                            </constraints>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Calling..." textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XSJ-gX-fez">
                            <rect key="frame" x="177.5" y="291.5" width="59" height="20"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="20" id="9Oy-Ef-69R"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="system" pointSize="15"/>
                            <color key="textColor" white="1" alpha="0.8416898545" colorSpace="calibratedWhite"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="HoangDuoc" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="oYs-Pt-AXb">
                            <rect key="frame" x="132.5" y="236.5" width="149" height="35"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="35" id="KDa-bd-Dmy"/>
                            </constraints>
                            <fontDescription key="fontDescription" type="boldSystem" pointSize="27"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="calibratedWhite"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="nIR-xU-jiG">
                            <rect key="frame" x="172" y="684" width="70" height="70"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="70" id="UcM-Ae-5XW"/>
                                <constraint firstAttribute="height" constant="70" id="wlI-Vu-gOz"/>
                            </constraints>
                            <state key="normal" backgroundImage="call_end_call"/>
                            <connections>
                                <action selector="endTapped:" destination="-1" eventType="touchUpInside" id="uV3-Kr-S6Z"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Yim-Ux-Vyh">
                            <rect key="frame" x="72" y="684" width="70" height="70"/>
                            <state key="normal" backgroundImage="call_end_call"/>
                            <connections>
                                <action selector="rejectTapped:" destination="-1" eventType="touchUpInside" id="NrF-Mh-UAr"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SJo-W2-ceo">
                            <rect key="frame" x="272" y="684" width="70" height="70"/>
                            <state key="normal" backgroundImage="icon_accept_call"/>
                            <connections>
                                <action selector="answerTapped:" destination="-1" eventType="touchUpInside" id="V0g-nf-UaC"/>
                            </connections>
                        </button>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="t5N-Zh-Z9K">
                            <rect key="frame" x="10" y="10" width="56" height="56"/>
                            <inset key="imageEdgeInsets" minX="0.0" minY="0.0" maxX="2.2250738585072014e-308" maxY="0.0"/>
                            <state key="normal" image="ic-back"/>
                            <connections>
                                <action selector="tappedBackButton:" destination="-1" eventType="touchUpInside" id="f83-AT-vL2"/>
                            </connections>
                        </button>
                    </subviews>
                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstItem="Yim-Ux-Vyh" firstAttribute="centerY" secondItem="nIR-xU-jiG" secondAttribute="centerY" id="2U4-ck-ztV"/>
                        <constraint firstItem="t5N-Zh-Z9K" firstAttribute="leading" secondItem="PDi-UU-Wb6" secondAttribute="leading" constant="10" id="2Wa-Lu-Xzd"/>
                        <constraint firstItem="XSJ-gX-fez" firstAttribute="centerX" secondItem="PDi-UU-Wb6" secondAttribute="centerX" id="3PB-Tp-vBg"/>
                        <constraint firstItem="XSJ-gX-fez" firstAttribute="centerX" secondItem="PDi-UU-Wb6" secondAttribute="centerX" id="7yY-Z3-4LI"/>
                        <constraint firstItem="XSJ-gX-fez" firstAttribute="top" secondItem="oYs-Pt-AXb" secondAttribute="bottom" constant="20" id="8ak-wl-15b"/>
                        <constraint firstItem="ReB-XB-Y8C" firstAttribute="top" secondItem="PDi-UU-Wb6" secondAttribute="top" constant="80" id="9Dr-ni-Ewk"/>
                        <constraint firstItem="IVv-Dn-v5x" firstAttribute="centerX" secondItem="PDi-UU-Wb6" secondAttribute="centerX" id="AKi-jO-tNM"/>
                        <constraint firstItem="SJo-W2-ceo" firstAttribute="width" secondItem="nIR-xU-jiG" secondAttribute="width" id="Abv-xq-3yi"/>
                        <constraint firstItem="IVv-Dn-v5x" firstAttribute="centerY" secondItem="PDi-UU-Wb6" secondAttribute="centerY" constant="50" id="CUT-SG-P8h"/>
                        <constraint firstItem="t5N-Zh-Z9K" firstAttribute="top" secondItem="PDi-UU-Wb6" secondAttribute="top" constant="10" id="OZe-GA-MBR"/>
                        <constraint firstItem="SJo-W2-ceo" firstAttribute="leading" secondItem="nIR-xU-jiG" secondAttribute="trailing" constant="30" id="T45-id-408"/>
                        <constraint firstItem="Yim-Ux-Vyh" firstAttribute="height" secondItem="nIR-xU-jiG" secondAttribute="height" id="T4s-yh-vC5"/>
                        <constraint firstItem="oYs-Pt-AXb" firstAttribute="centerX" secondItem="PDi-UU-Wb6" secondAttribute="centerX" id="XWF-oD-FFs"/>
                        <constraint firstItem="Yim-Ux-Vyh" firstAttribute="width" secondItem="nIR-xU-jiG" secondAttribute="width" id="aGa-gL-BcS"/>
                        <constraint firstItem="oYs-Pt-AXb" firstAttribute="top" secondItem="ReB-XB-Y8C" secondAttribute="bottom" constant="36.5" id="bxQ-w7-LYH"/>
                        <constraint firstItem="nIR-xU-jiG" firstAttribute="top" relation="greaterThanOrEqual" secondItem="IVv-Dn-v5x" secondAttribute="bottom" priority="200" constant="70" id="ePV-sW-SeV"/>
                        <constraint firstItem="oYs-Pt-AXb" firstAttribute="centerX" secondItem="PDi-UU-Wb6" secondAttribute="centerX" id="erF-hc-JT7"/>
                        <constraint firstItem="SJo-W2-ceo" firstAttribute="height" secondItem="nIR-xU-jiG" secondAttribute="height" id="ht3-Tr-Jmy"/>
                        <constraint firstItem="nIR-xU-jiG" firstAttribute="leading" secondItem="Yim-Ux-Vyh" secondAttribute="trailing" constant="30" id="i9o-uk-TTf"/>
                        <constraint firstItem="IVv-Dn-v5x" firstAttribute="top" relation="greaterThanOrEqual" secondItem="XSJ-gX-fez" secondAttribute="bottom" constant="75" id="iOI-Ir-rWX"/>
                        <constraint firstItem="SJo-W2-ceo" firstAttribute="centerY" secondItem="nIR-xU-jiG" secondAttribute="centerY" id="opJ-Df-jV2"/>
                        <constraint firstAttribute="bottom" secondItem="nIR-xU-jiG" secondAttribute="bottom" constant="40" id="pQO-Cc-jjC"/>
                        <constraint firstItem="nIR-xU-jiG" firstAttribute="centerX" secondItem="PDi-UU-Wb6" secondAttribute="centerX" id="q3g-Ea-ehl"/>
                        <constraint firstItem="ReB-XB-Y8C" firstAttribute="centerX" secondItem="PDi-UU-Wb6" secondAttribute="centerX" id="udJ-cc-24S"/>
                    </constraints>
                </view>
                <view alpha="0.0" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="YI4-he-cWp">
                    <rect key="frame" x="0.0" y="0.0" width="414" height="896"/>
                    <color key="backgroundColor" white="0.33333333329999998" alpha="1" colorSpace="calibratedWhite"/>
                </view>
            </subviews>
            <viewLayoutGuide key="safeArea" id="fnl-2z-Ty3"/>
            <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstItem="kNv-x5-ttk" firstAttribute="width" secondItem="7dt-Pn-wXm" secondAttribute="width" multiplier="0.3" id="2U4-BW-hhJ"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="trailing" secondItem="PDi-UU-Wb6" secondAttribute="trailing" id="38f-47-fkr"/>
                <constraint firstItem="kNv-x5-ttk" firstAttribute="leading" secondItem="7dt-Pn-wXm" secondAttribute="leading" constant="10" id="53S-pa-vRz"/>
                <constraint firstItem="7dt-Pn-wXm" firstAttribute="height" secondItem="i5M-Pr-FkT" secondAttribute="height" id="II6-Iv-vri"/>
                <constraint firstItem="7dt-Pn-wXm" firstAttribute="centerX" secondItem="i5M-Pr-FkT" secondAttribute="centerX" id="Jxe-5R-UL4"/>
                <constraint firstAttribute="trailing" secondItem="YI4-he-cWp" secondAttribute="trailing" id="Qvc-jl-eaN"/>
                <constraint firstItem="7dt-Pn-wXm" firstAttribute="centerY" secondItem="i5M-Pr-FkT" secondAttribute="centerY" id="Yu3-vj-PY2"/>
                <constraint firstItem="PDi-UU-Wb6" firstAttribute="leading" secondItem="fnl-2z-Ty3" secondAttribute="leading" id="ZCx-yG-IY6"/>
                <constraint firstItem="YI4-he-cWp" firstAttribute="top" secondItem="i5M-Pr-FkT" secondAttribute="top" id="eS4-JB-w2a"/>
                <constraint firstItem="YI4-he-cWp" firstAttribute="leading" secondItem="i5M-Pr-FkT" secondAttribute="leading" id="gVn-sw-0ui"/>
                <constraint firstItem="fnl-2z-Ty3" firstAttribute="bottom" secondItem="PDi-UU-Wb6" secondAttribute="bottom" constant="20" id="hf9-fn-MEY"/>
                <constraint firstItem="PDi-UU-Wb6" firstAttribute="top" secondItem="fnl-2z-Ty3" secondAttribute="top" id="rqr-lt-D6r"/>
                <constraint firstItem="kNv-x5-ttk" firstAttribute="top" secondItem="7dt-Pn-wXm" secondAttribute="top" constant="50" id="sv6-cX-MS2"/>
                <constraint firstItem="7dt-Pn-wXm" firstAttribute="width" secondItem="i5M-Pr-FkT" secondAttribute="width" id="t0a-dB-6BX"/>
                <constraint firstAttribute="bottom" secondItem="YI4-he-cWp" secondAttribute="bottom" id="wo8-N5-zCA"/>
            </constraints>
            <point key="canvasLocation" x="-3133" y="-635"/>
        </view>
    </objects>
    <resources>
        <image name="call_end_call" width="60" height="60"/>
        <image name="ic-back" width="56" height="56"/>
        <image name="icon_accept_call" width="60" height="60"/>
        <image name="icon_mute" width="47" height="47"/>
        <image name="icon_speaker" width="47" height="47"/>
    </resources>
</document>