/* Primary and Surface Palettes */
:root {
    --p-primary-50: #effefb;
    --p-primary-100: #c7fff4;
    --p-primary-200: #90ffe9;
    --p-primary-300: #51f7dc;
    --p-primary-400: #04c8b1;
    --p-primary-500: #009688;
    --p-primary-600: #058075;
    --p-primary-700: #0a655f;
    --p-primary-800: #0d544f;
    --p-primary-900: #0d544f;
    --p-primary-950: #063734;
    --p-surface-0: #ffffff;
    --p-surface-50: #fafafa;
    --p-surface-100: #f4f4f5;
    --p-surface-200: #e4e4e7;
    --p-surface-300: #d4d4d8;
    --p-surface-400: #a1a1aa;
    --p-surface-500: #71717a;
    --p-surface-600: #52525b;
    --p-surface-700: #3f3f46;
    --p-surface-800: #27272a;
    --p-surface-900: #18181b;
    --p-surface-950: #09090b;

    /* Secondary Palette */
    --p-secondary-500: #ff5722;

    --p-content-border-radius: 6px;

    --font-style: 'Inter', sans-serif;

    --scrollbar-width: 12px;
    --scrollbar-padding: 2px;
    --scrollbar-border-radius: 6px;

    /* swipercss custome */
    --swiper-pagination-color: #009688 !important;
    --swiper-theme-color: #009688 !important;
    -swiper-pagination-bottom: -6px !important;

    /* notivue */
    --nv-z: 9999;
}

/* Light */
:root {
    --p-primary-color: var(--p-primary-500);
    --p-secondary-color: var(--p-secondary-500);
    --p-primary-contrast-color: var(--p-surface-0);
    --p-primary-hover-color: var(--p-primary-600);
    --p-primary-active-color: var(--p-primary-700);
    --p-content-border-color: var(--p-surface-200);
    --p-content-hover-background: var(--p-surface-100);
    --p-content-hover-color: var(--p-surface-800);
    --p-highlight-background: var(--p-primary-50);
    --p-highlight-color: var(--p-primary-700);
    --p-highlight-focus-background: var(--p-primary-100);
    --p-highlight-focus-color: var(--p-primary-800);
    --p-text-color: var(--p-surface-700);
    --p-text-hover-color: var(--p-surface-800);
    --p-text-muted-color: var(--p-surface-500);
    --p-text-hover-muted-color: var(--p-surface-600);
}

.dp__theme_light {
    --dp-primary-color: #009688 !important;
    --dp-primary-disabled-color: #51f7dc !important;
    --dp-loader: 5px solid #009688 !important;
}

.p-avatar img {
    object-fit: cover !important;
}

/* Trick CSS for Form */
#longText-help {
    color: #dc2626;
    display: none;
}
