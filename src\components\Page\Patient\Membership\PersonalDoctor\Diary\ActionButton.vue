<script lang="ts" setup>
import { useI18n } from '#imports'

interface Props {
    actionState: string | null
    isCountdownInProgress: boolean
    formattedCountdown: string
    loading: boolean
}

defineProps<Props>()
const { t } = useI18n()

defineEmits(['action'])
</script>

<template>
    <div class="p-4 pt-0">
        <Button
            v-if="actionState"
            class="w-full"
            :label="isCountdownInProgress ? formattedCountdown : t(actionState)"
            :loading="loading"
            :disabled="loading || Boolean(isCountdownInProgress)"
            @click="$emit('action')"
        />
        <Button v-else label="" class="w-full" disabled />
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "start-consultation": "Start consultation",
        "remind": "Remind",
        "request-callback": "Request a callback"
    },
    "vi": {
        "start-consultation": "<PERSON>ắt đầu phiên tư vấn",
        "remind": "<PERSON><PERSON><PERSON><PERSON> gọi",
        "request-callback": "<PERSON><PERSON><PERSON> yêu cầu gọi lại"
    }
}
</i18n>
