<script setup lang="ts">
import { EffectCards } from 'swiper/modules'

const modules = [EffectCards]
</script>

<template>
  <swiper
    :grab-cursor="true"
    :modules="modules"
    :effect="'cards'"
    class="mySwiper"
  >
    <swiper-slide style="height: 200px; width: 200px; background: red"
      >Slide 1</swiper-slide
    >
    <swiper-slide style="height: 200px; width: 200px; background: yellow"
      >Slide 2</swiper-slide
    >
    <swiper-slide style="height: 200px; width: 200px; background: blue"
      >Slide 3</swiper-slide
    >
    <swiper-slide style="height: 200px; width: 200px; background: green"
      >Slide 4</swiper-slide
    >
    <swiper-slide style="height: 200px; width: 200px; background: orange"
      >Slide 5</swiper-slide
    >
  </swiper>
</template>
