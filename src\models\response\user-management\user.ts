export interface IAvatar {
    _id?: string
    name?: string
    url?: string
}
export interface ITimezone {
    local: boolean
}
export interface IAddress {
    country?: null
    postcode?: null
    state?: null
    street1: string
    suburb?: null
}
export interface IReferral {
    refCode: string
}
export interface IValidated {
    email?: boolean
    phone?: boolean
}
export interface IBank {
    account: string
    city: string
    main: string
    branch: string
}
export interface IIdCard {
    date?: null
    number: string
    place: string
}
export interface IInsurance {
    contract: string
}
export interface IUtm {
    campaign: string
    content: string
    medium: string
    source: string
}
export interface IRelationships {
    avatar?: IAvatar
    createdAt?: string
    dob?: string
    gender?: string
    isAdmin?: boolean
    isTest?: boolean
    locale?: string
    name?: string
    relationship?: string
    validated?: IValidated
    _id?: string
}

export interface IUserBase {
    id?: string
    _id?: string
    avatar?: IAvatar
    name?: string
    fullname?: string
    username?: string
    firstname?: string
    lastname?: string
    gender?: string
}

export interface IUser extends IUserBase {
    locale?: string
    timezone?: ITimezone
    role?: null[] | null
    countryCode?: string
    search?: string
    updatedAt?: string
    wallet?: number
    retired?: boolean
    address?: IAddress
    bankAccount?: string
    createAccount?: boolean
    dob?: string
    email?: string
    isProvider?: boolean
    oneSignalId?: string
    phone?: string
    provider?: string
    referral?: IReferral
    validated?: IValidated
    veryPatient?: null[] | null
    bank?: IBank
    idCard?: IIdCard
    updatedBy?: string
    fingerPrint?: string
    isAdmin?: boolean
    isTest?: boolean
    insurance?: IInsurance
    utm?: IUtm
    token?: string
    accessToken?: string
    refreshToken?: string
    relationships?: IRelationships[]
    isMember?: boolean
    membership?: any
    meta?: any
}
