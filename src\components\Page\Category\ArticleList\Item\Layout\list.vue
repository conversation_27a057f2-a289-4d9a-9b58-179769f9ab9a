<script setup lang="ts">
import { type PropType, computed } from '#imports'
import type { HitNotion } from '~/models'
type BlockType = 'title' | 'sapo' | 'thumbnail' | 'published-at'
const { article, blocks, slug } = defineProps({
    blocks: {
        type: Array as PropType<BlockType[]>,
        default: () => ['title', 'sapo'],
    },
    article: {
        type: Object as PropType<HitNotion>,
        default: () => ({}) as HitNotion,
        required: true,
    },
    slug: {
        type: String,
        default: '',
    },
})

const typeLink = computed(() => {
    if (article.page?.properties?.Type === 'Category') {
        return article.page?.properties?.Slug
    } else {
        return `${slug}/${article.page?.properties?.Slug}`
    }
})
</script>
<template>
    <article class="flex flex-col gap-4 px-3 xs:flex-row md:gap-5">
        <slot v-if="blocks.includes('thumbnail')" name="thumbnail">
            <div
                class="h-[150px] flex-[4] overflow-hidden rounded-xl hover:cursor-pointer"
            >
                <NuxtImg
                    :src="article.page?.cover?.url"
                    :alt="article.page?.properties?.Name"
                    loading="lazy"
                    class="duration-800 aspect-[16/9] h-full transform object-cover transition ease-in hover:scale-105"
                />
            </div>
        </slot>

        <div
            :class="
                blocks.includes('thumbnail')
                    ? 'flex-[8]'
                    : 'flex-[12]' + ' flex flex-col p-4 align-top'
            "
        >
            <slot v-if="blocks.includes('title')" name="title">
                <NuxtLink :to="typeLink">
                    <div
                        class="mb-2 text-xl font-bold tracking-tight text-surface-900 hover:text-primary md:text-2xl"
                    >
                        {{ article?.page?.properties?.Name }}
                    </div>
                </NuxtLink>
            </slot>

            <slot v-if="blocks.includes('sapo')" name="sapo">
                <p
                    class="mb-3 font-normal text-surface-700 dark:text-surface-400"
                >
                    {{ article?.page?.properties?.Sapo }}
                </p>
            </slot>
            <slot name="readtime">
                <p />
            </slot>
        </div>
    </article>
</template>
