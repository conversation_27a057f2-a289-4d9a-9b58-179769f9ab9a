import { Preferences } from '@capacitor/preferences'

interface ILog {
    level?: 'verbose' | 'warn' | 'debug' | 'error'
    message: string
    context: string
    route?: string
    data?: Record<string, any>
    time?: string
}

const TAG = 'debug-log'

export function useLogs() {
    const clearNativeLogs = async () => {
        await Preferences.remove({
            key: TAG,
        })
    }

    const getNativeLogs = async () => {
        try {
            const { value } = await Preferences.get({
                key: TAG,
            })
            const logs = value ? (JSON.parse(value) as Array<ILog>) : []
            return logs
        } catch (e) {
            console.log('🚀 ~ getNativeLogs ~ e:', e)
            return []
        }
    }

    const addNativeLogs = async (log: ILog) => {
        const logs = await getNativeLogs()
        if(logs.length > 50) {
            logs.length = 50
        }
        if(!log.time) log.time = new Date().toLocaleString()
        logs.unshift(log)
        await Preferences.set({
            key: TAG,
            value: JSON.stringify(logs),
        })
    }

    return {
        clearNativeLogs,
        getNativeLogs,
        addNativeLogs
    }
}
