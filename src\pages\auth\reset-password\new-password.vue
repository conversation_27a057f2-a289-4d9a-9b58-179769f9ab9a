<script setup lang="ts">
import { appRoutes } from '~/constants'

const authStore = useAuthStore()
const route = useRoute()

const redirectUri = computed(() => route.query?.redirect_uri as string || '')
const phoneNumber = computed(() => route.query?.phone_number || '')

const onSubmit = (password: string) => {
    authStore.setPasswordReset(password)

    navigateTo({
        path: appRoutes.auth.resetPassword.verifyOtp,
    })
}

onMounted(() => {
    authStore.setRedirectUri(String(redirectUri.value))
    authStore.setPhoneNumber(String(phoneNumber.value))
})
</script>

<template>
    <PageAuthTemplate>
        <PageAuthResetPasswordNewPassword
            :redirect-back="redirectUri"
            @on:submit="onSubmit"
        />
    </PageAuthTemplate>
</template>
