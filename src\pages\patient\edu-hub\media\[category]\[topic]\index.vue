<script lang="ts" setup>
import { ref, usePagePatientEduHubMediaTopicSlug, useRoute } from '#imports'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const route = useRoute()

const layoutStore = useLayoutStore()

const topicId = useLocalStorage('edu-hub-media-topic-id', '')

const from = ref<number>(0)
const size = ref<number>(20)

const {
    topic,
    items,
    total: totalItems,
} = usePagePatientEduHubMediaTopicSlug(topicId.value, from, size)

const onLoadMore = () => {
    const total = totalItems.value
    const currentSize = size.value

    if (total && currentSize < total) {
        size.value = Math.min(currentSize + 15, total)
    }
}

const videoCardComponent = defineAsyncComponent(
    () => import('~/components/Page/Patient/EduHub/VideoCard.vue'),
)

const componentMap: any = {
    'Short Videos': videoCardComponent,
    'Offline Events': videoCardComponent,
    'Live Streams': videoCardComponent,
    Podcasts: defineAsyncComponent(
        () => import('~/components/Page/Patient/EduHub/AudioCard.vue'),
    ),
}

watch(
    topic,
    () => {
        const name = topic.value?.page?.properties.Name
        if (name) {
            layoutStore.setTitle(name)
        }
    },
    {
        deep: true,
    },
)

onUnmounted(() => {
    layoutStore.$reset()
})
</script>

<template>
    <div class="mx-auto max-w-3xl">
        <SharedSmoothScroll class="no-scrollbar" @arrived-bottom="onLoadMore">
            <template #main>
                <Suspense>
                    <template #default>
                        <div v-for="item in items" :key="item._id">
                            <NuxtLink
                                :to="`${route.fullPath + '/' + item._id}`"
                            >
                                <component
                                    :is="componentMap[item.delivery]"
                                    v-if="componentMap[item.delivery]"
                                    :media="item"
                                />
                            </NuxtLink>
                        </div>
                    </template>
                    <template #fallback>
                        <NuxtLoadingIndicator />
                    </template>
                </Suspense>
            </template>
        </SharedSmoothScroll>
    </div>
</template>
