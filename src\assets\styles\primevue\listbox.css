@import './inputtext';
@import './iconfield';

.p-listbox {
    @apply bg-surface-0 dark:bg-surface-950 text-surface-700 dark:text-surface-0
        border border-surface-300 dark:border-surface-700 rounded-md
        shadow-[0_1px_2px_0_rgba(18,18,23,0.05)]
        transition-colors duration-200
}

.p-listbox.p-focus {
    @apply border-primary
}

.p-listbox.p-disabled {
    @apply bg-surface-200 text-surface-500 dark:bg-surface-700 dark:text-surface-400 opacity-100 cursor-default
}

.p-listbox.p-disabled .p-listbox-option {
    @apply text-surface-500 dark:text-surface-400 cursor-default
}

.p-listbox.p-invalid {
    @apply border-red-400 dark:border-red-300
}

.p-listbox-header {
    @apply pt-2 pb-1 px-4
}

.p-listbox-filter {
    @apply w-full
}

.p-listbox-list-container {
    @apply overflow-auto
}

.p-listbox-list {
    @apply list-none m-0 p-1 outline-none flex flex-col gap-[2px]
}

.p-listbox-option {
    @apply flex items-center cursor-pointer relative overflow-hidden px-3 py-2 border-none rounded-sm
        text-surface-700 dark:text-surface-0
        transition-colors duration-200
}

.p-listbox-striped li:nth-child(even of .p-listbox-option) {
    @apply bg-surface-50 dark:bg-surface-900
}

.p-listbox .p-listbox-list .p-listbox-option.p-listbox-option-selected {
    @apply bg-highlight
}

.p-listbox:not(.p-disabled) .p-listbox-option.p-listbox-option-selected.p-focus {
    @apply bg-highlight-emphasis
}

.p-listbox:not(.p-disabled) .p-listbox-option:not(.p-listbox-option-selected):not(.p-disabled).p-focus {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-listbox:not(.p-disabled) .p-listbox-option:not(.p-listbox-option-selected):not(.p-disabled):hover {
    @apply bg-surface-100 dark:bg-surface-800 text-surface-800 dark:text-surface-0
}

.p-listbox-option-check-icon {
    @apply relative -ms-[0.375rem] me-[0.375rem] text-surface-700 dark:text-surface-0
}

.p-listbox-option-group {
    @apply m-0 px-3 py-2 text-surface-500 dark:text-surface-400 font-semibold 
}

.p-listbox-empty-message {
    @apply px-3 py-2
}