<script setup lang="ts">
import type {
    IPrescription,
    PrescriptionRoute,
} from '~/models/page/health-records'

interface Props {
    prescription: IPrescription
}

const mapIcons: Record<PrescriptionRoute, string> = {
    Oral: 'material-symbols:medication-liquid',
    Injection: 'material-symbols:syringe-rounded',
    Other: 'material-symbols:local-hospital',
}

defineProps<Props>()
</script>

<template>
    <div
        class="flex flex-none flex-row gap-2 rounded-xl bg-surface-50 p-3 dark:bg-surface-950"
    >
        <div
            class="flex items-center self-start rounded-full bg-surface-100 p-3 dark:bg-surface-900"
        >
            <iconify-icon
                :icon="mapIcons[prescription.route]"
                class="text-3xl text-slate-600"
            />
        </div>
        <div>
            <p class="m-0 text-xl font-bold">{{ prescription.name }}</p>
            <p class="line-clamp-2 leading-tight">
                {{ prescription.description }}
            </p>
        </div>
    </div>
</template>
