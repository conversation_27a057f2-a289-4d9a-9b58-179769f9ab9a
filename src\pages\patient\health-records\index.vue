<script setup lang="ts">
import {
    computed,
    defineAsyncComponent,
    definePageMeta,
    useI18n,
} from '#imports'
import { appRoutes } from '~/constants'
import type { Relationship } from '~/models'
import dayjs from 'dayjs'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

// Page metadata
definePageMeta({
    layout: 'patient',
    middleware: 'auth-capacitor',
    pageTransition: {
        name: 'page',
        mode: 'out-in',
    },
})

const healthGptContext = useLocalStorage('health-gpt-context', {})

// Component imports
const components = {
    Teleconsultation: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/HealthRecords/Teleconsultation/index.vue'
            ),
    ),
    ConsultationsHistory: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/HealthRecords/ConsultationsHistory.vue'
            ),
    ),
    Medication: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/HealthRecords/Medication/index.vue'
            ),
    ),
    LabTest: defineAsyncComponent(
        () =>
            import('~/components/Page/Patient/HealthRecords/LabTest/index.vue'),
    ),
    Vitals: defineAsyncComponent(
        () =>
            import('~/components/Page/Patient/HealthRecords/Vitals/index.vue'),
    ),
    Immunization: defineAsyncComponent(
        () =>
            import(
                '~/components/Page/Patient/HealthRecords/Immunization/index.vue'
            ),
    ),
}

// Composables setup
const { t } = useI18n({ useScope: 'local' })
const { user } = useUserInfo({ scope: ['_id', 'meta', 'phone'] })
const { members, refreshMembers } = useMembers()
const healthRecordStore = useHealthRecordStore()
const { memberActivated } = storeToRefs(healthRecordStore)

// Computed properties
const memberInfo = computed(() => ({
    id: memberActivated.value?.related?._id || '',
    name: memberActivated.value?.related?.name || '',
    hasAvatar: !!memberActivated.value?.related?.avatar?.url,
    avatarUrl:
        memberActivated.value?.related?.avatar?.url ||
        `https://ui-avatars.com/api/?name=${memberActivated.value?.related?.name}`,
}))

const subscribedPrograms = computed(() =>
    ['preg_subscribe', 'baby_subscribe'].reduce(
        (sum, key) => sum + (user.value.meta?.[key] ? 1 : 0),
        0,
    ),
)
const { assignedInfo } = useDoctorList(computed(() => user.value?._id))

const medicalSections = computed(() => [
    {
        label: 'medication',
        icon: 'material-symbols:medication',
        to: appRoutes.patient.healthRecords.medicalSection(
            memberInfo.value.id,
            'prescriptions',
        ),
        component: components.Medication,
    },
    {
        label: 'lab',
        icon: 'material-symbols:science',
        to: appRoutes.patient.healthRecords.medicalSection(
            memberInfo.value.id,
            'lab',
        ),
        component: components.LabTest,
    },
    {
        label: 'vitals',
        icon: 'mdi:clipboard-vitals',
        component: components.Vitals,
    },
    {
        label: 'immunization',
        icon: 'material-symbols:vaccines-outline-rounded',
        component: components.Immunization,
    },
    {
        label: 'consultations-history',
        icon: 'material-symbols:history',
        component: components.ConsultationsHistory,
    },
])

// Methods
const scrollToElement = (event: MouseEvent) => {
    const element = event.target as HTMLElement
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'center',
    })
}

const setMemberActivated = (member: Relationship, event: MouseEvent) => {
    healthRecordStore.setMemberActivated(member)
    scrollToElement(event)
}

const handleRefreshMembers = async () => {
    await refreshMembers()
    const updatedMember = members.value.find(
        (member) => member.related?._id === memberActivated.value?.related?._id,
    )
    if (updatedMember) {
        healthRecordStore.setMemberActivated(updatedMember)
    }
}

const memberGender = computed(() =>
    memberActivated.value.related?.gender === 'M' ? t('male') : t('female'),
)

// Watchers
watch(
    members,
    () => {
        if (
            !memberActivated.value ||
            Object.keys(memberActivated.value).length === 0
        ) {
            const currentUser = members.value.find(
                (member) => member.related?._id === user.value._id,
            )
            if (currentUser) {
                healthRecordStore.setMemberActivated(currentUser)
            }
        }
    },
    {
        immediate: true,
        deep: true,
    },
)

// Add age calculation utility
const calculateAge = (dob: string) => {
    const birthDate = dayjs(dob)
    const today = dayjs()
    let returnText = ''

    switch (true) {
        // 3+ years: show in years
        case today.diff(birthDate, 'y', true) > 3:
            returnText = `${today.diff(birthDate, 'y')} ${t('years-old')}`
            break
        // 1-3 years: show in months
        case today.diff(birthDate, 'y', true) >= 1 &&
            today.diff(birthDate, 'y', true) <= 3:
            returnText = `${today.diff(birthDate, 'M')} ${t('months-old')}`
            break
        // 1 month - 1 year: show in weeks
        case today.diff(birthDate, 'y', true) < 1 &&
            today.diff(birthDate, 'm', true) > 1:
            returnText = `${today.diff(birthDate, 'w')} ${t('weeks-old')}`
            break
        // Under 1 month: show in days
        default:
            returnText = `${today.diff(birthDate, 'd')} ${t('days-old')}`
            break
    }

    return returnText
}

const { config } = useJsonConfigApp()
const uiblocks = computed(
    () => config.value.pages['health-records']?.UiBlocks ?? [],
)

onMounted(() => (healthGptContext.value = {}))
</script>

<template>
    <div>
        <!-- Header Section -->
        <SharedDynamicHeader class="col-span-full" :is-safe-area="false">
            <template #detailed>
                <div
                    class="relative flex flex-col overflow-hidden rounded-b-3xl shadow-lg pt-safe sm:rounded-md dark:bg-surface-900"
                    :class="[
                        'before:absolute before:inset-0 before:bg-[radial-gradient(circle_at_50%_0%,rgba(0,150,136,0.5),rgba(0,150,136,0.8)_40%,rgba(0,77,64,0.95))]',
                        'after:absolute after:inset-0 after:bg-[url(\'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNjAiIHZpZXdCb3g9IjAgMCA2MCA2MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48ZyBmaWxsPSJub25lIiBmaWxsLXJ1bGU9ImV2ZW5vZGQiPjxwYXRoIGQ9Ik0zNiAzNGM0LjQxOCAwIDgtMy41ODIgOC04cy0zLjU4Mi04LTgtOC04IDMuNTgyLTggOCAzLjU4MiA4IDggOHoiIHN0cm9rZT0icmdiYSgyNTUsMjU1LDI1NSwwLjEpIi8+PC9nPjwvc3ZnPg==\')]',
                        'after:opacity-20 after:mix-blend-soft-light',
                    ]"
                >
                    <!-- Decorative Elements -->
                    <div class="absolute inset-0 z-0 overflow-hidden">
                        <!-- Updated Modern Geometric Shapes -->
                        <div
                            class="absolute left-1/2 top-1/2 size-96 -translate-x-1/2 -translate-y-1/2 rounded-full border border-white/10"
                        ></div>
                        <div
                            class="absolute left-1/2 top-1/2 size-72 -translate-x-1/2 -translate-y-1/2 rounded-full border border-white/15"
                        ></div>
                        <div
                            class="absolute left-1/2 top-1/2 size-48 -translate-x-1/2 -translate-y-1/2 rounded-full border border-white/20"
                        ></div>

                        <!-- Enhanced Light Effects -->
                        <div
                            class="absolute -left-16 -top-16 size-48 rounded-full bg-primary-300/30 blur-3xl"
                        ></div>
                        <div
                            class="absolute right-8 top-8 size-32 rounded-full bg-primary-200/30 blur-2xl"
                        ></div>
                    </div>

                    <!-- Content -->
                    <div
                        class="relative z-10 mx-6 mt-6 flex flex-col items-center text-center"
                    >
                        <h4 class="mb-2 text-2xl font-bold text-primary-50">
                            {{ t('family-health-records') }}
                        </h4>

                        <transition name="slide-fade" mode="out-in">
                            <p
                                :key="memberInfo.name"
                                class="mb-3 text-lg font-medium text-primary-50/90"
                            >
                                {{ memberInfo.name }}
                            </p>
                        </transition>

                        <div
                            class="mb-4 flex flex-wrap items-center justify-center gap-2"
                        >
                            <span
                                v-if="
                                    memberActivated?.relationship &&
                                    memberActivated?.relationship !== 'self'
                                "
                                class="rounded-full bg-white/15 px-3 py-1 text-sm font-medium text-primary-50 backdrop-blur-sm"
                            >
                                {{ t(memberActivated?.relationship) }}
                            </span>
                            <span
                                v-if="memberActivated?.related?.gender"
                                class="rounded-full bg-white/15 px-3 py-1 text-sm text-primary-50 backdrop-blur-sm"
                            >
                                {{ memberGender }}
                            </span>
                            <span
                                v-if="memberActivated?.related?.dob"
                                class="rounded-full bg-white/15 px-3 py-1 text-sm text-primary-50 backdrop-blur-sm"
                            >
                                {{ calculateAge(memberActivated.related.dob) }}
                            </span>
                        </div>
                    </div>

                    <!-- Family Member Selection -->
                    <div class="relative z-10">
                        <h5
                            class="text-center text-sm font-medium text-primary-50/90"
                        >
                            {{ t('family-members') }}
                        </h5>
                        <PagePatientHealthRecordsMember
                            :members="members"
                            :member-activated="memberActivated"
                            @on:change="setMemberActivated"
                            @on:refresh="handleRefreshMembers"
                        />
                    </div>
                </div>
            </template>

            <template #header>
                <div class="flex items-center gap-3">
                    <Avatar
                        :image="memberInfo.avatarUrl"
                        :pt="{
                            root: 'size-10 border-2 border-primary-100',
                        }"
                        shape="circle"
                    />
                    <div class="flex flex-1 items-center justify-between">
                        <div class="flex flex-col">
                            <transition name="slide-fade" mode="out-in">
                                <h4
                                    :key="memberInfo.name"
                                    class="line-clamp-1 text-base font-semibold"
                                >
                                    {{ memberInfo.name }}
                                </h4>
                            </transition>
                            <div
                                class="flex items-center gap-2 text-xs text-surface-500"
                            >
                                <template
                                    v-if="
                                        memberActivated?.relationship &&
                                        memberActivated?.relationship !== 'self'
                                    "
                                >
                                    <span>{{
                                        t(memberActivated?.relationship)
                                    }}</span>
                                    <span class="text-surface-300">•</span>
                                </template>
                                <template
                                    v-if="memberActivated?.related?.gender"
                                >
                                    <span>{{ memberGender }}</span>
                                    <span class="text-surface-300">•</span>
                                </template>
                                <span v-if="memberActivated?.related?.dob">
                                    {{
                                        calculateAge(
                                            memberActivated.related.dob,
                                        )
                                    }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </SharedDynamicHeader>
        <!-- Phone Linking Notification Card -->
        <SharedPageSection
            v-if="!user.phone"
            :pt="{
                content: 'mx-4 mb-4',
            }"
        >
            <SharedAuthLinkPhoneCard />
        </SharedPageSection>

        <!-- Personal Doctor Card -->
        <SharedPageSection
            v-if="
                assignedInfo &&
                assignedInfo.length > 0 &&
                uiblocks.includes('personal-doctor')
            "
            :title="t('personal:doctor')"
            :pt="{
                content: 'mx-4',
            }"
        >
            <LazyPagePatientServicesPersonalDoctorCareLogCard
                :user-id="user?._id"
            />
        </SharedPageSection>
        <!-- Medical Sections -->
        <div class="mx-auto max-w-6xl space-y-4">
            <Suspense>
                <template #default>
                    <component
                        v-if="uiblocks.includes('teleconsultation')"
                        :is="components.Teleconsultation"
                        :title="t('teleconsultation')"
                        :member-activated-id="memberInfo.id"
                    />
                </template>
                <template #fallback>
                    <Skeleton height="200px" width="100%" class="rounded-xl" />
                </template>
            </Suspense>

            <template v-for="section in medicalSections" :key="section.label">
                <Suspense>
                    <template #default>
                        <component
                            v-if="uiblocks.includes('medical-report')"
                            :is="section.component"
                            :member-activated-id="memberInfo.id"
                            :member-activated="memberActivated"
                            :title="t(`${section.label}`)"
                            :to="section.to"
                            :gender="memberActivated.related?.gender"
                            :dob="memberActivated.related?.dob"
                            :user-id="memberActivated.related?._id"
                        />
                    </template>
                    <template #fallback>
                        <Skeleton
                            height="200px"
                            width="100%"
                            class="rounded-xl"
                        />
                    </template>
                </Suspense>
            </template>
        </div>

        <!-- Additional Sections -->
        <PagePatientServicesHealthProgram
            v-if="
                memberInfo.id === user._id &&
                subscribedPrograms &&
                uiblocks.includes('health-program')
            "
            show="subscribed"
            class="mx-auto max-w-6xl"
        />

        <PagePatientHealthRecordsNoRatingConsultation
            v-if="uiblocks.includes('consultation-history')"
        />
    </div>
</template>

<style scoped>
.no-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
}

.slide-fade-enter-active,
.slide-fade-leave-active {
    transition: all 0.2s ease;
}

.slide-fade-enter-from {
    transform: translateY(10px);
    opacity: 0;
}

.slide-fade-leave-to {
    transform: translateY(-10px);
    opacity: 0;
}
</style>

<i18n lang="json">
{
    "en": {
        "family-health-records": "Secure Your Family's Health History, for Life!",
        "health-records-tagline": "Secure Your Family's Health History, for Life!",
        "family-members": "Family Members",
        "health-records-description": "Your family's complete health journey - Track, manage, and access medical records securely in one place",
        "male": "Male",
        "female": "Female",
        "years-old": "years old",
        "months-old": "months old",
        "weeks-old": "weeks old",
        "days-old": "days old",
        "main-account": "Main Account",
        "child": "Child",
        "parent": "Parent",
        "spouse": "Spouse",
        "sibling": "Sibling",
        "link-phone-title": "Connect to Your Previous Health Records",
        "link-phone-description": "Link your phone number to retrieve your previous medical history, consultation records, and health data from your existing account",
        "link-phone-action": "Link Previous Account"
    },
    "vi": {
        "family-health-records": "Nơi lưu trữ hồ sơ sức khỏe gia đình bạn!",
        "health-records-tagline": "Nơi lưu trữ hồ sơ sức khỏe gia đình bạn!",
        "family-members": "Thành viên gia đình",
        "health-records-description": "Hành trình sức khỏe toàn diện của gia đình - Theo dõi, quản lý và truy cập hồ sơ y tế an toàn tại một nơi",
        "male": "Nam",
        "female": "Nữ",
        "years-old": "tuổi",
        "months-old": "tháng",
        "weeks-old": "tuần",
        "days-old": "ngày",
        "main-account": "Tài khoản chính",
        "child": "Con",
        "parent": "Bố/Mẹ",
        "spouse": "Vợ/Chồng",
        "sibling": "Anh/Chị/Em",
        "link-phone-title": "Kết nối với hồ sơ sức khỏe trước đây",
        "link-phone-description": "Liên kết số điện thoại để lấy lại lịch sử khám bệnh, hồ sơ tư vấn và dữ liệu sức khỏe từ tài khoản cũ của bạn",
        "link-phone-action": "Liên kết tài khoản cũ"
    }
}
</i18n>
