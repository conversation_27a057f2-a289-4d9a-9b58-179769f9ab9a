import { wellcareUrl } from '~/constants'
import { push } from 'notivue'
import type { User } from '@wellcare/nuxt3-module-account'

type TranslateFunction = (key: string) => string

interface Profile extends User {
    file: File | null
    otp: string
    password?: string
}

interface RequestBody extends User {
    createdAccount: boolean
    avatar?: {
        _id: string
        url: string
    }
}

export const useOnboarding = () => {
    const onboardingStore = useOnboardingStore()
    const { upload } = useUploadTemporary()
    const { $fetchWellcare } = useNuxtApp()
    const { account, refreshToken, getUserManager } = useAppAuth()
    const { refresh: refreshUserInfo } = useUserInfo()
    const dayjs = useDayjs()

    const isLoading = ref(false)
    const error = ref<string | null>(null)

    const registerPhr = async ({ t }: { t: TranslateFunction }) => {
        isLoading.value = true
        error.value = null
        const notification = push.promise(t('onboarding.registration-loading'))

        try {
            const profile = onboardingStore.profile as Profile
            await validateProfileData(profile)

            const avatar = await handleAvatarUpload(
                profile.file,
                // profile.avatar,
            )
            const { data, error }: any = await registerUser({
                profile,
                avatar,
            })

            if (error) {
                throw new Error(error.message)
            }

            await handlePostRegistration()
            notification.resolve(t('onboarding.registration-generic-success'))
            return { success: true, data }
        } catch (err) {
            const errorMessage = getErrorMessage(err, t)
            notification.reject(errorMessage)
            error.value = errorMessage
            return { success: false, error: errorMessage }
        } finally {
            isLoading.value = false
        }
    }

    const signUp = async ({ t }: { t: TranslateFunction }) => {
        isLoading.value = true
        error.value = null
        const notification = push.promise(t('onboarding.registration-loading'))

        try {
            const profile = onboardingStore.profile as Profile

            const avatar = await handleAvatarUpload(
                profile.file,
                // profile.avatar,
            )
            const { data, error }: any = await registerPublicApi({
                profile,
                avatar,
            })

            if (error) {
                throw new Error(error.message)
            }

            // await handlePostRegistration()
            notification.resolve(t('success'))
            return { success: true, data }
        } catch (err) {
            const errorMessage = getErrorMessage(err, t)
            notification.reject(errorMessage)
            error.value = errorMessage
            return { success: false, error: errorMessage }
        } finally {
            isLoading.value = false
        }
    }

    const validateProfileData = async (profile: Profile) => {
        if (!profile.name || !profile.username) {
            throw new Error('MISSING_PROFILE_INFO')
        }
    }

    const handleAvatarUpload = async (
        file: File | null,
        // existingAvatar: any,
    ) => {
        if (file) {
            try {
                const uploadResult = await upload({
                    key: Date.now(),
                    formdata: file,
                })
                if (!uploadResult)
                    return {
                        url: 'https://storage.googleapis.com/shared-uploads/64e57210f80ed5fd1e5629b1/default-avatar-icon-of-social-media-user-vector.jpg',
                    }
                return uploadResult
            } catch {
                return {
                    url: 'https://storage.googleapis.com/shared-uploads/64e57210f80ed5fd1e5629b1/default-avatar-icon-of-social-media-user-vector.jpg',
                }
            }
        }
    }

    const buildRequestBody = (profile: Profile, avatar: any): RequestBody => {
        const body: RequestBody = {
            createdAccount: true,
        }

        const mappings = {
            name: profile.name || undefined,
            dob: profile.dob ? dayjs(profile.dob).toISOString() : undefined,
            gender: profile.gender || undefined,
            username: profile.username || undefined,
            otp: profile.otp || undefined,
            phone: profile.phone || undefined,
            countryCode: profile.countryCode || undefined,
            email: account.value?.profile?.email || undefined,
            refreshToken: refreshToken.value || undefined,
            password: profile?.password || undefined,
            validated: {
                email: !!profile.validated?.email,
                phone: !!profile.validated?.phone,
            },
        }

        // Add fields that have values (giữ cả các giá trị boolean)
        Object.entries(mappings).forEach(([key, value]) => {
            if (value !== undefined) {
                // Giữ giá trị boolean (true/false)
                body[key as keyof RequestBody] = value
            }
        })

        // Add avatar if exists
        if (avatar?.url) {
            body.avatar = {
                _id: avatar?._id,
                url: avatar.url,
            }
        }

        return body
    }

    const buildRequestBodySignUp = (
        profile: Profile,
        avatar: any,
    ): RequestBody => {
        const body: RequestBody = {
            createdAccount: true,
        }

        const mappings = {
            name: profile.name || undefined,
            dob: profile.dob ? dayjs(profile.dob).toISOString() : undefined,
            gender: profile.gender || undefined,
            // username: profile.username || undefined,
            otp: profile.otp || undefined,
            phone: profile.phone || undefined,
            countryCode: profile.countryCode || undefined,
            email: profile?.email || undefined,
            // refreshToken: refreshToken.value || undefined,
            password: profile?.password || undefined,
            validated: {
                otp: true,
                phone: true,
            },
        }

        // Add fields that have values (giữ cả các giá trị boolean)
        Object.entries(mappings).forEach(([key, value]) => {
            if (value !== undefined) {
                // Giữ giá trị boolean (true/false)
                body[key as keyof RequestBody] = value
            }
        })

        // Add avatar if exists
        if (avatar?.url) {
            body.avatar = {
                _id: avatar?._id,
                url: avatar.url,
            }
        }

        return body
    }

    const registerUser = async ({
        profile,
        avatar,
    }: {
        profile: Profile
        avatar: any
    }) => {
        const requestBody = buildRequestBody(profile, avatar)

        const { data, error } = await useAsyncData('register-phr', async () => {
            return await $fetchWellcare(wellcareUrl.registerIdpOnboarding(), {
                method: 'POST',
                body: JSON.stringify(requestBody),
            })
        })

        if (error.value) {
            throw new Error(
                (error as any).value?.data?.message ||
                    error.value.message ||
                    'Registration failed',
            )
        }

        return { data: data.value, error: error.value }
    }

    const registerPublicApi = async ({
        profile,
        avatar,
    }: {
        profile: Profile
        avatar: any
    }) => {
        const requestBody = buildRequestBodySignUp(profile, avatar)

        const { data, error } = await useAsyncData('register-phr', async () => {
            return await $fetchWellcare(wellcareUrl.registerUserPublic(), {
                method: 'POST',
                body: JSON.stringify(requestBody),
                headers: {
                    version: '2',
                },
            })
        })

        if (error.value) {
            throw new Error(
                (error as any).value?.data?.message ||
                    error.value.message ||
                    'Registration failed',
            )
        }

        return { data: data.value, error: error.value }
    }

    const handlePostRegistration = async () => {
        await getUserManager.signinSilent()
        await refreshUserInfo()
    }

    const getErrorMessage = (err: unknown, t: TranslateFunction): string => {
        if (!(err instanceof Error)) {
            return t('onboarding.registration-generic-error')
        }

        const errorMessages = {
            MISSING_PROFILE_INFO: t('onboarding.missing-profile-info'),
            FILE_UPLOAD_FAILED: t('onboarding.file-upload-failed'),
            'Network Error': t('onboarding.network-error'),
            'otp not match': t('otp-not-match'),
            'otp is used': t('otp-is-used'),
            'otp was expired': t('otp-was-expired'),
            'missing otp': t('missing-otp'),
        }

        return (
            errorMessages[err.message as keyof typeof errorMessages] ||
            err.message ||
            t('onboarding.registration-generic-error')
        )
    }

    const checkPhone = async (val: {
        t: TranslateFunction
        phone: string
        countryCode: string
        disabledNotification?: boolean
    }) => {
        isLoading.value = true
        error.value = null
        const disabledNotification = val.disabledNotification || false
        const notification = push.promise(
            val.t('onboarding.phone-check-loading'),
        )

        try {
            const phone = val.phone || onboardingStore.profile.phone
            const countryCode =
                val.countryCode || onboardingStore.profile.countryCode

            if (!phone || !countryCode) {
                throw new Error('MISSING_PHONE_OR_COUNTRY_CODE')
            }

            const { data, error } = await useAsyncData(
                'check-phone',
                async () => {
                    const response = await $fetchWellcare(
                        wellcareUrl.checkPhone(phone, countryCode),
                        {
                            method: 'GET',
                        },
                    )
                    return response
                },
            )

            const code = data.value?.code

            if (error.value) {
                notification.clear()
                return {
                    success: false,
                    data: {
                        value: {
                            code: error.value.statusCode,
                        },
                    },
                }
            }

            switch (code) {
                case 200:
                    if (!disabledNotification) {
                        notification.resolve(
                            val.t('onboarding.phone-check-success'),
                        )
                    } else {
                        notification.clear()
                    }
                    break
                case 404:
                    notification.clear()
                    break
                case 400:
                    notification.clear()
                    break
                default:
                    notification.clear()
                    break
            }
            return { success: true, data }
        } catch (err) {
            const errorMessage =
                err instanceof Error
                    ? err.message
                    : val.t('onboarding.phone-check-error')
            if (!disabledNotification) {
                notification.reject(errorMessage)
            } else {
                notification.clear()
            }
            error.value = errorMessage
            return { success: false, error: errorMessage }
        } finally {
            isLoading.value = false
        }
    }

    return {
        registerPhr,
        checkPhone,
        isLoading,
        error,
        signUp,
    }
}
