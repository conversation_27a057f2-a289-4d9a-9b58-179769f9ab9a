import type { IVitalSign } from '~/models/page/health-records'

export function useVitalSigns() {
  const vitalSigns: Ref<IVitalSign[]> = ref([
    {
      label: 'Heart rate',
      icon: 'material-symbols:favorite-rounded',
      value: '40',
      unit: 'beats/min',
      bgColor: 'bg-red-50 dark:bg-red-950',
      iconColor: 'text-red-500 dark:text-red-600',
      lastUpdated: '2024-08-28T10:30:00Z',
    },
    {
      label: 'Blood Pressure',
      icon: 'material-symbols:local-hospital-rounded',
      value: '120/80',
      unit: 'mmHg',
      bgColor: 'bg-sky-50 dark:bg-sky-950',
      iconColor: 'text-sky-500 dark:text-sky-600',
      lastUpdated: '2024-08-28T10:30:00Z',
    },
    {
      label: 'Temperature',
      icon: 'material-symbols:thermostat-rounded',
      value: '98.6',
      unit: '°C',
      bgColor: 'bg-yellow-50 dark:bg-yellow-950',
      iconColor: 'text-yellow-500 dark:text-yellow-600',
      lastUpdated: '2024-08-28T10:30:00Z',
    },
    {
      label: 'Respiratory Rate',
      icon: 'material-symbols:air-rounded',
      value: '16',
      unit: 'breaths/min',
      bgColor: 'bg-green-50 dark:bg-green-950',
      iconColor: 'text-green-500 dark:text-green-600',
      lastUpdated: '2024-08-28T10:30:00Z',
    },
    {
      label: 'Oxygen Saturation',
      icon: 'material-symbols:oxygen-saturation-rounded',
      value: '98',
      unit: '%',
      bgColor: 'bg-purple-50 dark:bg-purple-950',
      iconColor: 'text-purple-500 dark:text-purple-600',
      lastUpdated: '2024-08-28T10:30:00Z',
    },
  ])

  return { vitalSigns }
}
