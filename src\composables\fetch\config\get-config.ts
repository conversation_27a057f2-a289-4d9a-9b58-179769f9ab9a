import type { JsonAppConfig } from '~/models/app-json-config'

const DEFAULT_CONFIG: JsonAppConfig = {
    navigation: {
        patient: [
            {
                label: 'Records',
                activeIcon: 'lets-icons:home-duotone',
                defaultIcon: 'lets-icons:home-duotone',
                size: 28,
                to: '/patient/health-records',
            },
            {
                label: 'Services',
                activeIcon: 'lets-icons:calendar-duotone',
                defaultIcon: 'lets-icons:calendar-duotone',
                size: 26,
                to: '/patient/services',
            },
            {
                label: 'HealthGPT',
                activeIcon: 'ic:twotone-auto-awesome',
                defaultIcon: 'ic:twotone-auto-awesome',
                size: 26,
                to: '/patient/health-gpt',
            },
            {
                label: 'Edu-hub',
                activeIcon: 'lets-icons:book-duotone',
                defaultIcon: 'lets-icons:book-duotone',
                size: 26,
                to: '/patient/edu-hub',
            },
            {
                label: 'Account',
                activeIcon: 'lets-icons:setting-line-duotone',
                defaultIcon: 'lets-icons:setting-line-duotone',
                to: '/patient/settings',
                size: 26,
                hideOnWeb: true,
            },
        ],
        provider: [
            {
                label: 'Home',
                activeIcon: 'lets-icons:home-duotone',
                defaultIcon: 'lets-icons:home-duotone',
                to: '/provider/home',
                size: 28,
            },
            {
                label: 'My Patients',
                activeIcon: 'solar:users-group-rounded-bold-duotone',
                defaultIcon: 'solar:users-group-rounded-bold-duotone',
                to: '/provider/patients',
                size: 28,
            },
            {
                label: 'Account',
                activeIcon: 'lets-icons:setting-line-duotone',
                defaultIcon: 'lets-icons:setting-line-duotone',
                to: '/provider/settings',
                size: 28,
                hideOnWeb: true,
            },
        ],
    },
    pages: {
        eduhub: {
            checkMembership: true,
            UiBlocks: ['category', 'article'],
        },
        services: {
            UiBlocks: [
                'membership',
                'personal-doctor',
                'telemedicine',
                'mental-health',
                'health-program',
            ],
        },
        'health-records': {
            UiBlocks: [
                'personal-doctor',
                'teleconsultation',
                'medical-report',
                'consultation-history',
                'health-program',
            ],
        },
        'health-gpt': {
            checkMembership: true,
        },
    },
    global: {},
}

let cacheConfig: JsonAppConfig | null = null

export function useJsonConfigApp() {
    // const { $fetchWellcare } = useNuxtApp()
    // const { data } = useAsyncData(
    //     'search-file-config',
    //     () =>
    //         $fetchWellcare('/file-management/file/search', {
    //             method: 'GET',
    //             query: {
    //                 filter: JSON.stringify({
    //                     name: 'config.json',
    //                     user: '621c42dd3fe3e1db977cfc1a',
    //                 }),
    //                 sort: '-createdAt',
    //             },
    //         }),
    //     {
    //         transform: (data) => data?.results[0],
    //     },
    // )
    const config = asyncComputed<JsonAppConfig>(async () => {
        try {
            if (cacheConfig) return cacheConfig
            const res = await fetch(
                // 'https://cdn.wellcare.vn/file-proxy/wellcare-ota-server/runtime/config.json?time=' +
                    '/default-config.json?time=' +
                    new Date().toISOString(),
                {
                    cache: 'no-store',
                },
            )
            const resJson = await res.json()
            console.log('fetch json ', resJson)
            cacheConfig = resJson
            return cacheConfig as JsonAppConfig
        } catch (error) {
            console.error(error)
            cacheConfig = DEFAULT_CONFIG
            return cacheConfig
        }
    }, DEFAULT_CONFIG)
    const getConfig = async () => {
        try {
            if (cacheConfig) return cacheConfig
            const res = await fetch(
                // 'https://cdn.wellcare.vn/file-proxy/wellcare-ota-server/runtime/config.json?time=' +
                    '/default-config.json?time=' +
                    new Date().toISOString(),
                {
                    cache: 'no-store',
                },
            )
            const resJson = await res.json()
            console.log('fetch json ', resJson)
            cacheConfig = resJson
            return cacheConfig as JsonAppConfig
        } catch (error) {
            console.error(error)
            cacheConfig = DEFAULT_CONFIG
            return cacheConfig
        }
    }
    return {
        config,
        getConfig,
    }
}
