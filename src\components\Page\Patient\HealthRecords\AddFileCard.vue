<script setup lang="ts">
import { ref, computed, useFileUpload } from '#imports'
import { useI18n } from 'vue-i18n'
import { push } from 'notivue'
import type { IFile } from '~/models'

const props = defineProps({
    project: {
        type: String,
        default: 'emr',
    },
    user: {
        type: String,
        default: '',
    },
    labels: {
        type: Array as () => string[],
        default: () => [],
    },
    tags: {
        type: Array as () => string[],
        default: () => [],
    },
    folder: {
        type: String,
        default: '',
    },
    asButton: {
        type: Boolean,
        default: true,
    },
})

const { t } = useI18n()
const emit = defineEmits(['upload-success', 'upload-error'])

const dragActive = ref(false)
const fileInput = ref<HTMLInputElement | null>(null)

// Payload cho useFileUpload
const uploadPayload = computed<IFile>(() => ({
    project: props.project,
    user: props.user,
    labels: `consultation:${props.user}`,
    tags: props.tags,
    folder: props.folder ? `/${props.folder}/` : '',
}))

const uploadedFiles = ref<
    Array<{ name: string; url: string; success: boolean }>
>([])

const { upload, loading, progress } = useFileUpload(
    uploadPayload,
    (response) => {
        emit('upload-success', response)

        uploadedFiles.value.push({
            name: response.results.name,
            url: response.results.url,
            success: true,
        })
    },
    (error) => {
        emit('upload-error', error)

        uploadedFiles.value.push({
            name: error.file?.name || 'Unknown file',
            url: '',
            success: false,
        })
    },
    () => {
        dragActive.value = false
    },
)

const validateFile = (file: File) => {
    const maxSize = 20 * 1024 * 1024
    if (file.size > maxSize) {
        console.log('Tệp quá lớn. Kích thước tối đa là 20MB')
        push.info(`Tệp quá lớn. Kích thước tối đa là 20MB`)
        return false
    }
    if (file.size > maxSize) {
        return false
    }

    return true
}

// Handle file selection
const handleFiles = async (files: FileList | null) => {
    if (!files?.length) return
    const fileArray = Array.from(files).filter(validateFile)

    // Prevent multiple uploads on same file selection
    if (loading.value) return

    for (const file of fileArray) {
        try {
            await upload(file)
        } catch (error) {
            console.error(`Upload failed for file ${file.name}:`, error)
        }
    }

    // Reset input value to ensure `change` event is triggered again
    if (fileInput.value) {
        fileInput.value.value = ''
    }
}

const handleClick = () => {
    if (fileInput.value) {
        fileInput.value.click()
    }
}

const handleDrag = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    if (e.type === 'dragenter' || e.type === 'dragover') {
        dragActive.value = true
    } else if (e.type === 'dragleave') {
        dragActive.value = false
    }
}

const handleDrop = (e: DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    dragActive.value = false

    const files = e.dataTransfer?.files
    handleFiles(files)
}
</script>

<template>
    <div
        :class="{ 'cursor-pointer': !asButton, 'cursor-auto': asButton }"
        @dragenter="!asButton && handleDrag"
        @dragover="!asButton && handleDrag"
        @dragleave="!asButton && handleDrag"
        @drop="!asButton && handleDrop"
    >
        <input
            ref="fileInput"
            type="file"
            class="hidden"
            @change="handleFiles($event.target.files)"
        />

        <slot
            :loading="loading"
            :progress="progress"
            :drag-active="dragActive"
            :handle-click="handleClick"
            :t="t"
        >
            <div
                :class="[
                    'flex h-32 flex-col items-center justify-center gap-2 rounded-xl border-2 border-dashed transition-colors',
                    dragActive
                        ? 'border-primary bg-primary/5'
                        : 'border-zinc-300',
                    loading ? 'cursor-wait' : 'cursor-pointer',
                ]"
                @click="handleClick"
            >
                <template v-if="loading">
                    <div class="flex flex-col items-center gap-2">
                        <div
                            class="h-1 w-48 overflow-hidden rounded-full bg-zinc-200"
                        >
                            <div
                                class="h-full bg-primary transition-all duration-300"
                                :style="{ width: `${progress}%` }"
                            />
                        </div>
                        <p class="text-sm text-zinc-500">{{ progress }}%</p>
                    </div>
                </template>

                <template v-else>
                    <iconify-icon
                        icon="lets-icons:file-dock-duotone"
                        class="text-5xl"
                        :class="dragActive ? 'text-primary' : 'text-zinc-400'"
                    />
                    <p
                        class="text-base font-medium"
                        :class="dragActive ? 'text-primary' : 'text-zinc-500'"
                    >
                        {{ dragActive ? t('drop-files-here') : t('upload') }}
                    </p>
                </template>
            </div>
        </slot>
    </div>
</template>

<i18n lang="json">
{
    "vi": {
        "drop-files-here": "Kéo và thả tập tin vào đây"
    },
    "en": {
        "drop-files-here": "Drop files"
    }
}
</i18n>
