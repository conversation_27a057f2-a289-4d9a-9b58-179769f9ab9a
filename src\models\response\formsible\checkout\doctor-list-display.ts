import type {
    IChoice,
    IValidation,
    IFetchElement,
    EComponentType,
} from '../element'

export interface IPersonalDoctorList {
    id: number
    label: string // Personal Doctor List
    component: EComponentType // 'multiple_choice'
    description: string // Select one option
    choices: IChoice[]
    fetch?: IFetchElement
    validations?: IValidation[]
}

// Example:
// {
//   "id": "personal-doctor-list",
//   "label": "Personal Doctor",
//   "component": "multiple_choice",
//   "description": "Select one option from the list.",
//   "placeholder": "Choose an option",
//   "choices": [
//     {
//       "label": "<PERSON><PERSON> (<PERSON><PERSON><PERSON> thần kinh)",
//       "value": "1"
//     },
//     {
//       "label": "Đỗ Nam Trung (<PERSON>)",
//       "value": "2"
//     }
//   ],
//   "key": "patient",
//   "fetch": {}
// }
