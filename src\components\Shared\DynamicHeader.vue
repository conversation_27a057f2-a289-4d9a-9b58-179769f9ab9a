<template>
    <div :class="{ 'pt-safe': isSafeArea }">
        <slot name="detailed" />
        <transition-group name="state-message" tag="div" class="flex flex-col">
            <Message
                v-for="msg of messages"
                :key="msg?.id"
                :severity="msg?.severity"
                :life="msg?.life"
                :icon="msg?.icon"
                :close-icon="msg?.closeIcon"
                :closable="msg?.closable"
                size="small"
                class="mt-4"
                @close="closeAlert(msg)"
                @life-end="closeAlert(msg)"
            >
                {{ msg?.content }}
            </Message>
        </transition-group>
        <Transition
            enter-active-class="transition ease-out duration-300"
            enter-from-class="transform -translate-y-full opacity-0"
            leave-active-class="transition ease-in duration-300"
            leave-to-class="transform -translate-y-full opacity-0"
        >
            <div
                v-show="showHeader"
                ref="headerEl"
                :class="[
                    'fixed left-0 right-0 top-0 z-10 bg-white pt-5 shadow-[0_8px_24px_rgba(149,157,165,0.2)] sm:hidden dark:bg-black',
                    {
                        'py-3': !isNative,
                        'pb-3 pt-safe': isNative,
                    },
                ]"
            >
                <div v-show="showHeader" class="px-5">
                    <slot name="header" />
                </div>
                <transition-group
                    name="state-message"
                    tag="div"
                    class="flex flex-col"
                >
                    <Message
                        v-for="msg of messages"
                        :key="msg?.id"
                        :severity="msg?.severity"
                        :life="msg?.life"
                        :icon="msg?.icon"
                        :close-icon="msg?.closeIcon"
                        :closable="msg?.closable"
                        size="small"
                        class="mt-4"
                        @close="closeAlert(msg)"
                        @life-end="closeAlert(msg)"
                    >
                        {{ msg?.content }}
                    </Message>
                </transition-group>
            </div>
        </Transition>
    </div>
</template>

<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
const THRESHOLD = 50

defineProps({
    isSafeArea: {
        type: Boolean,
        default: true,
    },
})

const { y } = useWindowScroll()
const { remove, getLast } = useHeaderAlert()

const showHeader = computed(() => y.value >= THRESHOLD)
const isNative = computed(() => Capacitor.isNativePlatform())
const messages = computed(() => getLast(-3))
const closeAlert = (msg: any) => remove(msg.id)
</script>
