<script setup lang="ts">
import type { PropType } from '#imports'
import type { ComponentBanner } from '~/models'

const { banner } = defineProps({
  banner: {
    type: Object as PropType<ComponentBanner>,
    default: () => ({}),
  },
})

defineEmits(['click'])
</script>
<template>
  <div
    class="fixed left-0 top-0 z-50 h-full bg-blue-500 p-4 text-white"
    @click="$emit('click')"
  >
    <p>This is a left banner {{ banner }}</p>
  </div>
</template>
