import { useClipboard, useShare } from '#imports'

/**
 * useActionToolkit - A composable providing utilities for sharing and copying URLs.
 *
 * This composable combines functionalities for sharing and copying URLs, leveraging
 * native sharing and clipboard capabilities when supported. It provides fallback mechanisms
 * to ensure functionality in environments where either feature may be unsupported.
 *
 * Functions:
 * - copySource: Attempts to copy a given URL to the clipboard. If successful, it calls the optional
 *   `onCopySuccess` callback. If unsuccessful, it calls `onCopyFailure`.
 * - shareSource: Attempts to share a URL along with a title and message using the native sharing
 *   feature. If sharing is unsupported or fails, it falls back to copying the URL.
 *
 * Usage:
 * Use `copySource` to directly copy a URL to the clipboard, with optional success/failure callbacks.
 * Use `shareSource` to try sharing a URL; if unavailable or failing, it automatically falls back to copying.
 *
 * @returns {Object} An object containing:
 *  - `isShareSupported`: Boolean reactive indicating if sharing is supported.
 *  - `shareSource`: Function to share a URL with fallback to copying.
 *  - `isClipboardSupported`: Boolean reactive indicating if clipboard copying is supported.
 *  - `copySource`: Function to copy a URL with success/failure callbacks.
 *
 * @example
 * const { copySource, shareSource, isShareSupported, isClipboardSupported } = useActionToolkit()
 *
 * const url = 'https://example.com'
 * const title = 'Check out this website'
 * const message = 'This is an amazing resource for learning!'
 *
 * copySource(
 *  url,
 *  () => console.log('URL copied successfully!'),
 *  () => console.log('Failed to copy URL.')
 * )
 *
 * shareSource(
 *  title,
 *  message,
 *  url,
 *  () => console.log('URL copied as fallback!'),
 *  () => console.log('Failed to share or copy URL.')
 * )
 */

export function useActionToolkit() {
    const { share, isSupported: isShareSupported } = useShare()
    const {
        copy,
        copied: isCopied,
        isSupported: isClipboardSupported,
    } = useClipboard()

    const copySource = async (
        url: string,
        onCopySuccess?: () => void,
        onCopyFailure?: () => void,
    ) => {
        if (isClipboardSupported.value) {
            try {
                await copy(url)
                onCopySuccess?.()
            } catch {
                onCopyFailure?.()
            }
        } else {
            onCopyFailure?.()
        }
    }

    const shareSource = async (
        title: string,
        message: string,
        url: string,
        onCopySuccess?: () => void,
        onCopyFailure?: () => void,
    ) => {
        if (isShareSupported.value) {
            try {
                await share({ title, text: message, url })
            } catch {
                // If sharing fails, fallback to copying
                await copySource(url, onCopySuccess, onCopyFailure)
            }
        } else {
            // Fallback to copying when sharing isn't supported
            await copySource(url, onCopySuccess, onCopyFailure)
        }
    }

    return {
        isClipboardSupported,
        isCopied,
        copySource,
        isShareSupported,
        shareSource,
    }
}
