import { ElasticIndex } from '~/models'
import type {
    HitNotionContentHealthProgram,
    IHealthProgramCalendar,
} from '~/models'

export function usePagePatientServicesHealthProgramsBabyDevelopment() {
    const dayjs = useDayjs()
    const { t } = useI18n()

    const { user } = useUserInfo({
        scope: ['meta'],
    })

    const parseLabel = (label: string) => {
        const [unit, valueStr] = label.split(':')

        if (!unit || !valueStr) {
            throw new Error('Invalid label format. Expected format: "month:1".')
        }

        const value = parseInt(valueStr, 10)

        return { unit, value }
    }

    const transformData = (
        articles: HitNotionContentHealthProgram[],
        baby_dob: number,
    ): IHealthProgramCalendar[] => {
        const timePriority: Record<string, number> = { month: 1, year: 2 }

        const parsedArticles = articles.map(({ page }) => {
            const { unit, value } = parseLabel(page?.properties?.Labels || '')
            return {
                unit,
                value,
                label: t(page?.properties?.Labels || ''),
                metadata: {
                    cover: page?.cover?.url,
                    name: page?.properties?.Name,
                    slug: page?.properties?.Slug,
                },
            }
        })

        const sortedArticles = parsedArticles.sort((a, b) =>
            timePriority[a.unit] !== timePriority[b.unit]
                ? timePriority[a.unit] - timePriority[b.unit]
                : a.value - b.value,
        )

        const activeIndex = sortedArticles.findIndex(
            ({ unit, value }) =>
                (unit === 'year' ? value * 12 : value) >= baby_dob,
        )

        return sortedArticles.map((article, index) => ({
            ...article,
            isActive: index <= activeIndex,
            isToday: index === activeIndex,
        }))
    }

    const baby_dob: ComputedRef<number> = computed(() => {
        const today = dayjs().startOf('day')
        const babyDate = user.value?.meta?.baby_date

        if (!babyDate) return 0

        return today.diff(babyDate, 'month')
    })

    const { hits: articles, loading } =
        useFetchElasticWithDefault<HitNotionContentHealthProgram>(
            ElasticIndex.NOTION_CONTENT_HEALTH_PROGRAM,
            {
                filters: [
                    {
                        term: {
                            'page.properties.Program.properties.Key.keyword':
                                'baby-development',
                        },
                    },
                ],
                size: 50,
            },
        )

    const times = computed(() => transformData(articles.value, baby_dob.value))

    return { times, loading }
}
