import type { IDate } from '../../transform'
import type { INotionPage, NotionFiles } from '../notion'
import type { IHitNotion } from './notion'
import type { NotionEduHub } from './notion-edu-hub'
import type { NotionProviders } from './notion-providers'

export interface NotionAskDoctor {
  _id?: string
  'Date Created'?: string
  'Last Updated?'?: string
  'Parent item'?: INotionPage<NotionAskDoctor>[]
  'provider._id'?: string
  'provider.name'?: string
  'Sub-item'?: INotionPage<NotionAskDoctor>[]
  AnsweredBy?: INotionPage<NotionProviders>
  chiefComplaint?: string
  Description?: string
  EduHub?: INotionPage<NotionEduHub>
  Files?: NotionFiles[]
  ModifiedAt?: IDate
  note?: string
  Providers?: INotionPage<NotionProviders>
  PublishedAt?: IDate
  Question?: string
  Select?: string
  Status?: string
  Tags?: string[]
  Type?: string
}

export interface HitNotionAskDoctor extends IHitNotion {
  page?: INotionPage<NotionAskDoctor>
}
