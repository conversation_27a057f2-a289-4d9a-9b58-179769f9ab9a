import { useRuntimeConfig } from '#imports'

interface IHiddenField {
    key: string
    value: any
    component?: string
}

export const form = (hidden_fields?: IHiddenField[]) => {
    const runtimeConfig: any = useRuntimeConfig()
    const appEnv = runtimeConfig.public.app.env

    return {
        objectId: appEnv === 'production' ? 'TImCNeUTrE' : 'Cm3IrCvV9u',
        pages: [
            {
                id: 'main',
                type: 'input',
                elements: [
                    {
                        id: 'longText',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Trả lời bằng tin nhắn',
                                component: 'long_text',
                                description:
                                    'Mỗi 50-160 ký tự được tính là 1 phút gọi tư vấn.',
                                placeholder: 'Nhập phản hồi gửi bệnh nhân',
                                key: 'longText',
                                validations: [
                                    {
                                        rule: 'required',
                                    },
                                ],
                            },
                        },
                    },
                ],
            },
        ],
        title: 'Provider Text Response',
        submission: 'remote',
        space: {
            __type: 'Pointer',
            className: 'Space',
            objectId: appEnv === 'production' ? 'opVNsUfKoM' : 'ZhKJvCjuhu',
        },
        computes: hidden_fields?.map((hidden_field: IHiddenField) => {
            return {
                key: hidden_field.key,
                value: hidden_field.value,
                component: 'hidden_field',
            }
        }),
    }
}
