<script lang="ts" setup>
import type { PropType } from '#imports'

type TState = 'subscribed' | 'unsubscribed'

defineProps({
    show: {
        type: String as PropType<TState>,
        default: 'full',
    },
})

const { user } = useUserInfo({
    scope: ['meta'],
})

const { t } = useI18n()
const { programs } = usePagePatientServicesHealthProgramsProgram()

const subscribedPrograms = computed(() => {
    return ['preg_subscribe', 'baby_subscribe'].reduce((sum, key) => {
        return sum + (user.value.meta?.[key] ? 1 : 0)
    }, 0)
})

const isSubscribed = (programKey: string): boolean => {
    const { meta } = user.value || {}

    if (!meta) return true

    switch (programKey) {
        case 'pregnancy-diary':
            return meta.preg_subscribe
        case 'baby-development':
            return meta.baby_subscribe
        default:
            return true
    }
}
</script>

<template>
    <SharedPageSection
        v-if="programs.length"
        :title="t('health:programs')"
    >
        <PagePatientServicesHealthProgramCard
            v-for="(program, index) in programs"
            :key="program.key"
            :program="program"
            :class="[
                'mt-3 mx-6 flex items-stretch gap-3 overflow-hidden rounded-md border dark:border-gray-700',
                {
                    hidden: show === 'subscribed' && !isSubscribed(program.key),
                    '!mt-0':
                        index === 0 || (show === 'subscribed' && subscribedPrograms === 1 && isSubscribed(program.key)),
                },
            ]"
        />
    </SharedPageSection>
</template>
