<script setup lang="ts">
import { defineAsyncComponent, definePageMeta } from '#imports'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const Teleconsultation = defineAsyncComponent(
    () => import('~/components/Page/Provider/Patients/Teleconsultation.vue'),
)
const Medication = defineAsyncComponent(
    () =>
        import('~/components/Page/Patient/HealthRecords/Medication/index.vue'),
)
const LabTest = defineAsyncComponent(
    () => import('~/components/Page/Patient/HealthRecords/LabTest/index.vue'),
)
const Vitals = defineAsyncComponent(
    () => import('~/components/Page/Patient/HealthRecords/Vitals/index.vue'),
)
const Immunization = defineAsyncComponent(
    () =>
        import(
            '~/components/Page/Patient/HealthRecords/Immunization/index.vue'
        ),
)

const { t } = useI18n()
const { params }: any = useRoute()
const layoutStore = useLayoutStore()

const { getUserById } = useFetchUserInfo()
const patientInfo = await getUserById({
    _id: params.id,
    query: {
        fields: 'name,avatar',
    },
})

// Constants
const MEDICAL_SECTIONS = [
    {
        label: 'medication',
        icon: 'material-symbols:medication',
        to: '/patient/health-records/[id]/prescriptions',
        component: Medication,
    },
    {
        label: 'lab',
        icon: 'material-symbols:science',
        to: '/patient/health-records/[id]/lab',
        component: LabTest,
    },
    {
        label: 'vitals',
        icon: 'mdi:clipboard-vitals',
        // to: '/patient/health-records/[id]/vital-signs',
        component: Vitals,
    },
    {
        label: 'immunization',
        icon: 'material-symbols:vaccines-outline-rounded',
        // to: '/patient/health-records/[id]/vaccination',
        component: Immunization,
    },
]

onMounted(() => layoutStore.setTitle(`${patientInfo.results?.name}`))
</script>

<template>
    <div class="mx-auto grid max-w-6xl grid-flow-row grid-cols-1 gap-4">
        <div class="flex flex-col">
            <Suspense>
                <template #default>
                    <component
                        :is="Teleconsultation"
                        :member-activated-id="params.id"
                    />
                </template>
                <template #fallback>
                    <Skeleton height="200px" width="100%" class="rounded-xl" />
                </template>
            </Suspense>
            <template v-for="section in MEDICAL_SECTIONS" :key="section.label">
                <Suspense>
                    <template #default>
                        <component
                            :is="section.component"
                            :title="t(`${section.label}`)"
                            :member-activated="{
                                related: {
                                    _id: params.id,
                                },
                            }"
                            :is-read-only="true"
                            :to="section.to"
                        />
                    </template>
                    <template #fallback>
                        <Skeleton
                            height="200px"
                            width="100%"
                            class="rounded-xl"
                        />
                    </template>
                </Suspense>
            </template>
        </div>

        <!-- Right Column -->
        <!-- <div class="flex flex-col">
            <template
                v-for="section in MEDICAL_SECTIONS.slice(
                    Math.floor(MEDICAL_SECTIONS.length / 2),
                )"
                :key="section.label"
            >
                <Suspense>
                    <template #default>
                        <component
                            :is="section.component"
                            :title="t(`${section.label}`)"
                            :to="section.to"
                        />
                    </template>
                    <template #fallback>
                        <Skeleton
                            height="200px"
                            width="100%"
                            class="rounded-xl"
                        />
                    </template>
                </Suspense>
            </template>
        </div> -->
    </div>
</template>

<style scoped>
.speed-dial-button {
    animation: bounce 0.3s ease-out;
}

@keyframes bounce {
    0% {
        transform: scale(0.8);
    }
    50% {
        transform: scale(1.2);
    }
    100% {
        transform: scale(1);
    }
}

.family-member-slide :deep(.swiper-wrapper) {
    display: flex !important;
    justify-content: center !important;
    align-items: center !important;
}

.no-scrollbar {
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.no-scrollbar::-webkit-scrollbar {
    display: none;
}
</style>

<i18n lang="json">
{
    "vi": {
        "medical-history": "Tiền sử bệnh án",
        "lab": "Xét nghiệm",
        "vitals": "Chỉ số sức khỏe",
        "immunization": "Tiêm chủng"
    },
    "en": {
        "medical-history": "Medical History",
        "lab": "Lab",
        "vitals": "Vitals",
        "immunization": "Immunization"
    }
}
</i18n>
