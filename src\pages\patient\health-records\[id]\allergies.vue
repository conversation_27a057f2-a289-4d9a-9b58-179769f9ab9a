<script setup lang="ts">
import { useAllergies } from '~/composables/page/patient/health-records/allergies'

definePageMeta({
    layout: 'nested-page',
})

const { allergies } = useAllergies()
</script>

<template>
    <div class="px-6">
        <h2>Allergies</h2>
        <div class="flex flex-col gap-3">
            <PagePatientHealthRecordsAllergyCard
                v-for="allergy in allergies"
                :key="allergy.name"
                :allergy="allergy"
            />
            <Button
                severity="secondary"
                label="Add Allergy"
                icon="pi pi-plus"
            />
        </div>
    </div>
</template>
