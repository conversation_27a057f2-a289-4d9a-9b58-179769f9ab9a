<script setup lang="ts">
import { useI18n } from 'vue-i18n'
definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})
const { t } = useI18n()
const { programs } = usePagePatientServicesHealthProgramsProgram()
</script>

<template>
    <div class="px-6">
        <h1 class="mb-1">{{ t('health:programs') }}</h1>
        <PagePatientServicesCard
            v-for="(program, index) in programs"
            :key="program.key"
            :program="program"
            :class="[
                'block',
                {
                    'mt-2': index === 0,
                    'mt-6': index !== 0,
                    'mb-4': index === programs.length - 1,
                },
            ]"
        />
    </div>
</template>
