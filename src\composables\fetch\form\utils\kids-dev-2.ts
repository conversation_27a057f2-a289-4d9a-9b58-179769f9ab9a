import { useRuntimeConfig } from '#imports'
import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => {
    const runtimeConfig = useRuntimeConfig()
    const appEnv = runtimeConfig.public.app.env

    return {
        objectId: appEnv === 'production' ? '3vCtsPNxum' : '7h6gS7vvF1',
        pages: [
            {
                id: 'Starting Page',
                elements: [
                    {
                        id: 'm2jwptahb3',
                        type: 'display',
                        properties: {
                            display: {
                                component: 'html_block_content',
                                tag: 'p',
                                content:
                                    'Các mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. H<PERSON>y tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 2 tháng tuổi. <PERSON><PERSON><PERSON> thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. Bác sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.\n',
                            },
                        },
                    },
                    {
                        id: 'm2jwq2chnyj',
                        type: 'display',
                        properties: {
                            display: {
                                component: 'html_block_media',
                                tag: 'img',
                                src: 'https://images.ctfassets.net/4h8s6y60f7sb/41VKJs6hwNqtEzDdCXYAGY/1458a54798476c2edf45a7b722395d91/NEW_Blog_Headers__28_.jpg?w=1440&fm=jpg&q=90',
                                alt: 'Nice picture',
                            },
                        },
                    },
                ],
                type: 'start',
            },
            {
                elements: [
                    {
                        id: 'm2jwrs2hy8',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Mắt nhìn vào khuôn mặt của cha mẹ',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwrs2hy8', //1
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2jwsd5gb3b',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Có vẻ rất vui khi được gặp, khi cha mẹ đang bước đến gần bé',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwsd5gb3b', //2
                                fetch: {},
                            },
                        },
                    },
                ],
                id: 'Giao tiếp xã hội và tình cảm',
                type: 'input',
            },
            {
                elements: [
                    {
                        id: 'm2jwtzret0s',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Miệng phát ra âm thanh khác, ngoài tiếng khóc',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwtzret0s',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2jwue2ujnt',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Phản ứng với âm thanh lớn',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwue2ujnt',
                                fetch: {},
                            },
                        },
                    },
                ],
                id: 'Ngôn ngữ và Giao tiếp',
                type: 'input',
            },
            {
                elements: [
                    {
                        id: 'm2jwvdkt0f',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Quan sát cha mẹ đang di chuyển gần đó',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwvdkt0f',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2jwvrpk2t',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Mắt nhìn vào một món đồ chơi trong vài giây',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwvrpk2t',
                                fetch: {},
                            },
                        },
                    },
                ],
                id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
                type: 'input',
            },
            {
                elements: [
                    {
                        id: 'm2jwwq91l16',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Ngẩng đầu lên khi nằm sấp',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwwq91l16',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2jwx3gysz',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Di chuyển cả hai tay và cả hai chân',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwx3gysz',
                                fetch: {},
                            },
                        },
                    },
                    {
                        id: 'm2jwxh1nb8',
                        type: 'input',
                        properties: {
                            input: {
                                label: 'Có thể mở lòng bàn tay nhanh chóng',
                                component: 'multiple_choice',
                                description: '',
                                placeholder: 'Choose an option',
                                choices: [
                                    {
                                        label: 'Có',
                                        value: 'yes',
                                    },
                                    {
                                        label: 'Không chắc',
                                        value: 'not_sure',
                                    },
                                    {
                                        label: 'Chưa',
                                        value: 'not_yet',
                                    },
                                ],
                                key: 'MultipleChoicem2jwxh1nb8',
                                fetch: {},
                            },
                        },
                    },
                ],
                id: 'Vận động/Phát triển thể chất',
                type: 'input',
            },
        ],
        title: '2 tháng ',
        submission: 'remote',
        status: 'draft',
        computes: hidden_fields.map((hidden_field: IHiddenField) => {
            return {
                key: hidden_field.key,
                value: hidden_field.value,
                component: 'hidden_field',
            }
        }),
    }
}
