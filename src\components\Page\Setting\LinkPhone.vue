<template>
    <div class="mx-auto max-w-md p-4 sm:p-6">
        <!-- Header Section -->
        <div class="mb-6 flex flex-col items-center gap-4 text-center">
            <div class="mb-4 flex justify-center">
                <iconify-icon
                    icon="fluent:person-link-20-regular"
                    class="text-5xl text-primary-600 dark:text-primary-400"
                />
            </div>
            <h1
                class="text-2xl font-bold text-surface-900 dark:text-surface-50"
            >
                {{ t('link-phone-title') }}
            </h1>
            <p
                class="mt-2 text-sm leading-relaxed text-surface-600 dark:text-surface-300"
            >
                {{ t('link-phone-description') }}
            </p>
            <Button
                :label="t('link-phone-action')"
                severity="primary"
                size="large"
                class="w-full max-w-xs"
                @click="showPhoneVerification = true"
            />
        </div>

        <!-- Main Card -->

        <SharedDialogPhoneVerification
            v-model:visible="showPhoneVerification"
            :user-id="user._id"
            @verification-complete="handleVerificationComplete"
        />
    </div>
</template>

<script lang="ts" setup>
import { appRoutes } from '~/constants'

const { t } = useI18n()
const { refreshTokenOidc } = useAppAuth()
const { user, refresh } = useUserInfo({ scope: ['_id', 'avatar'] })
const healthRecordStore = useHealthRecordStore()
const showPhoneVerification = ref(false)

const handleVerificationComplete = async () => {
    showPhoneVerification.value = false
    healthRecordStore.$reset()
    await refreshTokenOidc()
    await refresh()
    navigateTo(appRoutes.auth.callback)
}
</script>

<style></style>

<i18n lang="json">
{
    "en": {
        "link-phone-title": "Connect to Your Previous Health Records",
        "link-phone-description": "Access and manage your complete medical history by linking your phone number to your existing account.",
        "link-phone-subtitle": "Ready to Connect Your Health Records?",
        "link-phone-detail": "Link your phone number to retrieve your previous medical history, consultation records, and health data seamlessly.",
        "link-phone-action": "Connect"
    },
    "vi": {
        "link-phone-title": "Kết nối với hồ sơ sức khỏe trước đây",
        "link-phone-description": "Truy cập và quản lý toàn bộ lịch sử y tế bằng cách liên kết số điện thoại với tài khoản hiện có của bạn.",
        "link-phone-subtitle": "Sẵn sàng kết nối hồ sơ sức khỏe?",
        "link-phone-detail": "Liên kết số điện thoại để lấy lại lịch sử khám bệnh, hồ sơ tư vấn và dữ liệu sức khỏe một cách liền mạch.",
        "link-phone-action": "Kết nối"
    }
}
</i18n>
