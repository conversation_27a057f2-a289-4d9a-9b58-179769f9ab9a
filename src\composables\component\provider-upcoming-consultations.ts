import dayjs from 'dayjs'
import {
    type Ref,
    type ComputedRef,
    ref,
    computed,
    useFetchGenericApi,
} from '#imports'
import {
    GenericEndpoint,
    type IResponseConsultation,
    type Consultation,
} from '../../models'
export function useProviderUpcomingConsultations() {
    // const { user } = useUserInfo({ scope: ['provider._id'] as any })
    const laterAppointments: Ref<IResponseConsultation[]> = ref([])

    const { items } = useFetchGenericApi<IResponseConsultation>(
        GenericEndpoint.CONSULTATION,
        {
            filter: {
                // provider: user.value?.provider._id,
                state: {
                    $in: ['WAITING', 'INCONSULTATION', 'COMPLETED'],
                },
            },
            populate: [{ path: 'patient' }, { path: 'provider' }],
            size: 100,
        },
    )

    const todayAppointments: ComputedRef<Consultation[]> = computed(() => {
        return items.value
            .filter((i) => i.type == 'indepth')
            .filter((i) => ['INCONSULTATION', 'WAITING'].includes(i.state))
            .map((i) => {
                return {
                    ...i,
                    type: i.type.toUpperCase(),
                    status: i.state,
                    provider: {
                        name: i.provider?.name,
                        avatar: i.provider?.avatar?.url,
                    },
                    patient: {
                        name: i.patient?.name,
                        avatar: i.patient?.avatar?.url,
                    },
                    date: dayjs(i.date).format('MMM DD'),
                    chiefComplaint: i.reason,
                }
            })
    })

    const followUpQuestions: ComputedRef<Consultation[]> = computed(() => {
        return items.value
            .filter((i) => i.type == 'question')
            .filter((i) => ['INCONSULTATION', 'WAITING'].includes(i.state))
            .map((i) => {
                return {
                    ...i,
                    type: i.type.toUpperCase(),
                    status: i.state,
                    provider: {
                        name: i.provider?.name,
                        avatar: i.provider?.avatar?.url,
                    },
                    patient: {
                        name: i.patient?.name,
                        avatar: i.patient?.avatar?.url,
                    },
                    date: dayjs(i.date).format('MMM DD'),
                    chiefComplaint: i.reason,
                }
            })
    })

    const recentVisits: ComputedRef<Consultation[]> = computed(() => {
        return items.value
            .filter((i) => i.type == 'indepth')
            .filter((i) => i.state == 'COMPLETED')
            .map((i) => {
                return {
                    ...i,
                    type: i.type.toUpperCase(),
                    status: i.state,
                    provider: {
                        name: i.provider?.name,
                        avatar: i.provider?.avatar?.url,
                    },
                    patient: {
                        name: i.patient?.name,
                        avatar: i.patient?.avatar?.url,
                    },
                    date: dayjs(i.date).format('MMM DD'),
                    chiefComplaint: i.reason,
                }
            })
    })
    return {
        todayAppointments,
        followUpQuestions,
        laterAppointments,
        items,
        recentVisits,
    }
}
