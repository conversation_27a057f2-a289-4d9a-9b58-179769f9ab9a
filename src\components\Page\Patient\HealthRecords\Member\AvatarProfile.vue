<template>
    <div>
        <button
            v-tooltip="member.related?.name"
            :class="[
                'group relative mb-3 mt-2 flex flex-col items-center rounded-full transition',

                isActive ? '' : '',
            ]"
        >
            <Avatar
                shape="circle"
                :image="avatarUrl"
                :class="[
                    isActive
                        ? 'bg-primary-50-50 p-1 shadow-md'
                        : 'scale-75 contrast-50',

                    'rounded-full transition duration-300',
                ]"
                :pt="{
                    root: 'w-16 h-16',
                    image: 'text-2xl inline-flex items-center justify-center rounded-full bg-zinc-300 dark:bg-zinc-700 object-cover',
                }"
            />

            <button
                v-if="isActive"
                class="absolute -bottom-5 right-0 flex size-12 h-6 w-6 -translate-y-2/4 items-center justify-center rounded-full bg-primary text-white"
                @click="emit('open:menu')"
            >
                <iconify-icon class="text-md" icon="lets-icons:edit-fill" />
            </button>
        </button>
    </div>
</template>

<script setup lang="ts">
import type { Relationship } from '~/models'

interface Props {
    member: Relationship

    isActive: boolean

    isShowDelete: boolean
}

const props = defineProps<Props>()

const emit = defineEmits(['open:menu'])

const avatarUrl = computed<string>(
    () =>
        props.member.related?.avatar?.url ||
        `https://ui-avatars.com/api/?name=${props.member.related?.name}`,
)
</script>
