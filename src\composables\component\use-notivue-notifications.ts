import { push } from 'notivue'

const DURATION_DEFAULT = 3000

export const useNotivueNotifications = () => {
    const showNotification = (
        type: string,
        options: {
            title?: string
            message: string
            duration?: number
            props?: any
        },
    ) => {
        const notificationTypes: Record<string, any> = {
            success: push.success,
            error: push.error,
            warning: push.warning,
            info: push.info,
        }

        const notify = notificationTypes[type] || push.info
        console.log('push: ', options)
        notify({
            title: options.title,
            message: options.message,
            duration: options.duration || DURATION_DEFAULT,
            props: options.props ?? {},
        })
    }

    return {
        success: (options: {
            title?: string
            message: string
            duration?: number
        }) => showNotification('success', options),
        error: (options: {
            title?: string
            message: string
            duration?: number
        }) => showNotification('error', options),
        warning: (options: {
            title?: string
            message: string
            duration?: number
        }) => showNotification('warning', options),
        info: (options: {
            title?: string
            message: string
            duration?: number
        }) => showNotification('info', options),
    }
}
