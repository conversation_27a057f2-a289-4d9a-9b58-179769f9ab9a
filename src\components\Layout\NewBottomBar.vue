<script setup lang="ts">
import { Capacitor } from '@capacitor/core'
import { computed, navigateTo, useRoute } from '#imports'
import type { NavItem } from '~/models'

const props = defineProps<{ navItems: NavItem[] }>()

const onClickTab = async (index: number) => {
    navigateTo(props.navItems[index].to)
    // console.log(props.navItems.map((el) => el.to))
    // console.log(route.path)
}
const route = useRoute()
const { t } = useI18n()
const activeTab = computed(() =>
    props.navItems.findIndex((el) => el.to == route.path),
)

const isNative = computed(() => Capacitor.isNativePlatform())
</script>

<template>
    <div
        style="
            box-shadow:
                rgba(60, 64, 67, 0.3) 0 1px 2px,
                rgba(60, 64, 67, 0.15) 0 2px 6px 2px;
        "
        :class="[
            'bottom-bar fixed bottom-0 left-0 right-0 z-50 flex flex-row items-center justify-evenly rounded-t-xl bg-white py-3 shadow-[0_8px_27px_rgba(100,100,111,0.2)] backdrop-blur-sm sm:hidden dark:bg-surface-900',
            {
                'pb-safe': isNative,
            },
        ]"
    >
        <button
            v-for="(nav, index) in navItems"
            :key="index"
            :class="[
                'relative flex flex-1 flex-col items-center justify-center bg-white',
                activeTab == index
                    ? nav.activeColor || 'text-primary dark:text-primary'
                    : 'text-zinc-500',
            ]"
            @click="onClickTab(index)"
        >
            <div
                :class="[
                    nav.important
                        ? 'absolute -top-[25px] flex h-12 w-12 items-center justify-center rounded-full bg-primary-400 shadow-md'
                        : '',
                ]"
            >
                <iconify-icon
                    :class="[
                        nav.important
                            ? 'lightning-effect animate-pulse !text-white'
                            : '',
                        'text-2xl transition duration-500 ease-in-out active:scale-75',
                    ]"
                    :icon="
                        activeTab == index ? nav.activeIcon : nav.defaultIcon
                    "
                />
            </div>
            <small :class="['leading-none', nav.important ? 'mt-6' : '']">
                {{ t(`${nav?.label}`) }}
            </small>
        </button>
    </div>
</template>
<style scoped>
.bottom-bar::after {
    content: '';
    position: absolute;
    border-radius: 1000px;
    top: 0;
    z-index: -10;
    left: 50%;
    transform: translate(-60%, -30%);
    width: 60px;
    height: 55px;
}
</style>

<i18n lang="json">
{
    "vi": {
        "Records": "Lưu trữ",
        "Services": "Dịch vụ",
        "Account": "Tài khoản",
        "My Patients": "Bệnh nhân"
    },
    "en": {
        "Records": "Records",
        "Services": "Services",
        "Account": "Account",
        "My Patients": "My Patients"
    }
}
</i18n>
