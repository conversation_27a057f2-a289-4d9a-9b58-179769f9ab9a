<script setup lang="ts">
import { definePageMeta, useFetchElastic, useRoute } from '#imports'

import { ElasticIndex } from '~/models'

definePageMeta({
    layout: 'content',
    middleware: 'auth-capacitor',
})

const { params }: any = useRoute()

const layoutStore = useLayoutStore()

const { hit, loading }: any = useFetchElastic(ElasticIndex.NOTION_WEBSITE, {
    size: 1,
    filters: [
        {
            term: {
                'page.properties.Parent item.id.keyword':
                    '4a2c35d9-4a0a-4299-a714-d84de765adbf', // <PERSON><PERSON> cứu
            },
        },
        {
            term: {
                'page.properties.Slug.keyword': params?.slug,
            },
        },
    ],
})

watch(hit, () => {
    layoutStore.setTitle(hit.value.page.properties?.Name)
    layoutStore.setCover(hit.value.page.cover?.url)
})
</script>
<template>
    <div v-if="!loading" class="mt-4 space-y-4 px-4">
        <WArticleHeader :page="hit?.page" :blocks="hit?.page?.blocks" />
        <WArticle
            :page="hit?.page"
            :blocks="hit?.page?.blocks"
            body
            class="mt-4"
        />
    </div>
</template>
