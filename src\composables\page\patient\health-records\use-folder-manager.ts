import { ref, computed } from '#imports'

export function useFolderManager() {
    const folders = ref<Set<string>>(new Set(['/']))
    const currentPath = ref('/')

    const addFolder = (name: string) => {
        const path =
            currentPath.value === '/'
                ? `/${name}`
                : `${currentPath.value}/${name}`
        folders.value.add(path)
    }

    const setCurrentPath = (path: string) => {
        currentPath.value = path
    }

    const getCurrentPathFiles = computed(() => {
        return (files: any[]) => {
            return files.filter((file) => file.folder === currentPath.value)
        }
    })

    return {
        folders,
        currentPath,
        addFolder,
        setCurrentPath,
        getCurrentPathFiles,
    }
}
