<script setup lang="ts">
import { computed, useError } from '#imports'
const error = useError()
const { NODE_ENV } = process.env
const isShowLog = computed(() => NODE_ENV === 'development')
const errorStack = error.value.stack
  .split(/\r?\n/)
  .map((line) => `<span>${line}</span>`)
  .join('<br>')
</script>

<template>
  <div class="container pb-8">
    <WError
      :code="error.statusCode.toString()"
      :message="error.message || error.name"
    />
    <div
      v-show="isShowLog"
      class="bg-[# f5f5f5] mx-auto w-full max-w-[1000px] overflow-scroll rounded-xl border-t-[12px] border-t-red-400 p-10 leading-relaxed shadow-[0_8px_24px_rgba(149,157,165,0.2)]"
    >
      <div v-dompurify-html="errorStack"/>
    </div>
  </div>
</template>
