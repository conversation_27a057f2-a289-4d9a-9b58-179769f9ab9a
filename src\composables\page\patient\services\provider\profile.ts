import { ElasticIndex } from '~/models'
import type {
    HitNotionProviders,
    HitNotionWebsite,
    IProviderMetric,
    IProviderProfile,
    IProviderTag,
} from '~/models'

export function usePagePatientServicesProviderProfile(
    providerSlug: Ref<string> | ComputedRef<string>,
) {
    const dayjs = useDayjs()

    const providerFilters = computed(() => [
        {
            term: {
                'page.properties.Slug.keyword': providerSlug.value,
            },
        },
    ])

    const { hit: provider, loading: providerLoading } =
        useFetchElasticWithDefault<HitNotionProviders>(ElasticIndex.PROVIDERS, {
            size: 1,
            filters: providerFilters,
        })

    const contentFilters = computed(() => [
        {
            term: {
                'page.properties.Slug.keyword': providerSlug.value,
            },
        },
        {
            term: {
                'page.properties.Type.keyword': 'Person',
            },
        },
    ])

    const { hit: content, loading: contentLoading } =
        useFetchElasticWithDefault<HitNotionWebsite>(
            ElasticIndex.NOTION_WEBSITE,
            {
                size: 1,
                filters: contentFilters,
                _source: {
                    includes: ['page.properties.Sapo', 'blockstring'],
                },
            },
        )

    const { hits: specialtyHits, loading: specialtiesLoading } =
        useFetchElasticWithDefault<HitNotionWebsite>(
            ElasticIndex.NOTION_WEBSITE,
            {
                size: 150,
                filters: [
                    {
                        prefix: {
                            'page.properties.Labels.keyword': 'specialty:',
                        },
                    },
                ],
                _source: {
                    excludes: [],
                    includes: [
                        'page.properties.Labels',
                        'page.properties.Name',
                        'page.properties.Slug',
                    ],
                },
            },
        )

    const { hits: conditionHits, loading: conditionLoading } =
        useFetchElasticWithDefault<HitNotionWebsite>(
            ElasticIndex.NOTION_WEBSITE,
            {
                size: 1000,
                filters: [
                    {
                        prefix: {
                            'page.properties.Labels.keyword': 'condition:',
                        },
                    },
                ],
                _source: {
                    excludes: [],
                    includes: [
                        'page.properties.Labels',
                        'page.properties.Name',
                        'page.properties.Slug',
                    ],
                },
            },
        )

    const profile: ComputedRef<IProviderProfile> = computed(() => {
        const providerPage = provider.value?.page
        const outputMeta = provider.value?.output?.meta

        // Helper to map tags (specialties/conditions) to a consistent format
        const formatTags = (
            pageTags: any[] | undefined,
            hits: any[],
            typePrefix: string,
        ): IProviderTag[] =>
            pageTags
                ?.map((tag) => tag.properties.Slug)
                .map((slug: string) => {
                    return (
                        hits.find((hit: any) =>
                            hit.page.properties.Labels?.includes(
                                `${typePrefix}:${slug}`,
                            ),
                        ) || { page: { properties: { Name: slug } } }
                    )
                })
                .map((tag: any) => ({
                    name: tag.page.properties.Name || '',
                    key: tag.page.properties.Slug || '',
                })) || []

        // Format specialties and conditions using the helper
        const formatSpecialties = () =>
            formatTags(
                providerPage?.properties.Specialties,
                specialtyHits.value || [],
                'specialty',
            )

        const formatConditions = () =>
            formatTags(
                providerPage?.properties.Conditions,
                conditionHits.value || [],
                'condition',
            )

        // Metrics for profile
        const metrics: IProviderMetric[] = [
            {
                name: 'average-length',
                icon: 'ri-time-fill',
                label: outputMeta?.minuteAvg
                    ? Math.round(outputMeta.minuteAvg).toString()
                    : '-',
                unit: 'mins',
            },
            {
                name: 'satisfaction',
                icon: 'ri-thumb-up-fill',
                label: outputMeta?.ratingGood
                    ? Math.round(outputMeta.ratingGood * 100).toString()
                    : '-',
                unit: '%',
            },
            {
                name: 'experience',
                icon: 'ri-medal-fill',
                label: providerPage?.properties?.StartWork?.start
                    ? dayjs()
                        .diff(
                            dayjs(providerPage.properties.StartWork.start),
                            'year',
                        )
                        .toString()
                    : '-',
                unit: 'years',
            },
        ]

        // Return profile object
        return {
            _id: provider.value?._id || '',
            title: providerPage?.properties.Title || '',
            name: providerPage?.properties.Name || '',
            avatar: providerPage?.properties.Avatar?.url || '',
            cover: providerPage?.cover?.url || '',
            gender: providerPage?.properties?.Gender || '',
            specialties: formatSpecialties(),
            conditions: formatConditions(),
            highlight: content.value?.page?.properties?.Sapo || '',
            metrics,
            slug: providerPage?.properties.Slug || '',
            blocks: content.value?.page?.blocks || [],
        }
    })

    const loading = computed(
        () =>
            contentLoading.value ||
            conditionLoading.value ||
            specialtiesLoading.value ||
            providerLoading.value,
    )

    return { profile, loading }
}
