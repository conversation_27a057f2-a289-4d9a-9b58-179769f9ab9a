<!-- https://developer.stringee.com/docs/getting-started-stringee-react-native-sdk -->
<script setup lang="ts">
import { useHead } from '#imports'
import { Capacitor } from '@capacitor/core'

if (!Capacitor.isNativePlatform()) {
    useHead({
        script: [
            {
                src: 'https://cdn.stringee.com/sdk/web/latest/stringee-web-sdk.min.js',
                type: 'text/javascript',
                defer: true,
            },
        ],
    })
}

// const {
//     connect: connectStringee,
//     call: callStringee,
//     end: endStringee,
// } = useStringee()

// const from = ref<CallAgent>({
//     user: {
//         _id: '5a090761c8105c7d4076b94f',
//         name: 'caller',
//     },
//     app: '1002',
// })
// const to = ref<CallAgent>({
//     user: {
//         _id: '652f4f708813fd4e1344e4ef',
//         name: 'callee',
//     },
//     app: '1003',
// })
// const stringeeToken = ref(
//     'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCIsImN0eSI6InN0cmluZ2VlLWFwaTt2PTEifQ.eyJqdGkiOiJTS3NXTXlPZjhqUm51c3dPS1pEOXI5alZtVHE2bXB2MzhCXzE3MjM3NDE5OTgxODkiLCJpc3MiOiJTS3NXTXlPZjhqUm51c3dPS1pEOXI5alZtVHE2bXB2MzhCIiwiZXhwIjoxNzIzODI4Mzk4MTg5LCJ1c2VySWQiOiIxMDAyIiwiaWF0IjoxNzIzNzQxOTk4fQ.iUH74xqXVJosUPgkJPOzUMb96gA8gvYpKmJsKSIE6XI',
// )
// const error = ref('')
// const status = ref('')

// const call = async () => {
//     await callStringee({
//         from: from.value,
//         to: to.value,
//         displayName: to.value.user.name,
//         displayImage: to.value.user?.avatar?.url || 'https://i.pravatar.cc/300',
//     })
// }
// const end = async () => {
//     await endStringee()
//     // log({ context: 'Stringee', message: `onReject ${data.event}`, data })
// }

// onMounted(() => {
//     connectStringee(stringeeToken.value, {
//         onConnectionConnected: () => {
//             status.value = 'connected'
//         },
//         onAuthenticated: () => {
//             status.value = 'connected'
//         },
//         onStringeeCallEvent: ({ event, data }) => {
//             if (event === 'signaling-state') {
//                 status.value = data.reason
//             }
//             error.value = ''
//         },
//         exception: ({ message }) => {
//             error.value = message
//         },
//         onStringeeDidHangup: () => {
//             status.value = 'connected'
//             error.value = ''
//         },
//     })
// })
</script>

<template>
    <div
        class="flex min-h-screen items-center justify-center bg-gray-100 p-4 dark:bg-gray-900"
    >
        <div class="grid grid-cols-1 gap-8 lg:grid-cols-2">
            <!-- BackToHome -->
            <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
                <button @click="navigateTo('/debug')">
                    <span class="underline">Back to home</span>
                </button>
            </div>
        </div>
    </div>
</template>
