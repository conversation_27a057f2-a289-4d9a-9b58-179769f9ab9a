<script setup lang="ts">
const consultation = ref<string>('')
const conversation = ref<string>('')
const toId = ref<string>('')
const { user } = useUserInfo({
    scope: ['_id', 'isProvider'],
})
const voiceConnectingRef = useTemplateRef('voice-connecting')
const userId = computed(() => user?.value._id ?? '')

function open() {
    voiceConnectingRef.value?.open()
}

function close() {
    voiceConnectingRef.value?.close()
}

function test() {
    voiceConnectingRef.value?.test()
}
</script>

<template>
    <!-- <WVoiceContainer /> -->
    <div class="space-y-4 p-4 mt-safe-or-5">
        <!-- Conversation Text Input -->
        <div class="flex flex-col">
            <label for="conversation" class="mb-1 text-gray-700"
                >Conversation</label
            >
            <InputText
                id="conversation"
                v-model="conversation"
                class="p-inputtext p-component w-full"
            />
        </div>

        <!-- Consultation Text Input -->
        <div class="flex flex-col">
            <label for="consultation" class="mb-1 text-gray-700"
                >Consultation</label
            >
            <InputText
                id="consultation"
                v-model="consultation"
                class="p-inputtext p-component w-full"
            />
        </div>
        <div class="flex flex-col">
            <label for="to" class="mb-1 text-gray-700">Call to id</label>
            <InputText
                id="to"
                v-model="toId"
                class="p-inputtext p-component w-full"
            />
        </div>

        <!-- Buttons -->
        <div class="space-x-2">
            <Button
                label="Open"
                class="p-button p-button-primary"
                @click="open"
            />
            <Button
                label="Close"
                class="p-button p-button-danger"
                @click="close"
            />
            <Button
                label="Test"
                class="p-button p-button-danger"
                @click="test"
            />
            <Button
                label="Back"
                class="p-button p-button-secondary"
                @click="() => navigateTo('/')"
            />
        </div>
        <WVoiceConnecting
            ref="voice-connecting"
            :from-id="userId"
            :to-id="toId"
            :conversation="conversation"
            :consultation="consultation"
        />
        <WInternetMeeting
            ref="internet-call-ref"
            :from-id="userId"
            :to-id="toId"
            :conversation="conversation"
            :consultation="consultation"
        />
        <!-- <WCallOptions ref="call-option"/> -->
    </div>
</template>
