<template>
    <div class="px-4 pt-5 mt-safe">
        <div class="text-lg font-bold">Preference</div>
        <div
            v-for="key in keysPreference"
            :key="key"
            class="my-2 flex flex-col gap-2 rounded-md bg-gray-300"
        >
            <div class="p-2">{{ key }}</div>
            <div class="p-2">
                <Button class="w-full" @click="getDetailPreferences(key)"
                    >View</Button
                >
            </div>
        </div>
        <div class="mt-4">
            <Button severity="info" @click="copy">Copy</Button>
            <Button severity="info" @click="navigateTo('/')">Back</Button>
        </div>
        <div class="mt-4">
            <pre>{{ valuePreference }}</pre>
        </div>
    </div>
</template>
<script setup lang="ts">
import { Clipboard } from '@capacitor/clipboard'
import { toRaw } from 'vue'
const { getKeys, get } = usePreferences()

const keysPreference = ref<Array<any>>([])
const valuePreference = ref<any>({})

const getDetailPreferences = async (key: string) => {
    const data = await get({ key })
    console.log(typeof data)
    try {
        valuePreference.value = JSON.parse(data ?? '{}')
    } catch (e) {
        console.error(e)
        valuePreference.value = data ?? {}
    }
    console.log(toRaw(valuePreference.value))
}

const copy = async () => {
    let data = valuePreference.value
    try {
        data = JSON.stringify(data)
    } catch (e) {
        console.error(e)
    }
    Clipboard.write({
        string: data,
    })
}
onMounted(async () => {
    const keys = await getKeys()
    keysPreference.value = keys ?? []
})
</script>
