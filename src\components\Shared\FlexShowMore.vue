<script setup lang="ts">
const LINE_HEIGHT = 1.5 // rem
const MAX_LINES = 3
const MAX_HEIGHT = LINE_HEIGHT * MAX_LINES // rem

interface Props {
    show: boolean
    class: string
}

const props = defineProps<Props>()

const _Ref = useTemplateRef('_Ref')
const isExpanded = computed(() => props.show)
const isTruncated = ref(false)

const checkTruncation = () => {
    const container = _Ref.value
    if (container) {
        // Convert rem to pixels
        const rootFontSize = parseFloat(
            getComputedStyle(document.documentElement).fontSize,
        )
        const maxHeightPx = MAX_HEIGHT * rootFontSize

        // Check if content exceeds max height
        isTruncated.value = container.scrollHeight > maxHeightPx
    }
}

// Theo dõi resize window
let resizeObserver: ResizeObserver

onMounted(() => {
    checkTruncation()

    // Khởi tạo ResizeObserver để theo dõi thay đổi kích thước
    resizeObserver = new ResizeObserver(checkTruncation)
    if (_Ref.value) {
        resizeObserver.observe(_Ref.value)
    }

    // Thêm listener cho window resize
    window.addEventListener('resize', checkTruncation)
})

onBeforeUnmount(() => {
    // Cleanup
    if (_Ref) {
        resizeObserver.disconnect()
    }
    window.removeEventListener('resize', checkTruncation)
})
</script>

<template>
    <div class="flex flex-row flex-wrap">
        <div
            ref="_Ref"
            :class="[
                props.class,
                !isExpanded ? 'max-h-[4.5rem] overflow-hidden' : '',
            ]"
        >
            <slot />
        </div>
        <slot v-if="isTruncated" name="show-more" />
    </div>
</template>
