<script setup lang="ts">
interface TouchState {
    startY: number
    startX: number
    pullDistance: number
    isPulling: boolean
}

// Constants
const MAX_PULL = window.innerHeight / 6 // Tăng lên 1/6 chiều cao màn hình thay vì 1/8
const RELOAD_THRESHOLD = MAX_PULL * 0.7 // Tăng ngưỡng lên 70% để giảm độ nhạy
const RELOAD_DELAY = 500 // Thời gian chờ reload
const RESISTANCE_FACTOR = 0.5 // Thêm hệ số kháng để làm chậm hiệu ứng kéo

// State
const state = ref<TouchState>({
    startY: 0,
    startX: 0,
    pullDistance: 0,
    isPulling: false,
})

// Computed
// const isOverThreshold = computed((): boolean => state.value.pullDistance > RELOAD_THRESHOLD)

const dynamicElementStyle = computed((): Record<string, string> => {
    const height = `${Math.min(state.value.pullDistance, MAX_PULL)}px`
    return {
        height,
        // background: isOverThreshold.value
        //     ? 'rgba(0, 123, 255, 0.8)'
        //     : 'rgba(0, 123, 255, 0.5)',
        transition: state.value.isPulling ? 'none' : 'height 0.3s ease',
    }
})

const spinnerStyle = computed((): Record<string, string> => {
    // Tính toán góc xoay dựa trên khoảng cách kéo
    const rotate = `${Math.min((state.value.pullDistance / MAX_PULL) * 720, 720)}deg` // Tăng lên 720 độ để xoay 2 vòng
    const scale = Math.min(state.value.pullDistance / MAX_PULL, 1)

    return {
        transform: `rotate(${rotate}) scale(${scale})`,
        opacity: scale.toString(),
        transition: state.value.isPulling ? 'none' : 'all 0.3s ease',
    }
})

// Event Handlers
const touchStart = (e: TouchEvent): void => {
    if (window.scrollY === 0) {
        state.value.startY = e.touches[0].pageY
        state.value.startX = e.touches[0].pageX
        state.value.isPulling = true
    }
}

const touchMove = (e: TouchEvent): void => {
    if (!state.value.isPulling) return

    const currentY = e.touches[0].pageY
    const currentX = e.touches[0].pageX

    // Tính toán khoảng cách theo cả 2 trục
    const deltaY = currentY - state.value.startY
    const deltaX = Math.abs(currentX - state.value.startX)

    // Nếu khoảng cách theo X lớn hơn Y, có thể là vuốt ngang
    if (deltaX > Math.abs(deltaY)) {
        resetState()
        return
    }

    const rawDistance = deltaY
    // Áp dụng hệ số kháng để giảm độ nhạy
    const newPullDistance = Math.min(MAX_PULL, rawDistance * RESISTANCE_FACTOR)
    state.value.pullDistance = Math.max(0, newPullDistance)

    if (window.scrollY === 0 && state.value.pullDistance > 0 && e.cancelable) {
        e.preventDefault()
    }
}

const touchEnd = (): void => {
    if (!state.value.isPulling) return

    if (state.value.pullDistance > RELOAD_THRESHOLD) {
        animateBeforeReload()
    }

    resetState()
}

// Helper Functions
const resetState = (): void => {
    state.value = {
        startY: 0,
        startX: 0,
        pullDistance: 0,
        isPulling: false,
    }
}

const animateBeforeReload = (): void => {
    setTimeout(() => {
        window.location.reload()
    }, RELOAD_DELAY)
}

// Props & Emits
interface Props {
    disabled?: boolean
}

withDefaults(defineProps<Props>(), {
    disabled: false,
})

defineEmits<{
    'pull-refresh': []
    'pull-start': []
    'pull-end': []
}>()
</script>

<template>
    <div
        class="pull-to-refresh"
        @touchstart="touchStart"
        @touchmove="touchMove"
        @touchend="touchEnd"
    >
        <div
            v-show="state.isPulling || state.pullDistance > 0"
            class="flex w-full items-center justify-center"
            :style="dynamicElementStyle"
        >
            <i
                class="pi pi-spinner text-primary-500"
                :style="spinnerStyle"
                style="font-size: 1.5rem"
            />
        </div>

        <slot />
    </div>
</template>

<style scoped>
.pull-to-refresh {
    min-height: 100vh;
    touch-action: pan-y;
    -webkit-overflow-scrolling: touch;
}

.pi-spinner {
    will-change: transform;
    transform-origin: center;
}
</style>
