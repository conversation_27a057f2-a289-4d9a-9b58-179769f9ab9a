<script setup lang="ts">
import * as yup from 'yup'
const { t } = useI18n()

const onboardingStore = useOnboardingStore()

// Define Validation Schema
const validationSchema = yup.object({
    password: yup
        .string()
        .required(t('error.password-required'))
        .min(6, t('error.password-min-length')),
    confirmPassword: yup
        .string()
        .required(t('error.confirm-password-required'))
        .oneOf([yup.ref('password')], t('error.passwords-must-match')),
})
// Initialize Form
const { errors, defineField, validate } = useForm({ validationSchema })

// Define Fields
const [password, passwordAttrs] = defineField('password')
const [confirmPassword, confirmPasswordAttrs] = defineField('confirmPassword')

const passwordInputType = ref<'text' | 'password'>('password')
const confirmPasswordInputType = ref<'text' | 'password'>('password')

async function handleSave() {
    const result = await validate()
    if (result.valid) {
        onboardingStore.setPassword(password.value)
    }
}

const submit = async () => {
    onboardingStore.setPassword(password.value)
    const result = await validate()
    if (result.valid) {
        return true
    }
    return false
}

defineExpose({
    submit,
})
</script>
<template>
    <div class="flex min-h-[80vh] pt-8 md:items-center md:justify-center">
        <div class="w-full max-w-2xl rounded-lg">
            <div class="mb-6 w-full text-center">
                <div class="space-y-4 text-center">
                    <h1>{{ t('password-setting') }}</h1>

                    <div>
                        <InputGroup>
                            <InputText
                                ref="inputRef"
                                v-model="password"
                                class="w-full"
                                :class="{
                                    'border-red-400': errors.password,
                                }"
                                :placeholder="t('enter-password')"
                                :type="passwordInputType"
                                v-bind="passwordAttrs" />
                            <InputGroupAddon
                                ><i
                                    :class="
                                        passwordInputType === 'password'
                                            ? 'pi pi-eye-slash'
                                            : 'pi pi-eye'
                                    "
                                    class="cursor-pointer"
                                    @click="
                                        () =>
                                            passwordInputType === 'password'
                                                ? (passwordInputType = 'text')
                                                : (passwordInputType =
                                                      'password')
                                    "
                                ></i></InputGroupAddon
                        ></InputGroup>
                        <small v-if="errors.password" class="text-red-500">{{
                            errors.password
                        }}</small>
                        <InputGroup class="mt-5">
                            <InputText
                                ref="inputRef"
                                v-model="confirmPassword"
                                class="w-full"
                                :class="{
                                    'border-red-400': errors.confirmPassword,
                                }"
                                :placeholder="t('re-enter-password')"
                                v-bind="confirmPasswordAttrs"
                                :type="confirmPasswordInputType"
                                @keyup.enter="handleSave"
                                @blur="handleSave" />
                            <InputGroupAddon
                                ><i
                                    :class="
                                        confirmPasswordInputType === 'password'
                                            ? 'pi pi-eye-slash'
                                            : 'pi pi-eye'
                                    "
                                    class="cursor-pointer"
                                    @click="
                                        () =>
                                            confirmPasswordInputType ===
                                            'password'
                                                ? (confirmPasswordInputType =
                                                      'text')
                                                : (confirmPasswordInputType =
                                                      'password')
                                    "
                                ></i></InputGroupAddon
                        ></InputGroup>
                        <small
                            v-if="errors.confirmPassword"
                            class="text-red-500"
                            >{{ errors.confirmPassword }}</small
                        >
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<i18n lang="json">
{
    "en": {
        "password-setting": "Password Setting",
        "enter-password": "Enter password",
        "re-enter-password": "Enter password again",
        "error": {
            "password-required": "Password is required",
            "password-min-length": "Password must be at least 6 characters",
            "confirm-password-required": "Confirm password is required",
            "passwords-must-match": "Passwords must match"
        }
    },
    "vi": {
        "password-setting": "Cài mật khẩu",
        "enter-password": "Nhập mật khẩu",
        "re-enter-password": "Nhập lại mật khẩu",
        "error": {
            "password-required": "Mật khẩu là bắt buộc",
            "password-min-length": "Mật khẩu phải có ít nhất 6 ký tự",
            "confirm-password-required": "Xác nhận mật khẩu là bắt buộc",
            "passwords-must-match": "Mật khẩu không khớp"
        }
    }
}
</i18n>
