<script setup lang="ts">
import Email from './Email.vue'
import Password from './Password.vue'
import Ending from './Ending.vue'
// import Permissions from './Permissions.vue'
import VerifyPhone from './VerifyPhone.vue'
import Name from './Name.vue'
import ProfileAvatar from './Avatar.vue'
import Dob from './Dob.vue'
import Gender from './Gender.vue'
import { useOnboarding as useSignUp } from '~/composables'
// import { appRoutes } from '~/constants'
import { wellcareUrl } from '~/constants/wellcare-url'

interface Command {
    execute: () => any
}

interface Step {
    title: string
    component: any
    canSkip?: boolean
    canNext?: boolean
    canSwitchAccount?: boolean
    labelNext?: string
    command?: Command
    isHiddenBack?: boolean
    backToStep?: number
}
const route = useRoute()
const { t, locale } = useI18n()
const onboardingStore = useOnboardingStore()
const { signUp } = useSignUp()
const { $fetchWellcare } = useNuxtApp()
const { signIn } = useAppAuth()
const { execute: executeSendOtp } = useAsyncData(
    'verification-data',
    () => {
        return $fetchWellcare(wellcareUrl.sendOtp(), {
            method: 'POST',
            body: {
                phone: onboardingStore.profile.phone,
                countryCode: onboardingStore.profile.countryCode,
            },
        })
    },
    {
        immediate: false,
    },
)

const steps = reactive<Step[]>([
    {
        title: 'Email',
        component: Email,
        labelNext: 'next',
        canNext: true,
        canSkip: true,
        canSwitchAccount: true,
    },
    {
        title: 'Password',
        component: Password,
        labelNext: 'next',
        canNext: true,
        canSwitchAccount: true,
    },
    {
        title: 'Name',
        component: Name,
        labelNext: 'next',
        canNext: true,
        canSwitchAccount: true,
    },
    {
        title: 'Avatar',
        component: ProfileAvatar,
        labelNext: 'next',
        canNext: true,
        canSwitchAccount: true,
    },
    {
        title: 'Dob',
        component: Dob,
        labelNext: 'next',
        canNext: true,
        canSwitchAccount: true,
    },
    {
        title: 'Gender',
        component: Gender,
        labelNext: 'next',
        canNext: true,
        canSwitchAccount: true,
        command: {
            execute: async () => {
                await executeSendOtp()
                return true
            },
        },
    },
    {
        title: 'OTP',
        component: VerifyPhone,
        labelNext: 'next',
        command: {
            execute: async () => {
                const { success, data } = await signUp({
                    t,
                })
                if (!success || !data) {
                    return false
                }
                return true
            },
        },
        canNext: true,
    },
    {
        title: 'Ending',
        component: Ending,
        isHiddenBack: true,
        canNext: true,
        labelNext: 'btn.signin',
        command: {
            execute: async () => {
                await signIn({
                    login_hint: onboardingStore.profile.phone,
                    extraQueryParams: {
                        ui_locales: locale.value,
                        kc_country_code: onboardingStore.profile.countryCode,
                    },
                })
                return false
            },
        },
    },
])

const componentRef = ref()
const currentStep = ref(1)
const direction = ref('forward')
const isLoading = ref(false)
const supportDialog = ref(false)
const langDialog = ref(false)
const signOutDialog = ref(false)

const totalSteps = computed(() => steps.length)

const nextButtonLabel = computed(() => {
    const currentStepData = steps[currentStep.value - 1]
    return t(currentStepData.labelNext || 'next')
})

const executeStepCommand = async () => {
    const currentStepData = steps[currentStep.value - 1]
    if (currentStepData.command) {
        try {
            isLoading.value = true
            const result = await currentStepData.command.execute()
            return result
        } catch (error) {
            console.error(
                `Error executing command for step ${currentStepData.title}:`,
                error,
            )
            return false
        } finally {
            isLoading.value = false
        }
    }
    return true // Nếu không có `command`, mặc định cho phép bước tiếp
}

// const gotoStep = async (stepNumber: number) => {
//     if (stepNumber < 1 || stepNumber > totalSteps.value) {
//         return
//     }

//     direction.value = stepNumber > currentStep.value ? 'forward' : 'backward'

//     currentStep.value = stepNumber
// }

const nextStep = async () => {
    try {
        if (currentStep.value < totalSteps.value) {
            direction.value = 'forward'
            const isValid = await componentRef.value.submit()
            if (isValid) {
                const commandResult = await executeStepCommand()
                if (commandResult !== false) {
                    currentStep.value++
                }
            }
        } else {
            const commandResult = await executeStepCommand()
            if (commandResult !== false) {
                currentStep.value++
            }
        }
    } catch (err) {
        console.error(err)
    }
}

const backStep = () => {
    const currentStepData = steps[currentStep.value - 1]
    if (currentStepData.backToStep !== undefined) {
        direction.value = 'backward'
        currentStep.value = currentStepData.backToStep
    } else if (currentStep.value > 1) {
        direction.value = 'backward'
        currentStep.value--
    }
}

// Cập nhật skipStep để set isPhoneSkipped
const skipStep = () => {
    if (currentStep.value !== totalSteps.value) {
        direction.value = 'forward'
        currentStep.value++
    }
}

const switchAccount = () => {
    onboardingStore.$reset()
    signOutDialog.value = true
}

const canSkipCurrentStep = computed(() => steps[currentStep.value - 1].canSkip)

const canNextCurrentStep = computed(() => steps[currentStep.value - 1].canNext)

const canSwitchAccountCurrentStep = computed(
    () =>
        steps[currentStep.value - 1].canSwitchAccount &&
        currentStep.value !== totalSteps.value,
)

const currOnSteps = computed(() => `${currentStep.value}/${totalSteps.value}`)

// function onUpdatePermission(granted: boolean) {
//     console.log('update permissions ', granted)
//     const index = steps.findIndex((s) => s.title === 'Permissions')
//     console.log('index ', index)
//     if (granted) {
//         steps[index].canNext = true
//         steps[index].canSkip = false
//     } else {
//         steps[index].canNext = false
//         steps[index].canSkip = true
//     }
// }

function setPhone() {
    const query = route.query
    const phone = query?.phone
    const countryCode = query?.countryCode
    if (phone && countryCode) {
        onboardingStore.setPhoneData(phone as string, countryCode as string)
    }
}

// auto submit otp
watch(
    () => onboardingStore.profile.otp,
    async (val) => {
        if (val && val.length === 4) {
            await nextStep()
        }
    },
)

onMounted(() => setPhone())
</script>

<template>
    <div class="flex flex-col items-center justify-between gap-4">
        <div class="flex w-full items-center justify-between">
            <div class="flex w-full items-center gap-2">
                <Button
                    v-if="
                        currentStep !== 1 &&
                        !steps[currentStep - 1].isHiddenBack
                    "
                    icon="pi pi-angle-left"
                    text
                    rounded
                    :disabled="isLoading"
                    @click="backStep"
                />
                <Avatar
                    shape="circle"
                    image="/logo.svg"
                    alt="Logo"
                    class="h-[40px] w-[40px]"
                />
                <h6 class="font-medium uppercase">wellcare</h6>
            </div>

            <div class="flex items-center gap-2">
                <Button text plain size="small" @click="supportDialog = true">
                    <iconify-icon
                        icon="material-symbols:support-agent"
                        class="text-3xl"
                    />
                </Button>

                <Button
                    severity="secondary"
                    plain
                    size="small"
                    @click="langDialog = true"
                >
                    {{ locale }}
                </Button>
            </div>
        </div>

        <div class="w-full">
            <transition
                :name="
                    direction === 'forward'
                        ? 'slide-fade-left'
                        : 'slide-fade-right'
                "
                mode="out-in"
            >
                <component
                    :is="steps[currentStep - 1].component"
                    ref="componentRef"
                    :key="currentStep"
                    :curr-on-steps="currOnSteps"
                    class="mx-auto w-full"
                />
            </transition>
        </div>

        <div class="fixed inset-x-0 bottom-0 bg-white shadow-lg">
            <div class="container mx-auto px-4 pb-safe">
                <div
                    class="hidden md:flex md:items-center md:justify-between md:py-4"
                >
                    <div></div>
                    <div class="flex items-center gap-4">
                        <Button
                            v-if="canSwitchAccountCurrentStep"
                            class="text-surface-600 transition-colors duration-300"
                            text
                            :loading="isLoading"
                            :disabled="isLoading"
                            @click="switchAccount"
                        >
                            {{ t('onboarding.switch-account') }}
                        </Button>
                        <Button
                            v-if="canSkipCurrentStep"
                            class="text-surface-700 transition-colors duration-300"
                            text
                            :loading="isLoading"
                            :disabled="isLoading"
                            @click="skipStep"
                        >
                            {{ t('onboarding.skip') }}
                        </Button>
                        <Button
                            v-if="canNextCurrentStep"
                            :loading="isLoading"
                            :disabled="isLoading"
                            @click="nextStep"
                        >
                            {{ nextButtonLabel }}
                        </Button>
                    </div>
                </div>

                <div class="flex flex-col gap-4 py-4 md:hidden">
                    <Button
                        v-if="canNextCurrentStep"
                        class="w-full"
                        :loading="isLoading"
                        :disabled="isLoading"
                        :severity="isLoading ? 'secondary' : 'primary'"
                        @click="nextStep"
                    >
                        {{ nextButtonLabel }}
                    </Button>

                    <Button
                        v-if="canSkipCurrentStep"
                        class="w-full text-surface-700"
                        :loading="isLoading"
                        :disabled="isLoading"
                        text
                        @click="skipStep"
                    >
                        {{ t('onboarding.skip') }}
                    </Button>

                    <Button
                        v-if="canSwitchAccountCurrentStep"
                        class="w-full text-surface-600"
                        text
                        :loading="isLoading"
                        :disabled="isLoading"
                        @click="switchAccount"
                    >
                        {{ t('onboarding.switch-account') }}
                    </Button>
                </div>
            </div>
        </div>

        <SharedDialogSupport v-model:visible="supportDialog" />
        <SharedPopoverLang v-model:visible="langDialog" />
        <SharedAuthSignOutDialog v-model:visible="signOutDialog" />
    </div>
</template>

<style scoped>
.slide-fade-left-enter-active,
.slide-fade-left-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.slide-fade-left-enter,
.slide-fade-left-leave-to {
    transform: translateX(-50%);
    opacity: 0;
}

.slide-fade-right-enter-active,
.slide-fade-right-leave-active {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}
.slide-fade-right-enter,
.slide-fade-right-leave-to {
    transform: translateX(50%);
    opacity: 0;
}
</style>
<i18n lang="json">
{
    "vi": {
        "btn": {
            "signin": "Đăng nhập"
        }
    },
    "en": {
        "btn": {
            "signin": "Sign in"
        }
    }
}
</i18n>
