<script setup lang="ts">
import { useRuntimeConfig } from '#imports'
const runtime = useRuntimeConfig()
</script>
<template>
  <div class="mt-auto">
    <h2>Links</h2>
    <ol>
      <NuxtLink to="/version.json" target="_blank">
        <li class="underline">/version.json</li>
      </NuxtLink>

      <NuxtLink to="/sitemap.xml" target="_blank">
        <li class="underline">/sitemap.xml</li>
      </NuxtLink>

      <NuxtLink to="/robots.txt" target="_blank">
        <li class="underline">/robots.txt</li>
      </NuxtLink>
    </ol>

    <hr >
    <h2>runtime config</h2>
    <pre>{{ runtime }}</pre>
  </div>
</template>
