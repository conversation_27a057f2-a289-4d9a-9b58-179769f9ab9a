<script setup lang="ts">
import { ref, computed } from '#imports'
import { useFolderManager } from '~/composables'

const props = defineProps({
    project: {
        type: String,
        default: '',
    },
    user: {
        type: String,
        default: '',
    },
})

// State management
const showNewFolderDialog = ref(false)
const newFolderName = ref('')
// const selectedFiles = ref<Set<string>>(new Set())

// Folder management
const { folders, currentPath, addFolder, setCurrentPath, getCurrentPathFiles } =
    useFolderManager()

// File upload setup
const uploadPayload = computed(() => ({
    project: props.project,
    user: props.user,
    labels: [],
    tags: [],
    folder: '/test/',
}))

const {
    upload,
    loading: uploadLoading,
    progress,
} = useFileUpload(
    uploadPayload,
    () => {
        refresh() // Refresh file list after successful upload
    },
    (error) => {
        console.error('Upload failed:', error)
    },
    () => {},
)

// File search setup
const { data: files, refresh } = useFileSearch(
    'drive-files',
    computed(() => props.user),
    computed(() => props.project),
    { skip: 0, limit: 50 },
)

// Computed
const currentFiles = computed(() => {
    if (!files.value?.items) return []
    return getCurrentPathFiles.value(files.value.items)
})

// Methods
const handleCreateFolder = () => {
    if (!newFolderName.value.trim()) return

    console.log('🚀 ~ handleCreateFolder ~ newFolderName:', newFolderName.value)
    addFolder(newFolderName.value)
    newFolderName.value = ''
    showNewFolderDialog.value = false
}

const handleFileUpload = async (event: Event) => {
    const input = event.target as HTMLInputElement
    if (!input.files?.length) return

    const file = input.files[0]
    try {
        await upload(file)
    } catch (error) {
        console.error('Upload failed:', error)
    }
}

const navigateToFolder = (path: string) => {
    setCurrentPath(path)
}

const getBreadcrumbs = computed(() => {
    return currentPath.value.split('/').filter(Boolean)
})
</script>

<template>
    <div class="p-4">
        <!-- Breadcrumb Navigation -->
        <div class="mb-4 flex items-center gap-2">
            <button
                class="text-gray-600 hover:text-gray-800"
                @click="navigateToFolder('/')"
            >
                Home
            </button>
            <template v-for="(crumb, index) in getBreadcrumbs" :key="index">
                <span class="text-gray-400">/</span>
                <span class="text-gray-600">{{ crumb }}</span>
            </template>
        </div>

        <!-- Actions Bar -->
        <div class="mb-6 flex gap-4">
            <button
                class="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                @click="showNewFolderDialog = true"
            >
                New Folder
            </button>

            <label
                class="cursor-pointer rounded bg-green-500 px-4 py-2 text-white hover:bg-green-600"
            >
                Upload File
                <input type="file" class="hidden" @change="handleFileUpload" />
            </label>
        </div>

        <!-- New Folder Dialog -->
        <div
            v-if="showNewFolderDialog"
            class="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50"
        >
            <div class="w-96 rounded-lg bg-white p-6">
                <h3 class="mb-4 text-lg font-semibold">Create New Folder</h3>
                <input
                    v-model="newFolderName"
                    type="text"
                    class="mb-4 w-full rounded border px-3 py-2"
                    placeholder="Folder name"
                />
                <div class="flex justify-end gap-2">
                    <button
                        class="px-4 py-2 text-gray-600 hover:text-gray-800"
                        @click="showNewFolderDialog = false"
                    >
                        Cancel
                    </button>
                    <button
                        class="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600"
                        @click="handleCreateFolder"
                    >
                        Create
                    </button>
                </div>
            </div>
        </div>

        <!-- Upload Progress -->
        <div v-if="uploadLoading" class="mb-4">
            <div class="h-2 w-full rounded bg-gray-200">
                <div
                    class="h-full rounded bg-blue-500 transition-all duration-300"
                    :style="{ width: `${progress}%` }"
                />
            </div>
            <p class="mt-1 text-sm text-gray-600">Uploading: {{ progress }}%</p>
        </div>

        <!-- Files and Folders Grid -->
        <div class="grid grid-cols-1 gap-4 md:grid-cols-4">
            <!-- Folders -->
            <template v-for="folder in folders" :key="folder">
                <div
                    v-if="
                        folder !== '/' &&
                        folder.startsWith(currentPath.value) &&
                        folder.split('/').length ===
                            currentPath.value.split('/').length + 1
                    "
                    class="cursor-pointer rounded border p-4 hover:bg-gray-50"
                    @click="navigateToFolder(folder)"
                >
                    <div class="flex items-center gap-2">
                        <i class="fas fa-folder text-yellow-400"></i>
                        <span>{{ folder.split('/').pop() }}</span>
                    </div>
                </div>
            </template>

            <!-- Files -->
            <template v-for="file in currentFiles" :key="file.id">
                <div class="rounded border p-4 hover:bg-gray-50">
                    <div class="flex items-center gap-2">
                        <i class="fas fa-file text-gray-400"></i>
                        <span>{{ file.filename }}</span>
                    </div>
                </div>
            </template>
        </div>
    </div>
</template>
