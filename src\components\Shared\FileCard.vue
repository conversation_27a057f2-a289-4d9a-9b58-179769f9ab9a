<script setup lang="ts">
import { computed } from 'vue'
import type { IFile } from '~/models'

interface Props {
    file?: IFile
    layout?: 'Grid' | 'List'
}

const props = withDefaults(defineProps<Props>(), {
    layout: 'List',
})

// Enhanced file type detection with specific categories
const fileType = computed(() => {
    if (!props.file?.mimetype) return 'unknown'

    if (props.file.mimetype.startsWith('image/')) return 'image'
    if (props.file.mimetype.startsWith('video/')) return 'video'
    if (props.file.mimetype.includes('pdf')) return 'pdf'
    if (
        props.file.mimetype.includes('word') ||
        props.file.mimetype.includes('document')
    )
        return 'document'

    return 'other'
})

// Enhanced tag system with colors and labels
const fileTypeConfig = {
    image: {
        icon: 'bxs:image',
        label: 'Hình ảnh',
        color: 'bg-blue-100 text-blue-800',
    },
    video: {
        icon: 'fluent:video-48-filled',
        label: 'Video',
        color: 'bg-purple-100 text-purple-800',
    },
    pdf: {
        icon: 'teenyicons:pdf-solid',
        label: 'PDF',
        color: 'bg-red-100 text-red-800',
    },
    document: {
        icon: 'fa-solid:file-word',
        label: 'Tài liệu',
        color: 'bg-green-100 text-green-800',
    },
    other: {
        icon: 'fluent-mdl2:unknown-solid',
        label: 'Khác',
        color: 'bg-gray-100 text-gray-800',
    },
}

// Get display configuration based on file type
const typeConfig = computed(() => fileTypeConfig[fileType.value])

// Determine display source (url or thumbnail)
const displaySource = computed(() => {
    if (fileType.value === 'image' || fileType.value === 'pdf') {
        return props.file?.url
    }
    return props.file?.thumbnail || props.file?.url
})

// Format file size
const formatFileSize = (bytes?: number) => {
    if (!bytes) return '0 B'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
}

// Get custom tags array with type conversion
const getCustomTags = computed(() => {
    const customTags =
        props.file?.tags?.map((tag) => ({
            label: tag === 'medical' ? 'medication' : tag,
            color: 'bg-gray-100 text-gray-700',
        })) || []

    // Add file type as the first tag
    return [
        {
            label: typeConfig.value.label,
            color: typeConfig.value.color,
        },
        ...customTags,
    ]
})
</script>

<template>
    <div>
        <!-- List Layout -->
        <div
            v-if="layout === 'List'"
            class="card flex w-full flex-row items-start gap-3 p-2 transition-colors hover:bg-gray-50"
        >
            <!-- File Preview/Thumbnail -->
            <div class="relative flex-shrink-0">
                <NuxtImg
                    v-if="displaySource"
                    :src="displaySource"
                    class="h-16 w-16 rounded object-cover shadow"
                    :alt="typeConfig.label"
                />
                <div
                    v-else
                    class="flex h-16 w-16 items-center justify-center rounded bg-gray-100"
                >
                    <iconify-icon
                        :icon="typeConfig.icon"
                        class="text-2xl text-gray-500"
                    />
                </div>
            </div>

            <!-- File Details with Tags -->
            <div class="w-[calc(100%-92px)] flex-grow">
                <div class="mt-1 flex flex-wrap items-center gap-2">
                    <span
                        v-for="(tag, index) in getCustomTags"
                        :key="index"
                        :class="[
                            tag.color,
                            'rounded-full px-2 py-1 text-xs font-medium capitalize',
                        ]"
                    >
                        {{ tag.label }}
                    </span>
                </div>

                <div class="mt-2 flex items-center gap-2 text-xs text-gray-500">
                    <span>{{ formatFileSize(file?.size) }}</span>
                    <span>•</span>
                    <span>{{
                        formatTimeline(file?.createdAt as string, 'FullDate')
                    }}</span>
                </div>
            </div>
        </div>

        <!-- Grid Layout -->
        <div v-else-if="layout === 'Grid'" class="flex flex-col items-center">
            <Image preview>
                <template #image>
                    <div class="relative w-full">
                        <!-- File Preview -->
                        <NuxtImg
                            :src="displaySource"
                            :alt="typeConfig.label"
                            class="h-32 w-24 rounded object-cover shadow"
                        />

                        <!-- Type Indicator -->
                        <div
                            :class="typeConfig.color"
                            class="absolute bottom-0 left-0 right-0 px-2 py-1 text-center text-xs font-medium"
                        >
                            {{ typeConfig.label }}
                        </div>

                        <!-- Play Icon for Video -->
                        <div
                            v-if="fileType === 'video'"
                            class="absolute inset-0 flex items-center justify-center rounded bg-black/30"
                        >
                            <div
                                class="i-carbon-play-filled text-3xl text-white"
                            />
                        </div>
                    </div>
                </template>

                <!-- Full Preview -->
                <template #preview="slotProps">
                    <WVideo
                        v-if="fileType === 'video'"
                        ref="videoElement"
                        class="video-js vjs-default-skin vjs-big-play-centered !h-full !w-full transition-all"
                        autoplay
                        muted
                        controls
                        :playback-rates="[0.5, 1, 1.5, 2]"
                        :fluid="true"
                        :responsive="true"
                        native-controls-for-touch
                        native-audio-tracks
                        native-text-track
                        :user-actions="{
                            click: true,
                            doubleClick: true,
                            hotkeys: true,
                        }"
                        :sources="[{ src: file?.url }]"
                    />
                    <NuxtImg
                        v-else
                        :src="displaySource"
                        :alt="typeConfig.label"
                        :style="slotProps.style"
                        @click="slotProps.onClick"
                    />
                </template>
            </Image>
        </div>
    </div>
</template>
