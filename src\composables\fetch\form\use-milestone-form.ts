import { computed } from 'vue'
import type { Ref, ComputedRef } from '#imports'
import { useApp3Submission, milestoneFormFactory } from '~/composables'
import type { DevelopmentArea, MilestoneForm } from '~/models'

export const useMilestoneForm = (
    userId: Ref<string> | ComputedRef<string>,
    activeTab: Ref<string> | ComputedRef<string>,
) => {
    const currentMonth = computed(() =>
        milestoneFormFactory.formatToMonths(activeTab.value),
    )

    const nearestMilestone = computed(() =>
        milestoneFormFactory.getNearestMilestone(currentMonth.value),
    )

    const currentForm = computed<MilestoneForm | null>(
        () => milestoneFormFactory.getNearestForm(currentMonth.value)?.form,
    )

    const { data, execute } = useApp3Submission(
        userId.value,
        computed(() => currentForm.value?.objectId || ''),
    )
    execute()

    const getAreaProgress = (area: DevelopmentArea) => {
        if (!data.value?.results[0]?.response || !currentForm.value) return 0

        // Get all questions for this specific area from the form structure
        const areaQuestions = currentForm.value.pages
            .filter((page) => page.id === area)
            .flatMap((page) => page.elements)
            .filter((el) => el.properties?.input?.label)
            .map((el) => el.properties.input.label)

        if (!areaQuestions?.length) return 0

        // Filter responses that match questions from this area
        const areaResponses = data.value.results[0].response.filter(
            (item: any) => areaQuestions.includes(item.question),
        )

        if (!areaResponses?.length) return 0

        const completed = areaResponses.filter(
            (q: any) => q.answer === 'Có',
        ).length
        return Math.round((completed / areaQuestions.length) * 100)
    }

    const totalProgress = computed(() => {
        const allAnswers = data.value?.results[0]?.response
        if (!allAnswers?.length) return 0

        const completed = allAnswers.filter(
            (q: any) => q.answer === 'Có',
        ).length
        return Math.round((completed / allAnswers.length) * 100)
    })

    const getAnswerColor = (answer: string) => {
        switch (answer) {
            case 'Có':
                return 'bg-green-100 border-green-400'
            case 'Không chắc':
                return 'bg-yellow-50 border-yellow-400'
            case 'Chưa':
                return 'bg-red-50 border-red-400'
            default:
                return 'bg-gray-50 border-gray-300'
        }
    }

    return {
        currentForm,
        nearestMilestone,
        currentMonth,
        data,
        getAreaProgress,
        getAnswerColor,
        totalProgress,
    }
}
