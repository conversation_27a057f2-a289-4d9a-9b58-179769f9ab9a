<script setup lang="ts">
import { ref } from '#imports'
const { useMenu } = defineProps({
  useMenu: {
    type: Boolean,
    default: true,
  },
  usefulLinkLabel: {
    type: String,
    default: 'Useful links',
  },
})
const showSidebar = ref(false)
</script>
<template>
  <div class="flex flex-col sm:flex-row sm:px-8 xl:px-20">
    <Sidebar
      v-model:visible="showSidebar"
      position="bottom"
      :pt="{
        content: 'h-[70vh] p-4 flex flex-col overflow-y-auto',
        header: 'p-4 border-b-2 flex flex-row justify-between',
      }"
      block-scroll
      :header="$props.usefulLinkLabel"
    >
      <slot name="side"/>
    </Sidebar>
    <div
      class="sticky left-0 top-[var(--header-height)] hidden h-[calc(100vh-var(--header-height))] max-w-80 flex-none flex-col overflow-y-auto border-r px-4 py-8 sm:flex dark:border-gray-700"
    >
      <slot name="side"/>
    </div>
    <div class="flex-initial grow p-4 xl:p-8">
      <Button
        v-if="useMenu"
        icon="pi pi-th-large"
        aria-label="open links"
        severity="secondary"
        :label="$props.usefulLinkLabel"
        size="large"
        class="mx-4 my-8 self-start sm:hidden"
        @click="showSidebar = true"
      />
      <slot name="main"/>
    </div>
  </div>
</template>
