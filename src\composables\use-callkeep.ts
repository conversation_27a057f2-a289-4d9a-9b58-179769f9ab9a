import {
    useOnesignal,
    usePreferences,
    useRuntimeConfig,
    useUserInfo,
} from '#imports'
import { Capacitor } from '@capacitor/core'
import { CapacitorCallkeep } from '@wellcare/capacitor-callkeep'

export function useCallkeep() {
    const platform = Capacitor.getPlatform()
    const config: any = useRuntimeConfig()
    const { set, get, remove } = usePreferences()
    const { registerDevice, deletePlayerRecord } = useOnesignal()
    const appStore = useAppStore()
    const router = useRouter()
    const log = useLogs()
    const { user } = useUserInfo({ scope: ['avatar', 'name', '_id'] })

    const appid = String(config.public.onesignal.app_id)
    const voip = String(config.public.onesignal.voip)
    const env = String(config.public.app.env) as 'production' | 'sandbox'

    const getIdentifier = async () => {
        return get({ key: 'identifier' })
    }

    const handleCallEvent = async (data: any) => {
        try {
            // we need to reduce the size of the object to comply with the query string size limit
            const dataFormat = {
                conversation: data.conversation,
                mode: data.mode,
                appid: data.appid,
                token: data.token,
                from: data.from,
                to: data.to,
                provider: data.provider,
                patient: data.patient,
                path: data.path,
                remaining: data?.remaining ?? 0,
            }
            await set({
                key: 'callkeep:data',
                value: JSON.stringify(dataFormat),
            })
            const mode = data.mode === 'app' ? 'voice' : data.mode
            log.addNativeLogs({
                context: 'callkeep',
                message: 'redirect to call page: ' + `/call/${mode}`,
                data: dataFormat,
                time: new Date().toLocaleString(),
            })
            router.push({ path: `/call/${mode}`, query: dataFormat })
        } catch (e: any) {
            console.log('callkeep:handleCallEvent', e.message)
        }
    }

    const registrationIos = async () => {
        const [identifier, userIdentifier] = await Promise.all([
            get({ key: 'identifier' }),
            get({ key: 'user:identifier' }),
        ])
        if (platform !== 'ios' || !user.value?._id || !identifier) return

        if (userIdentifier !== identifier) {
            log.addNativeLogs({
                context: 'useCallkeep',
                message: 'deletePlayerRecord',
                data: { userIdentifier },
                time: new Date().toLocaleString(),
            })
            await deletePlayerRecord({ app_id: voip })
        }

        log.addNativeLogs({
            context: 'useCallkeep',
            message: 'registrationIos',
            data: { identifier, user: user.value?._id },
        })
        await registerDevice({
            identifier: identifier,
            external_user_id: user.value._id,
            app_id: voip,
        })
        await set({ key: 'user:identifier', value: identifier })
    }

    const init = async () => {
        if (!Capacitor.isNativePlatform() || appStore.callkeepInitialize) return
        try {
            const data = await CapacitorCallkeep.checkCurrentCall()
            console.log('callkeep:checkCurrentCall', data)
            log.addNativeLogs({
                context: 'callkeep',
                message: 'check current call',
                data,
                time: new Date().toLocaleDateString(),
            })
            handleCallEvent(data)
        } catch (e) {
            console.error(e)
        }

        CapacitorCallkeep.addListener('push', (data) => {
            console.log('callkeep:push', data)
        })
        CapacitorCallkeep.addListener('exception', (data) => {
            console.log('callkeep:exception', data)
        })
        CapacitorCallkeep.addListener('trace', (data) => {
            console.log('callkeep:trace', data)
        })
        CapacitorCallkeep.addListener('onCallAccepted', (data: any) => {
            console.log('callkeep:onCallAccepted', data)
            log.addNativeLogs({
                context: 'callkeep',
                message: 'call accepted',
                data,
            })
            handleCallEvent(data)
        })
        CapacitorCallkeep.addListener('onCallRejected', (data: any) => {
            console.log('callkeep:onCallRejected', data)
        })
        CapacitorCallkeep.addListener('push', (data) => {
            console.log('callkeep:push', data)
        })
        CapacitorCallkeep.addListener('registration', async (data) => {
            console.log('callkeep:registration', data.token)
            try {
                await set({ key: 'identifier', value: data.token })
                await registrationIos()
            } catch (e: any) {
                console.error('callkeep:registration', e.message)
            }
        })

        CapacitorCallkeep.setEnviroment({
            env,
        })
        if (platform === 'android') {
            console.log('callkeep:initialize:android', appid)
            log.addNativeLogs({
                context: 'callkeep',
                message: 'init callkeep android',
                data: {
                    appid: appid,
                },
                time: new Date().toLocaleString(),
            })
            await CapacitorCallkeep.initialize({ appId: appid })
            appStore.callkeepInitialize = true
        } else if (platform === 'ios') {
            console.log('callkeep:initialize:ios', voip)
            log.addNativeLogs({
                context: 'callkeep',
                message: 'init callkeep ios',
                data: {
                    appid: voip,
                },
                time: new Date().toLocaleString(),
            })
            await CapacitorCallkeep.initialize({ appId: voip })
            try {
                await registrationIos()
                appStore.callkeepInitialize = true
            } catch (e: any) {
                log.addNativeLogs({
                    context: 'callkeep',
                    level: 'error',
                    message: e.message,
                })
            }
        }
    }

    const logout = async () => {
        if (!appStore.callkeepInitialize) return
        try {
            if (platform === 'ios') {
                const userIdentifier = await get({ key: 'user:identifier' })
                if (!userIdentifier) return

                log.addNativeLogs({
                    context: 'useCallkeep',
                    message: 'logout:ios',
                    data: { app_id: voip },
                    time: new Date().toLocaleString(),
                })
                await deletePlayerRecord({ app_id: voip })
                await remove({ key: 'user:identifier' })
            }
            if (platform === 'android') {
                log.addNativeLogs({
                    context: 'useCallkeep',
                    message: 'logout:android',
                    data: { app_id: voip },
                    time: new Date().toLocaleString(),
                })
                await CapacitorCallkeep.logoutOneSignal()
            }
        } catch (error) {
            log.addNativeLogs({
                level: 'error',
                context: 'useCallkeep',
                message: 'logout',
                data: { error },
                time: new Date().toLocaleString(),
            })
        }
    }

    return {
        init,
        logout,
        getIdentifier,
    }
}
