import { describe, it, expect } from 'vitest'
import { $fetch } from '@nuxt/test-utils/e2e'

describe('Sitemap Generation', () => {
  it('should generate index with 2 sitemaps', async () => {
    const sitemapIndex = await $fetch('/sitemap.xml')
    expect(sitemapIndex).toContain('<loc>/vi-VN-sitemap.xml</loc>')
    expect(sitemapIndex).toContain('<loc>/en-US-sitemap.xml</loc>')
  })

  it('should contain valid vi-VN-sitemap.xml', async () => {
    const viVNSitemap = await $fetch('/vi-VN-sitemap.xml')
    expect(viVNSitemap).toContain('<urlset')
    expect(viVNSitemap).toContain('<loc>/some-specific-url</loc>')
  })

  it('should contain valid en-US-sitemap.xml', async () => {
    const enUSSitemap = await $fetch('/en-US-sitemap.xml')
    expect(enUSSitemap).toContain('<urlset')
    expect(enUSSitemap).toContain('<loc>/some-specific-url</loc>')
  })
})
