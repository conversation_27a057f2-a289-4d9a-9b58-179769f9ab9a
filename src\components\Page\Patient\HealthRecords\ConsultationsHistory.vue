<script setup lang="ts">
import { ref } from '#imports'
import { WConsultationHistory } from '#components'

defineProps({
    memberActivatedId: {
        type: String,
        required: true,
    },
})

const { t } = useI18n()
const router = useRouter()
const providerId = ref<string>('')
const consultationId = ref<string>('')
const providerAvatar = ref<string>('')
const isOpenRating = ref<boolean>(false)
const wConsultationHistory = ref<InstanceType<
    typeof WConsultationHistory
> | null>(null)
const isShow = ref(false)

const sharedCount = computed<number>(() => wConsultationHistory.value?.length)

const callHandle = useCallHandle()

const openEMRHandler = (consultation: any) => {
    navigateTo(`/patient/consultation/${consultation._id}`)
}
const callVideoHandler = async (data: any) => {
    callHandle.open('video', data)
}
const callVoiceHandler = async (data: any) => {
    callHandle.open('voice', data)
}

const onOpenRating = (selectedConsultation: any) => {
    isOpenRating.value = true
    consultationId.value = selectedConsultation._id
    providerId.value = selectedConsultation?.provider?._id
    providerAvatar.value = selectedConsultation?.provider?.avatar?.url
}

const handleRedicrect = () => {
    router.push('/patient/services/provider')
}

watch(sharedCount, () => {
    isShow.value = Boolean(sharedCount.value)
})
</script>
<template>
    <div v-show="isShow">
        <SharedPageSection
            :title="t('consultation-history')"
            :pt="{
                content:
                    'flex snap-x flex-row flex-nowrap gap-4 overflow-x-auto px-4 sm:flex flex-col',
            }"
            :view-all-to="`/patient/health-records/${memberActivatedId}/consultation`"
        >
            <WConsultationHistory
                ref="wConsultationHistory"
                :user-id="memberActivatedId"
                :showing-quantity="2"
                :showing-load-btn="false"
                :filter="{
                    user: memberActivatedId,
                    state: {
                        $in: ['COMPLETED', 'FREE'],
                    },
                    type: {
                        $in: ['indepth', 'question'],
                    },
                }"
                @click:call="callVoiceHandler"
                @click:open-mr="openEMRHandler"
                @click:call-video="callVideoHandler"
                @click:open-rating="onOpenRating"
                @click:redirect-choose-doctor="handleRedicrect"
            />

            <WConsultationRating
                :visible="isOpenRating"
                :provider-id="providerId"
                :consultation-id="consultationId"
                :provider-avatar="providerAvatar"
                @submitted="isOpenRating = false"
                @skip-rating="isOpenRating = false"
                @update:visible="isOpenRating = false"
            />
        </SharedPageSection>
    </div>
</template>

<i18n lang="json">
{
    "en": {
        "consultation-history": "Recent teleconsultation"
    },
    "vi": {
        "consultation-history": "Lịch sử khám"
    }
}
</i18n>
