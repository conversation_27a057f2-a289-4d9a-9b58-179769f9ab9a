<script setup lang="ts">
import type { IHealthProgramCalendar } from '~/models'

import ExpandedVaccinationSchedule from './ExpandedVaccinationSchedule.vue'

import {
    computed,
    ref,
    useI18n,
    usePagePatientServicesHealthProgramsBabyDevelopment,
    useRoute,
    useUserInfo,
    watch,
} from '#imports'

const { t, locale } = useI18n()
const { fullPath } = useRoute()
const { user } = useUserInfo({
    scope: ['meta', 'gender'],
})

const baby_id = computed(() => user.value?.meta?.baby_id)
const baby_date = computed(() => user.value?.meta?.baby_date)
const baby_gender = computed(
    () => user.value?.meta?.gender || user.value?.gender,
)
const selectedTab = ref<string>('')
const activeTodayTime = computed(() =>
    times.value.find(
        (time: IHealthProgramCalendar) => time.isActive && time.isToday,
    ),
)
const activeArticle = computed(() =>
    times.value.find(
        (item: IHealthProgramCalendar) => item.label === selectedTab.value,
    ),
)

const { times, loading } = usePagePatientServicesHealthProgramsBabyDevelopment()

const handleUpdateValue = (value: string) => {
    selectedTab.value = value
}

watch(loading, () => {
    if (!loading.value) {
        selectedTab.value = activeTodayTime.value?.label || ''
    }
})
</script>

<template>
    <div v-if="loading">
        <Skeleton width="100%" height="200px" />
        <Skeleton width="90%" height="20px" class="ml-4 mt-4" />
        <Skeleton width="60%" height="16px" class="ml-4 mt-4" />
        <Skeleton width="80%" height="16px" class="ml-4 mt-2" />
        <Skeleton width="70%" height="16px" class="ml-4 mt-2" />
        <Skeleton width="80%" height="16px" class="ml-4 mt-2" />
    </div>
    <PagePatientServicesHealthProgramCalendar
        v-else
        :active-tab="selectedTab"
        :times="times"
        :subtitle="{
            content: t(activeArticle?.unit || ''),
            position: locale === 'en' ? 'top' : 'bottom',
        }"
        @on-change-tab="handleUpdateValue"
    >
        <template #content>
            <div class="h-[calc(100vh-150px)] overflow-auto">
                <div class="relative h-56">
                    <!-- Cover -->
                    <div
                        class="h-full w-full bg-cover bg-center bg-no-repeat"
                        :style="{
                            backgroundImage: `url(${activeArticle?.metadata?.cover})`,
                        }"
                    />

                    <!-- Overlay -->
                    <div
                        class="absolute left-0 top-0 h-full w-full bg-gradient-to-t from-black/50 to-black/10"
                    />

                    <!-- Title -->
                    <h4
                        class="absolute bottom-2 left-2 w-[calc(100%-104px)] text-white"
                        style="
                            text-shadow:
                                2px 7px 5px rgba(0, 0, 0, 0.3),
                                0px -4px 10px rgba(255, 255, 255, 0.3);
                        "
                    >
                        {{ activeArticle?.metadata?.name }}
                    </h4>

                    <!-- Button -->
                    <NuxtLinkLocale
                        :to="`${fullPath}/${activeArticle?.metadata?.slug}`"
                        class="absolute -bottom-4 right-3"
                    >
                        <Button class="uppercase" :label="t('btn:detail')" />
                    </NuxtLinkLocale>
                </div>

                <SharedPageSection
                    v-if="selectedTab !== 'month:1'"
                    :title="t('headline:1')"
                    :pt="{
                        content: 'mx-4',
                    }"
                >
                    <PagePatientServicesHealthProgramBabyDevelopmentCheckList
                        :user-id="baby_id"
                        :active-tab="selectedTab"
                    />
                </SharedPageSection>

                <!-- <SharedPageSection
                    v-if="baby_id"
                    :title="t('personal:doctor')"
                    :pt="{
                        content: 'mx-4',
                    }"
                >
                    <LazyPagePatientServicesPersonalDoctorExpertCompanion
                        :user-id="baby_id"
                    />
                </SharedPageSection> -->

                <PagePatientHealthRecordsVitals
                    v-if="baby_id"
                    :user-id="baby_id"
                    :dob="baby_date"
                    :title="t('headline:2')"
                    :gender="baby_gender"
                />

                <SharedPageSection
                    :title="t('headline:3')"
                    :pt="{
                        content: 'mx-4',
                    }"
                >
                    <WVaccination v-if="baby_id" :user-id="baby_id" />
                </SharedPageSection>

                <SharedPageSection
                    :title="t('headline:4')"
                    :pt="{
                        content: 'mx-4',
                    }"
                >
                    <ExpandedVaccinationSchedule />
                </SharedPageSection>
            </div>
        </template>
    </PagePatientServicesHealthProgramCalendar>
</template>
<i18n lang="json">
{
    "en": {
        "title:1": "Pregnancy Package",
        "title:2": "Raise them right",
        "headline:1": "What has the baby achieved?",
        "headline:2": "Vitals",
        "headline:3": "Immunization",
        "headline:4": "Expanded vaccination schedule",
        "btn:detail": "Detail",
        "month": "month",
        "year": "year"
    },
    "vi": {
        "title:1": "Thai kỳ khoẻ mạnh",
        "title:2": "Cùng con khôn lớn",
        "headline:1": "Bé đã làm được gì?",
        "headline:2": "Chỉ số sức khỏe",
        "headline:3": "Tiêm chủng",
        "headline:4": "Lịch sử tiêm chủng mở rộng",
        "btn:detail": "Chi tiết",
        "month": "tháng",
        "year": "tuổi"
    }
}
</i18n>
