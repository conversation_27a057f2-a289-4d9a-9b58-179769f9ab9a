import { $fetch } from '@nuxt/test-utils/e2e'
import { describe, expect, it } from 'vitest'
import { useHtml } from '~/tests/utils/html'
import { expectNoClientErrors } from '../../utils'
const urls = [
  '/',
  // '/lien-he',
]

export const landing = async () => {
  urls.forEach((url) => {
    describe(url, async () => {
      describe('layout', async () => {
        it('should have viewport configured for responsive design', async () => {
          const html = await $fetch(url)
          expect(html.match('meta name="viewport"').length).toEqual(1)
          await expectNoClientErrors('/')
        })
      })

      describe('structured data', async () => {
        it('should contain at least one structured data', async () => {
          const html = await $fetch(url)
          expect(html).toContain(`"@context":"https://schema.org"`)
        })
      })

      describe('multi-language', async () => {
        it('should contains at least 3 hreflang links', async () => {
          const html = await $fetch(url)
          const { hreflangs } = useHtml(html)
          expect(hreflangs.length).toBeGreaterThanOrEqual(3)
        })
      })

      describe('image', async () => {
        it('should have only CDN enabled sources', async () => {
          const html = await $fetch(url)
          const { imgSrcs } = useHtml(html)

          imgSrcs.forEach((url: string) => {
            expect(url).toMatch(/^\/images|^https:\/\/storage\.googleapis\.com/)
          })
        })
      })

      describe('title', () => {
        it('should be less than 60 characters', async () => {
          const html = await $fetch(url)
          const { title } = useHtml(html)
          expect(title.split('|')[0].length).toBeLessThanOrEqual(60)
        })
      })
      describe('meta description', async () => {
        it('should be 50-160 characters', async () => {
          const html = await $fetch(url)
          const { metaDescription } = useHtml(html)
          expect(metaDescription.length).toBeLessThanOrEqual(160)
          expect(metaDescription.length).toBeGreaterThanOrEqual(50)
        })
      })
      describe('headings', async () => {
        it('should have only 1 <h1> tag', async () => {
          const html = await $fetch(url)
          const { h1s } = useHtml(html)
          expect(h1s).toHaveLength(1)
        })
        it('should have h2-headings less than 60 characters', async () => {
          const html = await $fetch(url)
          const { h2s } = useHtml(html)
          h2s.forEach((tag: string) => {
            expect(tag?.length)?.toBeLessThanOrEqual(60)
          })
        })
        // it('should have at least one h3-h6 headings', async()=>{})
      })
      describe('links', async () => {
        it('should have nofollow attributes for all external links', async () => {
          const html = await $fetch(url)
          const { links } = useHtml(html)
          links
            .filter((i) => !i.includes('khamtuxa.wellcare.vn'))
            .forEach((link: string) => {
              expect(link).toContain('rel="nofollow"')
            })
        })
      })
      describe('keywords', async () => {
        // it('should have main keywords appear on title, description or headings')
      })
      describe('content', async () => {
        it('should not have thin content')
      })
      describe('image', async () => {
        it('should have alt attributes')
      })
    })
  })
}
export default landing
