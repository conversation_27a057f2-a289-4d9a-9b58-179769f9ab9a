<script setup lang="ts">
import { definePageMeta } from '#imports'
definePageMeta({
    middleware: 'auth-capacitor',
})
</script>

<template>
    <SharedSafeArea>
        <SharedAppBar title="My App" elevated color="white" dark height="16">
            <template #left>
                <NuxtLink to="/patient/services">
                    <Button
                        icon="ri-arrow-left-s-line"
                        severity="secondary"
                        rounded
                        outlined
                        aria-label="Back"
                        class="h-8 w-8"
                    />
                </NuxtLink>
            </template>
            <template #title>
                <h3 class="mb-0 text-slate-950">Payment</h3>
            </template>
            <template #right />
        </SharedAppBar>
        <slot />
    </SharedSafeArea>
</template>
