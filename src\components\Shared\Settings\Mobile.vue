<script setup lang="ts">
import { navigateTo, ref } from '#imports'
import { Capacitor } from '@capacitor/core'
import type { Component } from 'vue'
import PageSettingLanguage from '~/components/Page/Setting/Language.vue'
import PageSettingWallet from '~/components/Page/Setting/Wallet.vue'
import PageSettingChangeRole from '~/components/Page/Setting/ChangeRole.vue'
import PageSettingLinkPhone from '~/components/Page/Setting/LinkPhone.vue'
import { appRoutes } from '~/constants'

const { t } = useI18n({ useScope: 'local' })
const { user } = useUserInfo()
const { signOut } = useAppAuth()
const { openExternalLink } = useExternalLink()

const platform: Ref<string> = ref(Capacitor.getPlatform())
const isSettingVisible: Ref<boolean> = ref(false)
const currentComponent = ref<{ component?: Component }>({})

const isAdmin = computed(() => {
    return user.value?.isAdmin
})

const isProvider = computed(() => {
    return user.value?.isProvider
})

const isTest = computed(() => {
    return user.value?.isTest
})

const dynamicComponents = [
    PageSettingWallet,
    PageSettingLanguage,
    PageSettingChangeRole,
    PageSettingLinkPhone,
]

const openSetting = (component: Component) => {
    isSettingVisible.value = true
    currentComponent.value = {
        component: dynamicComponents.find((c) => c == component),
    }
}

const openRatingApp = () => {
    const platform = Capacitor.getPlatform()
    if (platform === 'ios')
        window.open(
            'https://apps.apple.com/us/app/wellcare/id1039423586?see-all=reviews',
            '_blank',
        )
    else if (platform === 'android')
        window.open(
            'https://play.google.com/store/apps/details?id=vn.wellcare',

            '_blank',
        )
}

const navigateProfile = () => {
    if (isProvider.value) {
        navigateTo(appRoutes.provider.settings.profile)
    } else {
        navigateTo(appRoutes.patient.settings.profile)
    }
}
</script>

<template>
    <!-- General -->
    <SharedPageSection
        :title="t('General')"
        :pt="{ content: 'mx-4 flex flex-col gap-3' }"
    >
        <Button
            v-if="!user.phone"
            :label="t('link-account')"
            icon="pi pi-link"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="openSetting(PageSettingLinkPhone)"
        />
        <Button
            :label="t('setting.profile')"
            icon="pi pi-angle-right"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="navigateProfile"
        />
        <!-- <Button
            :label="t('Change my password')"
            icon="pi pi-angle-right"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="resetPassword()"
        /> -->
        <!-- <Button
          :label="t('Wallet')"
          icon="pi pi-angle-right"
          severity="secondary"
          icon-pos="right"
          class="w-full"
          :pt="{
              label: 'text-start text-lg font-medium w-full',
          }"
          @click="openSetting(PageSettingWallet)"
      /> -->
        <Button
            :label="t('Language')"
            icon="pi pi-angle-right"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="openSetting(PageSettingLanguage)"
        />
        <Button
            v-if="platform === 'ios' || platform === 'android'"
            :label="t('App rating')"
            icon="pi pi-angle-right"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="openRatingApp"
        />
    </SharedPageSection>

    <!-- Support -->
    <SharedPageSection
        :title="t('Support')"
        :pt="{ content: 'mx-4 flex flex-col gap-3' }"
    >
        <Button
            v-if="!isProvider"
            :label="t('FAQ')"
            icon="pi pi-angle-right"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="openExternalLink('https://khamtuxa.wellcare.vn/cau-hoi-thuong-gap')"
        />
        <Button
            :label="t('Terms and conditions')"
            icon="pi pi-angle-right"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="
                openExternalLink(
                    'https://wellcare.vn/ho-tro/dieu-khoan-su-dung',
                )
            "
        />
        <Button
            :label="t('Privacy policy')"
            icon="pi pi-angle-right"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="
                openExternalLink(
                    'https://wellcare.vn/ho-tro/chinh-sach-bao-mat',
                )
            "
        />
    </SharedPageSection>

    <SharedPageSection
        v-if="isTest"
        :title="t('setting-title')"
        :pt="{ content: 'mx-4 flex flex-col gap-3' }"
    >
        <Button
            :label="t('delete-account')"
            icon="pi pi-trash"
            severity="secondary"
            icon-pos="right"
            class="w-full border-none"
            :pt="{
                label: 'text-start text-lg font-medium w-full',
            }"
            @click="signOut"
        />
    </SharedPageSection>

    <!-- Admin -->
    <Panel v-if="isAdmin" class="mx-4 mt-8" header="Admin" toggleable>
        <div class="space-y-3">
            <Button
                :label="t('change-role')"
                icon="pi pi-angle-right"
                severity="secondary"
                icon-pos="right"
                class="w-full border-none"
                :pt="{
                    label: 'text-start text-lg font-medium w-full',
                }"
                @click="openSetting(PageSettingChangeRole)"
            />
            <Button
                :label="t('Debug')"
                icon="pi pi-angle-right"
                severity="secondary"
                icon-pos="right"
                class="w-full border-none"
                :pt="{
                    label: 'text-start text-lg font-medium w-full',
                }"
                @click="navigateTo('/debug')"
            />
            <Button
                :label="t('chat')"
                icon="pi pi-angle-right"
                severity="secondary"
                icon-pos="right"
                class="w-full border-none"
                :pt="{
                    label: 'text-start text-lg font-medium w-full',
                }"
                @click="navigateTo('/admin/chat')"
            />
        </div>
    </Panel>

    <Dialog v-model:visible="isSettingVisible" class="p-dialog-maximized">
        <component :is="currentComponent?.component" />
    </Dialog>
</template>

<i18n lang="yaml">
en:
    Appearance: Appearance
    dark/light: Change Theme
    General: General
    Change my password: Change my password
    Wallet: Wallet
    Language: Language
    Support: Support
    FAQ: FAQ
    Terms and conditions: Terms and conditions
    Privacy policy: Privacy policy
    change-role: Change role
    delete-account: Delete account
    setting-title: Account
    link-account: Connect account
vi:
    Appearance: Giao diện
    dark/light: Giao diện sáng / tối
    General: Cài đặt chung
    Change my password: Đổi mật khẩu
    Wallet: Ví
    Language: Ngôn ngữ
    Support: Hỗ trợ
    FAQ: Câu hỏi thường gặp
    Terms and conditions: Điều khoản và điều kiện
    Privacy policy: Chính sách bảo mật
    change-role: Đổi role
    delete-account: Xoá tài khoản
    setting-title: Tài khoản
    link-account: Kết nối tài khoản
</i18n>
