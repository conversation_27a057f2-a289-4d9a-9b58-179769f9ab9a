<script lang="ts" setup>
import { Capacitor } from '@capacitor/core'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

const { t } = useI18n()
const isNative = computed(() => Capacitor.isNativePlatform())
const support = ref(false)

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)

defineEmits(['back'])
</script>

<template>
    <header
        :class="[
            'relative flex items-center justify-center p-4 shadow-[0_8px_24px_rgba(149,157,165,0.2)]',
            { 'pt-safe': isNative },
        ]"
    >
        <Button
            plain
            text
            icon="pi pi-angle-left"
            class="!absolute left-2"
            @click="$emit('back')"
        />
        <div class="text-lg font-bold transition-all">
            {{ t('personal-doctor-diary') }}
        </div>
        <div v-if="!hideSupportContact" class="absolute right-4">
            <Button text plain size="small" @click="support = true">
                <iconify-icon
                    icon="material-symbols:support-agent"
                    class="text-3xl"
                />
            </Button>
        </div>
        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="support"
        />
    </header>
</template>

<i18n lang="json">
{
    "en": {
        "personal-doctor-diary": "Personal Doctor Diary"
    },
    "vi": {
        "personal-doctor-diary": "Nhật Ký Bác Sĩ Riêng"
    }
}
</i18n>
