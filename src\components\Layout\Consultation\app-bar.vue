<template>
  <div class="bg-primary sticky top-0 z-10 p-4 text-white">
    <div class="flex items-center justify-between">
      <div class="flex items-center space-x-2">
        <div>
          <Button
            icon="pi pi-angle-left"
            text
            severity="white"
            aria-label="back"
            class="h-10 w-10"
            @click="router.go(-1)"
          />
        </div>
        <div v-show="!shouldMinimize" class="flex space-x-2">
          <Avatar
            image="https://i.pravatar.cc/300?u=1"
            size="large"
            shape="circle"
            class="hidden sm:block"
          />
          <div>
            <div class="text-lg font-bold"><PERSON><PERSON>, Male, 47 year(s) old</div>
            <div>My dog has died</div>
          </div>
        </div>
      </div>
      <div class="flex items-center space-x-2">
        <Button
          icon="pi pi-video"
          text
          severity="white"
          aria-label="back"
          class="font-bold"
        />
        <Button
          icon="pi pi-phone"
          text
          severity="white"
          aria-label="back"
          class="h-10 w-10"
        />
        <Button
          icon="pi pi-bars"
          text
          severity="white"
          aria-label="back text-lg"
        />
      </div>
    </div>
    <div class="mt-2 flex space-x-2" :style="elementStyle">
      <Avatar
        image="https://i.pravatar.cc/300?u=1"
        size="large"
        shape="circle"
      />
      <div>
        <div class="text-lg font-bold">Jonh Weak, Male, 47 year(s) old</div>
        <div>My dog has died</div>
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
const { y } = useWindowScroll()
const router = useRouter()
const shouldMinimize = ref<boolean>(true)

const elementStyle = computed(() => {
  // Calculate the dimming and shrinking factor
  const factor = Math.max(0, 1 - y.value / 200)

  return {
    opacity: factor,
    transform: `scale(${factor})`,
    height: `${factor * 50}px`
  }
})

watch(y, (val) => {
  if (val <= 150) {
    shouldMinimize.value = true
  } else {
    shouldMinimize.value = false
  }
})
</script>
