<script setup lang="ts">
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'
import { appRoutes } from '~/constants'

import type { IQuickAction } from '~/models'

definePageMeta({
    layout: 'patient',
    middleware: 'auth-capacitor',
})

const { t, locale } = useI18n({ useScope: 'local' })
const colorMode = useColorMode()

const { signOut } = useAppAuth()
const { user } = useUserInfo({
    scope: ['_id', 'name', 'avatar', 'email', 'avatar'],
})

const support = ref(false)

const quickActions: ComputedRef<IQuickAction[]> = computed(() => [
    { icon: 'material-symbols:language', name: 'Language' },
    {
        icon: colorMode.value === 'dark' ? 'pi pi-sun' : 'pi pi-moon',
        name: t('theme'),
        command: toggleTheme,
    },
    {
        icon: 'material-symbols:support-agent',
        name: 'Support',
        important: true,
        command: () => {
            support.value = true
        },
    },
    {
        icon: 'material-symbols:logout',
        name: t('Sign Out'),
        command: signOut,
    },
    {
        icon: 'material-symbols:person',
        name: 'To doctor',
        to: '/provider/home',
    },
])

const categories: Record<string, Array<Record<string, string>>> = {
    telemedicine: [
        {
            title: t('adult'),
            cover: '/images/telemedicine_adult.png',
            group: locale.value === 'en' ? 'adult' : 'nguoi-lon',
        },
        {
            title: t('my kid'),
            cover: '/images/telemedicine_kid.png',
            group: locale.value === 'en' ? 'child' : 'nhi',
        },
    ],
    'mental health counseling': (() => {
        const group =
            locale.value === 'en' ? 'mental-health' : 'suc-khoe-tinh-than'
        return [
            {
                title: t('teenager'),
                cover: '/images/mental_health_teenager.png',
                group,
                target: 'teenager',
            },
            {
                title: t('myself'),
                cover: '/images/mental_health_myself.png',
                group,
                target: 'myself',
            },
            {
                title: t('couples'),
                cover: '/images/mental_health_couples.png',
                group,
                target: 'couples',
            },
        ]
    })(),
}

const isAccountVisible = ref()

const toggleTheme = () => {
    colorMode.preference = colorMode.value === 'dark' ? 'light' : 'dark'
}

const getCategoryQuery = (category: any): string => {
    const baseUrl = `${appRoutes.patient.services.provider.index}?_group=${category.group}`
    return category?.target ? `${baseUrl}&_target=${category.target}` : baseUrl
}

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)
const uiblocks = computed(() => config.value.pages?.services?.UiBlocks ?? [])
</script>

<template>
    <div class="flex flex-col">
        <SharedAccountSidebar
            v-model:visible="isAccountVisible"
            screen-size="Mobile"
            class="sm:hidden"
        />

        <SharedDynamicHeader>
            <template #header>
                <div class="flex flex-row items-center justify-between">
                    <h4
                        class="line-clamp-1 overflow-hidden overflow-ellipsis whitespace-normal break-words break-all"
                    >
                        {{ t('service') }}
                    </h4>

                    <Button
                        v-for="quickAction in quickActions?.filter(
                            (action) => action.important,
                        )"
                        :key="quickAction.name"
                        text
                        plain
                        size="small"
                        @click="quickAction.command"
                    >
                        <iconify-icon
                            :icon="quickAction.icon"
                            class="text-3xl"
                        />
                    </Button>
                </div>
            </template>
        </SharedDynamicHeader>

        <div class="grid grid-flow-row grid-cols-1 sm:grid-cols-2">
            <div>
                <PagePatientMembership
                    v-if="uiblocks.includes('membership')"
                    :user="{
                        name: user?.name,
                        avatar: user?.avatar?.url,
                        email: user?.email,
                    }"
                />
                <PagePatientServicesPersonalDoctor
                    v-if="uiblocks.includes('personal-doctor')"
                />
                <SharedPageSection
                    v-if="uiblocks.includes('telemedicine')"
                    :title="t('telemedicine')"
                    :pt="{
                        content:
                            'mt-2 no-scrollbar ml-6 max-w-full overflow-x-auto whitespace-nowrap',
                    }"
                    uppercase-title
                >
                    <SharedCoverCard
                        v-for="(category, j) in categories.telemedicine"
                        :key="j"
                        :title="category.title"
                        :cover="category.cover"
                        class="mb-2 ml-4 first:ml-0 last:mr-6"
                        :to="getCategoryQuery(category)"
                    />
                </SharedPageSection>
                <SharedPageSection
                    v-if="uiblocks.includes('mental-health')"
                    :title="t('mental health counseling')"
                    :pt="{
                        content:
                            'mt-2 no-scrollbar ml-6 max-w-full overflow-x-auto whitespace-nowrap',
                    }"
                    uppercase-title
                >
                    <SharedCoverCard
                        v-for="(category, j) in categories[
                            'mental health counseling'
                        ]"
                        :key="j"
                        :title="category.title"
                        :cover="category.cover"
                        class="mb-2 ml-4 first:ml-0 last:mr-6"
                        :to="getCategoryQuery(category)"
                    />
                </SharedPageSection>
            </div>

            <PagePatientServicesHealthProgram
                v-if="uiblocks.includes('health-program')"
            />
        </div>
        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="support"
        />
    </div>
</template>

<i18n lang="yaml">
en:
    'telemedicine': 'Telemedicine'
    'adult': 'Adult'
    'my kid': 'My kid'
    'mental health counseling': 'Mental health counseling'
    'teenager': 'Teenager'
    'myself': 'Myself'
    'couples': 'Couples'
    messageBasedHealthAdvice:
        title: 'Message-based Health Advice'
        infoMessage: 'Receive responses for medical and health questions within 24 hours through messaging - simple, effective, and cost-saving.'
        questionLabel: 'Question'
        askButtonLabel: 'Ask'
    seeDetail: 'See detail'
vi:
    'telemedicine': 'Khám từ xa'
    'adult': 'Khám người lớn'
    'my kid': 'Khám trẻ em'
    'teenager': 'Vấn đề với con trẻ'
    'myself': 'Vấn đề với bản thân'
    'couples': 'Vấn đề cặp đôi'
    'mental health counseling': 'Sức khỏe tinh thần'
    messageBasedHealthAdvice:
        title: 'Giải đáp sức khỏe'
        infoMessage: 'Nhận phản hồi cho các câu hỏi y khoa trong vòng 24 giờ thông qua tin nhắn - đơn giản, hiệu quả, tiết kiệm.'
        questionLabel: 'Câu hỏi'
        askButtonLabel: 'Hỏi'
    seeDetail: 'Xem chi tiết'
</i18n>
