import { computed, ref, type ComputedRef, type Ref } from '#imports'
import type { INotionPage } from '~/models'

export function usePageLink(page: INotionPage): {
  breadcrum: Ref<any[]>
  to: ComputedRef<string>
} {
  const primary = page.properties['Parent item']
    ? page.properties['Parent item'][0]
    : null
  const breadcrum = ref([])
  const to = computed<string>(() => {
    const parts = [page.properties.Slug]
    if (primary) {
      parts.unshift(primary.properties.Slug)
    } else {
      // console.log(15, page)
    }
    return '/' + parts.join('/')
  })
  return { breadcrum, to }
}
