<script lang="ts" setup>
import { appRoutes } from '~/constants'
import { Capacitor } from '@capacitor/core'
// import type { IWalletInfo } from '~/models'
import {
    computed,
    definePageMeta,
    onMounted,
    reactive,
    ref,
    useActionToolkit,
    useCallHandle,
    useConfirm,
    useConsultationSession,
    useDayjs,
    useI18n,
    useRoute,
    useRouter,
    useSocketIo,
    getPatientLimitUsage,
    useThrottleFn,
    useUserInfo,
    // useWalletManagement,
} from '#imports'
import { push } from 'notivue'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'

definePageMeta({
    middleware: 'auth-capacitor',
})

// const DEFAULT_REMAINING_TIME = {
//     remainingTime: '0/120',
//     balance: 120,
//     balanceFormatted: '00:00',
//     usable: 0,
//     usableFormatted: '00:00',
//     used: 120,
//     usedFormatted: '00:00',
//     percentageUsed: 0,
// }

const dayjs = useDayjs()
const { t } = useI18n()
const router = useRouter()
const { params, query }: any = useRoute()

const callHandle = useCallHandle()
const confirmDialog = useConfirm()
const { copySource } = useActionToolkit()
const { user } = useUserInfo({ scope: ['_id'] })

// State management
const latestSession = ref<any>(null)
const selectedMessage = ref<any>(null)
const isSessionCleared = ref<boolean>(false)
const visibleMessageAction = ref<boolean>(false)
const state = reactive({
    consultation: null as any,
    consultationUser: '',
    isMessageVisible: false,
    loadingEndAction: false,
    patient: query?.patient,
    provider: query?.provider,
    providerUser: query?.providerUser,
    support: false,
})

const actionState = reactive({
    isCallDisabled: true,
    isMessageDisabled: true,
    isEndDisabled: true,
})

// const remainingCallTime = ref<IWalletInfo>(DEFAULT_REMAINING_TIME)
// Get WalletInfo the Patient
const { refresh: refreshWallet, extractWalletsPatientInfo } =
    await getPatientLimitUsage(
        computed(() => state.providerUser),
        computed(() => state.patient),
    )

// Computed properties
const conversation = computed(() => params.roomId)
const userId = computed(() => user.value?._id ?? '')
const isNative = computed(() => Capacitor.isNativePlatform())

// API functions
const {
    getLatestConsultationSession,
    getConsultationSessionById,
    completeConsultationSession,
} = useConsultationSession()
// const { getPersonalDoctorWallet, formatRemainingTime } = useWalletManagement()
// const { fetchBenefit } = useBenefitPersonalDoctor()

// Navigation
const onBack = () => router.push('/provider/patients')

// Action handlers
const handleTextResponse = async () => {
    router.push({
        path: appRoutes.provider.form('provider-text-response'),
        query: {
            user: userId.value,
            sessionId: latestSession.value?._id,
            conversation: conversation.value,
            consultation: state.consultation?._id,
        },
    })
}

const handleEndConsultation = async () => {
    confirmDialog.require({
        message: t('confirm-end-consultation'),
        header: t('confirm'),
        acceptLabel: t('btn:confirm'),
        rejectLabel: t('btn:cancel'),
        position: 'bottom',
        accept: async () => {
            const promiseNotify = push.promise(t('loading'))

            try {
                state.loadingEndAction = true
                await completeConsultationSession(state.consultation?._id, {})

                latestSession.value = await getLatestConsultationSession(
                    userId.value,
                    conversation.value,
                )

                isSessionCleared.value = true

                actionState.isMessageDisabled = true
                actionState.isEndDisabled = true
                actionState.isCallDisabled = true

                promiseNotify.resolve(t('toast.action.complete.success'))
            } catch (e: any) {
                promiseNotify.error({
                    title: t('toast.action.complete.error'),
                    message: t(e?.message),
                    duration: 5000,
                })
                throw e
            } finally {
                state.loadingEndAction = false
            }
        },
    })
}

const handlePressTextMessage = (message: any) => {
    selectedMessage.value = message
    visibleMessageAction.value = true
}

const handleCopyMessageContent = () => {
    copySource(
        selectedMessage.value?.content,
        () => {
            push.success({
                message: t('toast.action.copy.success'),
            })

            visibleMessageAction.value = false
        },
        () =>
            push.error({
                message: t('toast.action.copy.error'),
            }),
    )
}

// Call handlers
const voiceCallHandle = () => {
    callHandle.open('voice', {
        _id: state.consultation?._id,
        conversation: conversation.value,
        providerUser: state.providerUser,
        user: state.consultationUser,
    })
}

const videoCallHandle = () => {
    callHandle.open('video', {
        _id: state.consultation?._id,
        conversation: conversation.value,
        providerUser: state.providerUser,
        user: state.consultationUser,
    })
}

// const refreshWallet = async (user: string) => {
//     if (!user) return
//     const personalDoctorWallet = await getPersonalDoctorWallet(user)
//     if (personalDoctorWallet) {
//         remainingCallTime.value = formatRemainingTime(
//             personalDoctorWallet.usable,
//             DEFAULT_REMAINING_TIME.balance,
//         )
//     }
// }

// Session refresh
const refreshAction = useThrottleFn(async () => {
    try {
        latestSession.value = await getLatestConsultationSession(
            userId.value,
            conversation.value,
        )

        if (!latestSession.value?._id) return

        const results = await getConsultationSessionById({
            session: latestSession.value._id,
            providerUser: state.providerUser,
        })

        if (!Array.isArray(results) || results.length === 0) return

        const consultationData = results[0]
        state.consultation = consultationData
        state.patient = consultationData?.patient
        state.provider = consultationData?.provider
        state.providerUser = consultationData?.providerUser
        state.consultationUser = consultationData?.user

        actionState.isCallDisabled = false

        switch (consultationData.state) {
            case 'WAITING':
                actionState.isMessageDisabled = false
                actionState.isEndDisabled = true
                break
            case 'INCONSULTATION':
                actionState.isMessageDisabled = false
                actionState.isEndDisabled = false
                break
            default:
                actionState.isCallDisabled = true
                actionState.isMessageDisabled = true
                actionState.isEndDisabled = true
        }
    } catch (e: any) {
        console.error('Error refreshing consultation session:', e)
        push.error({ message: t(e?.message), duration: 5000 })
    }
})

const actions = computed(() => [
    {
        lable: t('btn:call'),
        icon: 'mynaui:telephone-call-solid',
        command: voiceCallHandle,
        disabled: actionState.isCallDisabled,
    },
    {
        lable: t('video-call'),
        icon: 'mynaui:video-solid',
        command: videoCallHandle,
        disabled: actionState.isCallDisabled,
    },
    {
        lable: t('btn:message'),
        icon: 'mynaui:message-solid',
        command: handleTextResponse,
        disabled: actionState.isMessageDisabled,
    },
    {
        lable: t('btn:end'),
        icon: 'mynaui:check-circle-solid',
        command: handleEndConsultation,
        disabled: actionState.isEndDisabled,
        class: !actionState.isEndDisabled ? 'end-action-active' : '',
    },
])

async function onNewMessage() {
    await refreshAction()
    await refreshWallet()
}

// Socket Management
const consultationSocket = useSocketIo({
    channel: '/Consultation',
    debugLevel: 'debug',
})

consultationSocket.on('updated', async (consultation: any) => {
    if (consultation?.state === 'COMPLETED') {
        actionState.isCallDisabled = true
        actionState.isMessageDisabled = true
        actionState.isEndDisabled = true
    } else {
        await refreshAction()
    }
    await refreshWallet()
})

const { config } = useJsonConfigApp()
const hideSupportContact = computed(
    () => config.value.global?.hideContactSupport === true,
)

// Watchers
watch(
    [userId, () => consultationSocket.state.value.isConnected],
    async ([newUserId, isConnected]: any[]) => {
        if (newUserId && isConnected) {
            await consultationSocket.joinRoom({
                roomId: newUserId,
                userId: newUserId,
            })
        }
    },
    {
        immediate: true,
    },
)

onMounted(async () => {
    await refreshAction()
    await refreshWallet()
})
</script>

<template>
    <div>
        <w-chat-window
            v-if="userId"
            :user-id="userId"
            :room-id="conversation"
            :blocks="['header', 'body', 'footer']"
            hide-admin
            view="timelines"
            class="h-screen w-full text-black"
            @new-message="onNewMessage"
            @press-text-message="handlePressTextMessage"
        >
            <template #header>
                <div>
                    <header
                        :class="[
                            'relative flex items-center justify-center p-4 shadow-[0_8px_24px_rgba(149,157,165,0.2)]',
                            { 'pt-safe': isNative },
                        ]"
                    >
                        <Button
                            icon="pi pi-chevron-left"
                            rounded
                            text
                            severity="secondary"
                            class="!absolute left-2"
                            @click="onBack"
                        />
                        <div class="text-lg font-bold">
                            {{ t('personal-doctor-diary') }}
                        </div>
                        <div
                            v-if="!hideSupportContact"
                            class="absolute right-4"
                        >
                            <Button
                                text
                                plain
                                size="small"
                                @click="state.support = true"
                            >
                                <iconify-icon
                                    icon="material-symbols:support-agent"
                                    class="text-3xl"
                                />
                            </Button>
                        </div>
                    </header>

                    <div
                        class="flex items-center gap-2 bg-primary-50 px-3 py-2"
                    >
                        <iconify-icon
                            icon="material-symbols:info-rounded"
                            class="text-2xl text-primary-500"
                        />

                        <div class="text-xs text-primary-500">
                            <p>{{ t('diary-info.first') }}</p>
                            <p>{{ t('diary-info.second') }}</p>
                        </div>
                    </div>
                </div>
            </template>

            <template #session-start="{ session }">
                <div class="flex items-center gap-2 px-2 pt-2">
                    <p class="font-semibold">
                        {{
                            dayjs(session?.startMessage?.time).format(
                                'HH:mm DD/MM/YYYY',
                            )
                        }}
                    </p>
                    <span>&#10072;</span>
                    <p>{{ t('session:title') }}</p>
                </div>
                <div class="px-2 pb-2">{{ session?.title }}</div>
            </template>

            <template #session-end="{ message }">
                <div
                    v-if="
                        (latestSession?.lastMessage?.key === message?.key &&
                            isSessionCleared) ||
                        message?.isFinal
                    "
                    class="ml-[-44px] flex items-center sm:mx-4 lg:ml-12 lg:mr-0"
                >
                    <Divider layout="horizontal" class="flex" align="center">
                        <span class="mx-2 min-w-fit flex-1 text-xs">
                            {{ t('session:clear') }}
                        </span>
                    </Divider>
                </div>
            </template>

            <template #footer>
                <div
                    class="flex flex-col bg-white shadow-lg"
                    :class="{ 'pb-safe': isNative }"
                >
                    <PagePatientMembershipPersonalDoctorDiaryTimerSection
                        v-if="state.consultation"
                        :remaining-call-time="extractWalletsPatientInfo"
                    />

                    <!-- Action Buttons -->
                    <div class="space-y-4 border-t border-gray-100 p-4 pt-0">
                        <!-- Message & End Buttons -->
                        <div class="grid grid-cols-4 gap-3">
                            <div
                                v-for="(action, index) in actions"
                                :key="index"
                                class="flex flex-col items-center justify-center"
                            >
                                <Button
                                    :disabled="action.disabled"
                                    outlined
                                    text
                                    size="large"
                                    :class="action.class"
                                    @click="action.command"
                                >
                                    <iconify-icon
                                        class="text-3xl"
                                        :icon="action.icon"
                                    />
                                </Button>

                                <small class="text-gray-500">
                                    {{ action.lable }}
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </w-chat-window>

        <Dialog
            v-model:visible="state.isMessageVisible"
            :header="t('drawer:header')"
            position="center"
            modal
            pt:root:class="min-w-[80%]"
        >
            <div class="flex items-center gap-4">
                <Button
                    class="w-1/2"
                    :label="t('btn:text')"
                    @click="handleTextResponse"
                />
                <Button class="w-1/2" :label="t('btn:audio')" />
            </div>
        </Dialog>

        <SharedDialogSupport
            v-if="!hideSupportContact"
            v-model:visible="state.support"
        />

        <Dialog
            v-model:visible="visibleMessageAction"
            modal
            :header="t('dialog.message.action.header')"
            pt:root:class="w-4/5"
            pt:header:class="pb-0"
            pt:content:class="px-0 pb-2"
        >
            <Button
                text
                :label="t('dialog.message.action.copy')"
                severity="secondary"
                pt:root:class="w-full justify-start"
                @click="handleCopyMessageContent"
            >
                <template #icon>
                    <iconify-icon icon="stash:copy-duotone" class="text-4xl" />
                </template>
            </Button>
        </Dialog>
    </div>
</template>

<style scoped>
.action-button {
    @apply relative flex flex-col items-center justify-center gap-1 rounded-full p-4 transition-all duration-200 disabled:opacity-50;
}

.action-button:not(:disabled):active {
    @apply scale-95;
}

.voice-call {
    @apply h-16 w-16 bg-green-500 text-white shadow-lg hover:bg-green-600;
}

.video-call {
    @apply h-16 w-16 bg-blue-500 text-white shadow-lg hover:bg-blue-600;
}

/* Ripple effect */
.action-button:not(:disabled)::after {
    content: '';
    @apply absolute h-full w-full rounded-full opacity-0;
    background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.4) 0%,
        transparent 100%
    );
    transform: scale(0);
    transition:
        transform 0.3s ease-out,
        opacity 0.3s ease-out;
}

.action-button:not(:disabled):active::after {
    transform: scale(2);
    opacity: 1;
}

.end-action-active {
    @apply bg-primary-50 !important;
}

.end-action-active:hover {
    @apply bg-red-100 !important;
}
</style>

<i18n lang="json">
{
    "en": {
        "personal-doctor-diary": "Personal Doctor Diary",
        "btn:call": "Call",
        "btn:message": "Message",
        "btn:end": "Complete",
        "drawer:header": "Message",
        "btn:text": "Text",
        "btn:audio": "Audio",
        "remaining-time": "Remaining",
        "total-time": "Total Time",
        "used-time": "Used Time",
        "minutes": "minutes",
        "video-call": "Video Call",
        "confirm": "Confirm",
        "confirm-end-consultation": "Are you sure you want to end this consultation?",
        "btn:confirm": "Confirm",
        "btn:cancel": "Cancel",
        "session:title": "Call Log",
        "session:clear": "Session completed",
        "dialog": {
            "message": {
                "action": {
                    "header": "Options",
                    "copy": "Copy"
                }
            }
        },
        "toast": {
            "action": {
                "copy": {
                    "success": "Copied!",
                    "error": "Copy not supported"
                },
                "complete": {
                    "success": "Success!",
                    "error": "An error occurred"
                }
            }
        },
        "diary-info": {
            "first": "This Care Log is for clinical notes, not for general chat.",
            "second": "Every Doctor's response containing 50 to 160 characters will be counted as 1 minute of consultation."
        },
        "loading": "loading..."
    },
    "vi": {
        "personal-doctor-diary": "Nhật Ký Bác Sĩ Riêng",
        "btn:call": "Gọi",
        "btn:message": "Nhắn tin",
        "btn:end": "Hoàn tất",
        "drawer:header": "Nhắn tin",
        "btn:text": "Văn bản",
        "btn:audio": "Ghi âm",
        "remaining-time": "Còn lại",
        "total-time": "Tổng thời gian",
        "used-time": "Đã sử dụng",
        "minutes": "phút",
        "video-call": "Gọi video",
        "confirm": "Xác nhận",
        "confirm-end-consultation": "Bạn có chắc chắn muốn kết thúc cuộc tư vấn này?",
        "btn:confirm": "Xác nhận",
        "btn:cancel": "Hủy",
        "session:title": "Nhật Ký",
        "session:clear": "Đã hoàn tất phiên",
        "dialog": {
            "message": {
                "action": {
                    "header": "Tùy chọn",
                    "copy": "Sao chép"
                }
            }
        },
        "toast": {
            "action": {
                "copy": {
                    "success": "Đã sao chép!",
                    "error": "Không hỗ trợ sao chép"
                },
                "complete": {
                    "success": "Thành công!",
                    "error": "Đã có lỗi xảy ra"
                }
            }
        },
        "diary-info": {
            "first": "Nhật Ký này không phải là cửa sổ chat.",
            "second": "Mỗi tin nhắn chứa 50-160 ký tự của bác sĩ sẽ được tính là 1 phút tư vấn."
        },
        "loading": "Đang tải..."
    }
}
</i18n>
