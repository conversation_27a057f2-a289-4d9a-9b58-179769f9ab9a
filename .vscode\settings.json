{"cSpell.words": ["aggs", "<PERSON><PERSON><PERSON><PERSON>", "AVARY", "blockstring", "<PERSON><PERSON><PERSON>", "bookingoncall", "breadcrum", "callevent", "callkeep", "capgo", "capsync", "Careteam", "closebutton", "collapseicon", "composables", "consola", "descr", "dismissable", "dompurify", "<PERSON><PERSON><PERSON><PERSON>", "echarts", "ecommerce", "<PERSON><PERSON><PERSON>", "Engerix", "eventopposite", "expandicon", "firstname", "fullname", "greyonhov<PERSON>color", "gridlines", "healthgpt", "iconify", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "INCONSULTATION", "incrementbuttonicon", "indepth", "inperson", "itemscontent", "j<PERSON>ld", "khamtuxa", "knocklabs", "langgraph", "lastname", "lightbox", "markdownit", "muot", "nktk", "Notivue", "nuxi", "nuxt", "nuxtjs", "oidc", "ondemand", "onduty", "onesignal", "openapi", "outofoffice", "patientnote", "PATIENTNOTE", "pcc<PERSON><PERSON>ton", "pinia", "preg", "primeicons", "primeui", "primevue", "<PERSON><PERSON><PERSON><PERSON>", "quickcall", "rgba", "<PERSON><PERSON>", "stringee", "swiper", "tailwindcss", "teleconsultation", "telemedicine", "tiptap", "toggleablecontent", "topwrapper", "touchless", "tuan", "typecheck", "uptitle", "userdisplay", "userpic", "valign", "vuelidate", "vueuse", "wavesurfer", "Wellchat", "xlarge", "yearsofEXP", "zadigetvoltaire", "<PERSON><PERSON><PERSON>"], "tailwindCSS.experimental.classRegex": ["tw`(.*?)`", "tw\\('(.*?)'\\)", "<PERSON><PERSON><PERSON>"], "files.associations": {"*.css": "tailwindcss"}, "editor.quickSuggestions": {"strings": true}, "typescript.tsdk": "node_modules\\typescript\\lib", "i18n-ally.localesPaths": ["src/lang"], "i18n-ally.keystyle": "nested"}