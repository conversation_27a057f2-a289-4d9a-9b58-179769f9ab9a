<script setup lang="ts">
const { t } = useI18n({ useScope: 'global' })

interface PageSectionPt {
    root?: string
    content?: string
}

const props = defineProps({
    title: {
        type: String,
        default: '',
    },
    viewAllTo: {
        type: [String, Boolean],
        default: undefined,
    },
    pt: {
        type: Object as PropType<PageSectionPt>,
        default: () => ({
            root: '',
            content: '',
        }),
    },
})

const emit = defineEmits(['view-all-to'])

const isHeader = computed(() => props.title || props.viewAllTo)

const goto = () => {
    if (typeof props.viewAllTo === 'string') navigateTo(props.viewAllTo)
    else emit('view-all-to')
}
</script>

<template>
    <div :class="['mt-6 flex flex-col sm:mt-12', props.pt?.root]">
        <div
            v-if="isHeader"
            class="mx-6 mb-3 flex flex-row items-center justify-between"
        >
            <div v-if="props.title" class="inline-flex gap-2">
                <div
                    v-if="props.title"
                    class="h-6 w-1 items-center rounded-2xl bg-primary-400 sm:hidden"
                />
                <h3 v-if="props.title" class="flex-auto font-semibold">
                    {{ props.title }}
                </h3>
            </div>

            <button
                v-if="props.viewAllTo"
                v-bind="$attrs"
                class="flex flex-row items-center gap-2"
                @click="goto"
            >
                <span class="text-sm font-medium">
                    {{ t('view-all') }}
                    <Icon name="material-symbols:format-list-bulleted" />
                </span>
            </button>
        </div>

        <div :class="['mt-1', props.pt?.content]">
            <slot />
        </div>
    </div>
</template>
