import { defineStore } from 'pinia'

export const useAuthStore = defineStore({
    id: 'auth',
    state: () => ({
        isLoading: false,
        resetPassword: {
            password: '',
            redirectUri: '',
            phoneNumber: '',
        },
    }),
    actions: {
        setPasswordReset(newPassword: string) {
            this.resetPassword.password = newPassword
        },
        setRedirectUri(newRedirect: string) {
            this.resetPassword.redirectUri = newRedirect
        },
        setPhoneNumber(newPhoneNumber: string) {
            this.resetPassword.phoneNumber = newPhoneNumber
        },
        setIsLoading(newIsLoading: boolean) {
            this.isLoading = newIsLoading
        },
    },
})
