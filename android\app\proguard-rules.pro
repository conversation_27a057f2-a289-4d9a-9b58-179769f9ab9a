# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}

# Uncomment this to preserve the line number information for
# debugging stack traces.
#-keepattributes SourceFile,LineNumberTable

# If you keep the line number information, uncomment this to
# hide the original source file name.
#-renamesourcefileattribute SourceFile
# Amazon IAP
-keep class com.amazon.device.iap.PurchasingListener { *; }

# AndroidX AppCompat
-keep class androidx.appcompat.view.ContextThemeWrapper { *; }


## Agora
-keep class io.agora.**{*;}
-keep class io.agora.* { *; }

# Agora SDK
-keep class io.agora.base.VideoFrame$TextureBuffer { *; }
-keep class io.agora.base.VideoFrame$I420Buffer { *; }

## Capacior plugins 
-keep public class * extends com.getcapacitor.Plugin

# Firebase Performance Monitoring
-keep class com.google.firebase.perf.** { *; }
-dontwarn com.google.firebase.perf.**

# Firebase Performance Monitoring - URLConnection
-keep class com.google.firebase.perf.network.FirebasePerfUrlConnection { *; }
-dontwarn com.google.firebase.perf.network.FirebasePerfUrlConnection

## Local notification
-keep class com.dexterous.** { *; }

# okhttp
-keepattributes Signature
-keepattributes *Annotation*
-keep class okhttp3.** { *; }
-keep interface okhttp3.** { *; }
-dontwarn okhttp3.**

# okio
-keep class sun.misc.Unsafe { *; }
-dontwarn java.nio.file.*
-dontwarn org.codehaus.mojo.animal_sniffer.IgnoreJRERequirement
-keep class okio.** { *; }
-dontwarn okio.**

## Stringee
-keep class com.stringee.stringeeflutterplugin.** { *; }
-keep class com.stringee.sdk.android.** { *; }
-keep class org.webrtc.** { *; } 


# WebRTC
-dontwarn org.chromium.build.BuildHooksAndroid

# Jisti Meet SDK
-keep class org.jitsi.meet.** { *; }
-keep class org.jitsi.meet.sdk.** { *; }
-dontwarn com.google.appengine.**
-dontwarn com.squareup.okhttp.**
-dontwarn javax.servlet.**
-keep public class com.horcrux.svg.** {*;}
-keep class com.facebook.hermes.unicode.** { *; }

# Google Play Services
-keep class com.google.android.gms.common.api.GoogleApiClient { *; }
-dontwarn com.google.protobuf.**

# Volley
-keep class com.android.volley.NetworkResponse { *; }

-dontwarn com.google.devtools.build.android.desugar.runtime.ThrowableExtension