<script setup lang="ts">
import { onMounted, useI18n } from '#imports'

const { t } = useI18n({
    messages: {
        en: {
            'coming soon': 'coming soon',
        },
        vi: {
            'coming soon': 'Sớm ra mắt',
        },
    },
})
const isDarkMode = ref(false)

onMounted(() => {
    isDarkMode.value = window.matchMedia('(prefers-color-scheme: dark)').matches
    window
        .matchMedia('(prefers-color-scheme: dark)')
        .addEventListener('change', (e) => {
            isDarkMode.value = e.matches
        })
})
</script>

<template>
    <div class="space-y-2 text-center">
        <h1 class="text-primary text-3xl font-bold">
            {{ t('coming soon') }}
        </h1>
    </div>
</template>
