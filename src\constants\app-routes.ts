// app-routes.ts
export const appRoutes = {
    patient: {
        healthRecords: {
            index: '/patient/health-records',
            consultation: (memberId: string) =>
                `/patient/health-records/${memberId}/consultation`,
            medicalSection: (memberId: string, section: string) =>
                `/patient/health-records/${memberId}/${section}`,
            membership: {
                healthProgram: {
                    babyDevelopment:
                        '/patient/services/health-program/baby-development',
                    pregnancyDiary:
                        '/patient/services/health-program/pregnancy-diary',
                },
            },
        },
        services: {
            index: '/patient/services',
            provider: {
                index: '/patient/services/provider',
                profile: (providerSlug: string) =>
                    `/patient/services/provider/${providerSlug}`,
                rating: (providerSlug: string) =>
                    `/patient/services/provider/${providerSlug}/ratings`,
            },
            healthProgram: {
                babyDevelopment:
                    '/patient/services/health-program/baby-development',
            },
        },
        checkout: (productSlug: string) => `/patient/checkout/${productSlug}`,
        consultation: {
            index: '/patient/consultation',
            detail: (consultationId: string) =>
                `/patient/consultation/${consultationId}`,
        },
        diary: (conversationId: string) => `/patient/diary/${conversationId}`,
        eduhub: {
            index: '/patient/edu-hub',
            article: {
                index: '/patient/edu-hub/article',
                detail: (articleId: string) =>
                    `/patient/edu-hub/article/${articleId}`,
            },
        },
        form: (formSlug: string) => `/patient/form/${formSlug}`,
        settings: {
            profile: '/patient/settings/profile',
        },
    },
    doctor: {
        home: '/provider/home',
    },
    provider: {
        colleague: {
            index: '/provider/home/<USER>',
            profile: (colleagueSlug: string) =>
                `/provider/home/<USER>/${colleagueSlug}`,
        },
        patients: {
            index: '/provider/patients',
        },
        diary: (conversationId: string) => `/provider/diary/${conversationId}`,
        form: (formSlug: string) => `/provider/form/${formSlug}`,
        settings: {
            profile: '/provider/settings/profile',
        },
    },
    auth: {
        checkPhone: '/auth/check-phone',
        onboarding: '/auth/onboarding',
        signUp: '/auth/sign-up',
        callback: '/auth/callback',
        googleCallback: '/auth/google-callback',
        linkAccount: {
            google: '/auth/link-account/google',
        },
        resetPassword: {
            newPassword: '/auth/reset-password/new-password',
            verifyOtp: '/auth/reset-password/verify-otp',
        },
        signIn: '/auth/sign-in',
    },
    external: {
        web: {
            khamtuxa: {
                providerProfile: (providerSlug: string) =>
                    `https://khamtuxa.wellcare.vn/bac-si/${providerSlug}`,
            },
        },
    },
}
