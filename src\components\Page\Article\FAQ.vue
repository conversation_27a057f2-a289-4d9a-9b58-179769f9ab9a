<script setup lang="ts">
import { type PropType, useI18n } from '#imports'
import type { Hit, NotionPage } from '~/models'

const { t } = useI18n()
const { faqs } = defineProps({
    faqs: {
        type: Array as PropType<Hit<NotionPage>[]>,
        required: true,
    },
    loading: { type: Boolean, default: false },
})
</script>

<template>
    <div v-if="faqs?.length > 0">
        <template v-if="loading"
            ><Skeleton width="100%" height="300px" />
        </template>
        <template v-else>
            <h2 class="text-2xl font-semibold text-surface-900 lg:text-3xl">
                {{ t('faq') }}
            </h2>
            <AccordionQuestion :items="faqs" />
        </template>
    </div>
</template>

<i18n lang="yaml">
en:
    faq: 'Frequently Asked Question'
vi:
    faq: 'Câu hỏi thường gặp'
</i18n>
