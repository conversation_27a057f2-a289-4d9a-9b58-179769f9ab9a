<script lang="ts" setup>
import { ref, useScroll, watch } from '#imports'

defineProps({
  isLoading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['arrived-top', 'arrived-bottom'])

const containerRef = ref<HTMLElement | any>(null)
const componentRef = ref<HTMLElement | any>(null)
const triggerRef = ref<HTMLElement | any>(null)

const { arrivedState } = useScroll(componentRef, { throttle: 200 })

watch(
  () => arrivedState,
  (newState) => {
    if (newState.top) emit('arrived-top')
    else if (newState.bottom) emit('arrived-bottom')
  },
  {
    deep: true,
  },
)
</script>

<template>
  <div ref="containerRef">
    <div ref="componentRef" class="h-full max-h-dvh w-full overflow-y-scroll">
      <slot name="main" />
      <div
        v-if="isLoading"
        ref="triggerRef"
        class="flex h-10 items-center justify-center bg-white dark:bg-slate-900"
      >
        <ProgressSpinner />
      </div>
    </div>
  </div>
</template>
