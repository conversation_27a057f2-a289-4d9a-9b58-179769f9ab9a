// plugins/clarity.client.ts
import { defineNuxtPlugin } from '#app'
import { Capacitor } from '@capacitor/core'
import clarity from '@microsoft/clarity'

const setCustomTags = (tags: Record<string, string | string[]>) => {
    try {
        Object.entries(tags).forEach(([key, value]) => {
            clarity.setTag(key, value)
        })
    } catch (error) {
        console.error('Failed to set custom tags in Clarity:', error)
    }
}

export default defineNuxtPlugin(async () => {
    const config = useRuntimeConfig()
    const { checkAuthStatus } = useAppAuth()

    const isNative = Capacitor.isNativePlatform()
    const isDev = process.env.NODE_ENV === 'development'
    const projectId = config.public.clarityProjectId as string

    clarity.init(projectId)

    try {
        const isLogged = await checkAuthStatus()
        if (isLogged) {
            const { user } = useUserInfo({ scope: ['_id', 'email'] })

            if (user.value?._id) {
                clarity.identify(user.value?._id, '', '', user.value?.email)

                setCustomTags({
                    userType: 'registered',
                    platform: isNative ? Capacitor.getPlatform() : 'web',
                    environment: isDev ? 'development' : 'production',
                })
            }
        } else {
            setCustomTags({
                userType: 'anonymous',
                platform: isNative ? Capacitor.getPlatform() : 'web',
            })
        }
    } catch (error: any) {
        console.error('Error during Clarity setup:', error)

        // Set error tags for debugging
        setCustomTags({
            setupError: error.message,
            errorTimestamp: new Date().toISOString(),
        })
    }
})
