<script setup lang="ts">
import { ref, reactive, onMounted } from '#imports'

const { user } = useUserInfo({ scope: ['name'] })
const username = computed(() => user.value?.name)
// To debug, you need to add ?_debug=true to the query parameters .
const state = reactive({
    name: '<PERSON><PERSON><PERSON>',
    consultationId: '12345678910ID',
})

//or debug/form/follow-up?_debug=true&consultationId=123

const form = ref({
    objectId: 'Y2PD6uVbqC',
    pages: [
        {
            id: 'main',
            type: 'input',
            elements: [
                {
                    id: 'follow-up',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'h2',
                            content: 'Hỏi thăm sau khám',
                        },
                    },
                },
                {
                    id: 'follow_up_multiple_choice',
                    type: 'input',
                    properties: {
                        input: {
                            label: `Sau đợt bệnh hôm rồi, hiện ${username.value} đã khỏe chưa?`,
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Đã khỏe',
                                    value: 'option1',
                                },
                                {
                                    label: 'Đã đỡ và chưa khỏi hẳn',
                                    value: 'option2',
                                },
                                {
                                    label: 'Diễn tiến nặng hơn',
                                    value: 'option3',
                                },
                            ],
                            key: 'follow_up_multiple_choice',
                        },
                    },
                },
            ],
        },
    ],
    title: 'Follow Up ver 2',
    submission: 'remote',
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
    createdAt: '2024-11-15T04:09:33.676Z',
    updatedAt: '2024-11-25T11:21:17.962Z',
    computes: [
        {
            key: 'name',
            component: 'hidden_field',
        },
        {
            key: 'consultationId',
            component: 'hidden_field',
        },
    ],
    machine: {
        id: 'followUpStateMachine',
        initial: 'main',
        states: {
            main: {
                on: {
                    ANSWER: [
                        {
                            target: 'end1',
                            actions: [],
                            guard: 'isRecovered',
                        },
                        {
                            target: 'end2',
                            actions: [],
                            guard: 'isNotFullyRecovered',
                        },
                    ],
                },
            },
            end1: {
                type: 'final',
            },
            end2: {
                type: 'final',
            },
        },
        actions: {},
        guards: {
            isRecovered: 'return true',
            isNotFullyRecovered: 'return true',
        },
    },
    status: 'draft',
})
onMounted(() => {
    window.addEventListener('message', (event) => {
        if (event.data.event === 'submit') {
            console.log('Form submitted:', event.data.state)
        } else if (event.data.event === 'state') {
            const newState = event.data.state
            Object.assign(state, newState)
            console.log('State updated:', state)
        }
    })
})

const formComponentRef = ref()

const handleSubmitResponse = () => {
    formComponentRef.value?.externalSubmit?.() // Submit form
    // Phản hồi xong sẽ đi đâu???
}

const handleSubmitReVisit = () => {
    formComponentRef.value?.externalSubmit?.() // Submit form
    // Muốn tái khám thì sao ????
}

// Tại vì form chưa hoàn thiện và tham khảo ý kiến nghiệp vụ từ chị Huyền và Đức nên mình phải dùng chay cách này!!!
// Đó là thay vì dùng nút Submit mặc định của Form, sáng Đức đưa ra ý tưởng là ẩn nút mặc định của form, thay vào đó tự gắn 2 nút: Phản hồi và Tái khảm. Mình sẽ tác động nút submit bên ngoài form, ngoài ra mình có thể custome tùy ý cho 2 nút này nếu muốn thêm
</script>

<template>
    <div class="mx-auto w-full max-w-7xl p-4 md:p-8">
        <F
            v-if="form"
            ref="formComponentRef"
            :form="form"
            :state="state"
            :show-button-list-area="false"
        />
        <div class="flex gap-4">
            <button
                class="submit-button rounded bg-zinc-700 px-4 py-2 text-white"
                @click="handleSubmitResponse"
            >
                Phản hồi
            </button>
            <button
                class="submit-button rounded bg-zinc-700 px-4 py-2 text-white"
                @click="handleSubmitReVisit"
            >
                Tái khám
            </button>
        </div>
    </div>
</template>
