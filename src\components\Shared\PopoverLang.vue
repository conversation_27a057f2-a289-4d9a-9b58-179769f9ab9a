<script setup lang="ts">
import type Popover from 'primevue/popover'

type Mode = 'dialog' | 'popover' | 'auto'

const props = withDefaults(
    defineProps<{
        mode?: Mode
    }>(),
    {
        mode: 'auto',
    },
)

const { display } = useDisplay()
const isMobile = computed(() => display.breakpoint.isMobile)

const showPopover = computed(() => {
    if (props.mode === 'auto') return !isMobile.value
    return props.mode === 'popover'
})

const popoverRef = ref<InstanceType<typeof Popover> | null>(null)
const isDialogVisible = ref(false)

const { locales, locale, setLocale, t } = useI18n()

const isCurrentLocale = (code: string) => code === locale.value

const toggle = (event: Event) => {
    if (!showPopover.value) {
        isDialogVisible.value = !isDialogVisible.value
    } else {
        popoverRef.value?.toggle(event)
    }
}

defineExpose({ toggle })
</script>

<template>
    <!-- Popover -->
    <Popover v-if="showPopover" ref="popoverRef">
        <!-- Language Options -->
        <div class="space-y-2 py-2">
            <Button
                v-for="_locale in locales"
                :key="_locale.code"
                class="relative w-full py-4 transition-colors last:mb-0"
                :severity="
                    isCurrentLocale(_locale.code) ? 'primary' : 'secondary'
                "
                outlined
                @click="setLocale(_locale.code)"
            >
                <div class="flex w-full items-center justify-between px-1">
                    <div class="flex items-center gap-2">
                        <span class="font-medium">{{ _locale.name }}</span>
                    </div>
                    <i
                        v-if="isCurrentLocale(_locale.code)"
                        class="pi pi-check text-primary-500"
                    >
                    </i>
                </div>
            </Button>
        </div>
    </Popover>

    <!-- Dialog -->
    <Dialog
        v-else
        v-model:visible="isDialogVisible"
        position="bottom"
        dismissable-mask
        :modal="true"
        :style="{ width: '100%', maxWidth: '100vw' }"
        class="p-dialog-bottom"
    >
        <template #header>
            <div class="text-xl font-medium first-letter:uppercase">
                {{ t('select-language') }}
            </div>
        </template>
        <!-- Language Options -->
        <div class="space-y-2 py-2">
            <Button
                v-for="_locale in locales"
                :key="_locale.code"
                class="relative w-full py-4 transition-colors last:mb-0"
                :severity="
                    isCurrentLocale(_locale.code) ? 'primary' : 'secondary'
                "
                outlined
                @click="setLocale(_locale.code)"
            >
                <div class="flex w-full items-center justify-between px-1">
                    <div class="flex items-center gap-2">
                        <span class="font-medium">{{ _locale.name }}</span>
                    </div>
                    <i
                        v-if="isCurrentLocale(_locale.code)"
                        class="pi pi-check text-primary-500"
                    >
                    </i>
                </div>
            </Button>
        </div>
    </Dialog>
</template>

<style scoped>
.p-dialog-bottom {
    margin: 0;
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

.p-dialog-bottom .p-dialog-header {
    padding-bottom: 0;
}

.p-dialog-bottom .p-dialog-content {
    padding: 0;
}
</style>
