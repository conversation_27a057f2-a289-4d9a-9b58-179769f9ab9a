const includePaths = ['/admin/', '/patient/', '/provider/', '/payment/']

export default defineNuxtRouteMiddleware(async (to) => {
    const { init } = useCallkeep()
    const { addNativeLogs } = useLogs()

    if (!includePaths.find((p) => to.path.includes(p))) return

    try {
        addNativeLogs({
            context: 'useCallkeep',
            message: 'init callkeep',
            time: new Date().toLocaleString(),
        })
        await init()
    } catch (e: any) {
        console.error('callkeep', e)
    }
})
