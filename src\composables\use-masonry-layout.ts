import { useBreakpoints, computed, type Ref } from '#imports'

export type Breakpoint = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl'

export interface IMasonryResponsiveOption {
  breakpoint: Breakpoint
  columns: number
}
const breakpointsTailwind = {
  'xs': 570,
  'sm': 640,
  'md': 768,
  'lg': 1024,
  'xl': 1280,
  '2xl': 1536,
}

const { display } = useDisplay()
const breakpoint = computed(() => display?.breakpoint?.is)

const getCurrentResponsiveOption = (
  breakpoints,
  responsiveOptions: IMasonryResponsiveOption[],
): IMasonryResponsiveOption => {
  if (!responsiveOptions) return responsiveOptions[0]
  for (let i = responsiveOptions.length - 1; i >= 0; i--) {
    if (breakpoints.greaterOrEqual(responsiveOptions[i].breakpoint).value)
      return responsiveOptions[i]
  }
  return responsiveOptions[0]
}

export default function useMasonryLayout(
  items: Ref<any[]>,
  responsiveOptions?: IMasonryResponsiveOption[],
) {
  // breakpoints
  const breakpoints = useBreakpoints(breakpointsTailwind)

  const _columnCount = responsiveOptions
    ? getCurrentResponsiveOption(breakpoints, responsiveOptions).columns
    : 1

  // column mapping
  const columns: ComputedRef<any[]> = computed(() => {
    const columns = Array.from({ length: _columnCount }, () => [])
    items.value.forEach((hit, index) => {
      const columnIndex = index % _columnCount
      columns[columnIndex].push(hit)
    })

    return columns
  })

  return {
    columns,
    breakpoint,
  }
}
