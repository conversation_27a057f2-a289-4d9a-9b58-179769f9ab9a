<template>
    <div>
        <WConsultationHeader
            :loading-voice-action="voiceCallLoading"
            :loading-video-action="videoCallLoading"
            @on:back="handleGoBack"
            @on:voice-call="callVoiceHandler"
            @on:video-call="callVideoHandler"
        />
        <WConsultationContainer
            ref="consultation-container"
            :consultation-id="consultationId"
            @on:view-consultation="onViewConsultation"
            @on:delivery-medication="onDeliveryMedication"
        />
    </div>
</template>

<script setup lang="ts">
import type { PropType } from '#imports'
import { io } from 'socket.io-client'
// import { appRoutes } from '~/constants';

definePageMeta({
    layout: 'consultation',
    middleware: 'auth-capacitor',
})

const props = defineProps({
    role: {
        type: String as PropType<'patient' | 'provider'>,
        required: true,
    },
})

const consultationStore = useConsultationStore()

const route = useRoute()
const router = useRouter()
const callHandle = useCallHandle()

const consultationId = computed(() => route.params?.id as string)

const { user } = useUserInfo({ scope: ['_id'] })
const userId = computed(() => user.value?._id ?? '')

const voiceCallLoading = ref<boolean>(false)
const videoCallLoading = ref<boolean>(false)

const callVideoHandler = () => {
    if (!consultationStore.detailData) throw new Error('call was denied')
    const consultation: any = consultationStore.detailData
    videoCallLoading.value = true
    callHandle.open('video', {
        _id: consultation._id,
        conversation: consultation.conversation?._id,
        user: consultation.user._id,
        providerUser: consultation.providerUser,
    })
    setTimeout(() => {
        videoCallLoading.value = false
    }, 1800)
}
const callVoiceHandler = () => {
    if (!consultationStore.detailData) throw new Error('call was denied')
    const consultation: any = consultationStore.detailData
    voiceCallLoading.value = true
    callHandle.open('voice', {
        _id: consultation?._id,
        conversation: consultation?.conversation?._id,
        user: consultation?.user?._id,
        providerUser: consultation?.providerUser,
    })
    setTimeout(() => {
        voiceCallLoading.value = false
    }, 1800)
}

function handleGoBack() {
    if (window.history.length > 2) {
        router.back()
    } else {
        router.push('/')
    }
}

function onViewConsultation(data: any) {
    console.log(data)
    navigateTo(`/${props.role}/consultation/${data._id}`)
}

function onDeliveryMedication(data: any) {
    console.log(data)
    navigateTo(`/${props.role}/checkout/mua-thuoc?consultation=${data._id}`)
}

const consultationContainer = useTemplateRef('consultation-container')

const refreshStateAndTime = useDebounceFn(() => {
    console.log('consultation updated ...')
    if (consultationContainer.value) {
        consultationContainer.value.refreshStateAndTime()
    }
}, 2500)

const config = useRuntimeConfig()
const socketEndpoint = config.public.socketEndPoint as string
const socket = io(socketEndpoint + '/Consultation', {
    autoConnect: true,
    reconnection: true,
    reconnectionDelay: 1000,
    reconnectionDelayMax: 5000,
    reconnectionAttempts: 5,
    transports: ['websocket'],
})

socket.on('updated', refreshStateAndTime)

watch(
    userId,
    (val) => {
        socket.emit('join', {
            user: val,
            room: val,
        })
    },
    { immediate: true },
)
</script>

<i18n lang="yaml">
en:
    male: Male
    female: Female
    header:continue-with-app: Continue to open with
    title:telemedicine-app: Telemedicine mobile app
vi:
    male: Nam
    female: Nữ
    header:continue-with-app: Tiếp tục mở bằng
    title:telemedicine-app: Ứng dụng Khám từ xa
</i18n>
