<script setup lang="ts">
import {
    computed,
    navigateTo,
    onMounted,
    ref,
    useAppAuth,
    useDevice,
    useI18n,
    usePermission,
    useRouter,
    useRuntimeConfig,
    useUserInfo,
} from '#imports'
import { push } from 'notivue'
import { App } from '@capacitor/app'
import { Clipboard } from '@capacitor/clipboard'

const router = useRouter()
const config = useRuntimeConfig()
const { t } = useI18n({ useScope: 'local' })
const { signIn, handleAppAuthUrlOpen, signOut, signInIDP } = useAppAuth()
const { user } = useUserInfo({ scope: ['_id', 'search', 'avatar.url'] })
const { deviceInfo, batteryInfo, deviceId, languageCode, languageTag, isIos } =
    useDevice()
const { permissions } = usePermission()
// const dialog = useDialog()

const page = ref()
const routers = ref<any[]>([])
const searchRouter = () => {
    routers.value = router.getRoutes().map(({ name, path }) => ({ name, path }))
}

const showPermissionAlert = ref(false)

const copyToClipboard = async (text: string) => {
    try {
        await Clipboard.write({ string: text })
        // Optionally, you can show a push or notification here to indicate successful copy
        push.info({
            message: t('copied-to-clipboard'),
        })
    } catch (e: any) {
        push.error({
            message: t('copy-not-supported'),
        })
        console.log(
            'copyToClipboard',
            JSON.stringify({ error: e.message, stack: e.stack }),
        )
    }
}

// handle initial logic here
const fullName = computed(() => {
    return user?.value?.search
})

// const isAvatar = computed<boolean>(() => !!user.value?.avatar?.url)
const avatarUrl = computed(
    () =>
        user.value?.avatar?.url ||
        `https://ui-avatars.com/api/?name=${user.value.name}`,
)

const membershipPopup = ref(false)

onMounted(() => {
    App.addListener('appUrlOpen', handleAppAuthUrlOpen)
    // // permission is required
    // if (Capacitor.isNativePlatform() && !permissionInterval) {
    //     setTimeout(onCheckPermission, 3000)
    //     permissionInterval = setInterval(onCheckPermission, 10000)
    // }
})

const goToPage = () =>
    navigateTo(typeof page.value === 'string' ? page.value : page.value.path)
</script>

<template>
    <div
        class="flex min-h-screen items-center justify-center bg-surface-100 p-4 dark:bg-surface-900"
    >
        <div class="grid grid-cols-1 gap-2 lg:grid-cols-2">
            <!-- Device Info Card -->
            <div
                class="rounded-xl bg-surface-50 p-6 shadow-lg dark:bg-surface-700"
            >
                <h2
                    class="mb-4 text-center text-2xl font-bold text-surface-900"
                >
                    Device Information
                </h2>
                <div class="space-y-2">
                    <p>
                        <span class="font-semibold">Device Name:</span>
                        {{ deviceInfo }}
                    </p>
                    <p>
                        <span class="font-semibold">Battery Level:</span>
                        {{ batteryInfo?.batteryLevel }}
                    </p>
                    <p>
                        <span class="font-semibold">Device ID:</span>
                        {{ deviceId }}
                    </p>
                    <p>
                        <span class="font-semibold">Language Code:</span>
                        {{ languageCode }}
                    </p>
                    <p>
                        <span class="font-semibold">Language Tag:</span>
                        {{ languageTag }}
                    </p>
                    <p>
                        <span class="font-semibold">Is iOS:</span> {{ isIos }}
                    </p>
                    <p>
                        <span class="font-semibold"> Runtime config:</span>
                        <button
                            class="w-full rounded-lg bg-green-500 p-2 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                            @click="copyToClipboard(JSON.stringify(config))"
                        >
                            Copy
                        </button>
                    </p>
                </div>
            </div>

            <!-- Login Card -->
            <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
                <h2 class="mb-4 text-center text-2xl font-bold text-gray-900">
                    Login
                </h2>
                <div class="space-y-4">
                    <Avatar
                        v-if="avatarUrl"
                        :image="avatarUrl"
                        :pt="{}"
                        class="mr-2 h-16 w-16"
                        size="xlarge"
                        shape="circle"
                    />
                    <p
                        v-if="fullName"
                        class="mb-2 text-gray-600 dark:text-gray-400"
                    >
                        {{ fullName }}
                    </p>

                    <button
                        class="w-full rounded-lg bg-red-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-red-600"
                        @click="signOut"
                    >
                        sign out
                    </button>
                    <button
                        class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                        @click="signIn()"
                    >
                        Sign in
                    </button>
                    <button
                        class="w-full rounded-lg bg-green-500 px-4 py-3 font-semibold capitalize text-white transition duration-300 ease-in-out hover:bg-green-600"
                        @click="signInIDP('google')"
                    >
                        Sign in gg
                    </button>
                </div>
            </div>

            <!-- Permission Card -->
            <div
                class="rounded-xl bg-surface-50 p-6 shadow-lg dark:bg-surface-700"
            >
                <h2
                    class="mb-4 text-center text-2xl font-bold text-surface-900"
                >
                    Permissions
                </h2>

                <p>Camera: {{ permissions.camera }}</p>
                <p>Micro: {{ permissions.microphone }}</p>
                <p>Push: {{ permissions.notifications }}</p>
                <SharedPermissionAlert v-model="showPermissionAlert" />
            </div>

            <div class="rounded-xl bg-gray-50 p-6 shadow-lg dark:bg-gray-700">
                <h2
                    class="mb-4 text-center text-2xl font-bold text-surface-900"
                >
                    Go to page
                </h2>
                <InputGroup>
                    <!-- <InputText v-model="page" placeholder="Enter router" /> -->
                    <AutoComplete
                        v-model="page"
                        option-label="path"
                        :suggestions="routers"
                        @complete="searchRouter"
                    >
                        <template #option="slotProps">
                            <div class="flex items-center">
                                <div>{{ slotProps.option.path }}</div>
                            </div>
                        </template>
                    </AutoComplete>
                    <Button
                        icon="pi pi-arrow-right"
                        severity="info"
                        @click="goToPage"
                    />
                </InputGroup>
            </div>

            <div
                class="rounded-xl bg-gray-50 p-6 text-left shadow-lg dark:bg-gray-700"
            >
                <p>Onboarding</p>
                <button @click="router.push('/auth/onboarding')">
                    <span class="underline"> /auth/onboarding </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-gray-50 p-6 text-left shadow-lg dark:bg-gray-700"
            >
                <p>In-App Purchase</p>
                <button @click="router.push('/debug/in-app-purchase')">
                    <span class="underline"> /debug/in-app-purchase </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Patient dashboard</p>
                <button @click="router.push('/patient/health-records')">
                    <span class="underline"> /patient/health-records </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Doctor dashboard</p>
                <button @click="router.push('/provider/home')">
                    <span class="underline"> /provider/home </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Checkout flow</p>
                <button @click="router.push('/patient/checkout/flow-test')">
                    <span class="underline"> /patient/checkout/:slug </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Payment</p>
                <button @click="router.push('/payment/order/123')">
                    <span class="underline"> /payment/order/123 </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>PHR</p>
                <button
                    @click="router.push('/patient/consultations/loremipsum')"
                >
                    <span class="underline">
                        /patient/consultations/lorem
                    </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Activity feed</p>
                <button @click="router.push('/admin/activity-feed')">
                    <span class="underline"> /admin/activity-feed </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Push notification</p>
                <button @click="router.push('/admin/push')">
                    <span class="underline"> /admin/push </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Callkeep</p>
                <button @click="router.push('/admin/callkeep')">
                    <span class="underline"> /admin/callkeep </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Stringee call</p>
                <button @click="router.push('/admin/stringee')">
                    <span class="underline"> /admin/stringee </span>
                </button>
            </div>

            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Livwell demo</p>
                <button @click="router.push('/admin/livwell-demo')">
                    <span class="underline"> /admin/livwell-demo </span>
                </button>
            </div>
            <div
                class="rounded-xl bg-surface-50 p-6 text-left shadow-lg dark:bg-surface-700"
            >
                <p>Membership popup</p>
                <button @click="membershipPopup = true">
                    <span class="underline"> open</span>
                </button>
            </div>
            <SharedMembershipPopup v-model:visible="membershipPopup" @skip="membershipPopup = false" />
        </div>
    </div>
</template>
