import type { NuxtI18nOptions } from '@nuxtjs/i18n'

export const i18n = {
    locales: [
        {
            code: 'en',
            name: 'English',
        },
        {
            code: 'vi',
            name: 'Tiếng Việt',
        },
    ],
    defaultLocale: 'vi',
    strategy: 'no_prefix',
    vueI18n: './src/configs/vueI18n.config.ts',
    detectBrowserLanguage: false,
} satisfies NuxtI18nOptions
