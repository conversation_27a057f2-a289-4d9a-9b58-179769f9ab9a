<script setup lang="ts">
import { useMedicalHistory } from '~/composables/page/patient/health-records/medical-history'
import dayjs from 'dayjs'

const formatDiagnosisDate = (date: string): string => {
    const currentYear = dayjs().year()
    const diagnosisYear = dayjs(date).year()

    return diagnosisYear === currentYear
        ? dayjs(date).format('YYYY-MM')
        : dayjs(date).format('YYYY-MM')
}
const { medicalHistory } = useMedicalHistory()
const { t } = useI18n()
</script>

<template>
    <div>
        <SharedPageSection
            v-if="medicalHistory.length > 0"
            :title="t('past-conditions')"
            :pt="{ content: 'px-6' }"
            view-all-to="/patient/health-records/[id]/medical-history"
        >
            <Timeline
                :value="
                    medicalHistory.filter(
                        (_element: any, index: number) => index < 2,
                    )
                "
                pt:eventopposite:class="hidden"
            >
                <template #content="slotProps">
                    <small class="block text-surface-500 dark:text-surface-400">
                        {{ formatDiagnosisDate(slotProps.item.diagnosisDate) }}
                    </small>
                    <PagePatientHealthRecordsMedicalHistoryCard
                        :medical-history="slotProps.item"
                    />
                </template>
            </Timeline>
        </SharedPageSection>
        <PagePatientHealthRecordsAddInfo
            v-else
            title="Add Past Conditions"
            class="mx-6 mt-8"
            icon="material-symbols:healing"
            description="Document your past conditions for a complete health record."
        />
    </div>
</template>

<i18n lang="json">
{
    "vi": {
        "past-conditions": "Past Conditions"
    },
    "en": {
        "past-conditions": "Past Conditions"
    }
}
</i18n>
