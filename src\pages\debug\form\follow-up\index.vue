<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'

const router = useRouter()
const { user } = useUserInfo({ scope: ['name'] })
const username = computed(() => user.value?.name)
// To debug, you need to add ?_debug=true to the query parameters .
const state = reactive({
    name: '<PERSON><PERSON><PERSON>',
    consultationId: '12345678910ID',
})

//or debug/form/follow-up?_debug=true&consultationId=123

const form = ref({
    objectId: 'YeMXuAaRXC',
    pages: [
        {
            id: 'main',
            type: 'input',
            elements: [
                {
                    id: 'title',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block_content',
                            tag: 'h1',
                            content: 'Follow Up',
                        },
                    },
                },
                {
                    id: '1',
                    type: 'input',
                    properties: {
                        input: {
                            key: 'recovery',
                            component: 'multiple_choice',
                            label: `How is ${username.value} recovering since the last consultation?`,
                            choices: [
                                {
                                    value: 'well',
                                    label: 'Recovered',
                                },
                                {
                                    value: 'better',
                                    label: 'Getting better, but not yet fully recovered.',
                                },
                                {
                                    value: 'worse',
                                    label: 'Condition is worsening',
                                },
                            ],
                        },
                    },
                },
            ],
        },
        // {
        //     id: 'end1',
        //     type: 'end',
        //     elements: [
        //         {
        //             id: '4',
        //             type: 'display',
        //             properties: {
        //                 display: {
        //                     component: 'html_block_content',
        //                     tag: 'p',
        //                     content:
        //                         'Excellent! Your steady recovery is the outcome we are eagerly looking forward to.',
        //                 },
        //             },
        //         },
        //     ],
        // },
        // {
        //     id: 'end2',
        //     type: 'end',
        //     elements: [
        //         {
        //             id: '4',
        //             type: 'display',
        //             properties: {
        //                 display: {
        //                     component: 'html_block_content',
        //                     tag: 'p',
        //                     content:
        //                         "Please carefully read and follow the doctor's note or treatment plan, and follow-up as needed.",
        //                 },
        //             },
        //         },
        //     ],
        // },
    ],
    computes: [
        {
            key: 'name',
            component: 'hidden_field',
        },
        {
            key: 'consultationId',
            component: 'hidden_field',
        },
    ],
    machine: {
        id: 'followUpStateMachine',
        initial: 'main',
        states: {
            main: {
                on: {
                    ANSWER: [
                        {
                            target: 'end1',
                            actions: [],
                            guard: 'isRecovered',
                        },
                        {
                            target: 'end2',
                            actions: [],
                            guard: 'isNotFullyRecovered',
                        },
                    ],
                },
            },
            end1: {
                type: 'final',
            },
            end2: {
                type: 'final',
            },
        },
        actions: {},
        guards: {
            isRecovered: 'return true',
            isNotFullyRecovered: 'return true',
        },
    },
    title: 'Follow Up',
    status: 'draft',
    submission: 'remote',
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
})
onMounted(() => {
    window.addEventListener('message', (event) => {
        if (event.data.event === 'submit') {
            console.log('Form submitted:', event.data.state)
        } else if (event.data.event === 'state') {
            const newState = event.data.state
            Object.assign(state, newState)
            console.log('State updated:', state)
        }
    })
})
const handleFormSubmit = () => {
    router.push('/patient/health-records')
    // console.log('🚀 ~ handleFormSubmit ~ handleFormSubmit:', submittedState)
}
</script>

<template>
    <div class="mx-auto w-full max-w-7xl p-4 md:p-8">
        <F :form="form" :state="state" @submit="handleFormSubmit" />
    </div>
</template>
