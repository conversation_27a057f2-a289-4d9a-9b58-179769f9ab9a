<script setup lang="ts">
import { useRoute } from '#imports'

const route = useRoute()

const roles = ['patient', 'provider']
const role = computed(() => {
    return roles.find((r) => route.path.includes(r))
})

const setRole = (_role: string) => {
    navigateTo(`/${_role}/settings`)
}
</script>

<template>
    <div class="w-full rounded-lg">
        <!-- Header -->
        <div class="p-3">
            <h2
                class="flex items-center gap-2 font-medium text-gray-700 dark:text-gray-200"
            >
                <span class="uppercase">Change Role</span>
            </h2>
        </div>

        <div class="space-y-2 p-2">
            <Button
                v-for="(_role, index) in roles"
                :key="index"
                class="relative w-full py-4 transition-colors last:mb-0"
                :severity="_role === role ? 'primary' : 'secondary'"
                outlined
                @click="setRole(_role)"
            >
                <div class="flex w-full items-center justify-between px-1">
                    <div class="flex items-center gap-2">
                        <span class="font-medium">{{ _role }}</span>
                    </div>
                    <i
                        v-if="_role === role"
                        class="pi pi-check text-primary-500"
                    >
                    </i>
                </div>
            </Button>
        </div>
    </div>
</template>
