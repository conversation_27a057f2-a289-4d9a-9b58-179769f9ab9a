import { defineNuxtRouteMiddleware, useInternetCall } from '#imports'
const includePaths = ['/patient/', '/provider/']

export default defineNuxtRouteMiddleware(async (to) => {
    const { addNativeLogs } = useLogs()
    const { user } = useUserInfo({ scope: ['_id', 'isProvider', 'phone'] })
    if (!includePaths.find((p) => to.path.includes(p))) return
    if (!user.value) return
    const internetCall = useInternetCall()
    try {
        addNativeLogs({
            context: 'callHandleMiddleware',
            message: 'init useCallHandle',
            time: new Date().toLocaleString(),
        })
        internetCall.connect(user.value?._id || '')
    } catch (e: any) {
        addNativeLogs({
            level: 'error',
            context: 'callHandleMiddleware',
            message: e.message,
            time: new Date().toLocaleString(),
        })
    }
})
