import { ElasticIndex } from '~/models'
import { transform } from '../../../../../utils'
import type { HitNotionEduHub, ICategoryCard } from '~/models'

export function usePagePatientEduHubMediaCategory() {
    const { hits, loading } = useFetchElasticWithDefault<HitNotionEduHub>(
        ElasticIndex.NOTION_EDU_HUB,
        {
            filters: [{ term: { 'page.properties.Type.keyword': 'Category' } }],
            _source: {
                includes: [
                    'page.cover.url',
                    'page.properties.Name',
                    'page.properties.Slug',
                    'page.properties.Description',
                ],
            },
        },
    )

    const categories: ComputedRef<ICategoryCard[]> = computed(() => {
        return hits.value.map((hit: HitNotionEduHub) => {
            return {
                _id: hit._id || '',
                cover: hit.page?.cover?.url || '',
                name: hit.page?.properties?.Name || '',
                slug: hit.page?.properties?.Slug || '',
                description: hit.page?.properties?.Description || '',
            }
        })
    })

    const { aggregations } = useFetchElasticWithDefault(
        ElasticIndex.NOTION_EDU_HUB,
        {
            size: 0,
            aggs: {
                ids: {
                    terms: { field: 'page.properties.Parent item.id.keyword' },
                },
            },
            filters: [{ term: { 'page.properties.Type.keyword': 'Topic' } }],
        },
        {
            transform: transform.aggregations
        },
    )

    return { categories, loading, aggregations }
}
