import { ElasticIndex } from '~/models'
import type {
    HitNotionProviders,
    HitNotionWebsite,
    ISpecialtyCard,
} from '~/models'

export function usePageProviderHomeSpecialty(
    specialtySlugs: string[],
    providerSlug: ComputedRef<string>,
) {
    const { hits: specialtyHits, loading: loadingSpecialties } =
        useFetchElasticWithDefault<HitNotionWebsite>(
            ElasticIndex.NOTION_WEBSITE,
            {
                size: 100,
                filters: [
                    {
                        prefix: {
                            'page.properties.Labels.keyword': 'specialty:',
                        },
                    },
                    {
                        terms: {
                            'page.properties.Slug.keyword': specialtySlugs,
                        },
                    },
                ],
                _source: {
                    excludes: [],
                    includes: [
                        'page.cover.url',
                        'page.properties.Slug',
                        'page.properties.Name',
                    ],
                },
            },
        )

    const {
        hits: providerHits,
        loading: loadingProviders,
        execute: searchProviders,
    } = useFetchElasticWithDefault<HitNotionProviders>(
        ElasticIndex.PROVIDERS,
        {
            size: 150,
            must_not: [
                {
                    match_phrase: {
                        'page.properties.Slug.keyword': providerSlug.value,
                    },
                },
            ],
            _source: {
                includes: [
                    'page.properties.Name',
                    'page.properties.Avatar.url',
                    'page.properties.Specialties.properties.Slug',
                ],
            },
        },
        {
            immediate: false,
        },
    )

    const specialties: ComputedRef<ISpecialtyCard[]> = computed(() => {
        return specialtyHits.value.map((specialtyHit: HitNotionWebsite) => {
            const { Slug: specialtySlug, Name: specialtyName } =
                specialtyHit.page.properties
            const coverUrl = specialtyHit.page.cover?.url

            const matchingProviders = providerHits.value.filter(
                (providerHit: HitNotionProviders) =>
                    providerHit.page.properties?.Specialties?.some(
                        (_specialty: any) =>
                            _specialty.properties?.Slug === specialtySlug,
                    ),
            )

            return {
                slug: specialtySlug,
                name: specialtyName,
                cover: coverUrl,
                providers: matchingProviders.map(
                    (providerHit: HitNotionProviders) => {
                        return {
                            avatar: providerHit.page.properties.Avatar?.url,
                        }
                    },
                ),
            }
        })
    })

    const loading = computed(
        () => loadingSpecialties.value || loadingProviders.value,
    )

    watch(
        providerSlug,
        async (newProviderSlug) => {
            if (newProviderSlug) {
                await searchProviders()
            }
        },
        {
            immediate: true,
            deep: true,
        },
    )

    return { specialties, loading }
}
