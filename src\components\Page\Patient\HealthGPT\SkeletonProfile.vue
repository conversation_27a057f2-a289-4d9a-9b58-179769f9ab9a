<script setup lang="ts"></script>

<template>
    <div class="mx-auto flex h-screen max-w-md flex-col bg-gray-50">
        <!-- Header Skeleton -->
        <div class="flex items-center border-b bg-white p-4">
            <div class="size-8 rounded-full bg-gray-200 text-gray-300" />
            <div class="ml-4 flex items-center">
                <div class="size-10 animate-pulse rounded-full bg-gray-200" />
                <div class="ml-3">
                    <div class="h-4 w-20 animate-pulse rounded bg-gray-200" />
                    <div
                        class="mt-2 h-3 w-16 animate-pulse rounded bg-gray-200"
                    />
                </div>
            </div>
            <div class="ml-auto">
                <div class="h-8 w-8 animate-pulse rounded-full bg-gray-200" />
            </div>
        </div>

        <!-- Bot Profile Skeleton -->
        <div class="mb-2 bg-white px-6">
            <div class="mb-4 flex items-center">
                <div class="h-16 w-16 animate-pulse rounded-full bg-gray-200" />
                <div class="ml-4 flex-1">
                    <div class="h-5 w-24 animate-pulse rounded bg-gray-200" />
                    <div
                        class="mt-2 h-4 w-20 animate-pulse rounded bg-gray-200"
                    />
                </div>
            </div>
            <!-- Description lines -->
            <div class="mb-4 space-y-2">
                <div class="h-4 animate-pulse rounded bg-gray-200" />
                <div class="h-4 animate-pulse rounded bg-gray-200" />
                <div class="h-4 w-3/4 animate-pulse rounded bg-gray-200" />
            </div>
            <!-- Share button skeleton -->
            <div class="h-10 w-full animate-pulse rounded-md bg-gray-200" />
        </div>

        <!-- Quick Actions Skeleton -->
        <div class="flex-1 overflow-auto px-4">
            <!-- Gastritis diet card -->
            <div class="mb-2 rounded-lg bg-white p-4">
                <div class="flex items-center justify-between">
                    <div class="h-5 w-48 animate-pulse rounded bg-gray-200" />
                    <div class="h-5 w-5 animate-pulse rounded bg-gray-200" />
                </div>
                <div class="mt-2 h-4 w-40 animate-pulse rounded bg-gray-200" />
            </div>

            <!-- Question cards -->
            <div class="mt-10">
                <div
                    v-for="index in 4"
                    :key="index"
                    class="mb-2 rounded-lg bg-white p-4"
                >
                    <div class="flex items-center justify-between">
                        <div class="mr-4 flex-1">
                            <div
                                class="mb-2 h-4 animate-pulse rounded bg-gray-200"
                            />
                            <div
                                class="h-4 w-3/4 animate-pulse rounded bg-gray-200"
                            />
                        </div>
                        <div
                            class="h-5 w-5 flex-shrink-0 animate-pulse rounded bg-gray-200"
                        />
                    </div>
                </div>
            </div>
        </div>

        <!-- Input Area Skeleton -->
        <div class="border-t bg-white p-4">
            <div class="flex items-center rounded-full bg-gray-100 p-2">
                <div class="h-8 w-8 animate-pulse rounded-full bg-gray-200" />
                <div
                    class="mx-2 h-8 flex-1 animate-pulse rounded-full bg-gray-200"
                />
                <div class="flex space-x-2">
                    <div
                        v-for="n in 3"
                        :key="n"
                        class="h-8 w-8 animate-pulse rounded-full bg-gray-200"
                    />
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped></style>
