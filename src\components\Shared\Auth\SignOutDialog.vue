<script setup lang="ts">
import { useUserInfo } from '#build/imports'
import { Capacitor } from '@capacitor/core'
import { useDeviceTracking, useRevenueCat } from '~/composables'

interface Props {
    visible: boolean
    title?: string
    message?: string
    btnCancelTitle?: string
    btnSubmitTitle?: string
}

withDefaults(defineProps<Props>(), {
    title: 'signOut.action',
    message: 'signOut.confirmMessage',
    btnCancelTitle: 'btn:cancel',
    btnSubmitTitle: 'ok',
})

const emit = defineEmits(['update:visible', 'confirm'])
const { t } = useI18n()
const { user } = useUserInfo({ scope: '_id' })
const { logout: logoutCallkeep } = useCallkeep()
const { removeDevice } = useDeviceTracking()
const { logout: logoutPush } = usePushNotification()
const { signOut } = useAppAuth()
const { display } = useDisplay()
const { clear: clearUser } = useUserInfo()
const { clearMemberActivated } = useHealthRecordStore()
const { logout: logoutRevenueCat } = useRevenueCat()

const isIos = computed(() => Capacitor.getPlatform() === 'ios')

const handleCancel = () => {
    emit('update:visible', false)
}

const handleConfirm = async () => {
    if (Capacitor.isNativePlatform()) {
        try {
            removeDevice()
        } catch (error) {
            console.error(error)
        }
        try {
            await logoutCallkeep()
        } catch (error) {
            console.error(error)
        }
        try {
            await logoutPush()
        } catch (error) {
            console.error(error)
        }
    }
    if (user.value?._id && isIos.value) {
        await logoutRevenueCat()
    }

    clearMemberActivated()
    clearUser()
    await signOut()

    emit('confirm')
    emit('update:visible', false)
}
</script>

<template>
    <Dialog
        :visible="visible"
        :modal="true"
        :closable="false"
        :draggable="false"
        class="w-[30rem]"
        dismissable-mask
        :header="t(title)"
        :position="display?.breakpoint?.isMobile ? 'bottom' : 'center'"
        @update:visible="(val: boolean) => emit('update:visible', val)"
    >
        <p class="mb-4 text-surface-600 dark:text-surface-400">
            {{ t(message) }}
        </p>

        <div class="flex justify-end gap-3">
            <Button
                type="button"
                :label="t(btnCancelTitle)"
                severity="secondary"
                class="px-4 py-2"
                @click="handleCancel"
            />
            <Button
                type="button"
                :label="t(btnSubmitTitle)"
                class="w-1/5 px-4 py-2"
                @click="handleConfirm"
            />
        </div>
    </Dialog>
</template>
