<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import formData from './index.json'

const router = useRouter()
const state = reactive({
    user: '659bc0335028762cf46a2555',
    conversation: '66b20045d28f5574b24e4181',
    sessionId: '66b20045d28f5574b24e41811727326097111',
})

const form = ref(formData)
onMounted(() => {
    window.addEventListener('message', (event) => {
        if (event.data.event === 'submit') {
            console.log('Form submitted:', event.data.state)
        } else if (event.data.event === 'state') {
            const newState = event.data.state
            Object.assign(state, newState)
            console.log('State updated:', state)
        }
    })
})
const handleFormSubmit = () => {
    router.push('/patient/health-records')
}
</script>

<template>
    <div class="mx-auto w-full max-w-7xl p-4 md:p-8">
        <F :form="form" :state="state" @submit="handleFormSubmit" />
    </div>
</template>
