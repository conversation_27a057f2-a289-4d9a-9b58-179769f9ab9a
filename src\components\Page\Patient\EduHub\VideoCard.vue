<script setup lang="ts">
import type { IItemCard } from '~/models'

defineProps<{ media: IItemCard }>()
</script>

<template>
    <!-- Video Item -->
    <div class="border-b border-gray-100">
        <!-- Preview Section - Always visible -->
        <div class="flex items-start p-3">
            <!-- Thumbnail -->
            <div class="relative h-20 w-32 shrink-0 overflow-hidden rounded-md">
                <NuxtImg
                    :src="media.cover"
                    alt="Video thumbnail"
                    class="h-full w-full object-cover"
                    placeholder="https://placehold.co/1600x400/f5f5f5/FFF?text=\n"
                />
                <!-- Play Button Icon -->
                <button
                    class="absolute left-1/2 top-1/2 flex -translate-x-2/4 -translate-y-2/4 items-center justify-center rounded-full bg-black/25 p-2 text-white transition-opacity hover:opacity-90"
                    aria-label="Play video"
                >
                    <iconify-icon icon="solar:play-bold" />
                </button>
                <span
                    class="absolute bottom-1 right-1 rounded bg-black/70 px-1 text-xs text-white"
                >
                    {{ media.duration }}
                </span>
            </div>

            <div class="ml-3 flex flex-col">
                <!-- Title Preview -->
                <h3 class="line-clamp-5 text-sm font-medium leading-snug">
                    {{ media.name }}
                </h3>
                <!-- Meta Info -->

                <div class="mt-2 flex items-center gap-x-2">
                    <span class="text-xs text-gray-500">
                        {{ media.provider.title }}.
                        {{ media.provider.name }}
                    </span>
                </div>
            </div>
        </div>
    </div>
</template>
