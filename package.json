{"private": true, "type": "module", "scripts": {"build": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxi build", "cap:android": "pnpm generate-capacitor && npx cap sync android && npx cap open android", "cap:ios": "pnpm generate-capacitor && npx cap sync ios && npx cap open ios", "capsync:android": "pnpm generate-capacitor && npx cap sync android", "capsync:ios": "pnpm generate-capacitor && npx cap sync ios", "cleanup": "npx nuxi cleanup", "dev": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' && nuxi dev -o -p 3000 --host", "generate-capacitor": "pnpm generate && npx cap copy", "generate": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxi generate", "lint": "eslint src", "lint:fix": "eslint src --fix", "lint:play": "pnpm -C run lint", "openapi-typescript": "openapi-typescript ./models/response/openapi/consultation.json -o ./models/response/openapi/consultation.d.ts", "postinstall": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxi prepare", "prepare": "nuxi prepare", "preview": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxi preview", "readme:coverage": "istanbul-badges-readme", "start": "nuxi start", "test:unit": "vitest run tests/unit --coverage.enabled --coverage.reporter='json-summary'", "test:ci": "vitest run --coverage.enabled --coverage.reporter='json-summary'", "test:dev": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' vitest", "test": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' vitest run", "typecheck": "cross-env NODE_OPTIONS='--no-warnings=ExperimentalWarning' nuxi typecheck"}, "devDependencies": {"@capacitor/cli": "6.2.0", "@capacitor/screen-orientation": "^6.0.2", "@nuxt/devtools": "^1.5.2", "@nuxt/eslint": "^0.7.0", "@nuxt/eslint-config": "^0.7.0", "@nuxt/image": "^1.8.1", "@nuxt/test-utils": "^3.14.2", "@nuxtjs/color-mode": "^3.5.1", "@nuxtjs/google-fonts": "^3.2.0", "@nuxtjs/i18n": "8.5.6", "@nuxtjs/tailwindcss": "^6.12.2", "@playwright/test": "^1.47.1", "@primevue/nuxt-module": "^4.2.5", "@types/node": "22.7.8", "@vitest/coverage-istanbul": "^2.1.1", "@vitest/coverage-v8": "^2.1.1", "@vue/test-utils": "^2.4.6", "@vueuse/core": "^11.3.0", "@vueuse/nuxt": "^11.3.0", "@wellcare/muot-ui": "0.3.10", "@zadigetvoltaire/nuxt-gtm": "^0.0.13", "autoprefixer": "^10.4.20", "cross-env": "^7.0.3", "dayjs": "1.11.13", "dayjs-nuxt": "2.1.11", "firebase": "^11.0.0", "globals": "^15.9.0", "happy-dom": "^15.7.4", "husky": "^9.1.6", "istanbul-badges-readme": "^1.9.0", "nuxt": "3.14.1592", "oidc-client-ts": "^3.1.0", "openapi-typescript": "^7.3.0", "playwright-core": "^1.47.1", "postcss": "^8.4.47", "postcss-import": "^16.1.0", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.8", "rollup-plugin-regexp": "^5.0.1", "tailwindcss": "^3.4.14", "typescript": "^5.6.2", "video.js": "^8.19.1", "vite-plugin-vue-devtools": "^7.4.5", "vitest": "^2.1.1", "vue-tsc": "^2.0.29"}, "dependencies": {"@capacitor/android": "6.2.0", "@capacitor/app": "6.0.2", "@capacitor/assets": "^3.0.5", "@capacitor/browser": "6.0.2", "@capacitor/clipboard": "^6.0.1", "@capacitor/core": "6.2.0", "@capacitor/device": "^6.0.1", "@capacitor/ios": "6.2.0", "@capacitor/preferences": "^6.0.2", "@capacitor/splash-screen": "^6.0.2", "@capawesome/capacitor-app-update": "^6.0.0", "@capgo/inappbrowser": "^6.9.33", "@duccanhole/lightbox": "^0.0.2", "@formsible/element": "0.4.20", "@knocklabs/client": "^0.10.13", "@microsoft/clarity": "^1.0.0", "@microsoft/fetch-event-source": "^2.0.1", "@nuxtjs/robots": "^4.1.9", "@nuxtjs/sitemap": "6.1.2", "@pinia/nuxt": "^0.5.5", "@primevue/forms": "^4.2.5", "@revenuecat/purchases-capacitor": "^10.0.1", "@revenuecat/purchases-typescript-internal-esm": "^13.17.0", "@tiptap/extension-text-style": "^2.8.0", "@tiptap/extension-underline": "^2.7.3", "@tiptap/pm": "^2.7.2", "@tiptap/starter-kit": "^2.7.2", "@tiptap/vue-3": "^2.7.2", "@types/lodash": "^4.17.12", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vuepic/vue-datepicker": "^10.0.0", "@wellcare/capacitor-app-auth": "^0.0.9", "@wellcare/capacitor-callkeep": "6.0.6", "@wellcare/capacitor-editor": "6.0.0", "@wellcare/capacitor-permissions": "6.0.0-permission.3", "@wellcare/capacitor-plugin-agora": "6.0.6-socket", "@wellcare/capacitor-stringee": "6.0.1-hangup.2", "@wellcare/formsible-runner": "0.4.15", "@wellcare/nuxt3-module-account": "1.1.2", "@wellcare/nuxt3-module-chart": "1.2.5", "@wellcare/nuxt3-module-chat": "0.0.85", "@wellcare/nuxt3-module-conference": "0.0.16-fix.4", "@wellcare/nuxt3-module-consultation": "0.1.12", "@wellcare/nuxt3-module-data-layer": "1.0.15", "@wellcare/nuxt3-module-form": "0.8.0", "@wellcare/nuxt3-module-media": "0.0.6", "@wellcare/nuxt3-module-notion": "0.2.19", "@wellcare/nuxt3-module-phr": "0.0.41", "capacitor-native-settings": "^6.0.1", "capacitor-plugin-safe-area": "^3.0.3", "cordova-clarity": "2.3.0", "cordova-plugin-device": "^3.0.0", "cross-env": "^7.0.3", "eslint": "^9.9.1", "jwt-decode": "^4.0.0", "libphonenumber-js": "^1.11.15", "lodash": "^4.17.21", "notivue": "^2.4.5", "nuxt-jsonld": "^2.0.8", "onesignal-cordova-plugin": "5.2.5", "parse": "^5.3.0", "primevue": "^4.2.5", "quill": "1.3.7", "socket.io-client": "^4.8.0", "swiper": "11.1.9", "tailwindcss-primeui": "0.3.4", "tailwindcss-safe-area-capacitor": "^0.5.1", "vue-dompurify-html": "^5.1.0", "wavesurfer.js": "^7.8.6"}, "resolutions": {"rollup": "4.21.1", "vue-i18n": "9.14.2"}}