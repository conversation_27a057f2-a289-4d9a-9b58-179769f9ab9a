<script setup lang="ts">
import { useFamilyHealthHistory } from '~/composables/page/patient/health-records/family-health-history'
const { familyHealthHistory } = useFamilyHealthHistory()

interface Props {
    title: string
    to: string
}

defineProps<Props>()
</script>

<template>
    <div>
        <SharedPageSection
            v-if="familyHealthHistory.length > 0"
            :title="title"
            :pt="{ content: 'px-4' }"
            :view-all-to="to"
        >
            <DataTable :value="familyHealthHistory" size="small" class="w-full">
                <Column field="relation" header="Blood Relatives" />
                <Column field="description" header="Conditions" />
            </DataTable>
        </SharedPageSection>
        <PagePatientHealthRecordsAddInfo
            v-else
            title="Add Family Medical History"
            class="mx-4 mt-8"
            icon="material-symbols:family-restroom"
            description="Provide details about your family's medical history."
        />
    </div>
</template>
