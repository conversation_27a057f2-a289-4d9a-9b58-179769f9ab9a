<template>
    <div class="flex flex-col items-center pt-6">
        <NuxtImg
            :src="member.related?.avatar?.url"
            class="h-28 w-28 rounded-full object-cover shadow-lg"
        />
        <p class="mt-4 text-xl font-bold text-surface-900">
            {{ name }}
        </p>
        <p class="text-sm text-surface-500 dark:text-surface-300">
            {{ gender }}, {{ age }} years
        </p>
    </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
    member: any // Define the structure of the member object
}

const props = defineProps<Props>()

const dayjs = useDayjs()

// Computed properties to handle member data
const name = computed(() => props.member?.related?.name || 'N/A')
const gender = computed(() => props.member?.related?.gender || 'N/A')
const age = computed(() => {
    const dob = props.member?.related?.dob || ''
    return dob ? dayjs().diff(dob, 'year') : 'N/A'
})
</script>
