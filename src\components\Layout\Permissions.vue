<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { Capacitor } from '@capacitor/core'
import type { PermissionType } from '@wellcare/capacitor-permissions'

const showAlert = ref(false)
const showDialog = ref(false)
const isMinimized = ref(false) // New state for minimized alert

const { permissions, check: checkPermissionState } = usePermission()
const { addDevice } = useDeviceTracking()
const { t } = useI18n()

const permissionsRequire: PermissionType[] = [
    'camera',
    'microphone',
    'notifications',
]

const isNative = computed(() => Capacitor.isNativePlatform())

const checkPermissions = async () => {
    if (!isNative.value) return false
    console.log('[debug] check permission at layout ')
    console.log('camera ' + permissions.value.camera)
    console.log('microphone ' + permissions.value.microphone)
    console.log('notifications ' + permissions.value.notifications)
    await checkPermissionState(permissionsRequire)
    return permissionsRequire.some(
        (permission) =>
            permissions.value[permission] &&
            permissions.value[permission] !== 'granted',
    )
}

// const toggleMinimize = () => {
//     isMinimized.value = !isMinimized.value
// }

const handlePermissionsUpdate = async (granted: boolean) => {
    if (granted) {
        // Nếu đã cấp đủ quyền
        showDialog.value = false
        showAlert.value = false
    } else {
        // Nếu từ chối/không cấp quyền
        showDialog.value = false
        showAlert.value = true
    }

    if (isNative.value) {
        await addDevice()
    }
}

const openPermissionDialog = () => {
    showDialog.value = true
}

const handleDialogClose = async () => {
    // Khi đóng dialog, chuyển sang hiện alert nếu chưa cấp đủ quyền
    const hasUngranted = await checkPermissions()
    showAlert.value = hasUngranted
    showDialog.value = false
}

// Check permissions on mount
onMounted(async () => {
    if (!isNative.value) return

    const hasUngranted = await checkPermissions()
    if (hasUngranted) {
        // Nếu chưa cấp đủ quyền, hiện dialog ngay
        showDialog.value = true
    }
    await addDevice()
})

defineExpose({
    showAlert,
    showDialog,
})
</script>

<template>
    <div>
        <!-- Alert Banner -->
        <div
            v-if="isNative && showAlert"
            :class="{
                'pt-3': !isNative,
                'pt-safe': !isMinimized && isNative,
                'sticky-alert': !isMinimized,
                'floating-alert': isMinimized,
            }"
            class="space-y-2 px-3 pb-3"
        >
            <div class="flex items-start justify-between gap-2">
                <div class="flex items-start gap-2">
                    <i
                        class="pi pi-exclamation-triangle mr-2 text-yellow-600"
                    ></i>
                    <small v-if="!isMinimized" class="text-yellow-800">
                        {{ t('alertBannerMessage') }}
                    </small>
                    <small v-else class="text-yellow-800">
                        {{ t('alertBannerMessageMinimized') }}
                    </small>
                </div>
                <!-- <button
                    class="text-yellow-600 hover:text-yellow-700"
                    @click="toggleMinimize"
                >
                    <i
                        :class="
                            isMinimized
                                ? 'pi pi-window-maximize'
                                : 'pi pi-window-minimize'
                        "
                    ></i>
                </button> -->
            </div>
            <div v-if="!isMinimized" class="flex items-center justify-end">
                <Button
                    :label="t('grantPermissions')"
                    class="self-end"
                    text
                    @click="openPermissionDialog"
                />
            </div>
        </div>

        <!-- Main Content -->
        <div
            :class="{
                'content-with-alert': isNative && showAlert && !isMinimized,
            }"
        >
            <slot />
        </div>

        <!-- Permissions Dialog -->
        <Dialog
            v-model:visible="showDialog"
            modal
            class="p-dialog-maximized"
            :draggable="false"
            :closable="true"
            :pt="{
                content: {
                    class: 'pa-0',
                },
            }"
            @hide="handleDialogClose"
        >
            <SharedSettingsPermissions
                @update:permissions="handlePermissionsUpdate"
            />
        </Dialog>
    </div>
</template>

<style scoped>
.sticky-alert {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    background-color: rgb(254 249 195);
    border-bottom: 1px solid rgb(250 204 21);
    z-index: 20;
}

.floating-alert {
    position: absolute;
    top: 1rem;
    right: 1rem;
    max-width: 300px;
    background-color: rgb(254 249 195);
    border: 1px solid rgb(250 204 21);
    border-radius: 0.5rem;
    z-index: 20;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.content-with-alert {
    padding-top: 48px;
}
</style>

<i18n lang="json">
{
    "en": {
        "alertBannerMessage": "To make the most of Khám từ xa Wellcare, please grant the following permissions: Camera, Microphone, Notifications.",
        "alertBannerMessageMinimized": "Additional permissions required.",
        "grantPermissions": "Grant All",
        "dialogHeader": "Grant Access Permissions"
    },
    "vi": {
        "alertBannerMessage": "Để sử dụng dịch vụ Khám từ xa Wellcare, vui lòng cấp quyền truy cập sau: Camera, Micro, Thông báo.",
        "alertBannerMessageMinimized": "Cần cấp thêm quyền truy cập.",
        "grantPermissions": "Cấp quyền",
        "dialogHeader": "Cấp quyền truy cập"
    }
}
</i18n>
