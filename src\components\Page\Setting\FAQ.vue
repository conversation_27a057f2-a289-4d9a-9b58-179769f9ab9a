<script setup lang="ts">
const { faqs } = usePageSettingsSupportFAQ()
</script>

<template>
    <div>
        <Tabs
            v-if="faqs?.length"
            :value="
                faqs?.[0]?.category?.Slug || faqs?.[0]?.category?.Name || ''
            "
            scrollable
        >
            <TabList>
                <template v-for="(faq, index) in faqs" :key="index">
                    <Tab v-if="faq.category?.Slug" :value="faq.category?.Slug">
                        {{ faq.category.Name }}
                    </Tab>
                </template>
            </TabList>

            <TabPanels>
                <template v-for="(faq, index) in faqs" :key="index">
                    <TabPanel
                        v-if="faq.category?.Slug"
                        :value="faq.category?.Slug"
                    >
                        <Accordion
                            expand-icon="pi pi-angle-down"
                            collapse-icon="pi pi-angle-up"
                        >
                            <template
                                v-for="question in faq.questions"
                                :key="question.slug"
                            >
                                <AccordionPanel
                                    v-if="question.slug"
                                    :value="question.slug"
                                >
                                    <AccordionHeader
                                        pt:root:style="text-align: left"
                                    >
                                        {{ question.content }}
                                    </AccordionHeader>
                                    <AccordionContent>
                                        <WArticleBody
                                            :blocks="question.answer"
                                        />
                                    </AccordionContent>
                                </AccordionPanel>
                            </template>
                        </Accordion>
                    </TabPanel>
                </template>
            </TabPanels>
        </Tabs>
    </div>
</template>
