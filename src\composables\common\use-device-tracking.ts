import { Device } from '@capacitor/device'
import { CapacitorPluginPermissions } from '@wellcare/capacitor-permissions'
import { push } from 'notivue'

/**
 * Composable for device tracking and permission management.
 * Provides functionality to add a device with permissions and update permissions if necessary.
 *
 * @returns {object} - Contains:
 *   - `addDevice`: Function to add device information and permissions to the backend.
 *   - `updatePermission`: Function to update device permissions.
 *
 * @example
 * const { addDevice, updatePermission } = useDeviceTracking()
 *
 * // Add device on component mount
 * await addDevice()
 *
 * // Update permission when permissions change
 * const newPermissionsPayload = { camera: true, location: true }
 * await updatePermission(newPermissionsPayload)
 */
export function useDeviceTracking() {
    const { $fetchWellcare } = useNuxtApp()

    const permissions = reactive({
        camera: 'unknown',
        microphone: 'unknown',
        notifications: 'unknown',
    })

    const payload = ref<any>()
    const deviceInfo = ref<any>()
    const deviceId = ref<any>()

    // Lazy-loaded function to add device info to backend
    const { execute: executeAddDevice } = useLazyAsyncData(
        'add-device',
        async () => {
            console.log('[debug] track deviceId ' + deviceId.value)
            console.log('[debug] track deviceInfo ', deviceInfo.value)
            console.log(
                `[debug] track permission camera - ${permissions.camera}, microphone - ${permissions.microphone}, notifications - ${permissions.notifications}`,
            )
            if (deviceId.value && deviceInfo.value) {
                return await $fetchWellcare(
                    '/user-management/user-device/add-device',
                    {
                        method: 'POST',
                        body: {
                            permissions: toRaw(permissions),
                            info: toRaw(deviceInfo.value),
                            deviceId: deviceId.value,
                        },
                    },
                )
            }
            return undefined
        },
        { immediate: false },
    )

    // Main function to add device, including fetching device info and permissions
    const addDevice = async () => {
        try {
            // Fetch permissions and update app store
            for (const key of Object.keys(permissions)) {
                const res: any =
                    await CapacitorPluginPermissions?.checkPermission?.({
                        permission: key as any,
                    })
                console.log(`[debug] permission ${key}: ${res[key]}`)
                permissions[key] = res[key]
            }

            // Get device information and ID
            deviceInfo.value = await Device.getInfo()
            deviceId.value = (await Device.getId()).identifier

            // Execute add device API call
            await executeAddDevice()
        } catch (e) {
            // Show error in toast notification
            push.error({
                message: `Error while adding device: ${e}`,
            })
        }
    }

    const removeDevice = async () => {
        try {
            const deviceId = (await Device.getId()).identifier
            await $fetchWellcare(
                '/user-management/user-device/remove-device/' + deviceId,
                {
                    method: 'DELETE',
                },
            )
        } catch (e) {
            console.error(e)
        }
    }

    // Lazy-loaded function to update permissions for a specific device
    const { execute: executeUpdatePermission } = useLazyAsyncData(
        'update-permission',
        async () => {
            if (payload.value) {
                return await $fetchWellcare(
                    `/user-management/user-device/${deviceId.value}/permissions`,
                    {
                        method: 'PUT',
                        body: {
                            payload: payload.value,
                        },
                    },
                )
            }
            return undefined
        },
        { immediate: false },
    )

    // Function to update permissions based on new payload
    const updatePermission = async (newPayload: any) => {
        deviceId.value = (await Device.getId()).identifier
        payload.value = newPayload
        await executeUpdatePermission()
    }

    return { addDevice, removeDevice, updatePermission }
}
