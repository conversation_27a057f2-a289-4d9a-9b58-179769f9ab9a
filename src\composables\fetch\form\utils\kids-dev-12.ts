import type { IHiddenField } from '~/models'

export const form = (hidden_fields: IHiddenField[]) => ({
    objectId: 'kQYywwCvx4',
    pages: [
        {
            id: 'Starting Page',
            elements: [
                {
                    id: 'm2jtjs9e.hy',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'p',
                            content:
                                '<PERSON><PERSON><PERSON> mốc phát triển rất quan trọng! Cách em bé chơi, học, nói, hành động và vận động chính là những dấu hiệu quan trọng trong sự phát triển thể chất và thần kinh. Hãy tham chiếu và đánh dấu các mốc phát triển mà em bé đã đạt được khi 12 tháng tuổi. Đồng thời mang theo danh sách này khi đi khám định kỳ với bác sĩ của em bé. B<PERSON>c sĩ sẽ tư vấn thêm về các mốc phát triển mà em bé đã đạt được và giải đáp nếu cha mẹ có bất cứ băn khoăn nào.',
                        },
                    },
                },
                {
                    id: 'm2jtkak8.w3j',
                    type: 'display',
                    properties: {
                        display: {
                            component: 'html_block',
                            tag: 'img',
                            src: 'https://images.bauerhosting.com/affiliates/sites/12/2023/03/11-month-old-baby.jpg?ar=16:9&fit=crop&crop=top&auto=format&w=1440&q=80',
                            alt: 'Nice picture',
                        },
                    },
                },
            ],
            type: 'start',
        },
        {
            id: 'Giao tiếp xã hội và tình cảm',
            elements: [
                {
                    id: 'm2jtlbn0.fm9',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết chơi trò đập/vỗ tay 2 người',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtlbn0fm9',
                            fetch: {},
                        },
                    },
                },
            ],
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jtnpxl.p',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết vẫy tay “bye bye”',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtnpxlp',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtq9pm.1a7',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Biết gọi ba mẹ là “pa pa” “me me” hoặc một từ đặc biệt nào khác',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtq9pm1a7',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtqqji.ae',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Hiểu chữ “không”, và ngưng nhanh hoặc dừng lại khi nghe thấy',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtqqjiae',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Ngôn ngữ và Giao tiếp',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jtrfg0.91l',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Bỏ món đồ vào vật đựng, như bỏ một khối vuông vào cái tách',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtrfg091l',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtsjfg.8td',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Tìm món đồ bị giấu đi, như đồ chơi giấu dưới chăn chẳng hạn',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtsjfg8td',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Nhận thức (Học tập tư duy giải quyết vấn đề)',
            type: 'input',
        },
        {
            elements: [
                {
                    id: 'm2jtu7p0.5vo',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Kéo đứng dậy được',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtu7p05vo',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtumf6.f4j',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Đi men, vịn và đi men theo đồ đạc',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtumf6f4j',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtv5h6.pe',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Uống từ cốc không có nắp, khi được cha mẹ cầm cốc',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtv5h6pe',
                            fetch: {},
                        },
                    },
                },
                {
                    id: 'm2jtvq0t.7d',
                    type: 'input',
                    properties: {
                        input: {
                            label: 'Nhặt được đồ bằng ngón cái và ngón trỏ, chẳng hạn như miếng thức ăn nhỏ',
                            component: 'multiple_choice',
                            description: '',
                            placeholder: 'Choose an option',
                            choices: [
                                {
                                    label: 'Có',
                                    value: 'yes',
                                },
                                {
                                    label: 'Không chắc',
                                    value: 'not_sure',
                                },
                                {
                                    value: 'not_yet',
                                    label: 'Chưa',
                                },
                            ],
                            key: 'MultipleChoicem2jtvq0t7d',
                            fetch: {},
                        },
                    },
                },
            ],
            id: 'Vận động/Phát triển thể chất',
            type: 'input',
        },
    ],
    createdBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    updatedBy: {
        __type: 'Pointer',
        className: '_User',
        objectId: '71fBmVzaL8',
    },
    title: '12 tháng',
    submission: 'remote',
    computes: hidden_fields.map((hidden_field: IHiddenField) => {
        return {
            key: hidden_field.key,
            value: hidden_field.value,
            component: 'hidden_field',
        }
    }),
    space: {
        __type: 'Pointer',
        className: 'Space',
        objectId: 'ZhKJvCjuhu',
    },
})
