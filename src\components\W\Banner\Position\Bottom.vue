<script setup lang="ts">
import type { PropType } from '#imports'
import type { ComponentBanner } from '~/models'

const { banner } = defineProps({
  banner: {
    type: Object as PropType<ComponentBanner>,
    default: () => ({}),
  },
})

defineEmits(['click'])
</script>
<template>
  <div
    class="fixed bottom-0 left-0 z-50 w-full bg-blue-500 p-4 text-white"
    @click="$emit('click')"
  >
    <p>This is a bottom banner. {{ banner }}</p>
  </div>
</template>
