<script lang="ts" setup>
import type { Relationship } from '~/models'

const props = defineProps({
    members: {
        type: Array as PropType<Relationship[]>,
        default: () => [],
    },
    activeMember: {
        type: Object as PropType<Relationship>,
        default: () => ({}),
    },
    isShow: {
        type: Boolean,
        default: false,
    },
})

const emit = defineEmits(['select-member', 'add-member', 'update-member'])

const visible = defineModel<boolean>()

const { t } = useI18n()

const onSelectMember = (member: Relationship) => {
    emit('select-member', member)
}

watch(
    () => props.isShow,
    () => {
        setTimeout(() => {
            const contextMember = document.querySelector(
                `#context-member #member-${props.activeMember.related?._id}`,
            )
            if (contextMember) {
                contextMember.scrollIntoView({
                    behavior: 'smooth', // Smooth scrolling
                    block: 'nearest',
                })
            }
        }, 300)
    },
    { immediate: true, deep: true },
)
</script>

<template>
    <Dialog
        v-model:visible="visible"
        modal
        :header="t('header')"
        pt:root:class="w-11/12"
        pt:header:class="pb-0"
    >
        <p class="italic text-gray-500">
            {{ t('description') }}
        </p>
        <div id="context-member" class="max-h-72 overflow-y-auto">
            <div
                v-for="member in members"
                :id="`member-${member.related?._id}`"
                :key="member.related?._id"
                :class="[
                    'mt-3 flex items-center gap-4 rounded-xl border-2 border-transparent bg-gray-100 px-4 py-3 first:mt-2',
                    {
                        'border-2 !border-primary bg-primary-200/25':
                            member.related?._id === activeMember.related?._id,
                    },
                ]"
                @click="onSelectMember(member)"
            >
                <Avatar
                    :image="member.related?.avatar?.url"
                    shape="circle"
                    size="large"
                />
                <p class="flex-grow font-semibold">
                    {{ member.related?.name }}
                </p>
                <div
                    v-if="member.related?._id === activeMember.related?._id"
                    @click.stop="emit('update-member', member)"
                >
                    <iconify-icon
                        icon="material-symbols:check-rounded"
                        class="text-2xl text-primary"
                    />
                </div>
            </div>
        </div>

        <div
            class="mt-3 flex items-center gap-4 rounded-xl bg-gray-50 px-4 py-3"
            @click="emit('add-member')"
        >
            <iconify-icon
                icon="tabler:plus"
                class="rounded-full bg-gray-200 p-1 text-4xl"
            />
            <p>{{ t('add-member') }}</p>
        </div>
    </Dialog>
</template>

<i18n lang="json">
{
    "en": {
        "header": "Choose a Family Member",
        "description": "For which family member do you want personalized health advice?",
        "me": "Me",
        "add-member": "Add a member"
    },
    "vi": {
        "header": "Choose a Family Member",
        "description": "For which family member do you want personalized health advice?",
        "me": "Me",
        "add-member": "Add a member"
    }
}
</i18n>
