<template>
    <div>
        <div class="-mx-1 flex flex-wrap">
            <div
                v-for="(item, i) in renderComponents"
                :key="i"
                :class="'w-1/2 sm:w-1/3 md:w-1/3 lg:w-1/3 xl:w-1/3'"
                class="p-1"
            >
                <Skeleton
                    v-if="loading"
                    class="mx-auto"
                    height="190px"
                    border-radius="12px"
                />

                <Card
                    v-else
                    class="flex min-h-[190px] flex-col justify-between rounded-lg border border-gray-200 p-3"
                    :style="{ boxShadow: 'none' }"
                >
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <i
                                class="pi"
                                :class="item.icon"
                                style="font-size: 20px; color: #42a5f5"
                            />
                            <span class="ml-2 font-semibold">{{
                                item.name
                            }}</span>
                        </div>
                        <i class="pi pi-chevron-right text-surface-500" />
                    </div>

                    <div class="box-chart mt-4">
                        <div v-dompurify-html="item.data.html" />
                        <div v-if="!loading" class="h-15">
                            <!-- <component :is="item.componentOverview" :key="key" :user="patient" v-bind="{ ...item.attr }"
                                @data="($data) => handleData($data, item.key)" /> -->
                        </div>
                    </div>

                    <div
                        class="box-action mt-4 flex items-center justify-between"
                    >
                        <span class="flex items-center text-sm">
                            <i class="pi pi-clock mr-1" />{{
                                item.data.createdAt
                            }}
                        </span>
                        <Button
                            v-show="!isReadOnly"
                            icon="pi pi-plus"
                            class="p-button-rounded p-button-primary h-9 w-9"
                            @click.stop="openForm(item)"
                        />
                    </div>
                </Card>
            </div>
        </div>

        <!-- <BottomSheet ref="bottomSheet" :visible="isMobile" style="width: 480px;">
            <DynamicForm :key="formKey" :formData="typeDynamicForm" @submit="handleSubmitForm" />
        </BottomSheet> -->
    </div>
</template>
<script setup lang="ts">
// Mock Data
const renderComponents = ref([
    {
        icon: 'pi pi-chart-bar',
        name: 'Health Index',
        data: {
            html: '<p>Health data overview</p>',
            createdAt: '2024-09-17',
        },
        componentOverview: 'ComponentOverviewA',
        attr: { param: 'value' },
        key: 'health_index',
    },
    {
        icon: 'pi pi-heart',
        name: 'Heart Rate',
        data: {
            html: '<p>Heart rate data overview</p>',
            createdAt: '2024-09-16',
        },
        componentOverview: 'ComponentOverviewB',
        attr: { param: 'anotherValue' },
        key: 'heart_rate',
    },
])

// Mock Functions
const loading = ref(false)
const isReadOnly = ref(false)
// const isMobile = ref(false);
// const isBreakpoint = ref(false); // You can set this based on screen size

// const formKey = ref('uniqueFormKey');
// const typeDynamicForm = ref({}); // Mock form data

// const patient = ref({ name: 'John Doe', age: 30 }); // Example user data

// function handleOpenDetail(item) {
//     console.log('Open detail for:', item);
//     // Handle logic to open item detail
// }

// function handleData(data, key) {
//     console.log(`Received data for ${key}:`, $data);
//     // Handle data emitted from the dynamic component
// }

function openForm(item: any) {
    console.log('Opening form for:', item)
    // Logic to open a form for the clicked item
}
</script>
