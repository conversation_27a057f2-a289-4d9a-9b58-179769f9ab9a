<template>
    <div>
        <div>
            <Button @click="open">open</Button>
            <Button @click="close">close</Button>
        </div>
        <WPhoneInputDialog ref="phone-input" />
    </div>
</template>

<script setup lang="ts">
const phoneInputRef = useTemplateRef('phone-input')

function open() {
    phoneInputRef.value?.open()
}
function close() {
    phoneInputRef.value?.close()
}
</script>
