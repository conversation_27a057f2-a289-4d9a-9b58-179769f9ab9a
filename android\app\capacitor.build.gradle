// DO NOT EDIT THIS FILE! IT IS GENERATED EACH TIME "capacitor update" IS RUN

android {
  compileOptions {
      sourceCompatibility JavaVersion.VERSION_17
      targetCompatibility JavaVersion.VERSION_17
  }
}

apply from: "../capacitor-cordova-android-plugins/cordova.variables.gradle"
dependencies {
    implementation project(':capacitor-app')
    implementation project(':capacitor-browser')
    implementation project(':capacitor-clipboard')
    implementation project(':capacitor-device')
    implementation project(':capacitor-preferences')
    implementation project(':capacitor-splash-screen')
    implementation project(':capawesome-capacitor-app-update')
    implementation project(':capgo-inappbrowser')
    implementation project(':revenuecat-purchases-capacitor')
    implementation project(':wellcare-capacitor-app-auth')
    implementation project(':wellcare-capacitor-callkeep')
    implementation project(':wellcare-capacitor-editor')
    implementation project(':wellcare-capacitor-permissions')
    implementation project(':wellcare-capacitor-plugin-agora')
    implementation project(':wellcare-capacitor-stringee')
    implementation project(':capacitor-native-settings')
    implementation project(':capacitor-plugin-safe-area')
    implementation project(':capacitor-screen-orientation')
    implementation "com.onesignal:OneSignal:5.1.21"
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:1.7.10"
}
apply from: "../../node_modules/.pnpm/cordova-clarity@2.3.0/node_modules/cordova-clarity/src/android/build.gradle"
apply from: "../../node_modules/.pnpm/onesignal-cordova-plugin@5.2.5/node_modules/onesignal-cordova-plugin/build-extras-onesignal.gradle"

if (hasProperty('postBuildExtras')) {
  postBuildExtras()
}
