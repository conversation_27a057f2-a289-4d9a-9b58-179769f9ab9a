<script lang="ts" setup>
definePageMeta({
    layout: 'patient',
    middleware: 'auth-capacitor',
})

const { user } = useUserInfo({
    scope: ['_id'],
})
</script>

<template>
    <SharedDynamicHeader>
        <template #header>
            <h4
                class="line-clamp-1 overflow-hidden overflow-ellipsis whitespace-normal break-words break-all"
            >
                HealthGPT
            </h4>
        </template>
        <template #detailed>
            <div>
                <h1 class="mx-6 mt-4">HealthGPT</h1>
                <w-chat-explore
                    v-if="user._id"
                    class="mx-6"
                    :user-id="user._id"
                    view="agent-profile"
                    base-path="/patient/health-gpt/bot"
                    group
                    :passthrough="{
                        card: {
                            style: 'height: 152px'
                        },
                        cover: {
                            class: 'object-cover',
                            style: 'width: 132px; height: 152px',
                        },
                        description: {
                            class: 'flex-1 text-sm line-clamp-6',
                        },
                    }"
                />
            </div>
        </template>
    </SharedDynamicHeader>
</template>
