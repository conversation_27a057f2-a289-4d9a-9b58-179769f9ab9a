<script setup lang="ts">
import { yupResolver } from '@primevue/forms/resolvers/yup'
import * as yup from 'yup'

const props = withDefaults(
    defineProps<{
        title?: string
        description?: string
        phoneNumber?: string
        useStore?: boolean
        avatar?: string
    }>(),
    {
        useStore: true,
        title: '',
        description: '',
        phoneNumber: '',
        avatar: '',
    },
)

const emit = defineEmits<{
    'otp-submit': [otp: string]
}>()

const { t } = useI18n()
const onboardingStore = useOnboardingStore()

// Support phone number with proper spacing for readability
const PHONE_NUMBER = '(+84)2836226822'

const otp = ref('')

const initialValues = ref({
    passcode: '',
})

const resolver = ref(
    yupResolver(
        yup.object({
            passcode: yup
                .string()
                .required(t('passcode-required'))
                .test('len', t('passcode-length'), (val) => val?.length === 4)
                .test('numeric', t('passcode-number-only'), (val) =>
                    val ? /^\d+$/.test(val) : true,
                ),
        }),
    ),
)

const avatarUrl = computed(() =>
    props.useStore
        ? onboardingStore.profile?.avatar?.url || ''
        : props.avatar || '',
)

// Format phone number with proper spacing
const formatPhoneNumber = (phone: string) => {
    return phone
        ? phone.replace(/(\d{2})(\d{2})(\d{3})(\d{4})/, '+$1$2$3$4')
        : ''
}

const label = computed(() => {
    if (!props.useStore) {
        return props.phoneNumber ? formatPhoneNumber(props.phoneNumber) : ''
    }
    return onboardingStore.profile?.phone
        ? formatPhoneNumber(onboardingStore.profile.phone)
        : (onboardingStore.profile?.name ?? '')
})

// Handle input change to restrict to numbers only and limit to 4 digits
const handleInput = (e: Event) => {
    const input = e.target as HTMLInputElement
    const value = input.value

    // Allow only digits
    const numericValue = value.replace(/\D/g, '')

    // Limit to 4 digits
    if (numericValue.length > 4) {
        otp.value = numericValue.slice(0, 4)
    } else {
        otp.value = numericValue
    }
}

const submit = () => {
    const isValid =
        otp.value && otp.value.length === 4 && /^\d+$/.test(otp.value)
    if (isValid) {
        if (props.useStore) {
            onboardingStore.setOtp(otp.value)
        }
        emit('otp-submit', otp.value)
    } else {
        push.error({
            title: t('error'),
            message: t('passcode-required'),
        })
    }
    return isValid
}

// Handle OTP input changes
watch(otp, (val) => {
    if (val?.length === 4) {
        if (props.useStore) {
            onboardingStore.setOtp(val.toString())
        }
        emit('otp-submit', val.toString())
    }
})

defineExpose({ submit })
</script>

<template>
    <Form
        v-slot="$form"
        :resolver="resolver"
        :initial-values="initialValues"
        class="justify -center flex flex-col gap-2 text-center"
        @submit="submit"
    >
        <h3 v-if="title">
            {{ title }}
        </h3>
        <h3 v-else>
            {{ t('verify-otp-title') }}
        </h3>

        <div>
            <Chip v-if="label" :label="label" :image="avatarUrl" />
        </div>

        <p v-if="description" class="text-gray-700">
            {{ description }}
        </p>
        <p v-else class="text-gray-700">
            {{ t('description-prefix') }}
            <a
                :href="`tel:${PHONE_NUMBER}`"
                class="text-primary hover:underline"
            >
                {{ PHONE_NUMBER }}
            </a>
            {{ t('description-suffix') }}
        </p>

        <InputText
            v-model="otp"
            type="text"
            inputmode="numeric"
            pattern="[0-9]*"
            maxlength="4"
            placeholder="0000"
            class="w-full text-center text-2xl"
            @input="handleInput"
        />

        <Message
            v-if="$form.passcode?.invalid"
            severity="error"
            size="small"
            variant="simple"
        >
            {{ $form.passcode.error?.message }}
        </Message>
    </Form>
</template>

<i18n lang="json">
{
    "en": {
        "verify-otp-title": "Verify your phone number by OTP",
        "call-otp": "Call to get OTP",
        "description-prefix": "Use your phone number to call",
        "description-suffix": " and press 2 to receive your OTP",
        "passcode-required": "Please enter your OTP code",
        "passcode-length": "Passcode must be 4 digits",
        "passcode-number-only": "Passcode must contain only numbers",
        "submit": "Submit",
        "back": "Back",
        "error": "Error"
    },
    "vi": {
        "verify-otp-title": "Xác thực bằng mã OTP",
        "call-otp": "Gọi để lấy OTP",
        "description-prefix": "Dùng đúng số điện thoại đã đăng ký gọi vào",
        "description-suffix": " và bấm phím số 2 để nghe mã OTP",
        "passcode-required": "Vui lòng nhập mã OTP",
        "passcode-length": "Mã xác thực phải có 4 chữ số",
        "passcode-number-only": "Mã xác thực chỉ được chứa số",
        "submit": "Xác nhận",
        "back": "Quay lại",
        "error": "Thông báo"
    }
}
</i18n>
