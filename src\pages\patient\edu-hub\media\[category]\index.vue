<script lang="ts" setup>
import { ref, usePagePatientEduHubMediaCategorySlug } from '#imports'
import { useJsonConfigApp } from '~/composables/fetch/config/get-config'
import type { ITopicCard } from '~/models'

definePageMeta({
    layout: 'nested-page',
    middleware: 'auth-capacitor',
})

const { t } = useI18n()
const router = useRouter()
const { fullPath }: any = useRoute()

const layoutStore = useLayoutStore()

const topicId = useLocalStorage('edu-hub-media-topic-id', '')

const from = ref<number>(0)
const size = ref<number>(20)
const visibleMembershipPopup = ref<boolean>(false)

const { config } = useJsonConfigApp()
const { isMembership: checkMembership } = useMembership()

const isMembership = computed(() => {
    if (config.value?.pages?.eduhub?.checkMembership === false) return true
    else return checkMembership.value
})

const {
    category,
    loading,
    topics,
    totalTopics,
    totalVideo,
    totalAudio,
    countAudio,
    countVideo,
} = usePagePatientEduHubMediaCategorySlug(from, size)

const mediaItems = [
    { count: totalVideo, label: 'videos' },
    { count: totalAudio, label: 'audios' },
]

const getCategoryBlock = (category: any) => category?.page?.blocks

const onLoadMore = () => {
    const total = totalTopics.value
    const currentSize = size.value

    if (total && isMembership.value && currentSize < total) {
        size.value = Math.min(currentSize + 15, total)
    }
}

const navigateToTopic = (topic: ITopicCard) => {
    if (!isMembership.value) {
        visibleMembershipPopup.value = true
    } else {
        topicId.value = topic?._id
        router.push({ path: `${fullPath}/${topic?.slug}` })
    }
}

watch(
    category,
    () => {
        const name = category.value?.page?.properties.Name
        if (name) {
            layoutStore.setTitle(name)
        }
    },
    {
        deep: true,
    },
)

onUnmounted(() => {
    layoutStore.$reset()
})
</script>

<template>
    <div>
        <div v-if="loading" class="px-4">
            <WSkeleton block="article" />
        </div>
        <SharedSmoothScroll v-else @arrived-bottom="onLoadMore">
            <template #main>
                <div class="relative">
                    <WArticleBody
                        v-if="category?.page?.blocks"
                        :blocks="getCategoryBlock(category)"
                        class="overview mt-3"
                    />
                    <div
                        v-for="(item, index) in mediaItems"
                        :key="index"
                        :class="[index === 0 ? 'top-3' : 'top-28']"
                        class="absolute left-4 flex h-[84px] w-[84px] flex-col items-center justify-center rounded-full border-white/30 bg-white/20 text-center text-sm font-medium shadow-[0_4px_30px_rgba(0,0,0,0.1)] backdrop-blur-[5px]"
                    >
                        <span>{{ item?.count }}</span>
                        <span>{{ item?.label }}</span>
                    </div>
                </div>

                <div
                    v-for="topic in topics"
                    :key="topic?._id"
                    @click="navigateToTopic(topic)"
                >
                    <div class="my-4 cursor-pointer px-4">
                        <div
                            class="flex w-full items-stretch justify-between rounded-lg bg-surface-100 bg-opacity-45 p-1"
                        >
                            <div class="flex flex-1 flex-col p-3">
                                <div class="flex items-start justify-between">
                                    <p class="mb-2 font-bold">
                                        {{ topic.name }}
                                    </p>
                                    <button>
                                        <i
                                            class="pi pi-chevron-right text-xs"
                                        />
                                    </button>
                                </div>
                                <div
                                    class="flex flex-1 flex-col justify-between"
                                >
                                    <p
                                        class="mt-n1 mb-0 line-clamp-5 overflow-hidden pr-2 text-sm"
                                    >
                                        {{ topic.description }}
                                    </p>
                                    <div
                                        class="mt-2 flex items-center gap-2 pb-1 text-sm text-surface-600"
                                    >
                                        <div
                                            v-if="countAudio(String(topic._id))"
                                            class="mr-3 flex items-center text-primary"
                                        >
                                            <iconify-icon
                                                class="mr-1 text-xl"
                                                icon="bitcoin-icons:podcast-filled"
                                            />
                                            <span>
                                                {{
                                                    countAudio(
                                                        String(topic._id),
                                                    )
                                                }}
                                                audio
                                            </span>
                                        </div>
                                        <div
                                            v-if="countVideo(String(topic._id))"
                                            class="mr-3 flex items-center text-primary"
                                        >
                                            <iconify-icon
                                                class="mr-1 text-xl"
                                                icon="mingcute:youtube-fill"
                                            />
                                            <span>
                                                {{
                                                    countVideo(
                                                        String(topic._id),
                                                    )
                                                }}
                                                video
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div v-if="!isMembership" class="mb-4 flex justify-center">
                    <Button
                        v-if="totalTopics && topics?.length < totalTopics"
                        :label="t('btn:show-more')"
                        @click="visibleMembershipPopup = true"
                    />
                </div>
            </template>
        </SharedSmoothScroll>

        <SharedMembershipPopup
            v-model:visible="visibleMembershipPopup"
            @skip="visibleMembershipPopup = false"
            @update:visible="(val: boolean) => (visibleMembershipPopup = val)"
        />
    </div>
</template>

<style scoped>
.overview :deep(p) {
    padding: 0 16px;
}
</style>

<i18n lang="yaml">
en:
    'btn:show-more': 'See more'
vi:
    'btn:show-more': 'Xem thêm'
</i18n>
