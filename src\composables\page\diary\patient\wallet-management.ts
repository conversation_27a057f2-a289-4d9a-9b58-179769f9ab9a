import { useWallet } from "../wallet"

export const useWalletManagement = () => {
    const { getAllUserWallet } = useWallet()
    const DEFAULT_BALANCE_MINUTES = 90

    const getPersonalDoctorWallet = async (userId: string) => {
        try {
            const wallets = await getAllUserWallet(userId)
            const membershipWallet = wallets.find(
                (wallet: any) => wallet.type === 'membership',
            )
            return membershipWallet?.child.find(
                (wallet: any) => wallet.type === 'benefit-personal-doctor',
            )
        } catch (error) {
            console.error('Error fetching wallet:', error)
            return null
        }
    }

    const formatTimeString = (minutes: number) => {
        const totalSeconds = minutes * 60
        const formattedMinutes = Math.floor(totalSeconds / 60)
        const seconds = Math.floor(totalSeconds % 60)

        return {
            minutes: formattedMinutes,
            seconds,
            formatted: `${formattedMinutes.toString().padStart(2, '0')}:${seconds
                .toString()
                .padStart(2, '0')}`,
        }
    }

    const formatRemainingTime = (
        usableMinutes: number,
        balanceMinutes: number = DEFAULT_BALANCE_MINUTES,
    ) => {
        const usableTime = formatTimeString(usableMinutes)
        const balanceTime = formatTimeString(balanceMinutes)
        const usedMinutes = balanceMinutes - usableMinutes
        const usedTime = formatTimeString(usedMinutes)

        return {
            remainingTime: `${usableMinutes}/${balanceMinutes}`,
            balance: balanceMinutes,
            balanceFormatted: balanceTime.formatted,
            usable: usableMinutes,
            usableFormatted: usableTime.formatted,
            used: usedMinutes,
            usedFormatted: usedTime.formatted,
            percentageUsed: (usableMinutes / balanceMinutes) * 100,
        }
    }

    const refreshWallet = async (userId: string) => {
        const wallet = await getPersonalDoctorWallet(userId)
        if (!wallet) return formatRemainingTime(0) // Return default format with 0 usable minutes
        return formatRemainingTime(
            wallet.usable ?? 0,
            wallet.balance ?? DEFAULT_BALANCE_MINUTES,
        )
    }

    return {
        getPersonalDoctorWallet,
        formatRemainingTime,
        refreshWallet,
        DEFAULT_BALANCE_MINUTES,
    }
}
