<script setup lang="ts">
import Drawer from 'primevue/drawer'
import Stepper from 'primevue/stepper'
import <PERSON><PERSON><PERSON> from 'primevue/steplist'
import Step from 'primevue/step'
import Step<PERSON>anels from 'primevue/steppanels'
import StepPanel from 'primevue/steppanel'
import Rating from 'primevue/rating'
import { Capacitor } from '@capacitor/core'

const props = defineProps({
    providerId: {
        type: String,
        required: true,
    },
    providerAvatar: {
        type: String,
        required: true,
    },
    consultationId: {
        type: String,
        required: true,
    },
})

const visible = defineModel<boolean>()

const { t } = useI18n()

const { user } = useUserInfo({ scope: ['_id'] })

const { submitRating } = useConsultationRating()

const steps = [
    {
        title: t('step:1:title'),
    },
    {
        title: t('step:2:title'),
    },
]

const emit = defineEmits(['skip-rating', 'submitted'])

const activeStep = ref<number>(1)
const commentModel = ref<string>('')
const careteamRating = ref<number>(5)
const providerRating = ref<number>(5)
const isSubmitting = ref<boolean>(false)
const isShowSkipMessage = ref<boolean>(false)

const skipRating = () => {
    emit('skip-rating')
}

const handleSubmitRating = async () => {
    isSubmitting.value = true

    await submitRating(props.consultationId, {
        type: 'system',
        consultation: props.consultationId,
        rating: careteamRating.value,
        ratingBy: user.value?._id,
    })

    await submitRating(props.consultationId, {
        type: 'consultation',
        consultation: props.consultationId,
        rating: providerRating.value,
        comment: commentModel.value,
        provider: props.providerId,
        ratingBy: user.value?._id,
    })

    isSubmitting.value = false
    emit('submitted')
}

function onRatingApp() {
    const isIos = Capacitor.getPlatform() === 'ios'
    window.open(
        isIos
            ? 'https://apps.apple.com/us/app/wellcare/id1039423586'
            : 'https://play.google.com/store/apps/details?id=vn.wellcare',
    )
}
</script>

<template>
    <Drawer
        v-model:visible="visible"
        position="full"
        pt:header:class="flex flex-row-reverse"
    >
        <Stepper
            :value="activeStep"
            linear
            pt:root:class="max-w-md mx-auto space-y-6"
        >
            <StepList>
                <Step
                    v-for="(step, index) in steps"
                    :key="index"
                    :value="index + 1"
                >
                    {{ step.title }}
                </Step>
            </StepList>
            <StepPanels>
                <StepPanel :value="1">
                    <!-- Careteam -->
                    <div class="relative rounded-lg border border-primary p-6">
                        <div
                            class="absolute left-1/2 top-0 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-900"
                        >
                            <img
                                src="/logo.svg"
                                alt="Careteam Logo"
                                loading="lazy"
                                class="size-20 rounded-full"
                            />
                        </div>
                        <div class="pb-3 pt-6 text-center text-xl">
                            <span>
                                {{ t('rating for the system, careteam') }}
                            </span>
                        </div>
                        <Rating
                            v-model="careteamRating"
                            pt:onicon:class="!text-yellow-500 size-7"
                            pt:officon:class="size-7"
                            pt:root:class="justify-center gap-5"
                        />
                    </div>

                    <!-- Provider -->
                    <div
                        class="relative mt-16 rounded-lg border border-primary p-6"
                    >
                        <div
                            class="absolute left-1/2 top-0 -translate-x-1/2 -translate-y-1/2 bg-white dark:bg-slate-900"
                        >
                            <NuxtImg
                                :src="providerAvatar"
                                alt="Provider Avatar"
                                loading="lazy"
                                width="80"
                                height="80"
                                class="rounded-full"
                            />
                        </div>
                        <div class="pb-3 pt-6 text-center text-xl">
                            <span>
                                {{ t('reviews for doctors and professionals') }}
                            </span>
                        </div>
                        <Rating
                            v-model="providerRating"
                            pt:onicon:class="!text-yellow-500 size-7"
                            pt:officon:class="size-7"
                            pt:root:class="justify-center gap-5"
                        />
                        <div class="mt-4 text-center text-red-400">
                            <span>
                                {{
                                    t(
                                        'star ratings are the criteria for assessing the reputation and ranking of doctors and experts on the system',
                                    )
                                }}
                            </span>
                        </div>
                    </div>

                    <Message
                        v-if="isShowSkipMessage"
                        class="mt-4"
                        severity="warn"
                    >
                        {{
                            t(
                                'doctors and Wellcare continuously strive to provide accurate and personalized advice to each patient. We carefully review all of your feedback every Friday, to encourage and provide mutual suggestions. Please take a few minutes to rate and comment. Thank you!',
                            )
                        }}
                    </Message>

                    <!-- Skip -->
                    <div class="mt-6">
                        <span
                            v-if="!isShowSkipMessage"
                            class="underline"
                            @click="isShowSkipMessage = true"
                        >
                            {{ t('skip review') }}
                        </span>
                        <Button
                            v-else
                            outlined
                            rounded
                            :label="t('btn:skip')"
                            severity="secondary"
                            class="w-full"
                            @click="skipRating"
                        />
                    </div>

                    <!-- Submit -->
                    <div class="mt-2">
                        <Button
                            :label="t('btn:send')"
                            class="w-full"
                            rounded
                            @click="activeStep = 2"
                        />
                    </div>
                </StepPanel>

                <StepPanel :value="2">
                    <Message
                        pt:root:class="outline-primary/25"
                        pt:content:class="bg-primary-50/25"
                        pt:text:class="text-primary"
                    >
                        <span>
                            {{
                                t('review result', {
                                    providerRating,
                                    careteamRating,
                                })
                            }}
                        </span>

                        <span class="pl-1 underline" @click="activeStep = 1">
                            {{ t('edit') }}
                        </span>
                    </Message>
                    <Message
                        v-if="careteamRating === 5 && providerRating === 5"
                        pt:root:class="mt-4 outline-primary/25"
                        pt:content:class="bg-primary-50/25"
                        pt:text:class="text-primary"
                    >
                        <div>{{ t('please share your experience') }}</div>
                        <div class="flex justify-end">
                            <Button
                                text
                                class="text-primary/80"
                                @click="onRatingApp"
                            >
                                {{ t('btn-share') }}
                            </Button>
                        </div>
                    </Message>

                    <div class="mt-10">
                        <p>{{ t('comment (if any)') }}</p>
                        <textarea
                            v-model="commentModel"
                            placeholder="- Dành cho bác sĩ
- Dành cho careteam"
                            class="mt-2 min-h-80 w-full rounded-lg border border-gray-500 p-4 transition-all"
                        />
                    </div>

                    <div class="mt-20">
                        <Button
                            :label="t('btn:send')"
                            class="w-full"
                            rounded
                            :disabled="!commentModel || isSubmitting"
                            :loading="isSubmitting"
                            @click="handleSubmitRating"
                        />
                    </div>
                </StepPanel>
            </StepPanels>
        </Stepper>
    </Drawer>
</template>

<style scoped></style>

<i18n lang="yaml">
en:
    'step:1:title': 'Review'
    'step:2:title': 'Comment'
    'rating for the system, careteam': 'Rating for the system, careteam'
    'reviews for doctors and professionals': 'Reviews for doctors and professionals'
    'star ratings are the criteria for assessing the reputation and ranking of doctors and experts on the system': 'Star ratings are the criteria for assessing the reputation and ranking of doctors and experts on the system'
    'doctors and Wellcare continuously strive to provide accurate and personalized advice to each patient. We carefully review all of your feedback every Friday, to encourage and provide mutual suggestions. Please take a few minutes to rate and comment. Thank you!': 'Doctors and Wellcare continuously strive to provide accurate and personalized advice to each patient. We carefully review all of your feedback every Friday, to encourage and provide mutual suggestions. Please take a few minutes to rate and comment. Thank you!'
    'skip review': 'Skip review?'
    'btn:skip': 'Skip'
    'btn:send': 'Send'
    'review result': 'You have rated the doctor with {providerRating} stars and the careteam with {careteamRating} stars.'
    'edit': 'Edit'
    'comment (if any)': 'Comment (if any)'
    'please share your experience': 'Please share your experience about our doctors and services on the app store, so that people can be more confident when choosing Wellcare.'
    'btn-share': 'Share'
vi:
    'step:1:title': 'Đánh giá'
    'step:2:title': 'Nhận xét'
    'rating for the system, careteam': 'Đánh giá cho hệ thống, careteam'
    'reviews for doctors and professionals': 'Đánh giá cho bác sĩ và chuyên gia'
    'star ratings are the criteria for assessing the reputation and ranking of doctors and experts on the system': 'Số sao là tiêu chí đánh giá uy tín và xếp hạng cho bác sĩ, chuyên gia trên hệ thống'
    'doctors and Wellcare continuously strive to provide accurate and personalized advice to each patient. We carefully review all of your feedback every Friday, to encourage and provide mutual suggestions. Please take a few minutes to rate and comment. Thank you!': 'Bác sĩ và Wellcare không ngừng nổ lực để đưa ra những lời khuyên đúng đắn và phù hợp nhất với từng bệnh nhân. Chúng tôi đọc kỹ toàn bộ phản hồi của các bạn vào thứ 6 hàng tuần, để động viên và góp ý lẫn nhau, mong bạn dành ít phút chấm điểm và nhận xét. Xin cảm ơn!'
    'skip review': 'Bỏ qua đánh giá?'
    'btn:skip': 'Bỏ qua'
    'btn:send': 'Gửi'
    'review result': 'Bạn đã đánh giá {providerRating} sao cho bác sĩ và {careteamRating} sao cho nhóm chăm sóc.'
    'edit': 'Chỉnh sửa'
    'comment (if any)': 'Nhận xét (nếu có)'
    'please share your experience': 'Hãy chia sẻ trải nghiệm của bạn về bác sĩ và dịch vụ lên app store, để mọi người tự tin hơn khi chọn khám từ xa.'
    'btn-share': 'Chia sẻ'
</i18n>
