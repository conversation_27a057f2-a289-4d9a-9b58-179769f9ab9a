import type { Relationship } from '~/models'

export function useMembers() {
    const { $fetchWellcare } = useNuxtApp()

    // Fetch user info
    const { user, refresh: refreshUser } = useUserInfo({
        scope: ['_id', 'avatar', 'name', 'dob', 'gender'],
    })

    // Compute userId
    const userId = computed<string>(() => user.value?._id ?? '')

    // Create main user relationship
    const mainUser = computed<Partial<Relationship>>(() => ({
        isPersonalDoctor: false,
        isPrimaryDoctor: false,
        relationship: 'self',
        related: user.value,
    }))

    // Fetch relationships
    const { data: relationships, refresh: refreshRelationship } = useAsyncData<{
        results: Relationship[]
    }>('family-relationships', () =>
        $fetchWellcare(`/user-management/relationship/${userId.value}`, {
            method: 'GET',
            query: {
                filter: {
                    relationship: {
                        $ne: 'doctor',
                    },
                    related: {
                        $exists: true,
                    },
                },
                limit: 100,
                fields: 'relationship,user,related,createdAt',
            },
        }),
    )

    // Combine main user and relationships
    const results = computed(() =>
        relationships.value?.results?.sort(
            (a, b) =>
                a.related?.name?.localeCompare(b.related?.name || '') || 0,
        ),
    )
    const members = computed<Relationship[]>(() => [
        mainUser.value,
        ...(results.value ?? []),
    ])

    const refreshMembers = async () => {
        await refreshUser()
        await refreshRelationship()
    }

    return { members, refreshMembers }
}
