import { $fetch } from '@nuxt/test-utils/e2e'
import { describe, expect, it } from 'vitest'
import { useHtml } from '~/tests/utils/html'
import { expectNoClientErrors } from '../../utils'

// key pages
const urls = [
  '/dich-vu/kham-tu-xa',
  // '/doanh-nghiep/phuc-loi-kham-tu-xa-cho-nhan-vien',
  // '/doanh-nghiep/healthtalk/giac-ngu-ngon-la-giac-mo-co-the-dat-duoc-bs-nguyen-canh-nam',
  // '/gioi-thieu/cau-chuyen-wellcare',
  // '/ho-tro/faq/vi-sao-phai-follow-up-voi-bac-si',
  // '/song-khoe/tram-cam-va-benh-ly-tim-mach',
  // '/tin-tuc/hop-tac-wellcare-manulife-kham-tu-xa-mien-phi-tren-app-move',
  // '/hop-tac-wellcare-manulife-kham-tu-xa-mien-phi-tren-app-move',
]
// backlinked pages

export const article = async () => {
  urls.forEach((url) => {
    describe(url, async () => {
      describe('layout', async () => {
        it('should have viewport configured for responsive design', async () => {
          const html = await $fetch(url)
          expect(html.match('meta name="viewport"').length).toEqual(1)
          await expectNoClientErrors(url)
        })
      })

      // describe('structured data', async () => {
      //   it('should contain Article structured data', async () => {
      //     const html = await $fetch(url)
      //     expect(html).toContain(`"@type":"NewsArticle"`)
      //     expect(html).toContain(`datePublished`)
      //     expect(html).toContain(`dateModified`)
      //     expect(html).toContain(`headline`)

      //     expect(html).toContain(`"@type":"BreadcrumbList"`)
      //     expect(html).toContain(`"@type":"ListItem"`)
      //   })
      // })

      // describe('image', async () => {
      //   it('should have only CDN enabled sources', async () => {
      //     const html = await $fetch(url)
      //     const { imgSrcs } = useHtml(html)

      //     imgSrcs.forEach((url: string) => {
      //       expect(url).toMatch(/^\/images|^https:\/\/storage\.googleapis\.com/)
      //     })
      //   })
      // })

      describe('title', () => {
        it('should be less than 60 characters', async () => {
          const html = await $fetch(url)
          const { title } = useHtml(html as string)
          expect(title.split('|')[0].length).toBeLessThanOrEqual(60)
        })
        // it('should include h1', async () => {
        //   const html = await $fetch(url)
        //   const { title, h1s } = useHtml(html as string)
        //   expect(title).toBe(h1s[0])
        //   expect(title.startsWith(h1s[0])).toBe(true)
        // })
      })

      // describe('meta description', async () => {
      //   it('should be 50-160 characters', async () => {
      //     const html = await $fetch(url)
      //     const { metaDescription } = useHtml(html)
      //     expect(metaDescription.length).toBeLessThanOrEqual(160)
      //     expect(metaDescription.length).toBeGreaterThanOrEqual(50)
      //   })
      // })
      // describe('headings', async () => {
      //   // it('should have only 1 <h1> tag', async () => {
      //   //   const html = await $fetch(url)
      //   //   const { h1s } = useHtml(html as string)
      //   //   expect(h1s).toHaveLength(1)
      //   // })
      //   it('should have h2-headings less than 60 characters', async () => {
      //     const html = await $fetch(url)
      //     const { h2s } = useHtml(html as string)
      //     h2s.forEach((tag: string) => {
      //       expect(tag?.length)?.toBeLessThanOrEqual(60)
      //     })
      //   })
      // })
      // describe('links', async () => {
      //   it('should have nofollow attributes for all external links', async () => {
      //     const html = await $fetch(url)
      //     const { links } = useHtml(html as string)
      //     links
      //       .filter((i) => !i.includes('khamtuxa.wellcare.vn'))
      //       .forEach((link: string) => {
      //         expect(link).toContain('nofollow')
      //       })
      //   })
      // })
    })
  })
}
export default article
