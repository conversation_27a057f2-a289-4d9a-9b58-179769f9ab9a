<script setup lang="ts">
interface Props {
    bodyIndexMapped: any[]
}

const props = defineProps<Props>()
const dayjs = useDayjs()
const { locale } = useI18n()

const lastUpdate = computed(() => {
    if (props.bodyIndexMapped) {
        const now = dayjs()
        const nearestTime = props.bodyIndexMapped.reduce((nearest, current) => {
            const currentDiff = Math.abs(dayjs(current.observedAt).diff(now));
            const nearestDiff = Math.abs(dayjs(nearest.observedAt).diff(now));

            return currentDiff < nearestDiff ? current : nearest;
        }, [])
        return dayjs(nearestTime).locale(locale.value).fromNow()
    }
    return null
})
</script>

<template>
    <div class="card">
        <div class="flex flex-row justify-between">
            <template v-for="(stat, index) in bodyIndexMapped" :key="stat.label">
                <div>
                    <p class="text-slate-500 dark:text-slate-400">
                        {{ stat.label }}
                    </p>
                    <p class="text-3xl font-bold">{{ stat.value }}</p>
                </div>
                <Divider
v-if="index < bodyIndexMapped.length - 1" layout="vertical"
                    class="!mx-0 my-3 !border-[1.5px]" />
            </template>
        </div>
        <small v-if="lastUpdate">{{ lastUpdate }}</small>
    </div>
</template>
