import { ref } from '#imports'
import { createGlobalState } from '@vueuse/core'

export interface IPhoneOptions {
    userId: string
    phone: string
}

export const useUpdatePhone = createGlobalState(() => {
    const options = ref<IPhoneOptions>({
        userId: '',
        phone: '',
    })

    const open = (data: IPhoneOptions) => {
        options.value = data
    }

    return { options, open }
})
