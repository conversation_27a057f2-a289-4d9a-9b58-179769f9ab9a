<script setup lang="ts">
import type { IResponsiveOption } from '~/composables'
import { useComponentResponsiveCarousel } from '~/composables'

import type { ElasticIndex, HitNotionRatings, IFilter, IRating } from '~/models'

const { t } = useI18n()
const dayjs = useDayjs()

type TRatingsIndex = ElasticIndex.NOTION_RATINGS | ElasticIndex.RATINGS_HOMEPAGE

const props = defineProps({
    elasticIndex: {
        type: String as PropType<TRatingsIndex>,
        required: true,
    },
    title: {
        type: String,
        default: '',
    },
    description: {
        type: String,
        default: '',
    },
    pt: {
        type: Object,
        default: () => ({}),
    },
    filters: {
        type: Array as PropType<IFilter[]>,
        default: () => [
            {
                term: {
                    'page.properties.Sentiment.keyword': 'positive',
                },
            },
            {
                term: {
                    'page.properties.ShowOnHomepage': true,
                },
            },
        ],
    },
    showTime: {
        type: Boolean,
        default: false,
    },
    pathToAllRating: {
        type: String,
        default: '',
    },
})

const emit = defineEmits(['on-fetched'])

const { hits, loading, execute, total } = useFetchElastic<HitNotionRatings>(
    props.elasticIndex,
    {
        size: 10,
        function_score: {
            query: { bool: { filter: props.filters } },
            functions: [
                {
                    random_score: {
                        seed: parseInt(dayjs().format('YYYYMMDD')),
                        field: '_seq_no',
                    },
                },
            ],
            boost_mode: 'replace',
            score_mode: 'sum',
        },
    },
)

const { width } = useWindowSize()

const commentLength = 200
const ratings: Ref<IRating[]> = ref([])

//Responsive options for carousel
const responsiveOptions = ref([
    {
        breakpoint: '1400px',
        numVisible: 2,
        numScroll: 1,
    },
    {
        breakpoint: '575px',
        numVisible: 1,
        numScroll: 1,
    },
])

const defaultResponsiveOption: IResponsiveOption = {
    numVisible: 3,
    numScroll: 1,
}

const {
    currentPage,
    prev,
    next,
    numVisible,
    numScroll,
    _length,
    getCurrentResponsiveOption,
} = useComponentResponsiveCarousel(
    responsiveOptions,
    defaultResponsiveOption,
    ratings,
    width, // Pass the width ref here
)

watch(width, () => execute())

watch(
    hits,
    () => {
        ratings.value = hits.value?.map((hit) => ({
            _id: hit._id || '',
            authorGender: hit.page?.properties?.RatingByGender || '',
            authorName: hit.page?.properties?.RatingByName || '',
            avatar: '',

            dateCreated: hit.page?.properties?.CreatedAt?.start || '',
            fullComment: hit.page?.properties?.Comment || '',

            halfComment:
                hit.page?.properties?.Comment?.substring(0, commentLength) +
                (hit.page?.properties?.Comment?.length || 0 > commentLength
                    ? '...'
                    : ''),
            isOverlap:
                (hit.page?.properties?.Comment?.length || 0) > commentLength,
            notionLink: hit.page?.url || '',
            stars: hit.page?.properties.Rating || 0,
        }))

        emit('on-fetched', ratings.value)
    },
    { immediate: true },
)

onMounted(() => {
    execute()
    getCurrentResponsiveOption(width.value)
})
</script>

<template>
    <Skeleton v-if="loading" class="mb-2" height="200" />

    <template v-if="ratings && ratings.length > 0">
        <div>
            <h3 v-if="title">{{ title }}</h3>

            <p v-if="description">
                {{ description }}
            </p>

            <Carousel
                v-model:page="currentPage"
                :value="ratings"
                :num-visible="numVisible"
                :num-scroll="numScroll"
                :responsive-options="responsiveOptions"
                :show-navigators="false"
                :show-indicators="false"
                :pt="{
                    footer: 'flex flex-col items-center gap-4 mt-2 sm:mt-10',
                    itemsContainer: 'flex flex-row',
                }"
            >
                <template v-if="ratings?.length > 1" #footer>
                    <div class="flex flex-row gap-2">
                        <Button
                            text
                            severity="secondary"
                            icon="pi pi-arrow-left"
                            :disabled="currentPage == 0"
                            @click="prev"
                        />

                        <span class="my-auto text-sm">
                            {{ currentPage + 1 }} /
                            {{ _length + 1 - numVisible }}
                        </span>

                        <Button
                            text
                            severity="secondary"
                            icon="pi pi-arrow-right"
                            :disabled="currentPage == _length - numVisible"
                            @click="next"
                        />
                    </div>
                </template>

                <template #item="{ data }">
                    <WTestimonialFeedbackCard
                        :rating="data"
                        :pt="{ item: 'mx-2' }"
                        :show-time="showTime"
                    />
                </template>
            </Carousel>

            <NuxtLinkLocale
                v-if="pathToAllRating && total && ratings.length < total"
                :to="pathToAllRating"
                class="mt-4 flex justify-center"
            >
                <Button :label="t('see-more')" pt:root:class="border-none text-white" />
            </NuxtLinkLocale>
        </div>
    </template>
</template>
