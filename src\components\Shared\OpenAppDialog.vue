<template>
    <Dialog
v-model:visible="visible" :draggable="false" modal position="bottom"
        :header="t('header:continue-with-app')">
        <div
class="flex min-w-[350px] cursor-pointer items-center space-x-2 rounded-lg hover:bg-gray-200 dark:hover:bg-gray-700"
            @click="onOpenApp">
            <div>
                <NuxtImg
src="https://cdn.wellcare.vn/file-proxy/cms-gallery/logo-third-party/11CuLAAmuGVP2nO3.png"
                    class="w-10" />
            </div>
            <div>
                {{ t('title:telemedicine-app') }}
            </div>
        </div>
    </Dialog>
</template>
<script setup lang="ts">
const { t } = useI18n()
const visible = ref<boolean>(false)
function open() {
    visible.value = true
}
function close() {
    visible.value = false
}

const onOpenApp = () => {
    const platform = navigator.userAgent
    switch (true) {
        case /android/.test(platform.toLowerCase()):
            window.open(
                'https://play.google.com/store/apps/details?id=vn.wellcare',
                '_blank',
            )
            break
        case /iphone|ipad|ipod/.test(platform.toLowerCase()):
            window.open(
                'https://apps.apple.com/us/app/wellcare/id1039423586',
                '_blank',
            )
            break
        default:
            console.log('Can not detect platform.')
    }
}

defineExpose({
    open,
    close
})
</script>
<i18n lang="yaml">
vi:
    header:continue-with-app: Tiếp tục mở bằng
    title:telemedicine-app: Ứng dụng Khám từ xa
en:
    header:continue-with-app: Continue to open with
    title:telemedicine-app: Telemedicine mobile app
</i18n>