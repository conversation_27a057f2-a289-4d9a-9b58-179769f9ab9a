import { Capacitor } from '@capacitor/core'
import { useAuthRedirect } from '~/composables'
import { appRoutes } from '~/constants'

function handleAuthError(err: any): string {
    console.error('Authentication callback error:', err)

    if (err.error) {
        switch (err.error) {
            case 'login_required':
                return 'Login is required. Please try again.'
            case 'invalid_grant':
                return 'Invalid credentials. Please check your username and password.'
            case 'unauthorized_client':
                return 'Unauthorized client. Please contact support.'
            default:
                return (
                    err.error_description ||
                    err.message ||
                    'An unexpected error occurred'
                )
        }
    } else if (err.message) {
        return err.message
    }
    return 'An unexpected error occurred during authentication callback. Please try again later.'
}

export const useAuthCallback = () => {
    const { getUserManager, signIn, checkAuthStatus } = useAppAuth()
    const { redirect } = useAuthRedirect()
    const { get } = usePreferences()
    const { locale } = useI18n()
    const { login: loginRevenueCat } = useRevenueCat()

    const error = ref('')
    const loading = ref(true)

    const isIos = computed(() => Capacitor.getPlatform() === 'ios')

    const processAuthRedirect = async () => {
        const { user, error, refresh } = useUserInfo({
            scope: ['_id', 'isProvider', 'name', 'phone', 'email'],
        })
        await refresh()
        const statusCodeUser = error.value?.statusCode

        if (statusCodeUser === 404) {
            await navigateTo(appRoutes.auth.onboarding)
            return
        }

        const intendedRoute = await get({ key: 'intendedRoute' })
        if (intendedRoute) {
            await redirect()
            return
        }

        if (isIos.value) {
            await loginRevenueCat(user.value)
        }

        const route = user.value?.isProvider
            ? appRoutes.doctor.home
            : appRoutes.patient.healthRecords.index
        await navigateTo(route)
    }

    const processAuthCallback = async () => {
        try {
            const isLogged = await checkAuthStatus()

            if (!isLogged && !Capacitor.isNativePlatform()) {
                const userCallback = await getUserManager.signinCallback()
                if (!userCallback) {
                    throw new Error(
                        'Authentication callback failed: No user returned',
                    )
                }
            }

            await processAuthRedirect()
        } catch (err: any) {
            error.value = handleAuthError(err)
        } finally {
            loading.value = false
        }
    }

    const retryAuthentication = async () => {
        loading.value = true
        error.value = ''
        try {
            await signIn({
                extraQueryParams: {
                    ui_locales: locale.value,
                },
            })
        } catch (err: any) {
            console.error('Failed to get sign-in URL:', err)
            error.value = handleAuthError(err)
            loading.value = false
        }
    }

    return {
        error,
        loading,
        processAuthCallback,
        retryAuthentication,
    }
}
