interface LayoutState {
    title?: string
    subTitle?: string
    cover?: string
    target?: string
}

export const useLayoutStore = defineStore('layout', {
    state: (): LayoutState => ({
        title: '',
        subTitle: '',
        cover: '',
        target: ''
    }),
    actions: {
        setTitle(title: string) {
            this.title = title
        },
        setSubTitle(subTitle: string) {
            this.subTitle = subTitle
        },
        setCover(cover: string) {
            this.cover = cover
        },
        setTarget(target: string) {
            this.target = target
        },
        reset() {
            this.title = ''
            this.subTitle = ''
            this.cover = ''
            this.target = ''
        }
    },
})
