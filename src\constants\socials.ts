type ContentKey = 'FAQ' | 'TERMS_AND_CONDITIONS' | 'PRIVACY_POLICY'
interface ContentSlug {
    vi: string
    en: string
}
type Contents = {
    [key in ContentKey]: ContentSlug
}

export const CONTENTS: Contents = {
    FAQ: {
        vi: '/content/khamtuxa.wellcare.vn/cau-hoi-thuong-gap',
        en: '/content/khamtuxa.wellcare.vn/frequently-asked-questions',
    },
    TERMS_AND_CONDITIONS: {
        vi: '/content/khamtuxa.wellcare.vn/dieu-khoan-and-dieu-kien-su-dung',
        en: '/content/khamtuxa.wellcare.vn/terms-and-conditions',
    },
    PRIVACY_POLICY: {
        vi: '/content/khamtuxa.wellcare.vn/bao-mat-thong-tin',
        en: '/content/khamtuxa.wellcare.vn/privacy-policy',
    },
}

export const SUPPORT_CONTACTS = {
    EMAIL: '<EMAIL>',
    ZALO: 'https://zalo.me/0366905905',
    WHATSAPP:
        'https://api.whatsapp.com/send/?phone=84366905905&text&type=phone_number&app_absent=0',
    VIBER: 'viber://chat?number=%2B84366905905',
}

export const APP_LINKS = {
    ANDROID: 'https://play.google.com/store/apps/details?id=vn.wellcare',
    IOS: 'https://apps.apple.com/us/app/wellcare/id1039423586',
}
