import { transform as _transform } from '../../utils'

import { computed, isRef, useFetchElastic } from '#imports'
import type {
    ElasticIndex,
    IFilter,
    IFunctionScore,
    IHighlight,
    IHitNotion,
    IMust,
    IMustNot,
    IResponseElasticSearch,
    IShould,
    ISource,
    TSort,
    ValueOrRef,
} from '~/models'

import { useFetchDefault } from './default'

export function useFetchElasticWithDefault<T>(
    index: ElasticIndex,
    query: {
        _source?: ValueOrRef<ISource>
        filters?: ValueOrRef<IFilter[]>
        from?: ValueOrRef<number>
        function_score?: ValueOrRef<IFunctionScore>
        highlight?: ValueOrRef<IHighlight>
        key?: ValueOrRef<string>
        must?: ValueOrRef<IMust[]>
        must_not?: ValueOrRef<IMustNot[]>
        should?: ValueOrRef<IShould[]>
        size?: ValueOrRef<number>
        sort?: ValueOrRef<TSort[]>
        aggs?: ValueOrRef<object>
    },
    options?: {
        transform?: (value: IResponseElasticSearch<IHitNotion>) => any,
        immediate?: boolean
    },
) {
    const _default = useFetchDefault(index)

    const computeValue = <V>(_value: ValueOrRef<V>, _default?: V) =>
        isRef(_value) ? _value : computed(() => _value ?? _default)

    return useFetchElastic<T>(
        index,
        {
            _source: computed(() => ({
                ..._default._source,
                ...computeValue(query._source).value,
            })),
            filters: computed(() =>
                (_default.filter ?? []).concat(
                    computeValue(query.filters).value ?? [],
                ),
            ),
            from: computed(
                () => computeValue(query.from).value ?? 0, // Default to 0 for "from"
            ),
            function_score: computed(
                () => computeValue(query.function_score).value as any,
            ),
            highlight: computed(
                () => computeValue(query.highlight).value as any,
            ),
            key: computeValue(query.key).value,
            must: computed(
                () => computeValue(query.must).value ?? [], // Default to an empty array for must
            ),
            must_not: computed(() => computeValue(query.must_not).value ?? []),
            should: computed(() =>
                (_default.should ?? []).concat(
                    computeValue(query.should).value ?? [],
                ),
            ),
            size: computed(
                () => computeValue(query.size).value ?? 10, // Default to 10 for size
            ),
            sort: computed(
                () => computeValue(query.sort).value ?? _default.sort ?? [], // Default for sort
            ),
            aggs: computeValue(query.aggs),
        },
        options,
    )
}
