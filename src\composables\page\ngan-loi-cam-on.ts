export async function useCheckImageUrlExists(url: string): Promise<boolean> {
  if (!url) return false

  try {
    const response = await fetch(url, { method: 'HEAD' })
    if (!response.ok) return false

    const contentType = response.headers.get('Content-Type')
    if (!contentType || !contentType.startsWith('image/')) return false

    return await new Promise((resolve) => {
      const img = new Image()
      img.onload = () => resolve(true)
      img.onerror = () => resolve(false)
      img.src = url
    })
  } catch {
    return false
  }
}
