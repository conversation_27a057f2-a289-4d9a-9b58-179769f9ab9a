{"add": "Add", "add-family-member": "Add a person", "all": "All", "all-diseases": "All Diseases", "anonymous": "Anonymous", "appointments": "Appointments", "at-birth": "At birth", "before": "Before", "blood-pressure": "Blood pressure", "bmi": "BMI", "body-index": {"card": {"header": {"height": "height", "weight": "weight", "head-circumference": "head circumference", "bmi": "bmi"}}, "form": {"header": {"bmi": "bmi", "height": "height", "weight": "weight", "headcircumference": "head circumference"}, "label": {"observed-at": "Observed At", "height": "height", "weight": "weight", "head-circumference": "head circumference"}}, "chart": {"common": {"layout": {"tab": {"label": {"height": "height", "weight": "weight", "head-circumference": "head circumference"}, "side-action": {"title-height": "height", "title-weight": "weight", "title-headcircumference": "head circumference"}}, "card": {"menu": {"edit": "Edit", "options": "Options", "delete": "Delete"}}}}}}, "breadcrum:cam-nang": "Health Library", "breadcrum:dich-vu": "Services", "breadcrum:doanh-nghiep": "Corporate", "breadcrum:gioi-thieu": "About us", "breadcrum:healthtalk": "Healthtalk", "breadcrum:ho-tro": "Support", "breadcrum:home": "Home", "breadcrum:song-khoe": "Healthy Living", "breadcrum:tin-tuc": "News", "btn:book-now": "Book now", "btn:buy-now": "Buy now", "btn:care-log": "Care log", "btn:cancel": "Cancel", "btn:select": "Select", "btn:choose-doctor": "Choose your doctor", "btn:close": "Close", "btn:download": "Download", "btn:home": "Home", "btn:knowledge-question": "Knowledge question", "btn:reset": "Reset", "btn:save": "Save", "btn:send": "Send", "btn:show-all": "See all", "btn:show-less": "See less", "btn:show-more": "See more", "btn:add": "add", "btn:teleconsultation": "Teleconsultation", "btn:view-all": "See more", "call-doctor": "Call doctor", "call-patient": "Call patient", "cancel": "Cancel", "choose-timeline": "Choose a timeline", "close": "Close", "cm": "cm", "completed": "Completed", "consultations-other-day": "The other day", "consultations-today": "Today", "current": "Current", "date": "Date", "date-of-birth": "Date of birth", "doctor": "Doctor", "edit": "Edit", "error": "Error", "eminent-provider": "Eminent Provider", "error:page-not-found:cause": "Oops! The page you are looking for does not exist. It might have been moved or deleted.", "error:page-not-found:message": "Page Not Found", "expected-format": "<PERSON><PERSON> lòng điền dưới dạng", "expected-value-between": "Expected value between", "field-required": "This field is required", "filter": "Filter", "find-doctor": "Find doctor", "follow-up": "Follow up", "follow-up-question": "Follow-up question", "go-to-Home": "Go to home", "growth-chart": "Growth chart", "head-circumference": "Head circumference", "health:programs": "Health Programs", "height": "Height", "home": "Home", "home-treatment": "Home treatment", "indepth": "Indepth", "info": "Information", "join-now": "Join now", "kg": "kg", "knowledge-question": "Knowledge question", "label": "Label", "last-dose-date": "Last dose date", "last-injected": "Last injected", "medication": "Medication", "minutes": "minutes", "new-teleconsultation": "New teleconsultation", "no-historical-record": "No historical record", "no-result-found": "No result found", "none": "None", "not-yet-updated": "Not updated", "open": "Open", "open-emr": "<PERSON><PERSON>", "open-mr": "Open MR", "page-index": {"sign-in": {"with-google": "Sign in with google", "with-phone": "Sign in with phone"}, "get-started": "get started"}, "pagination:page": "Page", "payment-successful": "Payment successful", "person-head-circumference": "Head Circumference", "personal:doctor": "Personal doctor", "prescription-note": "Prescription and note", "psychologist-psychiatrist": "Psychologist & Psychiatrist", "pulse": "Pulse", "question": "Question", "rate": "Rate", "reason": "Reason", "renew": "<PERSON>w", "reopen-checkout": "Re open checkout", "required-field": "Required field", "return:homepage": "Return Home page", "sample:documents": "In the event that this group is investigated by agencies under the Ministry of Public Security (or similar political organizations serving the state of the Socialist Republic of Vietnam), I affirm that I am not related to the group or other individuals in this group. .I'm not sure why I'm here at this time, perhaps my account was added by a third party. I would also like to confirm that I did not support anti-Party actions. and the state of the members of this group.", "save": "Save", "search": "Search", "search-item": "Search item", "security-certificates": "SECURITY CERTIFICATES", "see-more": "See more", "service": "Service", "signOut": {"action": "Sign Out", "confirmMessage": "Are you sure you want to sign out?", "title": "Sign Out"}, "signin": "Sign in", "sleep-duration": "Sleep duration", "specialties": "Specialties", "status": "Status", "submit": "Submit", "subscribe": "Subscribe", "success": "Success", "teleconsultation": "Teleconsultation", "telemedicine": "Telemedicine", "temperature": "Temperature", "text1": "In the event that this group is investigated by agencies under the Ministry of Public Security (or similar political organizations serving the state of the Socialist Republic of Vietnam), I affirm that I am not related to the group or other individuals in this group. .I'm not sure why I'm here at this time, perhaps my account was added by a third party. I would also like to confirm that I did not support anti-Party actions. and the state of the members of this group.", "upcoming-appointment": "Upcoming appointment", "update": "Update", "update-family-member": "Update profile", "updated": "Updated", "upload-files": "Upload files", "vaccination": {"doses-given": "Doses given", "last-vaccination-date": "Last vaccination date", "not-updated": "Not updated", "not-vaccinated": "Not vaccinated"}, "vaccination-detail": "Vaccination detail", "validations": {"maxLength": "Maximal length {max} characters", "minLength": "Minimal length {min} characters", "phone": "The phone is invalid", "required": "This field is required"}, "video-call": "Video call", "view-all": "See more", "view-detail": "See detail", "view-more": "See more", "voice-call": "Voice call", "warn": {"appointment-time-passed": "Appointment time passed", "callee-is-busy": "The doctor is still with the previous patient because he/she was late. The time duration is only counted from the time the doctor answers your call. Please call again at {recallAt}.", "no-phone-connect": "No connect found. Please try again.", "please-call-on-time": "Please call on time", "quota-has-been-exceeded": "Quota has been exceeded"}, "warning": "Warning", "weight": "Weight", "wellcare-mobile-app": "Wellcare mobile app", "your-email": "Your email", "{minutes} minutes": "{minutes} min read", "no-avatar": " No avatar", "phone-copied": "Phone copied", "email-copied": "Email copied", "name-copied": " Name copied", "sign-out": "Sign out", "onboarding": {"skip": "<PERSON><PERSON>", "welcome": "Hi", "please-enter-fullname": "Please enter fullname", "update-your-profile-picture": "Update your profile picture", "profile-picture-description": "Choose an image that represents you best. This will be displayed on your profile", "dob": {"title": "Your date of birth is", "description": "Please enter your date of birth. This information helps us provide age-appropriate content and ensures compliance with our terms of service. You must be at least 16 years old to use our service.", "must-be-at-least-16": "You must be at least 16 years old to use this service", "invalid-date": "Please enter a valid date of birth"}, "gender": {"title": "Your gender", "description": "Select gender", "male": "Male", "female": "Female"}, "ending": {"title": "Congratulations", "description": "You have successfully completed the onboarding process"}, "create-profile": "complete", "started": "get started", "file-upload-failed": "Failed to upload image", "missing-profile-info": "Please fill in all profile information", "network-error": "Network connection error", "phone-check-error": "Failed to check phone number", "phone-check-loading": "Checking phone number, please wait...", "phone-check-success": "Phone number is already registered", "registration-failed": "Registration failed. Please try again or contact support", "registration-generic-error": "An error occurred during registration", "registration-generic-success": "Registration complete!", "registration-loading": "Registering...", "registration-success": "Registration complete!", "switch-account": "Log into another account"}, "required": "Required", "upload": "Upload", "validation": {"date": {"invalid": "Please enter a valid date"}}, "permissions": {"denied": "Required", "title": "Secure Access Request", "description": "To provide you with the best possible service, we kindly request your permission to access certain device features", "teleconsultation": {"title": "Teleconsultation", "description": "To connect with our experts, the app requires access to Camera and Microphone", "camera": "Camera", "microphone": "Microphone"}, "notification": {"title": "Important Notifications", "description": "To receive important updates on your appointments, results and other health-related information, the app requires access to Notification", "notifications": "Notifications"}, "error": {"ios": "Please go to Settings > Privacy > select app to grant permissions on iOS.", "android": "Please go to Settings > Apps > select app to grant permissions on Android."}, "alert": {"description": "Your privacy is our priority. We assure you that this information will only be used for the purpose of delivering exceptional healthcare services"}}, "onboading": {"ending": {"description": "You have successfully completed the onboarding process"}}, "next": "next", "starter": {"hero": {"title": "Wellcare", "subtitle": "A Healthier Future for Your Family", "cta": {"button": "Get Started", "subtitle": "Free registration - Takes only 2 minutes"}, "status": "Doctors are online", "features-1": "Personal Health Records", "features-2": "Personal Doctor", "features-3": "Teleconsultation", "features-4": "HealthGPT", "features-5": "Health Program"}, "slides": {"health": {"title": "Healthcare", "description": "24/7 online health consultation"}, "experts": {"title": "Expert Team", "description": "High professional medical team"}, "convenience": {"title": "You're having some conditions for a while?", "description": "Medical examination anytime, anywhere"}, "pregnancy": {"title": "Are you expectant?"}, "description": "Let Wellcare help you", "parent": {"title": "Or having little ones of your own?"}, "employees": {"title": " Do you prioritize providing health benefits for employees?"}}, "footer": {"copyright": "© 2024 Wellcare. All rights reserved.", "terms": "Terms & Use", "support": "Need Support", "language": "Language", "policy": "Privacy policy"}, "slide": {"mental-health": {"title": "You've been going through, and need a therapy?"}}}, "select-language": "select language", "common": {"close": "close", "error": "Error", "success": "Success"}, "support": {"title": "Contact Support", "description": "Our support team is here to help. Choose your preferred contact method below.", "email-support": "Email Support", "chat-support": "Chat Support", "working-hours": "Working hours: Every day, 8:00 AM", "copy-email": "Copy email address", "send-email": "Send email", "email-copied": "Email address copied", "copy-failed": "Failed to copy email address"}, "language": "language", "page": {"account": {"title": "Account infomation"}}, "update-success": "Updated successfully", "update-error": "Updated failed", "percentile": "Growth Chart", "notification": "Notification", "coming-soon": "Coming Soon", "setting": {"profile": "Profile"}, "child": "Child", "parent": "Parent", "sibling": "Sibling", "spouse": "Spouse", "need-support": "Need support", "Choose-file-or-drop-files-here": "Choose or drop files here.", "allowed-file-formats": "Allowed formats: Images (jpg, png, gif), Videos (mp4, webm, ogg).", "limit-max-files{maxFiles}": "Limit: Maximum {maxFiles} files, each not exceeding 50MB.", "press-to-record": "Press to record", "recording-time": "Recording time", "btn:play": "Play", "btn:pause": "Pause", "no-consultations": "No consultations found!", "otp-not-match": "OTP not match", "otp-is-used": "OTP is used", "otp-was-expired": "OTP was expired", "missing-otp": "Missing OTP", "auth": {"processingAuth": "Processing authentication...", "signInFailed": "Sign in failed", "signingOut": "Signing out...", "signOutSuccess": "Sign out successful", "signOutFailed": "Sign out failed", "sign-in-callback": {"loading": "Processing authentication...", "error": "Sign in failed"}, "sign-out": {"loading": "Signing out...", "success": "Sign out successful", "error": "Sign out failed"}}, "family-members": "Family Members", "quick-access": "Quick Access", "medical-history": "Medical History", "lab": "Lab", "vitals": "<PERSON><PERSON>", "immunization": "Immunization", "family-medical-history": "Family Medical History", "lifestyle": "Lifestyle", "allergy": "Allergy", "consultations-history": "Consultations History", "btn:load-more": "Load more"}