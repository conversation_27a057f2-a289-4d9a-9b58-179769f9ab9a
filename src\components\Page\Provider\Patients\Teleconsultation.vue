<script setup lang="ts">
defineProps({
    memberActivatedId: {
        type: String,
        required: true,
    },
})

const { t } = useI18n()
const { user } = useUserInfo({ scope: ['_id', 'isProvider', 'phone'] })
const callHandle = useCallHandle()

const userId = computed(() => user.value?._id ?? '')
// const userPhone = computed(() => user.value?.phone ?? '')

const openEMRHandler = (consultation: any) => {
    navigateTo(`/provider/consultation/${consultation._id}`)
}
const callVideoHandler = (data: any) => {
    callHandle.open('video', {
        _id: data._id,
        conversation: data.conversationId,
        providerUser: data.provider?.user,
        user: data.user._id,
    })
}
const callVoiceHandler = (data: any) => {
    callHandle.open('voice', {
        _id: data._id,
        conversation: data.conversationId,
        providerUser: data.provider?.user,
        user: data.user._id,
    })
}
// const openRatingHandler = () => {}
</script>

<template>
    <SharedPageSection
        :title="t('teleconsultation')"
        :pt="{
            content:
                'flex snap-x flex-row flex-nowrap gap-4 overflow-x-auto px-4 sm:flex flex-col',
        }"
        :view-all-to="`/provider/patients/${memberActivatedId}/consultation`"
    >
        <!-- <WUpcomingConsultations
            :user-id="memberActivatedId"
            @click:call="callVoiceHandler"
            @click:open-mr="openEMRHandler"
            @click:call-video="callVideoHandler"
            @click:open-rating="openRatingHandler"
        /> -->
        <WConsultationHistory
            ref="consultation-history"
            :user-id="memberActivatedId"
            :showing-quantity="3"
            :showing-load-btn="false"
            :filter="{
                providerUser: userId,
                patient: memberActivatedId,
                type: {
                    $in: ['indepth', 'question'],
                },
            }"
            :is-provider="true"
            @click:call="callVoiceHandler"
            @click:open-mr="openEMRHandler"
            @click:call-video="callVideoHandler"
        />
        <!-- <CallHandle
            ref="call-handle"
            default-route="provider"
            :from-id="userId"
            :user-phone="userPhone"
        /> -->
    </SharedPageSection>
</template>
