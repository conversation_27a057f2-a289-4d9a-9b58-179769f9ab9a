<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { ref, computed, watch, type ComputedRef, type Ref } from 'vue'
import type { IMasonryResponsiveOption } from '~/composables/use-masonry-layout'
import type { IRating } from '~/components/FeedbackCard.vue'
// import { useCheckImageUrlExists } from '~/composables/page/ngan-loi-cam-on'
import type { HitNotionRatings } from '~/models'
import { ElasticIndex } from '~/models'
// i18n setup
const { t } = useI18n({ useScope: 'local' })

// Image overlay functionality
const isOverlayVisible = ref(false)
const overlayImageSrc = ref('')
const overlayRef = ref(null)

const openOverlay = (src: string) => {
    overlayImageSrc.value = src
    isOverlayVisible.value = true
}

const closeOverlay = () => {
    isOverlayVisible.value = false
}

onClickOutside(overlayRef, closeOverlay)

// Ratings functionality
const ratingsAddNo: Ref<number> = ref(0)
const ratingsFrom = computed(() => ratingsAddNo.value * loadMoreRatingsSize)
const ratingsSize: ComputedRef<number> = computed(() => ratings.value.length)
const loadMoreRatingsSize: number = 30
const ratings: Ref<IRating[]> = ref([])

const {
    hits: ratingsHits,
    total: ratingsTotal,
    execute: fetchRatings,
    loading: ratingsLoading,
} = useFetchElasticWithDefault<HitNotionRatings>(ElasticIndex.NOTION_RATINGS, {
    size: loadMoreRatingsSize,
    from: ratingsFrom,
    filters: [
        {
            term: {
                'page.properties.Sentiment.keyword': 'positive',
            },
        },
    ],
    sort: [
        { 'page.properties.ShowOnHomepage': 'desc' },
        { 'page.properties.IsFeature': 'desc' },
        { _score: 'desc' },
    ],
})

watch(
    ratingsHits,
    async () => {
        if (
            ratings.value?.length + ratingsHits.value?.length >
            (ratingsAddNo.value + 1) * loadMoreRatingsSize
        )
            return

        const validRatings = await Promise.all(
            ratingsHits.value?.map((element) => {
                if (!element.page) return null
                // const avatarUrl = element.page.properties['RatingByAvatar']?.url
                // const isValidAvatar = avatarUrl
                //     ? await useCheckImageUrlExists(avatarUrl)
                //     : false
                const defaultAvatar =
                    element.page.properties.RatingByGender === 'F'
                        ? 'https://cdn.wellcare.vn/imaginary/resize?width=216&type=webp&url=https://storage.googleapis.com/cms-gallery/61e64af5003d0a9e5b2ed091/8ca19fbe-f6ff-5c84-9a7f-44ab52ce7cdf.png'
                        : 'https://cdn.wellcare.vn/imaginary/resize?width=221&type=webp&url=https://storage.googleapis.com/cms-gallery/61e64af4a5fbe579b6491310/82360227-a55b-5450-a884-41a082398a49.png'
                return {
                    avatar: defaultAvatar,
                    authorName: element.page.properties.RatingByName,
                    fullComment: element.page.properties.Comment,
                    stars: element.page.properties.Rating,
                    authorGender: element.page.properties.RatingByGender,
                    dateCreated: element.page.properties.CreatedAt.start,
                } as IRating
            }) || [],
        )
        ratings.value = [
            ...ratings.value,
            ...validRatings.filter(
                (rating): rating is IRating => rating !== null,
            ),
        ]
    },
    { immediate: true },
)

const loadMoreRatings = () => {
    ratingsAddNo.value += 1
}

watch(ratingsAddNo, () => {
    fetchRatings()
})

const allRatingsLoaded: ComputedRef<boolean> = computed(
    (): boolean => ratingsSize.value >= (ratingsTotal.value ?? 0),
)

// Masonry layout for ratings
const ratingsResponsiveOptions: IMasonryResponsiveOption[] = [
    { breakpoint: 'xs', columns: 1 },
    { breakpoint: 'sm', columns: 2 },
    { breakpoint: 'xl', columns: 3 },
]

const ratingsColumns: ComputedRef<Array<Array<IRating>>> = computed<
    Array<Array<IRating>>
>(() => useMasonryLayout(ratings, ratingsResponsiveOptions).columns.value)

// Screenshots functionality
const screenShotsAddNo: Ref<number> = ref(0)
const screenShotsFrom: ComputedRef<number> = computed(
    () => screenShotsAddNo.value * loadMoreScreenshotsSize,
)
const loadMoreScreenshotsSize: number = 12
const screenshotsSize = computed(() => screenShots.value.length)
const screenShots: Ref<string[]> = ref([])
// asdd
const {
    hits: screenShotHits,
    total: screenshotsTotal,
    execute: fetchScreenshots,
    loading: screenShotsLoading,
} = useFetchElasticWithDefault(ElasticIndex.NOTION_WEBSITE, {
    size: loadMoreScreenshotsSize,
    from: screenShotsFrom,
    filters: [
        {
            term: {
                'page.properties.Parent item.properties.Slug.keyword':
                    'wellcare-tren-mang-xa-hoi',
            },
        },
        {
            terms: {
                'page.properties.Site.keyword': ['wellcare.vn'],
            },
        },
    ],
    sort: [
        {
            'page.properties.Order': 'desc',
        },
        '_score',
    ],
})

watch(
    screenShotHits,
    () => {
        if (
            screenShots.value?.length + screenShotHits.value?.length >
            (screenShotsAddNo.value + 1) * loadMoreScreenshotsSize
        )
            return
        const validScreenshots =
            screenShotHits.value
                ?.filter((element: any) => element?.page?.cover?.url)
                ?.map((element: any) => element.page.cover.url) || []

        screenShots.value = [...screenShots.value, ...validScreenshots]
    },
    { immediate: true },
)

const loadMoreScreenShots = () => {
    screenShotsAddNo.value += 1
}

watch(screenShotsAddNo, () => {
    fetchScreenshots()
})
const screenshotsResponsiveOptions: IMasonryResponsiveOption[] = [
    {
        breakpoint: 'xs',
        columns: 1,
    },
    { breakpoint: 'sm', columns: 2 },
    {
        breakpoint: 'lg',
        columns: 4,
    },
]
const screenShotsColumns: ComputedRef<string[]> = computed(
    () =>
        useMasonryLayout(screenShots, screenshotsResponsiveOptions).columns
            .value as string[],
)

const allScreenshotsLoaded = computed(
    () => screenshotsSize.value >= screenshotsTotal.value,
)
</script>

<template>
    <div class="px-4 py-20 text-center xs:px-8 lg:px-16 xl:px-32">
        <!-- Screenshots Section -->
        <h2 class="mb-12">
            {{ t('title:social') }}
        </h2>
        <div class="mb-12 flex flex-row flex-wrap justify-center gap-8">
            <div class="flex flex-row gap-4">
                <div
                    v-for="(screenShotsColumn, colIndex) in screenShotsColumns"
                    :key="colIndex"
                    class="flex flex-col gap-4"
                >
                    <TransitionGroup
                        enter-from-class="opacity-0"
                        leave-to-class="opacity-0"
                        enter-active-class="transition duration-300"
                        leave-active-class="transition duration-300"
                    >
                        <div
                            v-for="social in screenShotsColumn"
                            :key="social"
                            class="relative overflow-hidden rounded-lg"
                        >
                            <div class="nuxt-img-wrapper p-4 pt-6">
                                <NuxtImg
                                    :src="social"
                                    alt="screenshot"
                                    class="relative z-10 mx-auto w-full cursor-pointer rounded"
                                    sizes="250px md:300px"
                                    format="webp"
                                    width="300"
                                    height="auto"
                                    blur="20px"
                                    lazy
                                    @click="openOverlay(social)"
                                />
                            </div>
                        </div>
                    </TransitionGroup>
                </div>
            </div>
        </div>

        <!-- Image Overlay -->
        <Transition
            enter-from-class="opacity-0"
            leave-to-class="opacity-0"
            enter-active-class="transition"
            leave-active-class="transition"
        >
            <div
                v-if="isOverlayVisible"
                class="duration fixed inset-0 z-50 flex flex-col items-center justify-center bg-gray-900 bg-opacity-80 transition dark:bg-neutral-900 dark:bg-opacity-80"
            >
                <NuxtImg
                    ref="overlayRef"
                    :src="overlayImageSrc"
                    format="webp"
                    class="overlay-content max-h-[90%] w-[90%] sm:h-[80%] sm:w-auto"
                    sizes="200px md:300px xl:400px"
                />
                <div
                    class="mt-3 grid h-10 w-10 cursor-pointer place-items-center rounded-full bg-white/80 text-xl text-gray-800 md:mt-10"
                    @click="closeOverlay"
                >
                    &times;
                </div>
            </div>
        </Transition>

        <!-- Load More Screenshots Button -->
        <div
            v-if="!allScreenshotsLoaded"
            class="flex w-full items-center justify-center gap-4"
        >
            <LoadMoreButton
                :loading="screenShotsLoading"
                @click="loadMoreScreenShots()"
            />
        </div>

        <!-- Ratings Section -->
        <h2 class="mt-32">
            {{ t('title') }}
        </h2>
        <p class="mx-auto mb-12 max-w-5xl">
            {{ t('subtitle') }}
        </p>

        <div class="grid grid-flow-col gap-4">
            <div
                v-if="ratingsLoading && !ratingsHits.length"
                class="grid grid-flow-col gap-4"
            >
                <div
                    v-for="col in 3"
                    :key="`rating-col-${col}`"
                    class="flex flex-col gap-4"
                >
                    <div
                        v-for="item in 3"
                        :key="`rating-item-${item}`"
                        class="rounded-lg bg-white p-6 shadow-md dark:bg-gray-800"
                    >
                        <div class="animate-pulse space-y-4">
                            <!-- Avatar and Name -->
                            <div class="flex items-center space-x-4">
                                <div
                                    class="h-12 w-12 rounded-full bg-gray-200 dark:bg-gray-700"
                                ></div>
                                <div
                                    class="h-4 w-32 rounded bg-gray-200 dark:bg-gray-700"
                                ></div>
                            </div>
                            <!-- Stars -->
                            <div class="flex space-x-1">
                                <div
                                    v-for="star in 5"
                                    :key="`star-${star}`"
                                    class="h-4 w-4 rounded bg-gray-200 dark:bg-gray-700"
                                ></div>
                            </div>
                            <!-- Comment -->
                            <div class="space-y-2">
                                <div
                                    class="h-4 w-full rounded bg-gray-200 dark:bg-gray-700"
                                ></div>
                                <div
                                    class="h-4 w-3/4 rounded bg-gray-200 dark:bg-gray-700"
                                ></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div
                v-for="(ratingsColumn, colIndex) in ratingsColumns"
                v-else
                :key="colIndex"
                class="flex flex-col gap-4"
            >
                <TransitionGroup
                    enter-from-class="opacity-0"
                    leave-to-class="opacity-0"
                    enter-active-class="transition duration-500"
                    leave-active-class="transition duration-500"
                >
                    <FeedbackCard
                        v-for="(rating, index) in ratingsColumn"
                        :key="`${rating.authorName}-${index}`"
                        :rating="rating"
                        :index="index"
                    />
                </TransitionGroup>
            </div>
        </div>

        <!-- Load More Ratings Button -->
        <div
            v-if="!allRatingsLoaded"
            class="mt-8 flex w-full items-center justify-center gap-4"
        >
            <LoadMoreButton
                :loading="ratingsLoading && !!ratingsHits.length"
                @click="loadMoreRatings()"
            />
        </div>
    </div>
</template>

<style scoped>
.nuxt-img-wrapper {
    position: relative;
}

.nuxt-img-wrapper::after {
    content: '';
    position: absolute;
    background-image: url('/images/background/White-pattern.jpg');
    background-size: cover;
    background-repeat: no-repeat;
    width: calc(100% + 24px);
    height: calc(100% + 24px);
    border-radius: 16px;
    z-index: 1;
    pointer-events: none;
}

.nuxt-img-wrapper::after {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    /* transform: rotate(5deg); */
}

/* Make sure the image is above the pseudo-elements */
.nuxt-img-wrapper img {
    position: relative;
    z-index: 2;
}
</style>

<i18n lang="yaml">
vi:
    'title': 'Phản hồi sau cuộc khám'
    'title:social': 'Lời bình trên mạng xã hội'
    'subtitle': 'Wellcare nỗ lực hàng ngày để mỗi bệnh nhân đều nhận được lời tư vấn chất lượng và trung thực từ các bác sĩ và tâm lý gia ưu tú nhất. Và chúng tôi hạnh phúc khi được chia sẻ với các bạn lời cảm ơn của rất nhiều các gia đình Việt trong và ngoài nước, để hiểu vì sao mọi người quyết định lựa chọn và gắn bó với Wellcare dài lâu.'
en:
    'title': 'Feedback after the teleconsultation'
    'title:social': 'Customer testimonials on social media'
    'subtitle': 'Whether you are seeking medical expertise or mental health support, Wellcare is here to walk alongside you on your journey to optimal health and happiness. We are honored that many Vietnamese families, both within the country and abroad, have welcomed us into their lives, and we look forward to extending that same level of personalized, exceptional care to you.'
</i18n>
