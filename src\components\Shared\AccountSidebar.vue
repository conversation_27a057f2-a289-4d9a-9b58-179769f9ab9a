<template>
    <!-- pt:mask:class="backdrop-blur-sm"-->
    <Sidebar
        v-bind="$attrs"
        position="full"
        header="Settings"
        :block-scroll="true"
        :modal="false"
        style="max-width: 50rem; max-height: 40rem"
        :pt="{
            header: 'flex flex-row items-center justify-between',
        }"
    >
        <SharedSettingsComputer v-if="screenSize == 'Computer'" />
        <SharedSettingsMobile v-else />
    </Sidebar>
</template>

<script setup lang="ts">
type ScreenSize = 'Mobile' | 'Computer'

interface Props {
    screenSize: ScreenSize
}

defineProps<Props>()
</script>
