import { computed, onMounted } from '#imports'
import { storeToRefs } from 'pinia'
import type { IPadding } from '~/models/common/device-info.interface'
import { useDeviceStore } from '~/stores/device'

export const useDevice = () => {
  const deviceStore = useDeviceStore()
  const {
    deviceInfo,
    batteryInfo,
    deviceId,
    languageCode,
    languageTag,
    safeArea,
  } = storeToRefs(deviceStore)

  const padding = computed<IPadding>(() => ({
    top: safeArea.value?.top || 0,
    right: safeArea.value?.right || 0,
    left: safeArea.value?.left || 0,
    bottom: safeArea.value?.bottom || 0,
  }))

  const fetchAllDeviceInfo = async () => {
    await Promise.all([
      deviceStore.fetchDeviceInfo(),
      deviceStore.fetchBatteryInfo(),
      deviceStore.fetchDeviceId(),
      deviceStore.fetchLanguageCode(),
      deviceStore.fetchLanguageTag(),
    ])
  }

  onMounted(async () => {
    await fetchAllDeviceInfo()
  })

  return {
    deviceInfo,
    batteryInfo,
    deviceId,
    languageCode,
    languageTag,
    safeArea,
    padding,
    isIos: computed(() => deviceStore.isIos),
    isAndroid: computed(() => deviceStore.isAndroid),
    isWeb: computed(() => deviceStore.isWeb),
  }
}
