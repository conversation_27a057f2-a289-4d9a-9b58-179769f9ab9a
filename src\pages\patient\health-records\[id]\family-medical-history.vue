<script setup lang="ts">
import { useFamilyHealthHistory } from '~/composables/page/patient/health-records/family-health-history'

definePageMeta({
    layout: 'nested-page',
})

const { familyHealthHistory } = useFamilyHealthHistory()
</script>

<template>
    <div class="flex flex-col gap-4 px-6 pb-6">
        <div class="text-xl font-semibold">Family Medical History</div>
        <div
            v-for="familyMember in familyHealthHistory"
            :key="familyMember.relation"
            class="flex flex-col gap-2 rounded-xl bg-surface-100 p-4 dark:bg-surface-900"
        >
            <div class="flex flex-row items-center gap-2">
                <Avatar
                    :image="familyMember.person.avatar"
                    shape="circle"
                    size="large"
                />
                <div class="flex-auto">
                    <p class="text-xl font-semibold">
                        {{ familyMember.person.name }}
                    </p>
                    <p class="text-slate-700 dark:text-slate-300">
                        {{ familyMember.relation }}
                    </p>
                </div>
                <Button
                    icon="pi pi-pencil"
                    text
                    plain
                    size="small"
                    class="self-start"
                />
            </div>
            <p>{{ familyMember.description }}</p>
        </div>
    </div>
</template>
