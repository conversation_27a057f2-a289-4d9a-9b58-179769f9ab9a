// composables/useRevenueCat.ts
import { ref, onMounted } from 'vue'
import { LOG_LEVEL, PRODUCT_CATEGORY } from '@revenuecat/purchases-capacitor'
import { readonly, useRuntimeConfig } from '#imports'
import type { IUser } from '~/models'
import { Capacitor } from '@capacitor/core'

export interface UseRevenueCatOptions {
    autoInit?: boolean
    logLevel?: LOG_LEVEL
    onCustomerInfoUpdate?: (customerInfo: any) => void
    onError?: (error: Error) => void
}

export interface PurchaseState {
    isLoading: boolean
    error: Error | null
    lastFunctionExecuted: string
    lastFunctionContent: string
}

export const useRevenueCat = (options: UseRevenueCatOptions = {}) => {
    const {
        autoInit = true,
        logLevel = LOG_LEVEL.INFO,
        onCustomerInfoUpdate,
        onError = console.error,
    } = options

    // Get API key from Nuxt config
    const config: any = useRuntimeConfig()
    const apiKey = config.public.revenueCat.apiKey
    const isIos = computed(() => Capacitor.getPlatform() === 'ios')

    // Get userID
    // const { user } = useUserInfo()

    const { $purchases } = useNuxtApp()

    if (!apiKey) {
        throw new Error('RevenueCat API key is not configured in Nuxt config')
    }

    // State
    const state = ref<PurchaseState>({
        isLoading: false,
        error: null,
        lastFunctionExecuted: 'No RC function executed',
        lastFunctionContent: 'No content',
    })

    const mockWebResults = ref(false)
    const simulatesAskToBuyInSandbox = ref(false)

    // Utility functions
    const prettifyJson = (obj: object): string => {
        return JSON.stringify(obj, null, 2)
    }

    const setLoading = (isLoading: boolean) => {
        state.value.isLoading = isLoading
    }

    const setError = (error: Error | null) => {
        state.value.error = error
        if (error && onError) {
            onError(error)
        }
    }

    const updateLastFunction = (
        functionName: string,
        content: object | string,
    ) => {
        state.value.lastFunctionExecuted = functionName
        state.value.lastFunctionContent =
            typeof content === 'string' ? content : prettifyJson(content)
    }

    // Wrapper for async functions with error handling
    const safeExecute = async <T>(
        functionName: string,
        operation: () => Promise<T>,
    ): Promise<T | null> => {
        setLoading(true)
        setError(null)

        try {
            const result = await operation()
            updateLastFunction(
                functionName,
                result || `${functionName} executed successfully`,
            )
            return result
        } catch (error) {
            const err =
                error instanceof Error ? error : new Error(String(error))
            setError(err)
            updateLastFunction(functionName, `Error: ${err.message}`)
            return null
        } finally {
            setLoading(false)
        }
    }

    // Core initialization
    const initializePurchases = async () => {
        await safeExecute('initializePurchases', async () => {
            await $purchases.setLogLevel({ level: logLevel })
            // Set up customer info listener
            if (onCustomerInfoUpdate) {
                await $purchases.addCustomerInfoUpdateListener(
                    onCustomerInfoUpdate,
                )
            }

            return 'Purchases initialized'
        })
    }

    // Environment controls
    const toggleMockWebResults = async () => {
        return safeExecute('setMockWebResults', async () => {
            await $purchases.setMockWebResults({
                shouldMockWebResults: mockWebResults.value,
            })
            return `Mock web results ${mockWebResults.value ? 'enabled' : 'disabled'}`
        })
    }

    const toggleSimulatesAskToBuyInSandbox = async () => {
        return safeExecute('setSimulatesAskToBuyInSandbox', async () => {
            await $purchases.setSimulatesAskToBuyInSandbox({
                simulatesAskToBuyInSandbox: simulatesAskToBuyInSandbox.value,
            })
            return `Ask to buy simulation ${simulatesAskToBuyInSandbox.value ? 'enabled' : 'disabled'}`
        })
    }

    // Purchase functions
    const getOfferings = async () => {
        return safeExecute('getOfferings', async () => {
            return await $purchases.getOfferings()
        })
    }

    const getProducts = async (productIds: string[]) => {
        return safeExecute('getProducts', async () => {
            return await $purchases.getProducts({
                productIdentifiers: productIds,
                type: PRODUCT_CATEGORY.SUBSCRIPTION,
            })
        })
    }

    const purchaseStoreProduct = async () => {
        return safeExecute('purchaseStoreProduct', async () => {
            const offerings = await $purchases.getOfferings()
            const productToBuy =
                offerings.current?.availablePackages?.[0]?.product

            if (!productToBuy) {
                throw new Error('No product found in current offering')
            }

            return await $purchases.purchaseStoreProduct({
                product: productToBuy,
            })
        })
    }

    const purchasePackage = async () => {
        return safeExecute('purchasePackage', async () => {
            const offerings = await $purchases.getOfferings()
            const packageToBuy = offerings.current?.availablePackages?.[0]

            if (!packageToBuy) {
                throw new Error('No package found in current offering')
            }

            return await $purchases.purchasePackage({
                aPackage: packageToBuy,
            })
        })
    }

    const restorePurchases = async () => {
        return safeExecute('restorePurchases', async () => {
            return await $purchases.restorePurchases()
        })
    }

    const getCustomerInfo = async () => {
        return safeExecute('getCustomerInfo', async () => {
            return await $purchases.getCustomerInfo()
        })
    }

    const syncPurchases = async () => {
        return safeExecute('syncPurchases', async () => {
            await $purchases.syncPurchases()
            return 'Purchases synced successfully'
        })
    }

    const checkTrialOrIntroductoryPriceEligibility = async (
        productIDsToCheck: string[],
    ) => {
        return safeExecute(
            'checkTrialOrIntroductoryPriceEligibility',
            async () => {
                return await $purchases.checkTrialOrIntroductoryPriceEligibility(
                    {
                        productIdentifiers: productIDsToCheck,
                    },
                )
            },
        )
    }

    const canMakePayments = async () => {
        return safeExecute('canMakePayments', async () => {
            return await $purchases.canMakePayments()
        })
    }

    const isConfigured = async () => {
        return safeExecute('isConfigured', async () => {
            return await $purchases.isConfigured()
        })
    }

    // Login RevenueCat với user ID mới
    const login = async (userData: IUser) => {
        try {
            // Login với user ID mới
            await $purchases.logIn({ appUserID: userData?._id || '' })

            // Set thông tin user
            if (userData.name) {
                await $purchases.setDisplayName({ displayName: userData.name })
            }
            if (userData.phone) {
                await $purchases.setPhoneNumber({ phoneNumber: userData.phone })
            }
            if (userData.email) {
                await $purchases.setEmail({ email: userData.email })
            }

            console.log('RevenueCat user logged in successfully!')
        } catch (error) {
            console.error('Failed to login RevenueCat user:', error)
            throw error
        }
    }

    // Logout khỏi RevenueCat
    const logout = async () => {
        try {
            await $purchases.logOut()
            console.log('RevenueCat user logged out successfully!')
        } catch (error) {
            console.error('Failed to logout RevenueCat user:', error)
            throw error
        }
    }

    // Auto-initialize if enabled
    onMounted(() => {
        if (autoInit && isIos.value) {
            initializePurchases()
        }
    })

    return {
        // State
        state: readonly(state),
        mockWebResults,
        simulatesAskToBuyInSandbox,

        // Core functions
        initializePurchases,

        // Environment controls
        toggleMockWebResults,
        toggleSimulatesAskToBuyInSandbox,

        // Purchase functions
        getOfferings,
        getProducts,
        purchaseStoreProduct,
        purchasePackage,
        restorePurchases,
        getCustomerInfo,
        syncPurchases,
        checkTrialOrIntroductoryPriceEligibility,
        canMakePayments,
        isConfigured,
        login,
        logout,

        $purchases,
    }
}
