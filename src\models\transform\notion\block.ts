import type { BlockType, ParentType } from '../../../types'
import type { IBookmark } from './bookmark'
import type { IBreadcrumb } from './breadcrumb'
import type { IBulletedListItem } from './bulleted-list-item'
import type { ICallout } from './callout'
import type { IChildDatabase } from './child-database'
import type { IChildPage } from './child-page'
import type { ICode } from './code'
import type { IColumn } from './column'
import type { IColumnList } from './column-list'
import type { IDivider } from './divider'
import type { IEmbed } from './embed'
import type { IEquation } from './equation'
import type { IHeading } from './heading'
import type { IImage } from './image'
import type { ILinkPreview } from './link-preview'
import type { IMention } from './mention'
import type { INotionFile } from './notion-file'
import type { INumberedListItem } from './number-list-item'
import type { IParagraph } from './paragraph'
import type { IPdf } from './pdf'
import type { IQuote } from './quote'
import type { ISyncedBlock } from './synced-block'
import type { ITable } from './table'
import type { ITableOfContents } from './table-of-contents'
import type { IToDo } from './todo'
import type { IToggle } from './toggle'
import type { IVideo } from './video'

interface IPerson {
  object: 'user'
  id: string
}

export interface INotionBlock {
  object: 'block'
  id: string
  parent: {
    type: ParentType
    page_id?: string
    database_id?: string
    workspace?: boolean
    block_id?: string
  }
  request_id?: string
  created_time: string
  last_edited_time: string
  created_by: IPerson
  last_edited_by: IPerson
  has_children: boolean
  archived: boolean
  type: BlockType
  heading_1?: IHeading
  heading_2?: IHeading
  heading_3?: IHeading
  bookmark?: IBookmark
  breadcrumb?: IBreadcrumb
  bulleted_list_item?: IBulletedListItem
  callout?: ICallout
  child_database?: IChildDatabase
  child_page?: IChildPage
  code?: ICode
  column_list?: IColumnList
  column?: IColumn
  divider?: IDivider
  embed?: IEmbed
  equation?: IEquation
  file?: INotionFile
  image?: IImage
  link_preview?: ILinkPreview
  mention?: IMention
  numbered_list_item?: INumberedListItem
  paragraph?: IParagraph
  pdf?: IPdf
  quote?: IQuote
  synced_block?: ISyncedBlock
  table?: ITable
  table_of_contents?: ITableOfContents
  to_do?: IToDo
  toggle?: IToggle
  video?: IVideo
}
