import { computed, useLazyAsyncData } from '#imports'
import { GenericEndpoint, type IProvider } from '~/models'

/**
 * A composable function to fetch and manage the assigned doctor for a specific patient.
 *
 * @param userId - The ID of the patient for whom the assigned doctor is to be fetched.
 *
 * @returns An object containing the following properties:
 * - `data`: A ref to the fetched data, which includes the assigned doctor details.
 * - `personalDoctorList`: A computed property that returns the list of assigned doctors from the fetched data.
 */

export function useGetAssignedDoctor(
    userId: Ref<string> | ComputedRef<string>,
) {
    const { $fetchWellcare } = useNuxtApp()
    const { data, status, execute } = useLazyAsyncData(
        () =>
            $fetchWellcare(
                `${GenericEndpoint.PERSONAL_DOCTOR}/${userId.value}`,
                {
                    query: {
                        populate: JSON.stringify([
                            { path: 'related' },
                            { path: 'provider' },
                        ]),
                        count: true,
                        pageSize: 20,
                        pageIndex: 1,
                    },
                },
            ),
        {
            immediate: false,
        },
    )

    // Function to transform each item into the IProvider structure
    function transformProviderData(item: any): IProvider {
        const provider = item.provider
        return {
            _id: provider._id,
            name: provider.name,
            avatar: {
                url: provider.avatar?.url || '',
            },
            title: provider.title || '',
            slug: provider.slug || '',
            highlight: provider.highlight || '',
            profile: {
                education: provider.profile?.education || '',
                experiencesDescription:
                    provider.profile?.experiencesDescription || '',
            },
            // specialties: provider.specialty || [],
            isPersonalDoctor: item?.isPersonalDoctor,
            specialty: provider.specialty || [],
            user: provider?.user,
        }
    }

    // Function to extract extra information (conversation, related fields)
    function extractAdditionalInfo(item: any) {
        const { conversation, related, isPersonalDoctor } = item
        return {
            conversation,
            isPersonalDoctor,
            related: {
                _id: related._id,
                name: related.name,
                avatar: {
                    url: related.avatar?.url || '',
                },
                gender: related.gender,
                meta: related.meta || {},
            },
        }
    }

    // Transform the data for providers
    const transformedProviders = computed<IProvider[]>(() =>
        (data.value?.results || []).map(transformProviderData),
    )

    // Extract additional related info
    const additionalInfo = computed(() =>
        (data.value?.results || []).map(extractAdditionalInfo),
    )

    return { data, status, execute, transformedProviders, additionalInfo }
}
