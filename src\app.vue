<script setup lang="ts">
// Capacitor imports
import { App } from '@capacitor/app'
import { Capacitor } from '@capacitor/core'
import { ScreenOrientation } from '@capacitor/screen-orientation'
import { SafeArea } from 'capacitor-plugin-safe-area'
// UI imports
import Toast from 'primevue/toast'
import { useJsonConfigApp } from './composables/fetch/config/get-config'

// Composables
const route = useRoute()
const { t } = useI18n({ useScope: 'local' })
const { handleAppAuthUrlOpen } = useAppAuth()
const { config } = useJsonConfigApp()
const localeHead = useLocaleHead({
    addDirAttribute: true,
    identifierAttribute: 'id',
    addSeoAttributes: true,
})

// Computed properties
const title = computed(
    () => route.meta?.title || route.matched[0].meta?.title || '',
)
const isNative = computed(() => Capacitor.isNativePlatform())
const isAndroid = computed(() => Capacitor.getPlatform() === 'android')
const shouldShowAnnouncement = computed(
    () => config.value?.global?.showAnnouncement === true,
)

// Initialization functions
const initSafeArea = async () => {
    const safeAreaData = await SafeArea.getSafeAreaInsets()
    const { insets } = safeAreaData
    for (const [key, value] of Object.entries(insets)) {
        const val = isAndroid.value ? value / 2 : value
        document.documentElement.style.setProperty(
            `--safe-area-inset-${key}`,
            `${val}px`,
        )
    }
}

const initScreenOrientation = async () => {
    if (isNative.value) {
        try {
            await ScreenOrientation.lock({ orientation: 'portrait' })
        } catch (error) {
            console.error('Error locking screen orientation:', error)
        }
    }
}

const visible = ref<boolean>(false)

function openWebsite() {
    window.open('https://wellcare.vn/', '_blank')
}

// Head configuration
useHead({
    title,
    titleTemplate: (t) => (t ? `${t} | Wellcare` : 'WELLCARE'),
    htmlAttrs: {
        lang: localeHead.value.htmlAttrs.lang,
        dir: localeHead.value.htmlAttrs.dir,
    },
    link: [
        {
            rel: 'apple-touch-icon',
            sizes: '57x57',
            href: '/apple-icon-57x57.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '60x60',
            href: '/apple-icon-60x60.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '72x72',
            href: '/apple-icon-72x72.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '76x76',
            href: '/apple-icon-76x76.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '114x114',
            href: '/apple-icon-114x114.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '120x120',
            href: '/apple-icon-120x120.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '144x144',
            href: '/apple-icon-144x144.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '152x152',
            href: '/apple-icon-152x152.png',
        },
        {
            rel: 'apple-touch-icon',
            sizes: '180x180',
            href: '/apple-icon-180x180.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '192x192',
            href: '/android-icon-192x192.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '32x32',
            href: '/favicon-32x32.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '96x96',
            href: '/favicon-96x96.png',
        },
        {
            rel: 'icon',
            type: 'image/png',
            sizes: '16x16',
            href: '/favicon-16x16.png',
        },
        { rel: 'manifest', href: '/manifest.json' },
    ],
    meta: [
        { charset: 'utf-8' },
        {
            name: 'viewport',
            content:
                'width=device-width, initial-scale=1.0, viewport-fit=cover',
        },
        { name: 'msapplication-TileColor', content: '#ffffff' },
        { name: 'theme-color', content: '#ffffff' },
        { name: 'msapplication-TileImage', content: '/ms-icon-144x144.png' },
    ],
})

// SEO configuration
useSeoMeta({
    viewport: 'width=device-width, initial-scale=1, maximum-scale=1',
    title: t('title'),
    ogTitle: t('title'),
    description: t('meta:description'),
    ogDescription: t('meta:description'),
    ogImage:
        'https://storage.googleapis.com/wellcare-assets/wellcare-banner.png',
    twitterCard: 'summary_large_image',
})

// Lifecycle hooks
onMounted(async () => {
    await initSafeArea()
    await App.addListener('appUrlOpen', handleAppAuthUrlOpen)
    await initScreenOrientation()
    // if(isNative.value) setTimeout(() => {
    //     visible.value = true
    // }, 2500);
})
</script>

<template>
    <div id="app" :class="['h-screen']">
        <!-- Notivue -->
        <WNotivueWrapper />
        <SharedCallHandle />
        <NuxtLayout>
            <div
                v-if="shouldShowAnnouncement"
                class="cursor-pointer px-4 py-2 text-center font-semibold text-white"
                :class="{
                    'pt-safe': isNative,
                }"
                style="
                    background: linear-gradient(
                        to right,
                        #ff66b0,
                        #fab13f,
                        #0ed294
                    );
                "
                @click="visible = true"
            >
                <p>📢 Thông báo quan trọng từ Wellcare</p>
            </div>
            <SharedAppVersionCheck />
            <NuxtPage
                :transition="{
                    name: 'slide-right',
                    mode: 'out-in',
                }"
            />
            <Toast />
            <DynamicDialog />
            <ConfirmDialog class="w-full md:min-w-[600px]" dismissable-mask />
            <Dialog
                v-model:visible="visible"
                header=""
                modal
                :draggable="false"
                :dismissable-mask="true"
                pt:mask:class="backdrop-blur-sm"
                position="bottom"
                :closable="false"
            >
                <template #header>
                    <div class="text-center text-lg font-semibold">
                        📢 Thông báo quan trọng từ Wellcare
                    </div>
                </template>
                <!-- <template #container="{ closeCallback }"> -->
                <div class="w-full sm:w-[550px]">
                    <!-- <div class="text-center text-lg font-semibold">
                            
                        </div> -->
                    <div>
                        <p>
                            Wellcare sẽ ngừng hoạt động khám từ xa kể từ
                            01/03/2025.
                        </p>
                        <p class="mt-2">
                            Tiền thanh toán dư hoặc được hoàn về đang lưu trong
                            sổ khám chỉ có thể dùng để khám từ xa và không thể
                            rút ra (Thông tư 39/2014/TT-NHNN và Thông tư
                            23/2019/TT-NHNN của Ngân hàng Nhà nước Việt Nam).
                        </p>

                        <ul class="mt-2 list-inside list-disc">
                            <li class="mt-2">
                                Hệ thống đặt hẹn vẫn tiếp tục duy trì đến
                                31/03/2025 và chỉ hoạt động để những bệnh nhân
                                đã chuyển tiền dư hoặc được hoàn về sổ khám
                                (tính đến 28/02/2025) tiếp tục sử dụng dịch vụ
                                đến 31/03/2025.
                            </li>
                            <li class="mt-2">
                                Từ 01/03/2025, Wellcare không hỗ trợ chuyển
                                khoản hoặc thanh toán mới.
                            </li>
                            <li class="mt-2">
                                Người dùng đã là thành viên vẫn có thể dùng các
                                quyền lợi Health GPT, EduHub, Health Programs
                                trên ứng dụng cho đến ngày hết hạn.
                            </li>
                        </ul>
                    </div>
                    <!-- <div class="mt-4 flex flex-col justify-between space-y-4">
                        <Button
                            class="bg-zinc-200 font-semibold uppercase text-zinc-700"
                            text
                            @click="openWebsite"
                        >
                            Chi tiết
                        </Button>
                        <Button
                                class="bg-zinc-200 font-semibold uppercase text-zinc-700"
                                text
                                @click="closeCallback"
                            >
                                Đóng
                            </Button>
                    </div> -->
                </div>
                <!-- </template> -->
                <template #footer>
                    <div class="w-full mt-4 flex flex-col justify-between space-y-4">
                        <Button
                            class="bg-zinc-200 font-semibold uppercase text-zinc-700"
                            text
                            @click="openWebsite"
                        >
                            Chi tiết
                        </Button>
                        <Button
                            class="bg-zinc-200 font-semibold uppercase text-zinc-700"
                            text
                            @click="visible = false"
                        >
                            Đóng
                        </Button>
                    </div>
                </template>
            </Dialog>
        </NuxtLayout>
    </div>
</template>

<i18n lang="yaml">
vi:
    'title': 'Wellcare - Chăm sóc sức khỏe toàn diện'
    'meta:description': 'Nền tảng chăm sóc sức khỏe toàn diện với đội ngũ bác sĩ chuyên môn cao, tư vấn sức khỏe từ xa, đặt lịch khám và nhiều dịch vụ y tế khác.'
en:
    'title': 'Wellcare - Comprehensive Healthcare Solutions'
    'meta:description': 'A comprehensive healthcare platform with highly qualified doctors, telemedicine consultations, appointment scheduling and various medical services.'
</i18n>
