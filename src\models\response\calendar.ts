import type { IBaseResponse } from '../../types/base'

export const ETYPE_CALENDAR_ITEM = [
    'consultation',
    'booking',
    'outofoffice',
    'onduty',
    'bookingoncall',
    'event',
    'others',
] as const
export const ESTATE_CALENDAR_ITEM = [
    'scheduled',
    'cancelled',
    'completed',
] as const
export const ECANCELLEDBY_CALENDAR_ITEM = [
    'guest',
    'owner',
    'system',
    'admin',
] as const
export const EDURATION_CALENDAR_ITEM = [5, 10, 15, 20, 25, 30, 45, 60] as const
export const EMEDIUM_CALENDAR_ITEM = ['video', 'phone', 'question'] as const

export interface BookingFromDto {
    now?: boolean
    date?: Date
}

export interface BookingToDto {
    never?: boolean
    date?: Date
}

export interface BookingDayDto {
    mon?: boolean
    tue?: boolean
    wed?: boolean
    thu?: boolean
    fri?: boolean
    sat?: boolean
    sun?: boolean
}

export interface IBooking {
    day?: BookingDayDto
    delay?: number
    from?: BookingFromDto
    to?: BookingToDto
    duration?: (typeof EDURATION_CALENDAR_ITEM)[number]
}

export interface ICalendarItem extends IBaseResponse {
    calendar?: string
    active?: boolean
    type?: (typeof ETYPE_CALENDAR_ITEM)[number]
    state?: (typeof ESTATE_CALENDAR_ITEM)[number]
    cancelledBy?: (typeof ECANCELLEDBY_CALENDAR_ITEM)[number]
    title?: string
    consultation?: string
    conversation?: string
    order?: string
    orderItem?: string
    icon?: string
    allDay?: boolean
    slotIndex?: number
    slotDuration?: number
    startTime?: Date
    endTime?: Date
    isHolliday?: boolean
    description?: string
    location?: string
    booking?: IBooking
    medium?: (typeof EMEDIUM_CALENDAR_ITEM)[number]
    metadata: Record<string, unknown>
}
