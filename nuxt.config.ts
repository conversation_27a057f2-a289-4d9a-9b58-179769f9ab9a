import {
    app,
    googleFonts,
    i18n,
    image,
    notivue,
    primevue,
    runtimeConfig,
} from './src/configs'

const env = process.env

// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
    app,

    ssr: false,

    i18n,

    image,

    googleFonts,

    primevue,

    alias: {
        yup: 'yup',
        dayjs: 'dayjs/esm',
    },

    modules: [
        '@primevue/nuxt-module',
        'notivue/nuxt',
        '@nuxtjs/tailwindcss',
        '@nuxtjs/color-mode',
        '@wellcare/nuxt3-module-data-layer',
        '@wellcare/nuxt3-module-account',
        '@wellcare/nuxt3-module-chart',
        '@wellcare/nuxt3-module-form',
        ['@wellcare/formsible-runner', { prefix: 'F' }],
        '@wellcare/nuxt3-module-consultation',
        '@wellcare/nuxt3-module-conference',
        ['@wellcare/nuxt3-module-notion', { prefix: 'n', level: 1 }],
        '@wellcare/muot-ui',
        '@wellcare/nuxt3-module-chat',
        '@wellcare/nuxt3-module-media',
        '@wellcare/nuxt3-module-phr',
        '@nuxt/test-utils/module',
        '@nuxtjs/google-fonts',
        '@pinia/nuxt',
        '@vueuse/nuxt',
        '@nuxt/image',
        '@zadigetvoltaire/nuxt-gtm',
        'nuxt-jsonld',
        '@nuxt/eslint',
        // If you use other modules (eg. nuxt-i18n), always declare the sitemap module at end of array
        'dayjs-nuxt',
        '@nuxtjs/i18n',
    ],

    devtools: {
        enabled: false,
    },

    srcDir: 'src/',

    imports: {
        dirs: ['./stores'],
    },

    dayjs: {
        locales: ['vi', 'en'],
        plugins: [
            'utc',
            'localizedFormat',
            'relativeTime',
            'calendar',
            'customParseFormat',
            'duration',
        ],
        defaultLocale: 'vi',
    },

    colorMode: {
        preference: 'light',
    },

    plugins: ['~/plugins/lodash.ts', '~/plugins/primevue-toastservice.ts'],

    css: [
        '@/assets/styles/index.css',
        'primeicons/primeicons.css',
        'notivue/notification-progress.css',
    ],

    tailwindcss: {
        configPath: '@@/tailwind.config',
    },

    postcss: {
        plugins: {
            'postcss-import': {},
            tailwindcss: {},
            autoprefixer: {},
        },
    },

    notivue,

    vite: {
        optimizeDeps: {
            exclude: ['primevue'],
        },
    },

    runtimeConfig: runtimeConfig(env),

    compatibilityDate: '2024-07-03',
})
