<script lang="ts" setup>
import type { IHealthProgramCalendar } from '~/models'

const { t } = useI18n()
const route = useRoute()

const selectedTab = ref<string>('')
const videoRef = ref<HTMLVideoElement | null>(null)
const week = computed(() => selectedTab.value.split(':')?.[1])
const activeTodayTime = computed(() =>
    times.value.find(
        (time: IHealthProgramCalendar) => time.isActive && time.isToday,
    ),
)
const activeUnit = computed(
    () =>
        times.value.find(
            (item: IHealthProgramCalendar) => item.label === selectedTab.value,
        )?.unit,
)

const { times, loading, shortArticle } =
    usePagePatientServicesHealthProgramsPregnancyDiary(selectedTab)

    const handleUpdateValue = (value: string) => {
    selectedTab.value = value
}

watch(loading, () => {
    if (!loading.value) {
        selectedTab.value = activeTodayTime.value?.label || ''
    }
})

watch(
    shortArticle,
    () => {
        if (videoRef.value) {
            videoRef.value.load()
        }
    },
    {
        immediate: true,
        deep: true,
    },
)
</script>

<template>
    <div v-if="loading">
        <Skeleton width="100%" height="200px" />
        <Skeleton width="90%" height="20px" class="ml-4 mt-4" />
        <Skeleton width="60%" height="16px" class="ml-4 mt-4" />
        <Skeleton width="80%" height="16px" class="ml-4 mt-2" />
        <Skeleton width="70%" height="16px" class="ml-4 mt-2" />
        <Skeleton width="80%" height="16px" class="ml-4 mt-2" />
    </div>
    <PagePatientServicesHealthProgramCalendar
        v-else
        :times="times"
        :active-tab="selectedTab"
        :subtitle="{
            content: t(activeUnit || ''),
            position: 'top'
        }"
        @on-change-tab="handleUpdateValue"
    >
        <template #content>
            <div class="h-[calc(100vh-150px)] overflow-auto">
                <div class="relative">
                    <!-- Video -->
                    <video
                        ref="videoRef"
                        loop
                        muted
                        autoplay
                        playsinline
                        poster="https://storage.googleapis.com/cms-gallery/66417b31cba4a4bf1c287757/thumbnail-mvb.png"
                    >
                        <source :src="shortArticle.cover" type="video/mp4" />
                    </video>

                    <!-- Overlay -->
                    <div
                        class="absolute left-0 top-0 h-full w-full bg-gradient-to-t from-black/50 to-black/10"
                    />

                    <!-- Title -->
                    <h4
                        class="absolute bottom-2 left-2 w-4/5 text-white sm:w-full"
                        style="
                            text-shadow:
                                2px 7px 5px rgba(0, 0, 0, 0.3),
                                0px -4px 10px rgba(255, 255, 255, 0.3);
                        "
                    >
                        {{ t(shortArticle.name) }}
                    </h4>

                    <!-- Button -->
                    <NuxtLinkLocale
                        :to="`${route.fullPath}/nktk-tuan-${week}`"
                        class="absolute -bottom-4 right-3"
                    >
                        <Button class="uppercase" :label="t('btn:detail')" />
                    </NuxtLinkLocale>
                </div>

                <!-- Content -->
                <WArticleBody
                    v-if="shortArticle.blocks?.length"
                    class="mt-6 px-6 pb-4"
                    :blocks="shortArticle.blocks"
                />
            </div>
        </template>
    </PagePatientServicesHealthProgramCalendar>
</template>

<i18n lang="json">
{
    "en": {
        "btn:detail": "Detail",
        "week": "Week"
    },
    "vi": {
        "btn:detail": "Chi tiết",
        "week": "Tuần"
    }
}
</i18n>
