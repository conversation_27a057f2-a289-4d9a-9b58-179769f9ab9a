import { ElasticIndex } from '~/models'
import type { HitNotionWebsite } from '~/models'

export function usePageSettingsSupportPrivacyPolicy() {
    const { hit, loading } = useFetchElasticWithDefault<HitNotionWebsite>(
        ElasticIndex.NOTION_WEBSITE,
        {
            filters: [
                {
                    term: {
                        'page.properties.Parent item.properties.Slug.keyword':
                            'ho-tro',
                    },
                },
                {
                    term: {
                        'page.properties.Slug.keyword': 'chinh-sach-bao-mat',
                    },
                },
                {
                    term: {
                        'page.properties.Type.keyword': 'Article',
                    },
                },
            ],
            _source: {
                includes: ['page', 'blockstring'],
            },
            size: 1,
        },
    )

    return { hit, loading }
}
